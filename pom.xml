<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

<!--    <parent>-->
<!--        <groupId>com.deepexi.cloud</groupId>-->
<!--        <artifactId>deepexi-cloud-parent</artifactId>-->
<!--        <version>1.1.0-SNAPSHOT</version>-->
<!--        <relativePath/>-->
<!--    </parent>-->
<!--    <parent>-->
<!--        <groupId>com.deepexi.cloud</groupId>-->
<!--        <artifactId>deepexi-cloud-dependencies</artifactId>-->
<!--        <version>1.1.0-SNAPSHOT</version>-->
<!--        <relativePath/>-->
<!--    </parent>-->

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>2.3.2.RELEASE</version>
        <relativePath/>
    </parent>

    <groupId>com.deepexi.dxp</groupId>
    <artifactId>dxp-domain-marketing</artifactId>
    <version>1.0.0-BASE-SNAPSHOT</version>
    <packaging>pom</packaging>

    <modules>
        <module>dxp-domain-marketing-api</module>
        <module>dxp-domain-marketing-common</module>
        <module>dxp-middle-promotion</module>
        <module>dxp-middle-marketing</module>
        <module>huafa-actvity-provider</module>
    </modules>

    <properties>
        <!--   parent     -->
        <java.version>1.8</java.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <!--   parent     -->

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-cloud-alibaba.version>2.1.3.RELEASE</spring-cloud-alibaba.version>
        <deepexi.cloud.common.version>1.1.0-SNAPSHOT</deepexi.cloud.common.version>
        <sentinel.version>1.8.0</sentinel.version>
        <deepexi.redis.client>2.0.2-snapshot</deepexi.redis.client>
        <joda.version>2.10.5</joda.version>
        <java.jwt.version>3.8.1</java.jwt.version>
        <jsonwebtoken.version>0.9.1</jsonwebtoken.version>
        <cloudera.imapla.jdbc.version>2.6.4</cloudera.imapla.jdbc.version>
        <skywalking.apm.version>8.3.0</skywalking.apm.version>
        <avatica.druid.version>1.16.0</avatica.druid.version>
        <dxp.marketing.api.version>1.0.0-BASE-SNAPSHOT</dxp.marketing.api.version>
        <dxp.marketing.common.version>1.0.0-BASE-SNAPSHOT</dxp.marketing.common.version>
        <dxp.middle.marketing.version>1.0.0-BASE-SNAPSHOT</dxp.middle.marketing.version>
        <dxp.middle.promotion.version>1.0.0-BASE-SNAPSHOT</dxp.middle.promotion.version>
        <dxp.domain.marketing.provider.version>1.0.0-BASE-SNAPSHOT</dxp.domain.marketing.provider.version>
        <dxp.member.api.version>1.0.0-BASE-SNAPSHOT</dxp.member.api.version>
        <redisson.version>3.16.1</redisson.version>
<!--        <rocketmq.version>2.1.1</rocketmq.version>-->



    </properties>


    <dependencyManagement>

        <dependencies>

            <!--    parent    -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>Hoxton.SR9</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>2.2.5.RELEASE</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-datasource-nacos</artifactId>
                <version>1.8.0</version>
            </dependency>

            <!--https://mvnrepository.com/artifact/com.baomidou/mybatis-plus-boot-starter/3.4.1-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>3.4.1</version>
            </dependency>

            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>8.0.22</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.70</version>
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>1.14</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.16</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>29.0-jre</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.10</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.4.1</version>
            </dependency>

            <!-- deepexi-cloud 本身的三个依赖 -->
            <dependency>
                <groupId>com.deepexi.cloud</groupId>
                <artifactId>deepexi-cloud-common-domain</artifactId>
                <version>1.1.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.deepexi.cloud</groupId>
                <artifactId>deepexi-cloud-common-utils</artifactId>
                <version>1.1.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.deepexi.cloud</groupId>
                <artifactId>deepexi-cloud-common-ext</artifactId>
                <version>1.1.0-SNAPSHOT</version>
            </dependency>

<!--            &lt;!&ndash; deepexi cloud starter 依赖 &ndash;&gt;-->
<!--            <dependency>-->
<!--                <groupId>com.deepexi.cloud</groupId>-->
<!--                <artifactId>deepexi-cloud-starter-openfeign</artifactId>-->
<!--                <version>1.1.0-SNAPSHOT</version>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>com.deepexi.cloud</groupId>-->
<!--                <artifactId>deepexi-cloud-starter-rocketmq</artifactId>-->
<!--                <version>1.1.0-SNAPSHOT</version>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>com.deepexi.cloud</groupId>-->
<!--                <artifactId>deepexi-cloud-starter-seata</artifactId>-->
<!--                <version>1.1.0-SNAPSHOT</version>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>com.deepexi.cloud</groupId>-->
<!--                <artifactId>deepexi-cloud-starter-web</artifactId>-->
<!--                <version>1.1.0-SNAPSHOT</version>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>com.deepexi.cloud</groupId>-->
<!--                <artifactId>deepexi-cloud-starter-xxljob</artifactId>-->
<!--                <version>1.1.0-SNAPSHOT</version>-->
<!--            </dependency>-->

            <!-- 其它第三方common共用依赖 -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>1.7.30</version>
            </dependency>
            <dependency>
                <groupId>net.sf.dozer</groupId>
                <artifactId>dozer</artifactId>
                <version>5.5.1</version>
            </dependency>
            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>3.6</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>3.14.9</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-jexl</artifactId>
                <version>2.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>2.1.3</version>
            </dependency>

            <dependency>
                <groupId>org.codehaus.jackson</groupId>
                <artifactId>jackson-mapper-asl</artifactId>
                <version>1.9.13</version>
            </dependency>

            <dependency>
                <groupId>cglib</groupId>
                <artifactId>cglib-nodep</artifactId>
                <version>3.2.9</version>
            </dependency>

            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>2.9.2</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.swagger</groupId>
                        <artifactId>swagger-annotations</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.swagger</groupId>
                        <artifactId>swagger-models</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>1.5.22</version>
            </dependency>

            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-models</artifactId>
                <version>1.5.22</version>
            </dependency>

            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>2.9.2</version>
            </dependency>

            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>6.0.17.Final</version>
            </dependency>

            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-logback-1.x</artifactId>
                <version>8.3.0</version>
            </dependency>
            <!--    parent    -->

            <dependency>
                <groupId>com.deepexi.cloud</groupId>
                <artifactId>deepexi-cloud-common-domain</artifactId>
                <version>${deepexi.cloud.common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.deepexi.cloud</groupId>
                <artifactId>deepexi-cloud-common-utils</artifactId>
                <version>${deepexi.cloud.common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.deepexi.dxp</groupId>
                <artifactId>dxp-domain-marketing-api</artifactId>
                <version>${dxp.marketing.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.deepexi.dxp</groupId>
                <artifactId>dxp-domain-marketing-common</artifactId>
                <version>${dxp.marketing.common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.deepexi.dxp</groupId>
                <artifactId>dxp-middle-promotion</artifactId>
                <version>${dxp.middle.promotion.version}</version>
            </dependency>

            <dependency>
                <groupId>com.deepexi.dxp</groupId>
                <artifactId>dxp-middle-marketing</artifactId>
                <version>${dxp.middle.marketing.version}</version>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>com.deepexi.inf</groupId>-->
<!--                <artifactId>deepexi-redis-client-core</artifactId>-->
<!--                <version>${deepexi.redis.client}</version>-->
<!--            </dependency>-->

            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda.version}</version>
            </dependency>

            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>${java.jwt.version}</version>
            </dependency>

            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jsonwebtoken.version}</version>
            </dependency>

            <!--druid 查询依赖-->
            <dependency>
                <groupId>org.apache.calcite.avatica</groupId>
                <artifactId>avatica</artifactId>
                <version>${avatica.druid.version}</version>
            </dependency>


        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- spring boot -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>


        <!-- 其它 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
        </dependency>

        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>

        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
        </dependency>

        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
        </dependency>

        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.apache.rocketmq/rocketmq-spring-boot-starter -->
<!--        <dependency>-->
<!--            <groupId>org.apache.rocketmq</groupId>-->
<!--            <artifactId>rocketmq-spring-boot-starter</artifactId>-->
<!--            <version>${rocketmq.version}</version>-->
<!--        </dependency>-->

        <!-- https://mvnrepository.com/artifact/org.redisson/redisson-spring-boot-starter -->
        <dependency>
            <groupId>com.cnhuafas</groupId>
            <artifactId>huafa-common-starter</artifactId>
            <version>1.0</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.alicp.jetcache</groupId>-->
<!--            <artifactId>jetcache-starter-redis</artifactId>-->
<!--            <version>2.6.0</version>-->
<!--        </dependency>-->

        <!-- swagger2 在线生成文档  -->
<!--        <dependency>-->
<!--            <groupId>io.github.swagger2markup</groupId>-->
<!--            <artifactId>swagger2markup</artifactId>-->
<!--            <version>1.3.1</version>-->
<!--        </dependency>-->



    </dependencies>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <dxp.marketing.api.version>1.0.0-BASE-SNAPSHOT</dxp.marketing.api.version>
                <dxp.marketing.common.version>1.0.0-BASE-SNAPSHOT</dxp.marketing.common.version>
                <dxp.middle.promotion.version>1.0.0-BASE-SNAPSHOT</dxp.middle.promotion.version>
                <dxp.domain.marketing.provider.version>1.0.0-BASE-SNAPSHOT</dxp.domain.marketing.provider.version>
                <deepexi.domain.sdk.commodity.version>3.0.2-BASE-SNAPSHOT</deepexi.domain.sdk.commodity.version>
                <deepexi.domain.tag.version>1.1.0-BASE-SNAPSHOT</deepexi.domain.tag.version>
            </properties>
        </profile>


        <profile>
            <id>uat</id>
            <properties>
                <dxp.marketing.api.version>1.0.0-BASE</dxp.marketing.api.version>
                <dxp.marketing.common.version>1.0.0-BASE</dxp.marketing.common.version>
                <dxp.middle.promotion.version>1.0.0-BASE</dxp.middle.promotion.version>
                <dxp.domain.marketing.provider.version>1.0.0-BASE</dxp.domain.marketing.provider.version>
                <deepexi.domain.sdk.commodity.version>3.0.2-BASE</deepexi.domain.sdk.commodity.version>
                <deepexi.domain.tag.version>1.1.0-BASE-SNAPSHOT</deepexi.domain.tag.version>
            </properties>
        </profile>

    </profiles>

    <build>
        <plugins>
            <!-- springboot jar包运行所需maven插件 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.3.2.RELEASE</version>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                    <fork>true</fork>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- swagger2 在线生成文档  -->
<!--            <plugin>-->
<!--                <groupId>org.asciidoctor</groupId>-->
<!--                <artifactId>asciidoctor-maven-plugin</artifactId>-->
<!--                <version>1.5.6</version>-->
<!--                <configuration>-->
<!--                    &lt;!&ndash;asciidoc文件目录&ndash;&gt;-->
<!--                    <sourceDirectory>D:\build</sourceDirectory>-->
<!--                    &lt;!&ndash;-生成html的路径&ndash;&gt;-->
<!--                    <outputDirectory>D:\build\adoc</outputDirectory>-->
<!--                    <backend>html</backend>-->
<!--                    <sourceHighlighter>coderay</sourceHighlighter>-->
<!--                    <attributes>-->
<!--                        &lt;!&ndash;导航栏在左&ndash;&gt;-->
<!--                        <toc>left</toc>-->
<!--                        &lt;!&ndash;显示层级数&ndash;&gt;-->
<!--                        &lt;!&ndash;<toclevels>3</toclevels>&ndash;&gt;-->
<!--                        &lt;!&ndash;自动打数字序号&ndash;&gt;-->
<!--                        <sectnums>true</sectnums>-->
<!--                    </attributes>-->
<!--                </configuration>-->
<!--            </plugin>-->
        </plugins>
    </build>

</project>

