<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.deepexi.dxp</groupId>
        <artifactId>dxp-domain-marketing</artifactId>
        <version>1.0.0-BASE-SNAPSHOT</version>
    </parent>

    <artifactId>dxp-middle-promotion</artifactId>
    <version>${dxp.middle.promotion.version}</version>

    <dependencies>

        <dependency>
            <groupId>com.deepexi.dxp</groupId>
            <artifactId>dxp-domain-marketing-common</artifactId>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.3.2.RELEASE</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
