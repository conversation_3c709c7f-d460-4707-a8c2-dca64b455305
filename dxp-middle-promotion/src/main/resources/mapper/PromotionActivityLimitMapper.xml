<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepexi.dxp.middle.promotion.mapper.PromotionActivityLimitMapper">
    <resultMap id="BaseResultMap" type="com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityLimitDO">
        <!--@mbg.generated-->
        <!--@Table promotion_activity_limit-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="activity_id" jdbcType="BIGINT" property="activityId"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="limits" jdbcType="VARCHAR" property="limits"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="app_id" jdbcType="BIGINT" property="appId"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
        <result column="is_deleted" jdbcType="INTEGER" property="deleted"/>
        <result column="ext" jdbcType="VARCHAR" property="ext" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,activity_id , `type`, `limits`, tenant_id, app_id,
        created_time, updated_time, is_deleted, remark, version,ext
    </sql>


    <!--auto generated by MybatisCodeHelper on 2019-11-28-->
    <select id="selectAllByActivityIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from promotion_activity_limit
        <where>
            is_deleted = 0
            <if test="activityIdCollection != null and activityIdCollection.size() > 0">
                and activity_id in
                <foreach item="item" index="index" collection="activityIdCollection"
                         open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
    </select>

    <!--auto generated by MybatisCodeHelper on 2019-11-29-->
    <select id="selectByActivityId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from promotion_activity_limit
        <where>
            is_deleted = 0
            <if test="activityId != null">
                and activity_id=#{activityId,jdbcType=BIGINT}
            </if>
        </where>
    </select>

    <!-- 查询限制表 -->
    <select id="findAllLimitS"
            resultType="com.deepexi.dxp.middle.promotion.domain.dto.PromotionActivityLimitQueryResultDTO">
        SELECT
        t.activity_id,
        t.limits
        FROM
        `promotion_activity_limit` t
        <where>
            t.is_deleted = 0
            AND t.type = 3
            <if test="tenantId != null">
            AND t.tenant_id = #{tenantId}
            </if>
            <if test="appId != null">
            AND t.app_id = #{appId}
            </if>
            <if test="id != null">
                AND t.activity_id = #{id}
            </if>
            <if test="idList != null and idList.size() > 0">
                and t.activity_id in
                <foreach item="item" index="index" collection="idList"
                         open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <!--- 筛选渠道限制 -->
            <if test="channelId != null">
                AND t.activity_id IN (
                SELECT
                channel.activity_id
                FROM
                `promotion_activity_limit` channel
                <where>
                    channel.is_deleted = 0
                    AND channel.type = 4
                    <if test="tenantId != null">
                        AND channel.tenant_id = #{tenantId}
                    </if>
                    <if test="appId != null">
                        AND channel.app_id = #{appId}
                    </if>
                    AND  JSON_CONTAINS ( channel.limits, <![CDATA['{"id": "clientTenant", "flag": "==", "value": ]]>"${channelId}"<![CDATA[}']]>)
                    <if test="id != null">
                        AND channel.activity_id = #{id}
                    </if>
                    <if test="idList != null and idList.size() > 0">
                        and channel.activity_id in
                        <foreach item="item" index="index" collection="idList"
                                 open="(" separator="," close=")">
                            #{item,jdbcType=BIGINT}
                        </foreach>
                    </if>
                </where>
                )
            </if>
            <!-- 筛选会员限制 -->
            <if test="memberId != null and menberGroupId != null">
                AND t.activity_id IN (
                SELECT
                member.activity_id
                FROM
                `promotion_activity_limit` member
                <where>
                    member.is_deleted = 0
                    AND member.type = 5
                    <if test="tenantId != null">
                        AND member.tenant_id = #{tenantId}
                    </if>
                    <if test="appId != null">
                        AND member.app_id = #{appId}
                    </if>
                    <if test="id != null">
                        AND member.activity_id = #{id,jdbcType=BIGINT}
                    </if>
                    <if test="idList != null and idList.size() > 0">
                        and member.activity_id in
                        <foreach item="item" index="index" collection="idList"
                                 open="(" separator="," close=")">
                            #{item,jdbcType=BIGINT}
                        </foreach>
                    </if>
                    AND (
                    JSON_CONTAINS ( JSON_EXTRACT ( member.limits, '$[*].id' ), '"allMember"' )
                    <if test="memberId != null">
                        OR <![CDATA[JSON_CONTAINS ( member.limits, '{"id": "assignMember", "flag": "==", "value": "]]>${memberId}<![CDATA["}']]>
                        )
                    </if>
                    <if test="menberGroupId != null and menberGroupId.size() > 0">
                        OR member.activity_id IN (
                        SELECT
                        mgroup.activity_id
                        FROM
                        `promotion_activity_limit` mgroup
                        <where>
                            mgroup.is_deleted = 0
                            <if test="tenantId != null">
                                AND mgroup.tenant_id = #{tenantId}
                            </if>
                            <if test="appId != null">
                                AND mgroup.app_id = #{appId}
                            </if>
                            AND mgroup.type = 5
                            <if test="id != null">
                                AND mgroup.activity_id = #{id}
                            </if>
                            <if test="idList != null and idList.size() > 0">
                                and mgroup.activity_id in
                                <foreach item="item" index="index" collection="idList"
                                         open="(" separator="," close=")">
                                    #{item,jdbcType=BIGINT}
                                </foreach>
                            </if>
                            AND JSON_CONTAINS ( JSON_EXTRACT ( mgroup.limits, '$[*].id' ), '"memberGroup"' )
                            <foreach item="item" index="index" collection="menberGroupId"
                                     open="AND(" separator="OR " close=")">
                                <![CDATA[JSON_CONTAINS ( mgroup.limits, '{"id": "memberGroup", "flag": "==", "value": "]]>${item}<![CDATA["}' )]]>
                            </foreach>
                        </where>
                        )
                    </if>
                </where>
                ))
            </if>
            <!-- 店铺限制 -->
            <if test="shopId != null">
                AND t.activity_id IN (
                SELECT
                shop.activity_id
                FROM
                `promotion_activity_limit` shop
                <where>
                    shop.is_deleted = 0
                    <if test="tenantId != null">
                        AND shop.tenant_id = #{tenantId}
                    </if>
                    <if test="appId != null">
                        AND shop.app_id = #{appId}
                    </if>
                    AND shop.type = 6
                    <if test="id != null">
                        AND shop.activity_id = #{id}
                    </if>
                    <if test="activityIdS != null and activityIdS.size() > 0">
                        and shop.activity_id in
                        <foreach item="item" index="index" collection="activityIdS"
                                 open="(" separator="," close=")">
                            #{item,jdbcType=BIGINT}
                        </foreach>
                    </if>

                    AND (
                    JSON_CONTAINS ( JSON_EXTRACT ( shop.limits, '$[*].id' ), '"allShop"' )
                    <![CDATA[OR JSON_CONTAINS ( shop.limits, '{"id": "shopId", "flag": "==", "value": "]]>${shopId}<![CDATA["}' ) )]]>
                    )
                </where>
            </if>
        </where>
        GROUP BY t.activity_id
        ORDER BY t.updated_time DESC
    </select>


    <select id="findPage" resultType="java.lang.Long">
        SELECT activity_id
        FROM promotion_activity_limit
        WHERE is_deleted = 0
        and tenant_id = #{tenantId,jdbcType=VARCHAR}
        and app_id = #{appId,jdbcType=BIGINT}
        and (
        (
        type = 5 and JSON_EXTRACT(limits, '$[*].id') like '%#{userLimit}%'
        ) or (
        FALSE
        )
        )

    </select>
    <!--auto generated by MybatisCodeHelper on 2019-11-29-->
    <delete id="deletedByActivityIds">
        update promotion_activity_limit set is_deleted = 1
        where activity_id in
        <foreach item="item" index="index" collection="activityIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </delete>

    <insert id="insertList">
        insert into promotion_activity_limit(
        activity_id , `type`, `limits`, tenant_id, app_id,
        created_time, updated_time, is_deleted, remark, version
        ) values
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.activityId,jdbcType=INTEGER},
            #{element.type,jdbcType=INTEGER},
            #{element.limits,jdbcType=INTEGER},
            #{element.tenantId,jdbcType=VARCHAR},
            #{element.appId,jdbcType=VARCHAR},
            #{element.createdTime,jdbcType=TIMESTAMP},
            #{element.updatedTime,jdbcType=TIMESTAMP},
            0,
            #{element.remark,jdbcType=VARCHAR},
            #{element.version,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

</mapper>