<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.deepexi.dxp.middle.promotion.mapper.specify.PromotionActivityAssociationMapper">

    <select id="activityTagList" resultType="java.lang.String">
        select distinct a.sub_text1 from promotion_activity_association a
          join promotion_activity pa on pa.id = a.main_id and pa.upper_status = 1 and JSON_EXTRACT(pa.`ext`,'$.deliveryChannel') = #{deliveryChannel,jdbcType=VARCHAR}
          join promotion_activity_participation p on p.activity_id = pa.id and p.real_city_id = #{cityId,jdbcType=VARCHAR}
        where a.`type` = 1
    </select>
</mapper>