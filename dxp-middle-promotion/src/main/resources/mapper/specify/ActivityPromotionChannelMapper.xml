<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.deepexi.dxp.middle.promotion.mapper.specify.ActivityPromotionChannelMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityPromotionChannelDO" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="createdTime" column="created_time"/>
        <result property="updatedTime" column="updated_time"/>
        <result property="remark" column="remark"/>
        <result property="createdBy" column="created_by"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="deleted" column="is_deleted"/>
        <result property="custom" column="is_custom"/>
        <result property="version" column="version"/>
        <result property="appId" column="app_id"/>
        <result property="activityId" column="activity_id"/>
        <result property="promotionType" column="promotion_type"/>
        <result property="type" column="type"/>
        <result property="url" column="url"/>
        <result property="channelName" column="channel_name"/>
        <result property="qrCode" column="qr_code"/>
        <result property="miniProgramCode" column="mini_program_code"/>
        <result property="codeType" column="code_type"/>
        <result property="codeId" column="code_id"/>
        <result property="miniUrl" column="mini_url"/>
        <result property="scene" column="scene"/>
    </resultMap>


</mapper>