<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.deepexi.dxp.middle.promotion.mapper.specify.ResourceHvImportLogMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.deepexi.dxp.middle.promotion.domain.entity.specify.ResourceHvImportLogDO" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="appId" column="app_id"/>
        <result property="createdTime" column="created_time"/>
        <result property="updatedTime" column="updated_time"/>
        <result property="remark" column="remark"/>
        <result property="createdBy" column="created_by"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="deleted" column="is_deleted"/>
        <result property="importNum" column="import_num"/>
        <result property="successNum" column="success_num"/>
        <result property="failNum" column="fail_num"/>
        <result property="importStatus" column="import_status"/>
        <result property="couponType" column="coupon_type"/>
        <result property="importFailJson" column="import_fail_json"/>
        <result property="version" column="version"/>
    </resultMap>

    <select id="findPage" resultType="com.deepexi.dxp.middle.promotion.domain.entity.specify.ResourceHvImportLogDO">
        SELECT id, created_time, created_by, import_num, success_num, fail_num, import_status  FROM promotion_resource_hv_import_log WHERE 1=1
        order by created_time desc
    </select>

</mapper>