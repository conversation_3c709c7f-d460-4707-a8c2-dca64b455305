<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.deepexi.dxp.middle.promotion.mapper.specify.ActivityGroupRelatedMapper">

    <sql id="Base_Column_List">
        id,tenant_id,created_time,updated_time,remark,created_by,updated_by,is_deleted,version,app_id,group_id,related_group_id,related_activity_id,type,sort,image_url
    </sql>
    <select id="pageList"
            resultType="com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityGroupRelatedDO">
        select <include refid="Base_Column_List"/> from promotion_activity_group_related where  is_deleted = 0
        <if test="query.groupId != null">
            and group_id = #{query.groupId}
        </if>
        order by sort asc
    </select>

    <select id="findRemoveGroupCheck" resultType="java.lang.Integer">
        select count(1)
        from promotion_activity_group pag
        join promotion_activity_group_related pagr on pag.id = pagr.group_id
        where  pag.is_deleted = 0 and pagr.is_deleted = 0 and pag.type = 1
          and pagr.related_group_id = #{query.groupId}
    </select>
</mapper>