<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.deepexi.dxp.middle.promotion.mapper.specify.ActivityParticipationMapper">


    <select id="getListByActivityList" resultType="com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityParticipationDO">
        SELECT DISTINCT  activity_id,project_id,project_name,real_city_id,real_city_name,area_id,area_name,city_id,city_name from promotion_activity_participation where is_deleted = 0
        and activity_id in
        <foreach collection="activityIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getAllProjectCityList"
            resultType="com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityParticipationDO">
        select project_id,real_city_id,real_city_name
        from promotion_activity_participation
        where real_city_id is not null
        group by project_id,real_city_id,real_city_name
    </select>
    <select id="listActivityId" resultType="java.lang.Long">
        select distinct activity_id from promotion_activity_participation b
          join promotion_activity a on b.activity_id = a.id
        where b.is_deleted = 0
        <if test="areaIdList != null and areaIdList.size > 0">
            and b.area_id in
            <foreach collection="areaIdList" item="item" open="(" close=")" separator="," >
                #{item}
            </foreach>
        </if>
        <if test="cityIdList != null and cityIdList.size > 0">
            and b.city_id in
            <foreach collection="cityIdList" item="item" open="(" close=")" separator="," >
                #{item}
            </foreach>
        </if>
        <if test="projectIdList != null and projectIdList.size > 0">
            and b.project_id in
            <foreach collection="projectIdList" item="item" open="(" close=")" separator="," >
                #{item}
            </foreach>
        </if>
        <if test="lineType != null and lineType > 0">
            and JSON_EXTRACT(a.`ext`,'$.lineType') = #{lineType}
        </if>
        <if test="deliveryChannel != null and deliveryChannel != ''">
            and JSON_EXTRACT(a.`ext`,'$.deliveryChannel') = #{deliveryChannel}
        </if>
    </select>

</mapper>