<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepexi.dxp.middle.promotion.mapper.specify.ActivityVerifyRejectLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="baseResultMap" type="com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityVerifyRejectLogDO">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="remark" property="remark"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="version" property="version"/>
        <result column="app_id" property="appId"/>
        <result column="reject_id" property="rejectId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="base_Column_List">
        id, tenant_id, created_time, updated_time, remark, created_by, updated_by, is_deleted, version, app_id, reject_id
    </sql>

    <select id="querys" resultMap="baseResultMap" parameterType="com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityVerifyRejectLogDO">
        select  <include refid="base_Column_List"></include> from promotion_activity_verify_reject_log
    </select>
</mapper>
