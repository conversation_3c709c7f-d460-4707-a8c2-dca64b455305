<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepexi.dxp.middle.promotion.mapper.specify.ActivityFissionAssistLogMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="baseResultMap" type="com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityFissionAssistLogDO">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="created_time" property="createdTime" />
        <result column="updated_time" property="updatedTime" />
        <result column="remark" property="remark" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
        <result column="is_deleted" property="isDeleted" />
        <result column="version" property="version" />
        <result column="app_id" property="appId" />
        <result column="activity_id" property="activityId" />
        <result column="partake_log_id" property="partakeLogId" />
        <result column="user_id" property="userId" />
        <result column="nick_name" property="nickName" />
        <result column="user_name" property="userName" />
        <result column="phone" property="phone" />
        <result column="prize_result" property="prizeResult" />
        <result column="code" property="code" />
        <result column="get_time" property="getTime" />
        <result column="verify_time" property="verifyTime" />
        <result column="verify_by" property="verifyBy" />
        <result column="resource_id" property="resourceId" />
        <result column="union_id" property="unionId" />
        <result column="type" property="type" />
        <result column="received" property="is_received"/>
        <result  column="projectName" property="project_name"/>
        <result  column="projectId" property="project_id"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="base_Column_List">
        id, tenant_id, created_time, updated_time, remark, created_by, updated_by, is_deleted, version, app_id, activity_id, partake_log_id, user_id, nick_name, user_name, phone, prize_result, code, get_time, verify_time, verify_by, resource_id, union_id, type,is_received,project_name,project_id
    </sql>
</mapper>
