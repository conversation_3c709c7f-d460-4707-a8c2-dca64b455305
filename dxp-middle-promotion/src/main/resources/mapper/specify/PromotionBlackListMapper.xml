<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.deepexi.dxp.middle.promotion.mapper.specify.PromotionBlackListMapper">
    <select id="pageByCondition"
            resultType="com.deepexi.dxp.middle.promotion.domain.dto.PromotionBlackListPageVO">
        SELECT *
        FROM promotion_black_list
        WHERE deleted = 0
        <if test="form.cityId != null and form.cityId != ''">
            AND city_id = #{form.cityId}
        </if>
        <if test="form.keyword != null and form.keyword != ''">
            AND (name LIKE CONCAT('%', #{form.keyword}, '%') OR phone LIKE CONCAT('%', #{form.keyword}, '%'))
        </if>
        order by id desc
    </select>
    <select id="check"
            resultType="com.deepexi.dxp.middle.promotion.domain.entity.specify.PromotionBlackListDO">
        select distinct l.* from promotion_black_list l
            join promotion_activity_participation p on p.activity_id = #{activityId} and p.is_deleted = 0 and p.real_city_id = l.city_id
            join promotion_activity a on a.id = p.activity_id and JSON_EXTRACT(a.`ext`,'$.deliveryChannel') = l.delivery_channel
        where l.status = 1 and l.end_date > now() and l.phone in
        <foreach collection="phones" item="phone" open="(" separator="," close=")">
            #{phone}
        </foreach>
    </select>
</mapper>