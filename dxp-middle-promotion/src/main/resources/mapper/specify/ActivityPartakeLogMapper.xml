<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.deepexi.dxp.middle.promotion.mapper.specify.ActivityPartakeLogMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.deepexi.dxp.marketing.domain.marketing.response.ActivityPartakeLogResponseDTO">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="created_time" property="createdTime" />
        <result column="updated_time" property="updatedTime" />
        <result column="remark" property="remark" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
        <result column="app_id" property="appId" />
        <result column="activity_id" property="activityId" />
        <result column="user_id" property="userId" />
        <result column="nick_name" property="nickName" />
        <result column="user_name" property="userName" />
        <result column="phone" property="phone" />
        <result column="id_card" property="idCard" />
        <result column="area" property="area" />
        <result column="deta_address" property="detaAddress" />
        <result column="prize_result" property="prizeResult" />
        <result column="code" property="code" />
        <result column="get_time" property="getTime" />
        <result column="verify_time" property="verifyTime" />
        <result column="verify_by" property="verifyBy" />
        <result column="order_no" property="orderNo" />
        <result column="order_id" property="orderId" />
        <result column="resource_id" property="resourceId" />
        <result column="pay_time" property="payTime" />
        <result column="pay_money" property="payMoney" />
        <result column="union_id" property="unionId" />
        <result column="type" property="type" />
        <result column="avatar" property="avatar" />
        <result column="need_fisson_count" property="needFissonCount" />
        <result column="current_fisson_count" property="currentFissonCount" />
        <result column="current_fisson_price" property="currentFissonPrice" />
        <result column="fisson_end_time" property="fissonEndTime" />
        <result column="fisson_status" property="fissonStatus" />
        <result column="fisson_type" property="fissonType" />
        <result column="project_name" property="projectName" />
        <result column="project_id" property="projectId" />
        <result column="video_id" property="videoId" />
        <result column="cover_url" property="coverUrl" />
        <result column="video_title" property="videoTitle" />
        <result column="video_url" property="videoUrl" />
        <result column="account_info" property="accountInfo" />
        <result column="user_key" property="userKey" />
        <result column="ext" jdbcType="VARCHAR" property="ext" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,created_time,updated_time,remark,created_by,updated_by,is_deleted,version,app_id,activity_id,user_id,nick_name,user_name,phone,id_card,area,deta_address,prize_result,code,get_time,verify_time,verify_by,order_id,order_no,resource_id,pay_time,pay_money,union_id,type,avatar,need_fisson_count,current_fisson_count,current_fisson_price,fisson_end_time,fisson_status,fisson_type,project_name,project_id,video_id,cover_url,video_title,video_url,account_info,user_key,share_icon,ext
    </sql>

    <select id="luckyDrawFindPage"
            resultType="com.deepexi.dxp.marketing.domain.marketing.response.ActivityPartakeLogResponseDTO">
        select <include refid="Base_Column_List"/>
        from promotion_activity_partake_log
        <where>
            is_deleted = 0
            <if test="query.nameOrPhone != null and query.nameOrPhone != ''">
                and (nick_name like concat('%',#{query.nameOrPhone},'%') OR phone like concat('%',#{query.nameOrPhone},'%'))
            </if>
            <if test="query.prizeResult != null and query.prizeResult != ''">
                and prize_result = #{query.prizeResult}
            </if>
            <if test="query.userId != null and query.userId > 0">
                and user_id = #{query.userId}
            </if>
            <if test="query.activityId != null">
                and activity_id = #{query.activityId}
            </if>
            <if test="query.resourceId != null">
                and resource_id = #{query.resourceId}
            </if>
        </where>
    </select>

    <select id="findPage" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from promotion_activity_partake_log
        <include refid="activity_partake_log_sql"/>
        ORDER BY current_fisson_count,current_fisson_price DESC
    </select>

    <select id="findPageSerial"
            resultType="com.deepexi.dxp.marketing.domain.marketing.response.ActivityPartakeLogResponseDTO">
        select * from (
            select (@i:=@i+1) serialNum,<include refid="Base_Column_List"/>
            from promotion_activity_partake_log  a,(select @i:=0) t
            <include refid="activity_partake_log_sql"/>
            ORDER BY created_time asc
        ) a order by serialNum desc

    </select>

    <sql id="activity_partake_log_sql">
        <where>
            is_deleted = 0
            <if test="query.searchText != null and query.searchText != ''">
                and (nick_name like concat('%',#{query.searchText},'%') OR phone like concat('%',#{query.searchText},'%') OR order_no like concat('%',#{query.searchText},'%'))
            </if>
            <if test="query.prizeResult != null and query.prizeResult != ''">
                and prize_result = #{query.prizeResult}
            </if>
            <if test="query.userId != null and query.userId > 0">
                and user_id = #{query.userId}
            </if>
            <if test="query.activityId != null">
                and activity_id = #{query.activityId}
            </if>
            <if test="query.resourceId != null">
                and resource_id = #{query.resourceId}
            </if>
            <if test="query.startTime != null and query.endTime != null">
                and (
                (pay_time between #{query.startTime,jdbcType=TIMESTAMP} and #{query.endTime,jdbcType=TIMESTAMP})
                or (get_time between #{query.startTime,jdbcType=TIMESTAMP} and #{query.endTime,jdbcType=TIMESTAMP})
                or (created_time between #{query.startTime,jdbcType=TIMESTAMP} and #{query.endTime,jdbcType=TIMESTAMP})
                )
            </if>
            <if test="query.prizeResultNot != null and query.prizeResultNot != ''">
                and prize_result != #{query.prizeResultNot}
            </if>
            <if test="query.fissonStatus != null">
                and fisson_status = #{query.fissonStatus}
            </if>
            <if test="query.area != null and query.area != ''">
                and area = #{query.area}
            </if>
            <if test="query.projectId != null and query.projectId != ''">
                and project_id = #{query.projectId}
            </if>
            <if test="query.userName != null and query.userName != ''">
                and user_name like concat('%',#{query.userName},'%')
            </if>
            <if test="query.nickName != null and query.nickName != ''">
                and nick_name like concat('%',#{query.nickName},'%')
            </if>
        </where>
    </sql>

    <select id="verifyInfoList" resultType="com.deepexi.dxp.marketing.domain.marketing.response.VerifyInfoResponseDTO">
        SELECT r.issued_quantity as issuedQuantity,
        (select count(1)  from promotion_activity_partake_log where    get_time is  NULL  and prize_result !='谢谢参与' and resource_id = r.id  and code not in (SELECT c.`code` from promotion_activity_verify as c where c.activity_id=#{activityId} and c.resource_id = r.id)) as notClaimedQuantity,
        (select count(1) from promotion_activity_verify where resource_id = r.id and is_deleted = 0) as receivedQuantity,
        r.remaining_quantity as remainingQuantity,
        (select count(1) from promotion_activity_verify where resource_id = r.id and verify_status = 1 and is_deleted = 0) as writtenOffQuantity,
        (select count(1) from promotion_activity_verify where resource_id = r.id and verify_status = 0 and is_deleted = 0) as notWrittenOffQuantity,
        (select count(1) from promotion_activity_verify where resource_id = r.id and refund_status = 1 and is_deleted = 0) as refundedQuantity,
        r.coupon_category,r.third_category,r.type,r.coupon_type,r.name as resourceName
        from promotion_his_resource r
        where r.is_deleted = 0 and r.activity_id = #{activityId} and r.resource_id != -1
        <if test="resourceName != null and resourceName != ''">
            and r.name like concat('%',#{resourceName},'%')
        </if>
    </select>

    <!-- 优惠券活动参与明细  -->
    <select id="couponFindPage"
            resultType="com.deepexi.dxp.marketing.domain.marketing.response.ActivityCouponPartakeLogResponseDTO">
        select <include refid="Base_Column_List"/>
        from promotion_activity_partake_log
        <where>
            is_deleted = 0
            <if test="query.nameOrPhoneOrOrderNo != null and query.nameOrPhoneOrOrderNo != ''">
                and (nick_name like concat('%',#{query.nameOrPhoneOrOrderNo},'%') OR phone like concat('%',#{query.nameOrPhoneOrOrderNo},'%') OR order_no like concat('%',#{query.nameOrPhoneOrOrderNo},'%'))
            </if>
            <if test="query.userId != null and query.userId > 0">
                and user_id = #{query.userId}
            </if>
            <if test="query.activityId != null">
                and activity_id = #{query.activityId}
            </if>
            <if test="query.startTime != null">
                and DATE_FORMAT(pay_time,'%Y-%m-%d') &gt;= DATE_FORMAT(#{query.startTime,jdbcType=TIMESTAMP},'%Y-%m-%d')
            </if>
            <if test="query.endTime != null">
                and DATE_FORMAT(pay_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{query.endTime,jdbcType=TIMESTAMP},'%Y-%m-%d')
            </if>
        </where>
    </select>

    <select id="getPartakeLogById"
            resultType="com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityPartakeLogDO">
        select <include refid="Base_Column_List"/>
        from promotion_activity_partake_log
        where id = #{id}
    </select>
    <select id="fissionActivityStatistics"
            resultType="com.deepexi.dxp.marketing.domain.marketing.request.specify.FissionActivityStatisticsVO">
        select
               activity_id as activityId,
               sum(need_fisson_count) as sumNeedFissonCount,
               count(DISTINCT user_id) AS initiatorsNumber,
               count(DISTINCT case when fisson_status = 1 then user_id else null end ) AS successNumber
        from promotion_activity_partake_log
        where is_deleted = 0
          <if test="activityIds != null and activityIds.size() > 0">
            and activity_id in
            <foreach close=")" collection="activityIds" item="item" open="(" separator=", ">
                #{item,jdbcType=BIGINT}
            </foreach>
          </if>
         group by activity_id
    </select>
    <select id="findCountByActivityIdUserId"
            resultType="com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityVerifyCountDTO">
        select
            resource_id as resourceId,count(resource_id) as userCount
        from promotion_activity_partake_log
        where is_deleted = 0 and phone = #{phone} and activity_id = #{activityId}
        group by resource_id
    </select>

    <update id="updateActivityPartakeLogById">
        UPDATE promotion_activity_partake_log
        <set>
            <if test="updated.nickName != null">
                nick_name = #{updated.nickName,jdbcType=VARCHAR},
            </if>
            <if test="updated.userName != null">
                user_name = #{updated.userName,jdbcType=VARCHAR},
            </if>
            <if test="updated.phone != null">
                phone = #{updated.phone,jdbcType=VARCHAR},
            </if>
            <if test="updated.idCard != null">
                id_card = #{updated.idCard,jdbcType=VARCHAR},
            </if>
            <if test="updated.area != null">
                area = #{updated.area,jdbcType=VARCHAR},
            </if>
            <if test="updated.detaAddress != null">
                deta_address = #{updated.detaAddress,jdbcType=VARCHAR},
            </if>
            <if test="updated.prizeResult != null">
                prize_result = #{updated.prizeResult,jdbcType=VARCHAR},
            </if>
            <if test="updated.getTime != null">
                get_time = #{updated.getTime,jdbcType=VARCHAR},
            </if>
            <if test="updated.resourceId != null">
                resource_id = #{updated.resourceId,jdbcType=BIGINT},
            </if>
            <if test="updated.unionId != null">
                union_id = #{updated.unionId,jdbcType=VARCHAR},
            </if>
            <if test="updated.type != null">
                type = #{updated.type,jdbcType=INTEGER},
            </if>
            <if test="updated.updatedBy != null">
                updated_by = #{updated.updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updated.updatedTime != null">
                updated_time = #{updated.updatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.deleted != null">
                is_deleted = #{updated.deleted,jdbcType=INTEGER},
            </if>
            <if test="updated.code != null">
                code = #{updated.code,jdbcType=VARCHAR},
            </if>
            <if test="updated.projectId != null">
                project_id = #{updated.projectId,jdbcType=VARCHAR},
            </if>
            <if test="updated.projectName != null">
                project_name = #{updated.projectName,jdbcType=VARCHAR},
            </if>
        </set>
        <where>
           id = #{updated.id,jdbcType=BIGINT}
        </where>
    </update>
    <select id="findTermEndList"
            resultType="com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityPartakeLogDO">
        select <include refid="Base_Column_List"/>
        from promotion_activity_partake_log where
        fisson_status = 0
        and  <![CDATA[ fisson_end_time <= NOW() ]]>
        <if test="fissionTypes != null and fissionTypes.size() > 0">
             and fisson_type in
            <foreach item="item" index="index" collection="fissionTypes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="getSuccessRecord"
            resultType="com.deepexi.dxp.marketing.domain.marketing.response.ActivityPartakeLogResponseDTO">
        select a.*,r.name as prizeResult from (
        select resource_id,user_id,nick_name as nickName,max(created_time) as createdTime
        from promotion_activity_partake_log
        <where>
            <if test="query.activityId != null">
                and activity_id = #{query.activityId}
            </if>
            <if test="query.prizeResultNot != null and query.prizeResultNot != ''">
                and prize_result != #{query.prizeResultNot}
            </if>
            <if test="query.fissonStatus != null">
                and fisson_status = #{query.fissonStatus}
            </if>
        </where>
        group by user_id
        ) a join promotion_his_resource r on a.resource_id = r.id
    </select>

    <!-- 发起助力成功活动数 -->
    <select id="getAssistSuccessRecordCount" resultType="java.lang.Integer">
        SELECT count(1) cnt from  (select DISTINCT user_id
        from promotion_activity_partake_log
        <where>
            <if test="activityId != null">
                and activity_id = #{activityId}
            </if>
            <if test="fissonStatus != null">
                and fisson_status = #{fissonStatus}
            </if>
        </where>
        group by user_id) as t
    </select>
    <!-- 助力成功用户 -->
    <select id="getAssistSuccessRecord"
            resultType="com.deepexi.dxp.marketing.domain.marketing.response.ActivityPartakeLogResponseDTO">
        SELECT max(b.id) as id,b.user_id,b.nick_name
        from  promotion_activity_partake_log as b
        <where>
            <if test="activityId != null">
                and b.activity_id = #{activityId}
            </if>
            <if test="fissonStatus != null">
                and b.fisson_status = #{fissonStatus}
            </if>
        </where>
        GROUP BY b.user_id ORDER BY  MAX(b.id) DESC
    </select>
    <select id="findUnGiftVouchers"
            resultType="com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityPartakeLogDO">
        select p.id
        from promotion_activity_partake_log p
        join promotion_his_resource r on p.resource_id = r.id and coupon_category = 2
        where p.is_deleted = 0 and p.phone = #{phone} and p.activity_id = #{activityId}
          and p.resource_id not in (
              select resource_id from promotion_activity_verify
              where verify_status = 1 and verify_type = 1 and phone = #{phone} and activity_id = #{activityId}
          )
    </select>
    <!-- 用户抽奖活动未领取列表 -->
    <select id="findCertificatesList"
            resultType="com.deepexi.dxp.marketing.domain.marketing.response.ActivityPartakeLogResponseDTO">
        select <include refid="Base_Column_List"/>
        from promotion_activity_partake_log where
        phone = #{query.phone} and  activity_id = #{query.activityId} and  get_time is  NULL
        and prize_result !='谢谢参与'
        and code not in (SELECT c.`code` from promotion_activity_verify as c where c.activity_id=#{query.activityId}
        and c.phone = #{query.phone})
        ORDER BY created_time DESC
    </select>

    <select id="queryLikesRanking"
            resultType="com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityPartakeLogDO">
        select project_id,
               project_name,
               sum(current_fisson_price) as current_fisson_price
        from promotion_activity_partake_log
        where activity_id = #{activityId}
          and fisson_status = 1
          and is_deleted = 0
          and project_id is not null
        group by project_id, project_name
    </select>

    <select id="queryVideoByProjectIdList"
            resultType="com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityPartakeLogDO">
        select id,
               video_id,
               video_title,
               video_url,
               cover_url,
               user_id,
               nick_name,
               current_fisson_price,
               avatar
        from promotion_activity_partake_log
        <where>
            and activity_id = #{activityId}
            and fisson_status = 1
            and is_deleted = 0
            <if test="projectIdList != null and projectIdList.size() > 0">
                and project_id in
                <foreach close=")" collection="projectIdList" item="projectId" open="(" separator=", ">
                    #{projectId}
                </foreach>
            </if>
        </where>
        order by current_fisson_price desc
    </select>
    <select id="queryVideoByAccountId"
            resultType="com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityPartakeLogDO">
        select id,
               video_id,
               video_title,
               video_url,
               cover_url,
               current_fisson_price,
               user_id,
               nick_name,
               user_name,
               avatar,
               phone,
               user_key,
               account_info,
               project_id,
               share_icon
        from promotion_activity_partake_log
        <where>
            and user_id = #{accountId}
            and activity_id = #{activityId}
            and fisson_status = 1
            and is_deleted = 0
        </where>
        order by current_fisson_price desc
    </select>
    <select id="getById" resultType="com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityPartakeLogDO">
        select id,
               video_id,
               video_title,
               video_url,
               cover_url,
               user_id,
               nick_name,
               current_fisson_price,
               user_id,
               user_name,
               avatar,
               phone,
               user_key,
               account_info,
               project_id,
               activity_id
        from promotion_activity_partake_log
        where video_id = #{videoId} and activity_id = #{activityId} and is_deleted = 0
    </select>

    <select id="queryLikesTotalGroupByUserId"
            resultType="com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityPartakeLogDO">
        select user_id, sum(current_fisson_price) as current_fisson_price
        from promotion_activity_partake_log
        where activity_id = #{activityId}
          and fisson_status = 1
          and is_deleted = 0
        group by user_id
        order by current_fisson_price desc
    </select>
    <select id="partakeCount" resultType="map">
        select activity_id as activityId,count(1) as count from promotion_activity_partake_log where is_deleted = 0 and activity_id in
        <foreach collection="activityIds" item="activityId" open="(" close=")" separator=", ">
            #{activityId}
        </foreach>
        group by activity_id
    </select>
    <select id="partakeCountTotal" resultType="map">
        select activity_id as activityId,sum(need_fisson_count) as count from promotion_activity_partake_log where is_deleted = 0 and activity_id in
        <foreach collection="activityIds" item="activityId" open="(" close=")" separator=", ">
            #{activityId}
        </foreach>
        group by activity_id
    </select>

    <update id="likesVideoById">
        update promotion_activity_partake_log
        set current_fisson_price = current_fisson_price + 1
        where id = #{id}
    </update>

</mapper>