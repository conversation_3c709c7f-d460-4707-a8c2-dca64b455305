<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepexi.dxp.middle.promotion.mapper.specify.PromotionHisResourceMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.deepexi.dxp.middle.promotion.domain.entity.specify.PromotionHisResourceDO" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="resourceId" column="resource_id"/>
        <result property="activityId" column="activity_id"/>
        <result property="type" column="type"/>
        <result property="couponCategory" column="coupon_category"/>
        <result property="thirdCategory" column="third_category"/>
        <result property="name" column="name"/>
        <result property="couponType" column="coupon_type"/>
        <result property="couponValue" column="coupon_value"/>
        <result property="url" column="url"/>
        <result property="validTimeType" column="valid_time_type"/>
        <result property="grantWay" column="grant_way"/>
        <result property="validStartTime" column="valid_start_time"/>
        <result property="validEndTime" column="valid_end_time"/>
        <result property="validDay" column="valid_day"/>
        <result property="useRule" column="use_rule"/>
        <result property="houseName" column="house_name"/>
        <result property="houseVolume" column="house_volume"/>
        <result property="houseMessage" column="house_message"/>
        <result property="costPrice" column="cost_price"/>
        <result property="discountPrice" column="discount_price"/>
        <result property="costPriceType" column="cost_price_type"/>
        <result property="discountPriceType" column="discount_price_type"/>
        <result property="itemName" column="item_name"/>
        <result property="createdTime" column="created_time"/>
        <result property="updatedTime" column="updated_time"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="appId" column="app_id"/>
        <result property="deleted" column="is_deleted"/>
        <result property="remark" column="remark"/>
        <result property="version" column="version"/>
        <result property="code" column="code"/>
        <result column="ext" jdbcType="VARCHAR" property="ext" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result property="issuedQuantity" column="issued_quantity"/>
        <result property="issuanceCap" column="issuance_cap"/>
        <result property="oddsOfWinning" column="odds_of_winning"/>
        <result property="limitType" column="limit_type"/>
        <result property="remainingQuantity" column="remaining_quantity"/>
        <result property="limitTimes" column="limit_times"/>
        <result property="receiveMode" column="receive_mode"/>
        <result property="purchasePrice" column="purchase_price"/>
        <result property="fissonCount" column="fisson_count"/>
        <result property="fissonResourceType" column="fisson_resource_type"/>
        <result property="ladderSort" column="ladder_sort"/>
        <result property="projectName" column="project_name"/>
        <result property="projectId" column="project_id"/>
        <result property="sysType" column="sys_type"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,resource_id,activity_id,type,coupon_category,third_category,name,coupon_type,coupon_value,url,valid_time_type,grant_way,valid_start_time,
        valid_end_time,valid_day,use_rule,house_name,house_volume,house_message,cost_price,discount_price,item_name,created_time,updated_time,tenant_id,
        app_id,is_deleted,remark,version,code,ext,issued_quantity,issuance_cap,odds_of_winning,limit_type,remaining_quantity,limit_times,receive_mode,purchase_price,
        fisson_count,fisson_resource_type,ladder_sort,cost_price_type,discount_price_type,project_name,project_id,sys_type
    </sql>

    <select id="findPage" resultMap="BaseResultMap"
            parameterType="com.deepexi.dxp.marketing.domain.promotion.query.specify.PromotionHisResourceQuery">
        SELECT <include refid="Base_Column_List"/>  FROM promotion_his_resource WHERE is_deleted = 0
        <if test="query.tenantId!=null and query.tenantId!=''">
            and tenant_id = #{query.tenantId}
        </if>
        <if test="query.name!=null and query.name!=''">
            and name like concat('%', #{query.name}, '%')
        </if>
        <if test="query.type!=null">
        and type = #{query.type}
        </if>
        <if test="query.id!=null">
            and id = #{query.id}
        </if>
        <if test="query.receiveMode!=null">
            and receive_mode = #{query.receiveMode}
        </if>
        <if test="query.activityId != null">
            and activity_id = #{query.activityId}
        </if>
        <if test="query.resourceId != null">
            and resource_id = #{query.resourceId}
        </if>
        <if test="query.activityIds != null and query.activityIds.size() > 0">
            and activity_id in
            <foreach item="item" index="index" collection="query.activityIds"
                     open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>

        <if test="query.ids != null and query.ids.size() > 0">
            and id in
            <foreach collection="query.ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>

    </select>
    <update id="decreaseRemainQty">
        update promotion_his_resource set remaining_quantity = remaining_quantity - #{count} where id = #{id} and remaining_quantity > 0
    </update>

    <update id="increaseRemainQty">
        update promotion_his_resource set remaining_quantity = remaining_quantity + #{count} where id = #{id} and remaining_quantity + #{count} &lt;= issued_quantity
    </update>

    <update id="increaseIssuedQty">
        update promotion_his_resource set issued_quantity = issued_quantity + #{count},remaining_quantity = remaining_quantity + #{count} where id = #{id}
    </update>

</mapper>