<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepexi.dxp.middle.promotion.mapper.specify.ActivityFissionAssistResourceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="baseResultMap" type="com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityFissionAssistResourceDO">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="created_time" property="createdTime" />
        <result column="updated_time" property="updatedTime" />
        <result column="remark" property="remark" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
        <result column="is_deleted" property="isDeleted" />
        <result column="version" property="version" />
        <result column="app_id" property="appId" />
        <result column="activity_id" property="activityId" />
        <result column="partake_log_id" property="partakeLogId" />
        <result column="user_id" property="userId" />
        <result column="resource_id" property="resourceId" />
        <result column="union_id" property="unionId" />
        <result column="type" property="type" />
        <result column="ladder_sort" property="ladderSort" />
        <result column="get_time" property="getTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="base_Column_List">
        id, tenant_id, created_time, updated_time, remark, created_by, updated_by, is_deleted, version, app_id, activity_id, partake_log_id, user_id, resource_id, union_id, type, ladder_sort,get_time
    </sql>
    <select id="findCertificatesList"
            resultType="com.deepexi.dxp.marketing.domain.marketing.response.ActivityFissionAssistResourceResponseDTO">
        SELECT a.id,a.activity_id,a.partake_log_id,a.phone,a.resource_id,a.is_received,a.ladder_sort,a.get_time
        from  promotion_activity_fission_assist_resource as  a
            JOIN promotion_activity_partake_log as b on a.partake_log_id=b.id and b.fisson_status in (0,1)
            <if test="query.activityId != null">
                and b.activity_id=#{query.activityId}
            </if>
            <if test="query.phone != null and query.phone != ''">
                and b.phone=#{query.phone}
            </if>
        where a.is_received = 0
        ORDER BY a.created_time DESC
    </select>
</mapper>
