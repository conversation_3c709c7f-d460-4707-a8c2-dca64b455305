<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.deepexi.dxp.middle.promotion.mapper.specify.ActivityUserRelatedMapper">

    <resultMap id="BaseResultMap" type="com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityUserRelatedDO">
        <!--@mbg.generated-->
        <!--@Table promotion_activity-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="nick_name" jdbcType="VARCHAR" property="nickName"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="join_type" jdbcType="INTEGER" property="joinType"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="activity_id" jdbcType="BIGINT" property="activityId"/>
        <result column="union_id" jdbcType="VARCHAR" property="unionId"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="created_date" jdbcType="VARCHAR" property="createdDate"/>
        <result column="limits" jdbcType="VARCHAR" property="limits" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="type" jdbcType="BIGINT" property="type"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="app_id" jdbcType="BIGINT" property="appId"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,created_time,updated_time,remark,created_by,updated_by,is_deleted,version,app_id,activity_id,user_id,nick_name,user_name,phone,join_type,union_id,type,created_date,limits
    </sql>


    <select id="getInitActivityUserList" resultMap="BaseResultMap">
    select  au.*
       FROM promotion_activity_user_related au
       join promotion_activity pa on au.activity_id = pa.id
       where  pa.status = 2 and au.join_type = 0 and pa.is_deleted = 0 and au.is_deleted = 0 and pa.PA_template_id=15
        and au.created_date &lt; now()
    </select>
    <select id="listByUserId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from promotion_activity_user_related
        <where>
            is_deleted = 0
            <if test="activityId != null">
                and activity_id = #{activityId}
            </if>

            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="phone != null">
                and phone = #{phone}
            </if>
            order by created_time desc
        </where>
    </select>
</mapper>