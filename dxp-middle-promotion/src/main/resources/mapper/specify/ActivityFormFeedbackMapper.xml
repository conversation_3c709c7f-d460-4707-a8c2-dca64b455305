<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.deepexi.dxp.middle.promotion.mapper.specify.ActivityFormFeedbackMapper">

    <resultMap id="BaseResultMap" type="com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityFormFeedbackDO">
        <!--@mbg.generated-->
        <!--@Table promotion_activity-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="project_id" jdbcType="VARCHAR" property="projectId"/>
        <result column="project_name" jdbcType="VARCHAR" property="projectName"/>
        <result column="limits" jdbcType="VARCHAR" property="limits" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="nick_name" jdbcType="VARCHAR" property="nickName"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="activity_id" jdbcType="BIGINT" property="activityId"/>
        <result column="resource_id" jdbcType="BIGINT" property="resourceId"/>
        <result column="activity_type" jdbcType="BIGINT" property="activityType"/>
        <result column="union_id" jdbcType="VARCHAR" property="unionId"/>
        <result column="type" jdbcType="BIGINT" property="type"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="app_id" jdbcType="BIGINT" property="appId"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,created_time,updated_time,remark,created_by,updated_by,is_deleted,version,app_id,project_id,project_name,limits,user_id,nick_name,user_name,phone,activity_id,resource_id,activity_type,union_id,type
    </sql>

    <select id="pageList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from promotion_activity_form_feedback WHERE is_deleted = 0
        <if test="query.projectName != null and query.projectName!=''">
            AND project_name LIKE CONCAT(CONCAT('%',#{query.projectName},'%'))
        </if>
        <if test="query.searchText != null and query.searchText!=''">
            AND (
            phone LIKE CONCAT('%',#{query.searchText},'%')
            or nick_name LIKE
            CONCAT('%',#{query.searchText},'%')
            or user_name LIKE  CONCAT('%',#{query.searchText},'%')
            )
        </if>
        <if test="query.activityId != null">
            AND activity_id = #{query.activityId}
        </if>
        <if test="query.userId != null">
            AND user_id = #{query.userId}
        </if>
        <if test="query.phone != null">
            AND phone = #{query.phone}
        </if>

        <if test="query.userIds != null and query.userIds.size() > 0">
            and user_id in
            <foreach item="item" index="index" collection="query.userIds"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.phoneList != null and query.phoneList.size() > 0">
            and phone in
            <foreach item="item" index="index" collection="query.phoneList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="query.projectId != null">
            AND project_id = #{query.projectId}
        </if>
        <if test="query.resourceId != null">
            AND resource_id = #{query.resourceId}
        </if>
        <if test="query.startTime != null">
            and created_time &lt;= #{query.startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="query.endTime != null">
            and created_time >= #{query.endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="query.activityType != null">
        </if>
        <choose>
            <when test="query.activityType != null and query.activityType == 0">
                order by updated_time desc
            </when>
            <otherwise>
                order by created_time desc
            </otherwise>
        </choose>
    </select>
    <select id="getActivityFormFeedbackById" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from promotion_activity_form_feedback where id = #{id}
    </select>

    <select id="getNewByActivityId" resultMap="BaseResultMap">
        select max(created_time) as created_time from promotion_activity_form_feedback where activity_id = #{activityId}
    </select>

    <select id="getLimitCount" resultType="java.lang.Integer">
        select count(1) as cnt from promotion_activity_form_feedback where activity_id = #{activityId}
        and phone != #{phone}
        and limits LIKE  CONCAT('%',#{limits},'%')
    </select>
    <!-- 表单活动参与记录查询 -->
    <select id="getFormFeedbackByPhone" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from promotion_activity_form_feedback WHERE is_deleted = 0
        and activity_id = #{activityId}
        and phone = #{phone} limit 1
    </select>
</mapper>