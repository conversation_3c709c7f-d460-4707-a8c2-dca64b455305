<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepexi.dxp.middle.promotion.mapper.PromotionActivityLoggerMapper">
    <resultMap id="BaseResultMap" type="com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityLoggerDO">
        <!--@mbg.generated-->
        <!--@Table promotion_activity_logger-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="activity_id" jdbcType="INTEGER" property="activityId"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="order_detail" jdbcType="OTHER" property="orderDetail"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="app_id" jdbcType="BIGINT" property="appId"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="follow_number" jdbcType="VARCHAR" property="followNumber"/>
        <result column="order_number" jdbcType="VARCHAR" property="orderNumber"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
        <result column="user_type" jdbcType="TINYINT" property="userType"/>

        <result column="pre_price" jdbcType="DECIMAL" property="prePrice"/>
        <result column="discounts_price" jdbcType="DECIMAL" property="discountsPrice"/>
        <result column="coupon_flag" jdbcType="VARCHAR" property="couponFlag"/>
        <result column="user_phone" jdbcType="VARCHAR" property="userPhone"/>
        <result column="order_qty" jdbcType="INTEGER" property="orderQty"/>
        <result column="first_pay_time" jdbcType="TIMESTAMP" property="firstPayTime"/>
        <result column="end_pay_time" jdbcType="TIMESTAMP" property="endPayTime"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="final_payment_time" jdbcType="TIMESTAMP" property="finalPaymentTime"/>
        <result column="ext" jdbcType="VARCHAR" property="ext" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, activity_id, user_id, order_detail, created_time, tenant_id, app_id, is_deleted,
        `status`, follow_number, order_number, remark, version, updated_time, user_type,
        pre_price,discounts_price,coupon_flag,user_phone,order_qty,first_pay_time,end_pay_time,code,final_payment_time,ext
    </sql>

    <!--auto generated by MybatisCodeHelper on 2019-11-21-->
    <delete id="deleteByActivityIds">
        update promotion_activity_logger
        set is_deleted = 1
        <where>
            <if test="orderIdCollection != null and orderIdCollection.size() > 0">
                and order_number in
                <foreach item="item" index="index" collection="orderIdCollection"
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
    </delete>

    <!--auto generated by MybatisCodeHelper on 2019-11-21-->
    <select id="selectByAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from promotion_activity_logger
        <where>
            is_deleted = 0
            <if test="param.activityId != null">
                and activity_id=#{param.activityId,jdbcType=INTEGER}
            </if>
            <if test="param.userId != null">
                and user_id=#{param.userId,jdbcType=INTEGER}
            </if>
            <if test="param.status != null">
                and `status`=#{param.status,jdbcType=TINYINT}
            </if>
            <if test="param.followNumber != null">
                and follow_number=#{param.followNumber,jdbcType=VARCHAR}
            </if>
            <if test="param.orderNumber != null">
                and order_number=#{param.orderNumber,jdbcType=VARCHAR}
            </if>
            <if test="param.userType != null">
                and user_type=#{param.userType,jdbcType=TINYINT}
            </if>
            <if test="param.id != null">
                and id=#{param.id,jdbcType=BIGINT}
            </if>
            <if test="param.tenantId != null">
                and tenant_id=#{param.tenantId,jdbcType=VARCHAR}
            </if>
            <if test="param.appId != null">
                and app_id=#{param.appId,jdbcType=BIGINT}
            </if>
            order by created_time desc
        </where>
    </select>

    <!--auto generated by MybatisCodeHelper on 2019-11-28-->
    <select id="selectListByActivityId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from promotion_activity_logger
        <where>
            is_deleted = 0
            <if test="userId != null">
                and user_id=#{userId,jdbcType=VARCHAR}
            </if>
            <if test="userType != null">
                and user_type=#{userType,jdbcType=VARCHAR}
            </if>
            <if test="activityIdCollection != null and activityIdCollection.size() > 0">
                and activity_id in
                <foreach item="item" index="index" collection="activityIdCollection"
                         open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
    </select>

    <!--auto generated by MybatisCodeHelper on 2019-12-06-->
    <insert id="insertList">
        INSERT INTO promotion_activity_logger(
        activity_id,
        user_id,
        order_detail,
        tenant_id,
        app_id,
        status,
        follow_number,
        order_number,
        user_type,
        pre_price,
        discounts_price,
        coupon_flag,
        user_phone,
        order_qty,
        created_time,
        updated_time,
        version,
        remark,
        is_deleted,
        first_pay_time,
        end_pay_time,
        code,
        final_payment_time

        )VALUES
        <foreach collection="list" item="element" index="index" separator=",">
            (
            #{element.activityId,jdbcType=INTEGER},
            #{element.userId,jdbcType=INTEGER},
            #{element.orderDetail,jdbcType=OTHER},
            #{element.tenantId,jdbcType=VARCHAR},
            #{element.appId,jdbcType=VARCHAR},
            #{element.status,jdbcType=TINYINT},
            #{element.followNumber,jdbcType=VARCHAR},
            #{element.orderNumber,jdbcType=VARCHAR},
            #{element.userType,jdbcType=TINYINT},
            #{element.prePrice,jdbcType=DECIMAL},
            #{element.discountsPrice,jdbcType=DECIMAL},
            #{element.couponFlag,jdbcType=VARCHAR},
            #{element.userPhone,jdbcType=VARCHAR},
            #{element.orderQty,jdbcType=INTEGER},
            #{element.createdTime,jdbcType=TIMESTAMP},
            #{element.updatedTime,jdbcType=TIMESTAMP},
            #{element.version,jdbcType=INTEGER},
            #{element.remark,jdbcType=VARCHAR},
            0,
            #{element.firstPayTime,jdbcType=TIMESTAMP},
            #{element.endPayTime,jdbcType=TIMESTAMP},
            #{element.code,jdbcType=VARCHAR},
            #{element.finalPaymentTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

    <!--auto generated by MybatisCodeHelper on 2019-12-06-->
    <update id="updateStatusByOrderNumber">
        update promotion_activity_logger
        set `status`=#{updatedStatus,jdbcType=TINYINT}
        <where>
            <if test="orderNumber != null">
                and order_number=#{orderNumber,jdbcType=VARCHAR}
            </if>
        </where>
    </update>

    <!--auto generated by MybatisCodeHelper on 2020-03-12-->
    <update id="updateByOrderNumberAndAppIdAndTenantId">
        update promotion_activity_logger
        <set>
            <if test="updated.activityId != null">
                activity_id = #{updated.activityId,jdbcType=INTEGER},
            </if>
            <if test="updated.userId != null">
                user_id = #{updated.userId,jdbcType=INTEGER},
            </if>
            <if test="updated.orderDetail != null">
                order_detail = #{updated.orderDetail,jdbcType=OTHER},
            </if>
            <if test="updated.status != null">
                status = #{updated.status,jdbcType=TINYINT},
            </if>
            <if test="updated.followNumber != null">
                follow_number = #{updated.followNumber,jdbcType=VARCHAR},
            </if>
            <if test="updated.remark != null">
                remark = #{updated.remark,jdbcType=VARCHAR},
            </if>
            <if test="updated.version != null">
                version = #{updated.version,jdbcType=INTEGER},
            </if>
            <if test="updated.updatedTime != null">
                updated_time = #{updated.updatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.userType != null">
                user_type = #{updated.userType,jdbcType=TINYINT},
            </if>
            <if test="updated.prePrice != null">
                pre_price = #{updated.prePrice,jdbcType=DECIMAL},
            </if>
            <if test="updated.discountsPrice != null">
                discounts_price = #{updated.discountsPrice,jdbcType=DECIMAL},
            </if>
            <if test="updated.couponFlag != null">
                coupon_flag = #{updated.couponFlag,jdbcType=VARCHAR},
            </if>
            <if test="updated.userPhone != null">
                user_phone = #{updated.userPhone,jdbcType=VARCHAR},
            </if>
            <if test="updated.orderQty != null">
                order_qty = #{updated.orderQty,jdbcType=INTEGER},
            </if>
            <if test="updated.firstPayTime != null">
                first_pay_time = #{updated.firstPayTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.endPayTime != null">
                end_pay_time = #{updated.endPayTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.code != null">
                code = #{updated.code,jdbcType=VARCHAR},
            </if>
            <if test="updated.finalPaymentTime != null">
                final_payment_time = #{updated.finalPaymentTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        <where>
            <if test="updated.orderNumber != null">
                and order_number=#{updated.orderNumber,jdbcType=VARCHAR}
            </if>
            <if test="updated.appId != null">
                and app_id=#{updated.appId,jdbcType=BIGINT}
            </if>
            <if test="updated.tenantId != null">
                and tenant_id=#{updated.tenantId,jdbcType=VARCHAR}
            </if>
        </where>
    </update>

    <select id="statisticsUserActivityCount"
            resultType="com.deepexi.dxp.marketing.domain.promotion.dto.activity.StatisticsUserActivityCountDetailDTO">
        SELECT PA_template_id as paTemplateId , count(*) as activitySize
        FROM promotion_activity_logger logger
        INNER JOIN promotion_activity activity
        ON activity.id = logger.activity_id
        WHERE logger.is_deleted = 0
        AND logger.user_id = #{userId,jdbcType=BIGINT}
        AND logger.user_type = #{userType}
        and activity.tenant_id = #{tenantId,jdbcType=VARCHAR}
        and activity.app_id = #{appId,jdbcType=BIGINT}
        GROUP BY activity.PA_template_id
    </select>


    <!-- 查询活动日志统计 -->
    <select id="getLogNumCount" resultType="com.deepexi.dxp.middle.promotion.domain.dto.ActivityLogCountDTO">
        select
        t.activity_id  as 'activityId',
        count(1) as 'countData'
        from promotion_activity_logger t
        <where>
            is_deleted = 0
            <if test="activityId != null">
                and activity_id=#{activityId}
            </if>

            <if test="activityIdS != null and activityIdS.size() > 0">
                and activity_id in
                <foreach item="item" index="index" collection="activityIdS" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="createdTimeFrom != null and createdTimeFrom != ''">
                and created_time <![CDATA[>= ]]>#{createdTimeFrom}
            </if>

            <if test="createdTimeTo != null and createdTimeTo != ''">
                and created_time <![CDATA[<= ]]>#{createdTimeTo}
            </if>

            <if test="userId != null">
                and user_id=#{userId}
            </if>

            <if test="tenantId != null and tenantId != ''">
                and tenant_id=#{tenantId}
            </if>

            <if test="appId != null">
                and app_id=#{appId}
            </if>

            <if test="status != null">
                and `status`=#{status}
            </if>

        </where>
        group by t.activity_id
    </select>



    <select id="retornPreSalesTailMoney" resultType="com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityLoggerDO">
        select
        <include refid="Base_Column_List"/>
        from promotion_activity_logger
        <where>
            is_deleted = 0
            and status != 1
            <if test="tenantId != null">
                and tenant_id=#{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="appId != null">
                and app_id=#{appId,jdbcType=VARCHAR}
            </if>
            <if test="tailDate != null ">
                and final_payment_time &lt;= #{tailDate,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>

</mapper>