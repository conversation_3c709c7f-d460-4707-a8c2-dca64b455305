<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepexi.dxp.middle.promotion.mapper.PromotionCouponMapper">
    <resultMap id="BaseResultMap" type="com.deepexi.dxp.middle.promotion.domain.entity.PromotionCouponDO">
        <!--@mbg.generated-->
        <!--@Table promotion_coupon-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="coupon_name" jdbcType="VARCHAR" property="couponName"/>
        <result column="coupon_type" jdbcType="VARCHAR" property="couponType"/>
        <result column="coupon_value" jdbcType="DECIMAL" property="couponValue"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="condition" jdbcType="DECIMAL" property="condition"/>
        <result column="limits" jdbcType="OTHER" property="limits"/>
        <result column="coupon_code" jdbcType="VARCHAR" property="couponCode"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="app_id" jdbcType="BIGINT" property="appId"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
        <result column="ext" jdbcType="VARCHAR" property="ext" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, coupon_name,coupon_code, coupon_type,coupon_value,`status`,`condition`,limits,created_time,updated_time,
        tenant_id, app_id, is_deleted, remark, version,code,ext
    </sql>

    <select id="findAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from promotion_coupon
        <where>
            and is_deleted = 0
            <if test="ids != null and ids.size() != 0">
                and id in
                <foreach item="item" index="index" collection="ids"
                         open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="couponName != null and ''!=couponName">
                and coupon_name like concat ('%', #{couponName,jdbcType=VARCHAR},'%')
            </if>
            <if test="couponType !=null and ''!=couponType">
                and `coupon_type`=#{couponType,jdbcType=INTEGER}
            </if>
            <if test="couponValue !=null">
                and `coupon_value`=#{couponValue,jdbcType=DECIMAL}
            </if>
            <if test="status !=null">
                and `status`=#{status,jdbcType=INTEGER}
            </if>
            <if test="condition !=null">
                and `condition`=#{condition,jdbcType=DECIMAL}
            </if>
            <if test="limits !=null">
                and `limits`=#{limits,jdbcType=OTHER}
            </if>
            <if test="tenantId != null">
                and tenant_id=#{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="appId != null">
                and app_id=#{appId,jdbcType=BIGINT}
            </if>
            <if test="id != null">
                and id=#{id,jdbcType=BIGINT}
            </if>
        </where>
        order by created_time desc
    </select>

<!--auto generated by MybatisCodeHelper on 2019-12-28-->
    <select id="findAllByCouponNameAndId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from promotion_coupon
        <where>
            is_deleted  =  0
            <if test="dto.couponName != null">
                and coupon_name=#{dto.couponName,jdbcType=VARCHAR}
            </if>
            <if test="dto.id != null">
                and id=#{dto.id,jdbcType=BIGINT}
            </if>
            <if test="dto.tenantId != null">
                and tenant_id=#{dto.tenantId,jdbcType=VARCHAR}
            </if>
            <if test="dto.appId != null">
                and app_id=#{dto.appId,jdbcType=BIGINT}
            </if>
        </where>
    </select>

    <!--auto generated by MybatisCodeHelper on 2019-12-28-->

</mapper>