<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepexi.dxp.middle.promotion.mapper.PromotionStrategyMapper">
  <resultMap id="BaseResultMap" type="com.deepexi.dxp.middle.promotion.domain.entity.PromotionStrategyDO">
    <!--@mbg.generated-->
    <!--@Table promotion_strategy-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="strategy_type" jdbcType="VARCHAR" property="strategyType" />
    <result column="activity_id" property="activityId" />
    <result column="rules" jdbcType="VARCHAR" property="rules" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="app_id" jdbcType="BIGINT" property="appId" />
    <result column="is_deleted" jdbcType="BOOLEAN" property="deleted" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="version" jdbcType="INTEGER" property="version" />
      <result column="ext" jdbcType="VARCHAR" property="ext" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, strategy_type,activity_id, rules,created_time,updated_time,
    tenant_id, app_id, is_deleted,  remark, version,ext
  </sql>


<!--auto generated by MybatisCodeHelper on 2019-11-28-->
  <select id="selectAllByActivityIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from promotion_strategy
        <where>
            is_deleted = 0
            <if test="idCollection != null and idCollection.size() > 0">
                and activity_id in
                <foreach item="item" index="index" collection="idCollection"
                         open="(" separator="," close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
    </select>

<!--auto generated by MybatisCodeHelper on 2019-11-29-->
  <select id="selectByActivityId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from promotion_strategy
        <where>
            is_deleted = 0
            <if test="activityId != null">
                and activity_id=#{activityId,jdbcType=BIGINT}
            </if>
        </where>
    </select>

    <select id="selectByActivityIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from promotion_strategy
        <where>
            is_deleted = 0
            <if test="activityIds != null and activityIds.size() > 0">
                and activity_id in
                <foreach item="item" index="index" collection="activityIds"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

<!--auto generated by MybatisCodeHelper on 2019-11-29-->
  <delete id="deletedByActivityIds">
        update  promotion_strategy set is_deleted = 1
        <where>
            <if test="activityIdCollection != null and activityIdCollection.size() > 0">
                and activity_id in
                <foreach item="item" index="index" collection="activityIdCollection"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </delete>

</mapper>