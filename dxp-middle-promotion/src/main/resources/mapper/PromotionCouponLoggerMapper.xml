<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.deepexi.dxp.middle.promotion.mapper.PromotionCouponLoggerMapper">
    <resultMap id="BaseResultMap" type="com.deepexi.dxp.middle.promotion.domain.entity.PromotionCouponLoggerDO">
        <!--@mbg.generated-->
        <!--@Table promotion_coupon_logger-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="coupon_id" jdbcType="BIGINT" property="couponId"/>
        <result column="release_id" jdbcType="BIGINT" property="releaseId"/>
        <result column="release_type" jdbcType="INTEGER" property="releaseType"/>
        <result column="amount" jdbcType="INTEGER" property="amount"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="user_type" jdbcType="INTEGER" property="userType"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="app_id" jdbcType="BIGINT" property="appId"/>
        <result column="is_deleted" jdbcType="BOOLEAN" property="deleted"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="receive_time" jdbcType="TIMESTAMP" property="receiveTime"/>
        <result column="used_time" jdbcType="TIMESTAMP" property="usedTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
        <result column="ext" jdbcType="VARCHAR" property="ext" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
    </resultMap>

    <resultMap id="statisticMap" type="com.deepexi.dxp.middle.promotion.domain.entity.PromotionCouponLoggerStatisticDO">
        <result column="release_id" jdbcType="BIGINT" property="releaseId"/>
        <result column="release_type" jdbcType="INTEGER" property="releaseType"/>
        <result column="unused" jdbcType="INTEGER" property="unused"/>
        <result column="used" jdbcType="INTEGER" property="used"/>
        <result column="overtime" jdbcType="INTEGER" property="overtime"/>
        <result column="lock" jdbcType="INTEGER" property="lock"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, coupon_id, release_id,release_type,amount,order_id,user_id,user_type,`status`,created_time,updated_time,
        start_time,end_time,receive_time,used_time,
        tenant_id, app_id, is_deleted, remark, version,`code`,ext
    </sql>

    <select id="countTodayCreateNumber" resultType="java.lang.Integer">
        select count(id)
        from promotion_coupon_logger
        where
        release_type = 0
        <if test="null != var3 and '' != var3 ">
            and to_days(created_time) = to_days(now())
        </if>
        <if test="null != var1">
            and user_id=#{var1}
        </if>
        <if test="null != var2">
            and release_id=#{var2}
        </if>
        <if test="null !=var4">
            and user_type=#{var4}
        </if>
    </select>

    <select id="findAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from promotion_coupon_logger
        where
        is_deleted = 0
        <if test="couponId != null">
            and coupon_id=#{couponId,jdbcType=BIGINT}
        </if>
        <if test="releaseId !=null">
            and `release_id`=#{releaseId,jdbcType=BIGINT}
        </if>
        <if test="releaseType !=null">
            and `release_type`=#{releaseType,jdbcType=INTEGER}
        </if>
        <if test="amount !=null">
            and `amount`=#{amount,jdbcType=INTEGER}
        </if>
        <if test="orderId !=null">
            and `order_id`=#{orderId,jdbcType=VARCHAR}
        </if>
        <if test="status !=null">
            and `status`=#{status,jdbcType=INTEGER}
        </if>
        <if test="userId !=null">
            and `user_id`=#{userId,jdbcType=BIGINT}
        </if>
        <if test="userType !=null">
            and `user_type`=#{userType,jdbcType=INTEGER}
        </if>
        <if test="tenantId != null">
            and tenant_id=#{tenantId,jdbcType=VARCHAR}
        </if>
        <if test="appId != null">
            and app_id=#{appId,jdbcType=BIGINT}
        </if>
        order by `updated_time` desc
    </select>

    <update id="updateStatusBatch">
        update promotion_coupon_logger
        set `status` =#{status,jdbcType=INTEGER},
        updated_time = now()
        where
        id in
        <if test="ids != null and ids.size() > 0">
            <foreach item="item" index="index" collection="ids"
                     open="(" separator="," close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
    </update>


    <select id="pageList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from promotion_coupon_logger
        where
        is_deleted = 0
        <if test="po.couponId != null">
            and coupon_id=#{po.couponId,jdbcType=BIGINT}
        </if>
        <if test="po.releaseId !=null">
            and `release_id`=#{po.releaseId,jdbcType=BIGINT}
        </if>
        <if test="po.releaseType !=null">
            and `release_type`=#{po.releaseType,jdbcType=INTEGER}
        </if>
        <if test="po.amount !=null">
            and `amount`=#{po.amount,jdbcType=INTEGER}
        </if>
        <if test="po.orderId !=null">
            and `order_id`=#{po.orderId,jdbcType=VARCHAR}
        </if>
        <if test="po.status !=null and po.status != 99">
            and `status`=#{po.status,jdbcType=INTEGER}
        </if>
        <!--专门给移动端用的-->
        <if test="po.status !=null and po.status == 99">
            and `status` in (1,3)
        </if>
        <if test="po.userId !=null">
            and `user_id`=#{po.userId,jdbcType=BIGINT}
        </if>
        <if test="po.userType !=null">
            and `user_type`=#{po.userType,jdbcType=INTEGER}
        </if>
        <!--产品要求快到期的优先排序，null的给默认2999-12-31 23:59:59 -->
        ORDER BY IFNULL(`end_time`,32503651199000) ASC

    </select>
    <!--auto generated by MybatisCodeHelper on 2019-12-03-->
    <select id="selectUserCoupon" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from promotion_coupon_logger
        <where>
            is_deleted = 0
            <if test="userId != null">
                and user_id=#{userId,jdbcType=BIGINT}
            </if>
            <if test="userType != null">
                and user_type = #{userType,jdbcType=VARCHAR}
            </if>
            <if test="couponId != null">
                and coupon_id = #{couponId,jdbcType=BIGINT}
            </if>
            <if test="releaseId != null">
                and release_id = #{releaseId,jdbcType=BIGINT}
            </if>
            <if test="releaseType != null">
                and release_type = #{releaseType,jdbcType=BIGINT}
            </if>
            <if test="tenantId != null">
                and tenant_id=#{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="appId != null">
                and app_id=#{appId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="countUsingStatistics" resultMap="statisticMap">
        SELECT
                release_id, release_type,
                count(IF (STATUS=0,1,NULL)) AS unused,
                count(IF (STATUS =1,1,NULL)) AS used,
                count(IF (STATUS=2,1,NULL)) AS overtime,
                count(IF (STATUS =3,1,NULL)) AS `lock`
        FROM promotion_coupon_logger
        <where>
            is_deleted = 0
            <if test="releaseId != null">
                and release_id = #{releaseId,jdbcType=BIGINT}
            </if>
            <if test="releaseType != null">
                and release_type = #{releaseType,jdbcType=BIGINT}
            </if>
            <if test="tenantId != null">
                and tenant_id=#{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="appId != null">
                and app_id=#{appId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>