package com.deepexi.dxp.middle.promotion.dao.impl.specify;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepexi.dxp.middle.promotion.common.base.SuperEntity;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityPageDAO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityPageDO;
import com.deepexi.dxp.middle.promotion.mapper.specify.ActivityPageMapper;
import com.deepexi.util.CollectionUtil;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> xinjian.yao
 * @date 2019/11/27 15:51
 */
@Repository
public class ActivityPageDAOImpl extends ServiceImpl<ActivityPageMapper,
        ActivityPageDO> implements ActivityPageDAO {

    @Override
    public Boolean deletedByActivityIds(List<Long> activityIds) {
        UpdateWrapper<ActivityPageDO> wrapper = new UpdateWrapper<>();
        wrapper.lambda().in(ActivityPageDO::getActivityId,activityIds).set(ActivityPageDO::getDeleted,1).set(ActivityPageDO::getUpdatedTime,new Date());
        return   baseMapper.update(null, wrapper) > 0;
    }


    @Override
    public ActivityPageDO getByActivityId(Long id) {
        QueryWrapper<ActivityPageDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ActivityPageDO::getActivityId,id);
        List<ActivityPageDO> activityPageShare = baseMapper.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(activityPageShare)){
            return activityPageShare.get(0);
        }
        return null;
    }

    @Override
    public ActivityPageDO getByActivity(Long activityId, Integer type) {
        return getByActivity(Collections.singletonList(activityId),type).get(0);
    }

    public List<ActivityPageDO> getByActivity(List<Long> activityIds, Integer type) {
        QueryWrapper<ActivityPageDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(ActivityPageDO::getActivityId,activityIds);
        queryWrapper.lambda().eq(ActivityPageDO::getDeleted, SuperEntity.DR_NORMAL);
        queryWrapper.lambda().eq(ActivityPageDO::getType,type);
        return this.list(queryWrapper);
    }
}
