package com.deepexi.dxp.middle.promotion.dao.impl.specify;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityOrderQuery;
import com.deepexi.dxp.marketing.enums.specify.OrderTypeEnum;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityOrderDAO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityOrderDO;
import com.deepexi.dxp.middle.promotion.mapper.specify.ActivityOrderMapper;
import com.deepexi.util.StringUtil;
import com.deepexi.util.pageHelper.PageBean;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;


/**
 * @Description
 * <AUTHOR>
 * @Date 2019/7/17 11:03
 */
@Service
public class ActivityOrderDAOImpl extends ServiceImpl<ActivityOrderMapper, ActivityOrderDO> implements ActivityOrderDAO {


	/**
	 * 列表查询
	 **/
	@Override
	public PageBean<ActivityOrderDO> pageList(ActivityOrderQuery query) {


		Page<ActivityOrderDO> pageParam = new Page<>(query.getPage(), query.getSize());

		IPage<ActivityOrderDO> page = this.baseMapper.pageList(pageParam, query);

		//如果返回的数据为空
		if (Objects.isNull(page)) {
			return new PageBean<>(Lists.newArrayList());
		}

		return new PageBean<>(page);
	}

	@Override
	public int updateCloseStatus(Long orderId, String reason) {
		return this.baseMapper.updateCloseStatus(orderId,reason);
	}

	@Override
	public ActivityOrderDO getRefundOrderByPayOrderNo(String payOrderNo) {
		QueryWrapper<ActivityOrderDO> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(ActivityOrderDO::getWxOrderNo,payOrderNo);
		queryWrapper.lambda().in(ActivityOrderDO::getStatus, ListUtil.toList(0,1));
		queryWrapper.lambda().eq(ActivityOrderDO::getOrderType, OrderTypeEnum.REFUND.getId());//退款订单
		return getOne(queryWrapper);
	}

	@Override
	public int updateStatus(Long orderId, Integer status) {
		return this.baseMapper.updateStatus(orderId,status);
	}

	@Override
	public List<ActivityOrderDO> findList(ActivityOrderQuery query) {
		return this.baseMapper.pageList(query);
	}

	@Override
	public ActivityOrderDO getByCode(String code) {
		if(StringUtil.isNotEmpty(code)){
			return lambdaQuery().eq(ActivityOrderDO::getCode,code).one();
		}
		return null;
	}
}