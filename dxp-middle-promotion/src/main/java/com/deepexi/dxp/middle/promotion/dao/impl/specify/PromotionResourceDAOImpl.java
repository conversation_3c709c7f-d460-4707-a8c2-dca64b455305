package com.deepexi.dxp.middle.promotion.dao.impl.specify;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepexi.dxp.marketing.domain.promotion.query.specify.PromotionResourceQuery;
import com.deepexi.dxp.middle.promotion.dao.specify.PromotionResourceDAO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.PromotionResourceDO;
import com.deepexi.dxp.middle.promotion.mapper.specify.PromotionResourceMapper;
import com.deepexi.util.pageHelper.PageBean;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class PromotionResourceDAOImpl extends ServiceImpl<PromotionResourceMapper,
        PromotionResourceDO> implements PromotionResourceDAO {

    @Override
    public PageBean<PromotionResourceDO> findPage(PromotionResourceQuery query) {
        IPage<PromotionResourceDO> page = baseMapper.findPage(new Page(query.getPage(), query.getSize()), query);
        return new PageBean<>(page);
    }

    @Override
    public int batchInsert(List<PromotionResourceDO> doList) {
        if(doList.isEmpty()) {
            return 0;
        }
        return baseMapper.batchInsert(doList);
    }
}
