package com.deepexi.dxp.middle.promotion.dao.impl.specify;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityGroupQuery;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityAndActivityGroupResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityGroupResponseDTO;
import com.deepexi.dxp.marketing.enums.specify.MiniOrgTypeEnum;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityGroupDAO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityGroupDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityOrderDO;
import com.deepexi.dxp.middle.promotion.mapper.specify.ActivityGroupMapper;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.StringUtil;
import com.deepexi.util.domain.query.BaseQuery;
import com.deepexi.util.pageHelper.PageBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ActivityGroupDAOImpl extends ServiceImpl<ActivityGroupMapper, ActivityGroupDO> implements ActivityGroupDAO {

    @Autowired
    private ActivityGroupMapper activityGroupMapper;

    @Override
    public ActivityGroupResponseDTO findById(Long id) {
        return activityGroupMapper.findById(id);
    }

    @Override
    public PageBean<ActivityGroupResponseDTO> pageList(ActivityGroupQuery query) {

        Page<ActivityOrderDO> pageParam = new Page<>(query.getPage(), query.getSize());

        PageBean<ActivityGroupResponseDTO> activityGroupResponseDTOPageBean = new PageBean<>(baseMapper.pageList(pageParam, query));

        if(activityGroupResponseDTOPageBean != null && CollectionUtil.isNotEmpty(activityGroupResponseDTOPageBean.getContent())){
            List<ActivityGroupResponseDTO> content = activityGroupResponseDTOPageBean.getContent();
            content.forEach(item ->{
                Integer orgType = MapUtils.getInteger(item.getExt(), "orgType");
                item.setOrgType(orgType);
                if(StringUtil.isNotEmpty(item.getSceneCode()) && orgType == null){
                    item.setOrgType(MiniOrgTypeEnum.NATIONAL.getId());//历史默认为全国
                }
            });
        }

        return activityGroupResponseDTOPageBean;
    }


    @Override
    public PageBean<ActivityAndActivityGroupResponseDTO> getActivityAndActivityGroupList(ActivityGroupQuery query) {
        Page<ActivityOrderDO> pageParam = new Page<>(query.getPage(), query.getSize());
        return new PageBean<>(baseMapper.getActivityAndActivityGroupList(pageParam,query));
    }
}
