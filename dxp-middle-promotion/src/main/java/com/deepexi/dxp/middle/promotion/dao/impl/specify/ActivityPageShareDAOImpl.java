package com.deepexi.dxp.middle.promotion.dao.impl.specify;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepexi.dxp.middle.promotion.common.base.SuperEntity;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityPageShareDAO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityPageShareDO;
import com.deepexi.dxp.middle.promotion.mapper.specify.ActivityPageShareMapper;
import com.deepexi.util.CollectionUtil;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> xinjian.yao
 * @date 2019/11/27 15:51
 */
@Repository
public class ActivityPageShareDAOImpl extends ServiceImpl<ActivityPageShareMapper,
        ActivityPageShareDO> implements ActivityPageShareDAO {

    @Override
    public Boolean deletedByActivityIds(List<Long> activityIds) {
        UpdateWrapper<ActivityPageShareDO> wrapper = new UpdateWrapper<>();
        wrapper.lambda().in(ActivityPageShareDO::getActivityId,activityIds).set(ActivityPageShareDO::getDeleted,1).set(ActivityPageShareDO::getUpdatedTime,new Date());
        return   baseMapper.update(null, wrapper) > 0;
    }

    @Override
    public ActivityPageShareDO getByActivityId(Long id) {
        QueryWrapper<ActivityPageShareDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ActivityPageShareDO::getActivityId,id);
        List<ActivityPageShareDO> activityPageShare = baseMapper.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(activityPageShare)){
            return activityPageShare.get(0);
        }
        return null;
    }

    @Override
    public ActivityPageShareDO getByActivity(Long activityId, Integer type) {
        QueryWrapper<ActivityPageShareDO> queryWrapperShare = new QueryWrapper<>();
        queryWrapperShare.lambda().eq(ActivityPageShareDO::getActivityId,activityId);
        queryWrapperShare.lambda().eq(ActivityPageShareDO::getDeleted, SuperEntity.DR_NORMAL);
        queryWrapperShare.lambda().eq(ActivityPageShareDO::getType, type);
        return this.getOne(queryWrapperShare);
    }
}
