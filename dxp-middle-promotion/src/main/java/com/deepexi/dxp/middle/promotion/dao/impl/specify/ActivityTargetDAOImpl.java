package com.deepexi.dxp.middle.promotion.dao.impl.specify;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityTargetQuery;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityTargetDAO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityTargetDO;
import com.deepexi.dxp.middle.promotion.mapper.specify.ActivityTargetMapper;
import com.deepexi.util.pageHelper.PageBean;
import org.springframework.stereotype.Repository;

@Repository
public class ActivityTargetDAOImpl extends ServiceImpl<ActivityTargetMapper, ActivityTargetDO>
		implements ActivityTargetDAO {


	@Override
	public PageBean<ActivityTargetDO> getPage(ActivityTargetQuery query) {

		Page<ActivityTargetDO> pageParam = new Page<>(query.getPage(), query.getSize());
		IPage<ActivityTargetDO> page = this.baseMapper.getPage(pageParam, query);
		return new PageBean<>(page);
	}
}
