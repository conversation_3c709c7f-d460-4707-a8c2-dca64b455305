package com.deepexi.dxp.middle.promotion.dao.impl.specify;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.CustomerFeedbackQuery;
import com.deepexi.dxp.marketing.domain.specify.dto.ActivityFormFeedbackDTO;
import com.deepexi.dxp.marketing.enums.activity.strategy.StrategyGroupEnum;
import com.deepexi.dxp.middle.promotion.dao.specify.CustomerFeedbackDAO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityFormFeedbackDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityOrderDO;
import com.deepexi.dxp.middle.promotion.mapper.specify.ActivityFormFeedbackMapper;
import com.deepexi.util.StringUtil;
import com.deepexi.util.pageHelper.PageBean;
import com.deepexi.util.pojo.CloneDirection;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Repository
public class CustomerFeedbackDAOImpl extends ServiceImpl<ActivityFormFeedbackMapper, ActivityFormFeedbackDO> implements CustomerFeedbackDAO {

    @Override
    public PageBean<ActivityFormFeedbackDO> pageList(CustomerFeedbackQuery query) {
        Page<ActivityOrderDO> pageParam = new Page<>(query.getPage(), query.getSize());

        IPage<ActivityFormFeedbackDO> page = this.baseMapper.pageList(pageParam, query);
        //如果返回的数据为空
        if (Objects.isNull(page)) {
            return new PageBean<>(Lists.newArrayList());
        }

        return new PageBean<>(page);
    }

    @Override
    public ActivityFormFeedbackDO getActivityFormFeedbackById(Long id) {
        return baseMapper.getActivityFormFeedbackById(id);
    }

    @Override
    public ActivityFormFeedbackDTO getActivityFormFeedUserId(Long activityId, String phone,Long hisResourceId) {
        //获取信息登记
        CustomerFeedbackQuery query = new CustomerFeedbackQuery();
        query.setActivityId(activityId);
        //query.setUserId(userId);
        query.setPhone(phone);
        query.setResourceId(hisResourceId);
        List<ActivityFormFeedbackDO> formFeedbackList = this.pageList(query).getContent();
        ActivityFormFeedbackDTO activityFormFeedbackDTO = null;

        if(formFeedbackList.size()>0){
            activityFormFeedbackDTO = formFeedbackList.get(0).clone(ActivityFormFeedbackDTO.class, CloneDirection.FORWARD);
        }
        return activityFormFeedbackDTO;
    }

    @Override
    public Integer getCountByActivityId(Long activityId) {
        QueryWrapper<ActivityFormFeedbackDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ActivityFormFeedbackDO::getActivityId,activityId);
        return baseMapper.selectCount(queryWrapper);
    }

    @Override
    public ActivityFormFeedbackDO getNewByActivityId(Long activityId) {
        return baseMapper.getNewByActivityId(activityId);
    }

    @Override
    public List<ActivityFormFeedbackDO> findList(CustomerFeedbackQuery query) {
        return baseMapper.pageList(query);
    }

    @Override
    public Integer getLimitCount(Long activityId, String limits,String phone) {
        return baseMapper.getLimitCount(activityId,limits,phone);
    }

    @Override
    public ActivityFormFeedbackDO getFormFeedbackByPhone(Long activityId, String phone) {
        if(activityId == null || StringUtil.isEmpty(phone)){
            return null;
        }
        return this.baseMapper.getFormFeedbackByPhone(activityId,phone);
    }
    @Override
    public ActivityFormFeedbackDTO getFormFeedbackDTOByActivty(Integer paTemplateId, Long activityId, String phone, Long resourceId) {
        ActivityFormFeedbackDTO activityFormFeedbackDTO = null;
        if(paTemplateId.toString().equals(StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId())
                ||  paTemplateId.toString().equals(StrategyGroupEnum.HF_SECKILL_ACT.getId())
                ||  paTemplateId.toString().equals(StrategyGroupEnum.HF_CARD_COLLECTING_ACT.getId())
                ||  paTemplateId.toString().equals(StrategyGroupEnum.HF_FORM_ACT.getId())){
            ActivityFormFeedbackDO activityFormFeedbackDO = this.getFormFeedbackByPhone(activityId, phone);
            activityFormFeedbackDTO =  Objects.isNull(activityFormFeedbackDO)?null:activityFormFeedbackDO.clone(ActivityFormFeedbackDTO.class);
        }else{
            activityFormFeedbackDTO  = this.getActivityFormFeedUserId(activityId, phone, resourceId);
        }
        return activityFormFeedbackDTO;
    }
}
