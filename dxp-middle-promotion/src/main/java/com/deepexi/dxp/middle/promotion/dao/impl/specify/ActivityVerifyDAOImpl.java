package com.deepexi.dxp.middle.promotion.dao.impl.specify;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityAnalysisQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.VerifyPageQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityVerifyCountDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityTopicAnalysisResourceResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityVerifyInResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.VerifyOrderResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.query.specify.VerifyOrderPageQuery;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityVerifyDAO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityVerifyDO;
import com.deepexi.dxp.middle.promotion.mapper.specify.ActivityVerifyMapper;
import com.deepexi.util.StringUtil;
import com.deepexi.util.pageHelper.PageBean;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> xinjian.yao
 * @date 2019/11/27 15:51
 */
@Repository
public class ActivityVerifyDAOImpl extends ServiceImpl<ActivityVerifyMapper,
        ActivityVerifyDO> implements ActivityVerifyDAO {

    @Override
    public PageBean<ActivityTopicAnalysisResourceResponseDTO> analysisResourceInfo(ActivityAnalysisQuery query) {
        Page<ActivityVerifyDO> pageParam = new Page<>(query.getPage(), query.getSize());
        IPage<ActivityTopicAnalysisResourceResponseDTO> iPage = baseMapper.getAnalysisResourceInfo(pageParam,query);
        return new PageBean<>(iPage);
    }

    @Override
    public Boolean removeGiftVoucher(List<Long> verifyIds ) {
        return baseMapper.updateGiftVoucher(verifyIds);
    }
    @Override
    public PageBean<ActivityVerifyInResponseDTO> findPage(VerifyPageQuery query) {
        if(StringUtil.isNotEmpty(query.getVerifyTypeList())){
            List<Integer> collect = Arrays.stream(query.getVerifyTypeList().split(",")).map(Integer::parseInt).collect(Collectors.toList());
            query.setVerifyTypeLists(collect);
        }
        return new PageBean<>(this.baseMapper.findPage(new Page(query.getPage(), query.getSize()), query));
    }

    @Override
    public List<ActivityVerifyInResponseDTO> getExcelData(VerifyPageQuery query) {

        if(StringUtil.isNotEmpty(query.getVerifyTypeList())){
            List<Integer> collect = Arrays.stream(query.getVerifyTypeList().split(",")).map(Integer::parseInt).collect(Collectors.toList());
            query.setVerifyTypeLists(collect);
        }
        return baseMapper.getExcelData(query);

    }

    @Override
    public Boolean updateRejectVerify(Integer verifyStatus, Long id,String updatedBy,String projectId,String projectName) {
        return baseMapper.updateRejectVerify(verifyStatus,id,updatedBy,projectId,projectName);
    }

    @Override
    public Boolean updateOver(Integer verifyStatus, Long id) {
        return baseMapper.updateOver(verifyStatus,id);
    }

    @Override
    public List<ActivityVerifyInResponseDTO> findOverList() {
        return baseMapper.findOverList();
    }

    @Override
    public List<ActivityVerifyInResponseDTO> findUnGiftVouchers(Long activityId, String phone) {
        return baseMapper.findUnGiftVouchers( activityId, phone);
    }

    @Override
    public ActivityVerifyDO getByCode(String code) {
        QueryWrapper<ActivityVerifyDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ActivityVerifyDO::getCode,code);
        ActivityVerifyDO verifyDO = baseMapper.selectOne(queryWrapper);
        Assert.notNull(verifyDO,"核销码不存在");
        return verifyDO;
    }

    @Override
    public PageBean<VerifyOrderResponseDTO> getOrderList(VerifyOrderPageQuery query) {
        Page<VerifyOrderResponseDTO> pageParam = new Page<>(query.getPage(), query.getSize());
        IPage<VerifyOrderResponseDTO> iPage = baseMapper.getOrderList(pageParam,query);
        return new PageBean<>(iPage);
    }

    @Override
    public ActivityVerifyDO getByOrderId(Long activityId, Long id) {
        return lambdaQuery().eq(ActivityVerifyDO::getActivityId,activityId).eq(ActivityVerifyDO::getOrderId,id).one();
    }

    @Override
    public List<ActivityVerifyCountDTO> findCountByActivityIdUserId(Long activityId,String phone) {
        return baseMapper.findCountByActivityIdUserId(activityId,phone);
    }

}
