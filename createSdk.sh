#!/bin/bash

cd $(dirname $0)
#-i 指定swagger文档地址，也可以指定本地目录
#          -l 指定SDK语言 包括php、Python、go等几十种语言
#          -o 指定SDK输出目录
#          -Dmodels                  生成models
#          -Dapis                       生成api
#          -DsupportingFiles      生成support类
#          -DmodelDocs=false   不生成doc 默认为ture
#          -DmodelTests=false   不生成单元测试  默认为ture
#         --model-package 指定model包目录
#         --api-package
#         --invoker-package 指定invoker包目录
#         --group-id
#         --artifact-id
#         --artifact-version
#         --git-repo-id  git地址
#         --git-user-id   git用户id
#指定swagger文档地址，也可以指定本地目录
swagger_path="/e/deepexi/sdk/marketing.json"
#指定SDK输出目录
out_path="$PWD/"
sdk_name="dxp-domain-sdk-marketing"
group_id="com.deepexi.dxp"
artifact="dxp-domain-sdk-marketing"
version="1.0.0-BASE-SNAPSHOT"

#指定model包目录
model_package="com.deepexi.sdk.marketing.model"
api_package="com.deepexi.sdk.marketing.api"

#指定invoker包目录
invoker_package="com.deepexi.sdk"
rm -rf dxp-domain-sdk-marketing


java -jar swagger-codegen-cli.jar generate \
   -i http://localhost:38031/v2/api-docs \
   -l java \
   -Dapis -Dmodels -DsupportingFiles -DmodelDocs=false  -DmodelTests=false   -DapiTests=false \
   -o ${out_path}/${sdk_name} \
   --group-id  ${group_id} \
   --artifact-id  ${artifact} \
   --artifact-version ${version} \
   --model-package ${model_package} \
   --api-package  ${api_package} \
   --invoker-package ${invoker_package}
   
cd ./dxp-domain-sdk-marketing

mvn deploy -U -Dmaven.test.skip=true
