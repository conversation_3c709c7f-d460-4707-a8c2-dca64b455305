package com.deepexi.dxp.marketing;

import cn.hutool.http.HttpUtil;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @date 2022/1/4
 */
@SpringBootTest
public class CashBagTest {

    public static void main(String[] args) {
        String content = "{\"actName\":\"集卡测试zfs0016\",\"amount\":100,\"appId\":\"wx7f05ade0396f497c\",\"bizOrderNo\":\"0020211231113200001678\",\"description\":\"活动激励\",\"openid\":\"oiaXF5EHxXuIqmKG89B85ofKApIw\",\"projectId\":\"00000030\",\"sendName\":\"华发置业通\",\"wishing\":\"恭喜发财\"}";
        String url = "http://basepay-test.cnhuafas.com/v1/transaction-api/transfer";
        String mes = HttpUtil.post(url,content);
        System.out.println(mes);
    }
}
