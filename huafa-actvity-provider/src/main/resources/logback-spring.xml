<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">
    <include resource="org/springframework/boot/logging/logback/defaults.xml" />
    <!--定义日志文件的存储地址 勿在 LogBack 的配置中使用相对路径-->
    <property name="LOG_HOME" value="/logs" />
    <conversionRule conversionWord="ip" converterClass="com.deepexi.dxp.marketing.utils.LogIpConfig" />
<!--    <springProperty scope="context" name="kafkaServer" source="com.zipkin.kafkaHosts"/>-->
    <springProperty scope="context" name="appName" source="spring.application.name"/>

    <springProperty scope="context" name="port" source="server.port"/>
    <!--kafka日志输出类-->
<!--    <appender name="KAFKA" class="com.huafagroup.core.configuration.kafka.KafkaAppender">-->
<!--        <topic>app-to-kafka</topic>-->
<!--        <brokerList>${kafkaServer}</brokerList>-->
<!--        <formatter class="com.huafagroup.core.configuration.kafka.MessageFormatter">-->
<!--        </formatter>-->
<!--    </appender>-->

    <appender name="sqlAppender"  class="ch.qos.logback.core.rolling.RollingFileAppender">
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/updatesql/${appName}.%d{yyyy-MM-dd}.%i.sql.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>10</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder
                class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <pattern>
                    <pattern>
                        {
                        "timestamp": "%d{yyyy-MM-dd HH:mm:ss.SSS}",
                        "level": "%level",
                        "serviceName":"${appName}",
                        "ip": "%ip",
                        "port": "${port}",
                        "traceid": "%X{traceId}",
                        "spanid": "%X{spanId}",
                        "thread": "%thread",
                        "location":"%logger{50}",
                        "message": "%message",
                        "stackInfo":"%exception{10}"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
    </appender>

    <!--    <appender name="mqAppender1" class="org.apache.rocketmq.logappender.logback.RocketmqLogbackAppender">
            <tag>yourTag</tag>
            <topic>testtopic</topic>
            <producerGroup>yourLogGroup</producerGroup>
            <nameServerAddress>**********:9876;**********:9876</nameServerAddress>
            <layout>
                <pattern>%date %p %t - %m%n</pattern>
            </layout>
        </appender>

        <appender name="mqAsyncAppender1" class="ch.qos.logback.classic.AsyncAppender">
            <queueSize>1024</queueSize>
            <discardingThreshold>80</discardingThreshold>
            <maxFlushTime>2000</maxFlushTime>
            <neverBlock>true</neverBlock>
            <appender-ref ref="mqAppender1"/>
        </appender>-->
    <!-- 控制台输出 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
        </encoder>
    </appender>
    <!-- 按照每天生成日志文件 -->
    <appender name="FILE"  class="ch.qos.logback.core.rolling.RollingFileAppender">
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${appName}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>10</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>

        <encoder
                class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <pattern>
                    <pattern>
                        {
                        "timestamp": "%d{yyyy-MM-dd HH:mm:ss.SSS}",
                        "level": "%level",
                        "serviceName":"${appName}",
                        "ip": "%ip",
                        "port": "${port}",
                        "traceid": "%X{traceId}",
                        "spanid": "%X{spanId}",
                        "thread": "%thread",
                        "location":"%logger{50}",
                        "message": "%message",
                        "stackInfo":"%exception{10}"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>

    </appender>

    <!-- 日志输出级别 -->
    <!--此logger约束将sql日志输出到sqlappender-->
    <!--<logger name="com.huafagroup.core.configuration.SQLInterceptor" level="INFO" additivity="false">-->
        <!--<appender-ref ref="sqlAppender" />-->
    <!--</logger>-->

    <!-- 日志输出级别 -->
    <root level="INFO">
        <appender-ref ref="STDOUT" />
        <appender-ref ref="FILE" />
        <!--  <appender-ref ref="mqAsyncAppender1"></appender-ref>-->
        <!--<appender-ref ref="KAFKA"/>-->
    </root>

    <logger level="warn" name="org.apache.kafka"/>
    <!--<logger level="warn" name="org.springframework.cloud.netflix.zuul"/>-->
</configuration>