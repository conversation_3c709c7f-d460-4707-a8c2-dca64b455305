//package com.deepexi.dxp.marketing.engine.task;
//
//
//import com.deepexi.util.exception.ApplicationException;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//
//
///**
// * 任务管理器
// *
// * <AUTHOR>
// */
//@Component
//public class TaskManager {
//
//    @Resource
//    private SmsTask smsTask;
//    @Resource
//    private WxTask wxTask;
//
//    public Task getTask(String taskTypeShortName) {
//        switch (taskTypeShortName) {
//            case "sms":
//                return smsTask;
//            case "wx":
//                return wxTask;
//            default:
//                throw new ApplicationException("找不到任务:" + taskTypeShortName);
//        }
//    }
//
//}
