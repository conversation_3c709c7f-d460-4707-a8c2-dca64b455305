package com.deepexi.dxp.marketing.controller.specify.adminapi;

import com.deepexi.dxp.marketing.api.specify.ActivityTargetApi;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityTargetQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityTargetRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityTargetResponseDTO;
import com.deepexi.dxp.marketing.service.specify.ActivityTargetService;
import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/admin-api/v1/activity-target")
@Slf4j
@Api(value = "目标管理接口", tags = "目标管理接口")
public class ActivityTargetApiController implements ActivityTargetApi {

	@Autowired
	private ActivityTargetService activityTargetService;

	@Override
	@PostMapping("/save")
	public Data<Boolean> save(ActivityTargetRequestDTO dto) {

		Boolean result = activityTargetService.create(dto);

		return new Data<>(result);
	}

	@Override
	@GetMapping("/getPage")
	public Data<PageBean<ActivityTargetResponseDTO>> getPage(ActivityTargetQuery query) {


		return new Data<>(activityTargetService.getPage(query));
	}

	@PostMapping("/getPagePost")
	public Data<PageBean<ActivityTargetResponseDTO>> getPagePost(@RequestBody ActivityTargetQuery query) {


		return new Data<>(activityTargetService.getPage(query));
	}
}
