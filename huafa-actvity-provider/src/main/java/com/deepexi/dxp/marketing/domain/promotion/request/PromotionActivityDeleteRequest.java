package com.deepexi.dxp.marketing.domain.promotion.request;

import com.deepexi.dxp.marketing.common.base.dto.SuperExtDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.*;
import com.deepexi.dxp.marketing.domain.promotion.dto.ComdityLimitAdminDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.ActivityRuleDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 删除活动DTO
 * <AUTHOR>
 * @version 1.0
 * @date 2020-08-17 17:19
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel
@Data
public class PromotionActivityDeleteRequest extends AbstractObject {

    @ApiModelProperty(value = "活动id list",required = true)
    private List<Long> ids;

}