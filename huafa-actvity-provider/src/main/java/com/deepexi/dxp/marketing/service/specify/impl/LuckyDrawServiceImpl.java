package com.deepexi.dxp.marketing.service.specify.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.deepexi.dxp.marketing.common.base.utils.CommonExceptionCode;
import com.deepexi.dxp.marketing.common.base.vo.SuperExtVO;
import com.deepexi.dxp.marketing.config.HuafaConstantConfig;
import com.deepexi.dxp.marketing.constant.PromotionActivityConstant;
import com.deepexi.dxp.marketing.constant.RedisConstants;
import com.deepexi.dxp.marketing.converter.ActivityExtConverter;
import com.deepexi.dxp.marketing.converter.ActivityInfoConverter;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.*;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.*;
import com.deepexi.dxp.marketing.domain.marketing.response.*;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.PromotionActivityResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.ResourceHisDetailResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.query.specify.PromotionHisResourceQuery;
import com.deepexi.dxp.marketing.domain.specify.dto.ActivityFormFeedbackDTO;
import com.deepexi.dxp.marketing.enums.activity.limit.MemberTypeEnum;
import com.deepexi.dxp.marketing.enums.activity.limit.PATemplateBaseEnum;
import com.deepexi.dxp.marketing.enums.activity.limit.PATemplateLuckyDrawEnum;
import com.deepexi.dxp.marketing.enums.activity.strategy.StrategyGroupEnum;
import com.deepexi.dxp.marketing.enums.coupon.WhetherEnum;
import com.deepexi.dxp.marketing.enums.resource.ActivityStatusEnum;
import com.deepexi.dxp.marketing.enums.resource.ResourceGrantWayEnum;
import com.deepexi.dxp.marketing.enums.specify.*;
import com.deepexi.dxp.marketing.manager.promotion.PromotionActivityManager;
import com.deepexi.dxp.marketing.service.specify.*;
import com.deepexi.dxp.marketing.utils.ExcelExportUtil;
import com.deepexi.dxp.marketing.utils.GenerateIdUtil;
import com.deepexi.dxp.marketing.utils.HuaFaHmacAuthUtil;
import com.deepexi.dxp.marketing.utils.LotteryUtil;
import com.deepexi.dxp.middle.marketing.common.base.SuperEntity;
import com.deepexi.dxp.middle.promotion.converter.specify.BargainingConverter;
import com.deepexi.dxp.middle.promotion.converter.specify.LuckyDrawConverter;
import com.deepexi.dxp.middle.promotion.dao.PromotionActivityDAO;
import com.deepexi.dxp.middle.promotion.dao.PromotionActivityLimitDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.*;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityLimitDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.*;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.DateUtils;
import com.deepexi.util.JsonUtil;
import com.deepexi.util.StringUtil;
import com.deepexi.util.domain.entity.BaseEntity;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import com.deepexi.util.pojo.CloneDirection;
import com.deepexi.util.pojo.ObjectCloneUtils;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.CloneUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class LuckyDrawServiceImpl implements LuckyDrawService {

    LoadingCache<Long, List<PromotionActivityLimitDO>> activityLimitCache = CacheBuilder.newBuilder()
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .maximumSize(1000)
            .build(new CacheLoader<Long, List<PromotionActivityLimitDO>>() {
                @Override
                public List<PromotionActivityLimitDO> load(Long key) throws Exception {
                    return promotionActivityLimitDAO.selectByActivityId(key);
                }
            });

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ActivityPageDAO activityPageDAO;

    @Autowired
    private ActivityPageShareDAO activityPageShareDAO;

    @Autowired
    private PromotionActivityDAO promotionActivityDAO;

    @Autowired
    private ActivityParticipationDAO activityParticipationDAO;

    @Autowired
    private PromotionActivityLimitDAO promotionActivityLimitDAO;

    @Autowired
    private ActivityPartakeLogDAO activityPartakeLogDAO;

    @Resource
    public CustomerFeedbackDAO customerFeedbackDAO;

    @Resource
    public PromotionResourceDAO promotionResourceDAO;

    @Resource
    public PromotionHisResourceDAO promotionHisResourceDAO;

    @Resource
    public ActivityUserRelatedDAO activityUserRelatedDAO;

    @Resource
    public ActivityVerifyDAO activityVerifyDAO;

    @Autowired
    private ActivityTargetService activityTargetService;

    private int switchNumberMax = 3;
    @Autowired
    private ActivityPromotionChannelService activityPromotionChannelService;
    @Autowired
    private PromotionActivityService promotionActivityService;
    @Autowired
    private PromotionActivityManager promotionActivityManager;

    @Autowired
    private GenerateIdUtil generateIdUtil;

    @Autowired
    private IncentiveService incentiveService;

    @Autowired
    private ActivityFissionAssistResourceDAO activityFissionAssistResourceDAO;

    @Autowired
    private ActivityFissionAssistLogDAO activityFissionAssistLogDAO;

    @Autowired
    private HuafaConstantConfig huafaConstantConfig;

    @Autowired
    private HuaFaHmacAuthUtil huaFaHmacAuthUtil;
    private static ActivityPartakeLogResponseDTO apply(ActivityPartakeLogResponseDTO log) {
        log.setIssuanceTime(DateUtils.toDateText(log.getCreatedTime()));
        return log;
    }

    @Override
    @Transactional(rollbackFor = ApplicationException.class)
    public boolean save(LuckyDrawRequestDTO requestDTO) {
        //添加校验
        this.addCheck(requestDTO);

        //保存活动基本信息
        PromotionActivityDO promotionActivityDO = LuckyDrawConverter.converter(requestDTO);
        promotionActivityDAO.save(promotionActivityDO);

        //保存适用项目
        List<ActivityParticipationDO> activityParticipationList = LuckyDrawConverter.participationConverter(requestDTO,promotionActivityDO.getId());
        activityParticipationDAO.saveBatch(activityParticipationList);

        //保存活动规则配置
        PromotionActivityLimitDO promotionActivityLimitDO = LuckyDrawConverter.limitConverter(requestDTO);
        promotionActivityLimitDO.setActivityId(promotionActivityDO.getId());
        promotionActivityLimitDAO.save(promotionActivityLimitDO);

        //保存活动奖品配置
        List<HisResourceJsonVO> hisResourceJsonList = ObjectCloneUtils.convertList(requestDTO.getPrizeList(),HisResourceJsonVO.class);
        this.getHisResourceJsonList(hisResourceJsonList);
        promotionHisResourceDAO.saveHisResource(hisResourceJsonList,promotionActivityDO.getId());

        //保存界面活动页配置
        ActivityPageDO activityPageDO = LuckyDrawConverter.activityPageConverter(requestDTO);
        activityPageDO.setActivityId(promotionActivityDO.getId());
        activityPageDAO.save(activityPageDO);

        //保存界面分享页配置
        ActivityPageShareDO activityPageShareDO = LuckyDrawConverter.activityShareConverter(requestDTO);
        activityPageShareDO.setActivityId(promotionActivityDO.getId());
        activityPageShareDAO.save(activityPageShareDO);

        activityPromotionChannelService.createDefaults(promotionActivityDO.getId(), ActivityPromotionTypeEnum.ACTIVITY.getType(), promotionActivityDO.getPaTemplateId());
        return Boolean.TRUE;
    }

    private void getHisResourceJsonList(List<HisResourceJsonVO> hisResourceJsonList) {
        //奖品数量
        int size = hisResourceJsonList.size();

        //最大奖品数量（7奖品 + 谢谢参与）
        int prizeSize = PromotionActivityConstant.DZP_PRIZE_SIZE - 1;
        if (size >= prizeSize){
            return ;
        }

        //奖品概率之和
        double OddsOfWinningSum = hisResourceJsonList.stream().mapToDouble(HisResourceJsonVO::getOddsOfWinning).sum();

        //剩余概率
        BigDecimal residual = new BigDecimal(PromotionActivityConstant.ODDS_OF_WINNING - OddsOfWinningSum);

        //平摊剩余概率给谢谢参与
        BigDecimal divide = residual.divide(new BigDecimal(PromotionActivityConstant.DZP_PRIZE_SIZE - size), 2, BigDecimal.ROUND_HALF_UP);

        for (int i = 0; i < PromotionActivityConstant.DZP_PRIZE_SIZE; i++) {
            if (i >= size){
                HisResourceJsonVO hisResourceJsonVO = new HisResourceJsonVO();
                hisResourceJsonVO.setResourceId(-1L);
                if (i < PromotionActivityConstant.DZP_PRIZE_SIZE - 1){
                    residual = residual.subtract(divide);
                    hisResourceJsonVO.setOddsOfWinning(divide.doubleValue());
                }else{
                    hisResourceJsonVO.setOddsOfWinning(residual.doubleValue());
                }
                hisResourceJsonList.add(hisResourceJsonVO);
            }
        }
    }

    private void addCheck(LuckyDrawRequestDTO requestDTO) {
        QueryWrapper<PromotionActivityDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PromotionActivityDO::getName,requestDTO.getName());
        PromotionActivityDO one = promotionActivityDAO.getOne(queryWrapper);
        if (Objects.nonNull(one)){
            throw new ApplicationException("活动名称重复");
        }

        this.check(requestDTO);
    }

    private void check(LuckyDrawRequestDTO requestDTO) {

        List<PrizeConfigVO> prizeList = requestDTO.getPrizeList();
        if(CollectionUtil.isEmpty(prizeList)){
            throw new ApplicationException("奖品配置不能为空");
        }

        double sum = prizeList.stream().mapToDouble(PrizeConfigVO::getOddsOfWinning).sum();
        if (sum > PromotionActivityConstant.ODDS_OF_WINNING){
            throw new ApplicationException("所有奖品的中奖率相加不可超过100%");
        }

        Date startDate = DateUtils.getDate(requestDTO.getStartTime(), DateUtils.DEFAULT_DATE_TIME_FORMAT);
        Date endDate = DateUtils.getDate(requestDTO.getEndTime(), DateUtils.DEFAULT_DATE_TIME_FORMAT);
        Date now = DateUtils.now();
        if (startDate.after(endDate)){
            throw new ApplicationException("活动结束时间不能小于活动开始时间");
        }
        if (requestDTO.getStatus() != null) {
            if (endDate.before(now)) {
                //结束时间在当前时间之前  ---》 活动已结束
                requestDTO.setStatus(Integer.parseInt(ActivityStatusEnum.FINISH.getId()));
            } else if (startDate.after(now)) {
                //开始时间在当前时间之后 ---》 活动未开始
                requestDTO.setStatus(Integer.parseInt(ActivityStatusEnum.UN_START.getId()));
            } else {
                requestDTO.setStatus(Integer.parseInt(ActivityStatusEnum.IN_PROGRESS.getId()));
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateActivityById(Long id, LuckyDrawRequestDTO requestDTO) {

        this.updateCheck(requestDTO);
        //未开始的状态下支持编辑全部信息
        //进行中的状态下仅支持增加奖品数量，其它信息不可编辑
        if (requestDTO.getStatus().equals(Integer.parseInt(ActivityStatusEnum.UN_START.getId()))) {
            //添加校验
            requestDTO.setId(id);

            //编辑活动基本信息
            PromotionActivityDO promotionActivityDO = LuckyDrawConverter.converter(requestDTO);
            promotionActivityDO.setId(id);
            promotionActivityDAO.updateById(promotionActivityDO);

            //保存适用项目---->删除旧数据重新添加
            UpdateWrapper<ActivityParticipationDO> participationUpdateWrapper = new UpdateWrapper<>();
            participationUpdateWrapper.lambda().eq(ActivityParticipationDO::getActivityId, id);
            participationUpdateWrapper.lambda().eq(ActivityParticipationDO::getDeleted, 0);
            activityParticipationDAO.remove(participationUpdateWrapper);
            List<ActivityParticipationDO> activityParticipationList = LuckyDrawConverter.participationConverter(requestDTO, promotionActivityDO.getId());
            activityParticipationDAO.saveBatch(activityParticipationList);

            //编辑活动规则配置
            PromotionActivityLimitDO promotionActivityLimitDO = LuckyDrawConverter.limitConverter(requestDTO);
            UpdateWrapper<PromotionActivityLimitDO> wrapperLimit = new UpdateWrapper<>();
            wrapperLimit.lambda().eq(PromotionActivityLimitDO::getActivityId, id);
            promotionActivityLimitDAO.update(promotionActivityLimitDO, wrapperLimit);

            //保存活动奖品配置----》删除旧数据重新添加
            UpdateWrapper<PromotionHisResourceDO> hisResourceUpdateWrapper = new UpdateWrapper<>();
            hisResourceUpdateWrapper.lambda().eq(PromotionHisResourceDO::getActivityId, id);
            hisResourceUpdateWrapper.lambda().eq(PromotionHisResourceDO::getDeleted, SuperEntity.DR_NORMAL);
            promotionHisResourceDAO.remove(hisResourceUpdateWrapper);
            List<HisResourceJsonVO> hisResourceJsonList = ObjectCloneUtils.convertList(requestDTO.getPrizeList(), HisResourceJsonVO.class);
            this.getHisResourceJsonList(hisResourceJsonList);
            promotionHisResourceDAO.saveHisResource(hisResourceJsonList, promotionActivityDO.getId());

            //编辑界面活动页配置
            ActivityPageDO activityPageDO = LuckyDrawConverter.activityPageConverter(requestDTO);
            UpdateWrapper<ActivityPageDO> wrapperPage = new UpdateWrapper<>();
            wrapperPage.lambda().eq(ActivityPageDO::getActivityId, id);
            activityPageDAO.update(activityPageDO, wrapperPage);

            //编辑界面分享页配置
            ActivityPageShareDO activityPageShareDO = LuckyDrawConverter.activityShareConverter(requestDTO);
            UpdateWrapper<ActivityPageShareDO> wrapperShare = new UpdateWrapper<>();
            wrapperShare.lambda().eq(ActivityPageShareDO::getActivityId, id);
            activityPageShareDAO.update(activityPageShareDO, wrapperShare);
        }else if (requestDTO.getStatus().equals(Integer.parseInt(ActivityStatusEnum.IN_PROGRESS.getId()))){
            //进行中的状态下仅支持增加奖品数量，其它信息不可编辑
            promotionHisResourceDAO.updateResourceIssuedNumber(ObjectCloneUtils.convertList(requestDTO.getPrizeList(), HisResourceJsonVO.class));
        }
        return Boolean.TRUE;
    }

    private void updateCheck(LuckyDrawRequestDTO requestDTO) {
        PromotionActivityDO byId = promotionActivityDAO.getById(requestDTO.getId());
        if (byId == null){
            throw new ApplicationException("活动不存在");
        }
        if (!byId.getName().equals(requestDTO.getName())) {
            QueryWrapper<PromotionActivityDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(PromotionActivityDO::getName, requestDTO.getName());
            PromotionActivityDO one = promotionActivityDAO.getOne(queryWrapper);
            if (Objects.nonNull(one)) {
                throw new ApplicationException("活动名称已存在");
            }
        }
        this.check(requestDTO);
    }

    @Override
    public Boolean delete(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)){
            throw new ApplicationException("活动id不能为空");
        }
        return promotionActivityDAO.removeByIds(ids);
    }

    @Override
    public LuckyDrawResponseDTO detail(Long id) {
        if (id == null){
            throw new ApplicationException("活动id不能为空");
        }

        //查询活动基础信息
        PromotionActivityDO detail = promotionActivityDAO.getById(id);
        if (detail == null){
            throw new ApplicationException("活动id不存在");
        }

        LuckyDrawResponseDTO luckyDrawResponseDTO = new LuckyDrawResponseDTO();
        luckyDrawResponseDTO.setName(detail.getName());
        ActivityExtVO converter = ActivityExtConverter.converter(detail.getExt());
        if (converter != null){
            ActivityTargetResponseDTO byId = activityTargetService.getById(converter.getActivityGoal().longValue());
            converter.setActivityGoalName(byId.getTargetName());
        }
        luckyDrawResponseDTO.setActivityExtVO(converter);
        luckyDrawResponseDTO.setStatus(detail.getStatus());
        luckyDrawResponseDTO.setStartTime(DateUtils.toDateText(detail.getStartTime(),DateUtils.DEFAULT_DATE_TIME_FORMAT));
        luckyDrawResponseDTO.setEndTime(DateUtils.toDateText(detail.getEndTime(),DateUtils.DEFAULT_DATE_TIME_FORMAT));
        luckyDrawResponseDTO.setCreatedBy(detail.getCreatedBy());
        luckyDrawResponseDTO.setCreatedTime(detail.getCreatedTime());
        luckyDrawResponseDTO.setPaTemplateId(detail.getPaTemplateId());


        //获取项目名称
        List<ActivityParticipationDO> activityParticipationList = activityParticipationDAO.listByActivityId(id);
        if (CollectionUtil.isNotEmpty(activityParticipationList)){
            luckyDrawResponseDTO.setProjectList(ObjectCloneUtils.convertList(activityParticipationList,ActivityParticipationDTO.class));
        }
        //活动规则信息
        luckyDrawResponseDTO.setRuleConfigVO(this.getActivityLimit(id));

        //活动奖品信息
        luckyDrawResponseDTO.setPrizeList(this.getPrizeList(id));

        //界面活动页配置
        luckyDrawResponseDTO.setActivityPageDTO(Optional.ofNullable(activityPageDAO.getByActivity(id, ActivityTypeEnum.ACTIVITY.getId())).map(e->e.clone(ActivityPageDTO.class)).orElse(null));

        //界面分享页配置
        luckyDrawResponseDTO.setActivityPageShareDTO(Optional.ofNullable(activityPageShareDAO.getByActivity(id, ActivityTypeEnum.ACTIVITY.getId())).map(e->e.clone(ActivityPageShareDTO.class)).orElse(null));
        return luckyDrawResponseDTO;
    }

    private List<PromotionHisResourceDTO> getPrizeList(Long activityId) {

        PromotionHisResourceQuery query = new PromotionHisResourceQuery();
        query.setActivityId(activityId);
        List<PromotionHisResourceDO> list = promotionHisResourceDAO.findPage(query).getContent();
        if (CollectionUtil.isNotEmpty(list)){
            return ObjectCloneUtils.convertList(list,PromotionHisResourceDTO.class);
        }
        return Lists.newArrayList();
    }

    @Override
    public PageBean<ActivityPartakeLogResponseDTO> userLotteryRecord(UserLotteryRecordQuery query) {
        PageBean<ActivityPartakeLogResponseDTO> page = activityPartakeLogDAO.findPage(query);

        //添加用户登记明细
        this.getCustomerFeedbackList(page.getContent(),query.getActivityId());
        return page;
    }

    @Override
    public Set<String> prizeResultList(Long id) {
        if(id == null){
            throw new ApplicationException("活动id不存在");
        }
        QueryWrapper<PromotionHisResourceDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PromotionHisResourceDO::getActivityId,id);
        queryWrapper.lambda().eq(PromotionHisResourceDO::getDeleted,SuperEntity.DR_NORMAL);
        List<PromotionHisResourceDO> list = promotionHisResourceDAO.list(queryWrapper);
        if (CollectionUtil.isNotEmpty(list)){
            return list.stream().map(e->{
                if (Objects.equals(e.getName(), PromotionActivityConstant.THANK_PARTICIPATION)){
                    e.setName(PromotionActivityConstant.NOT_WINNING);
                }
                return e.getName();
            }).collect(Collectors.toSet());
        }
        return new HashSet<>();
    }

    @Override
    public List<VerifyInfoResponseDTO> verifyInfoList(VerifyInfoQuery query) {
        List<VerifyInfoResponseDTO> list = activityPartakeLogDAO.verifyInfoList(query);
        return list.stream().peek(item->{
            Integer sendCount = item.getIssuedQuantity()-item.getRemainingQuantity();
            Integer waitReceived = sendCount-item.getReceivedQuantity();
            if(waitReceived > 0){
                item.setNotClaimedQuantity(waitReceived);
            }
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LotteryResourceDTO lottery(LotteryRequestDTO query) {
        //活动校验
        this.lotteryCheck(query);

        ActivityUserRelatedDTO activityUserRelated = this.getActivityUserRelated(query);
        Object drawNumber = activityUserRelated.getLimits().get(ActivityTemplateNumberEnum.DRAW_NUMBER.getId());
        if(activityUserRelated.getLimits() == null ||
                drawNumber == null ||
                (int) drawNumber <= 0){
            throw new ApplicationException("您当前剩余抽奖次数为0，不可继续抽奖！");
        }

        //查询活动关联奖品及奖品规则
        List<PromotionHisResourceDO> hisResourceList = this.getPromotionHisResource(query.getActivityId());

        //奖品规则校验
        this.prizeConfig(query.getActivityId(),query.getPhone(),hisResourceList);

        List<Double> collect = hisResourceList.stream().map(PromotionHisResourceDO::getOddsOfWinning).collect(Collectors.toList());
        int lottery = LotteryUtil.lottery(collect);
        PromotionHisResourceDO promotionHisResourceDO = hisResourceList.get(lottery);

        //保存用户抽奖记录
        //没有选择项目或者完成信息登记，领取不成功，奖品数量不扣减，消耗抽奖次数
        ActivityPartakeRequest partakeRequest = null;
        try {
            partakeRequest = JSON.parseObject(JSON.toJSONString(CloneUtils.clone(query)), ActivityPartakeRequest.class);
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
        assert partakeRequest != null;
        String resourceCode = generateIdUtil.getResourceCode();
        ActivityPartakeLogDTO activityPartakeLogDTO = LuckyDrawConverter.partakeLogConverter(partakeRequest, promotionHisResourceDO,resourceCode);
        Long partakeLogId = activityPartakeLogDAO.save(activityPartakeLogDTO);

        LotteryResourceDTO lotteryResourceDTO = promotionHisResourceDO.clone(LotteryResourceDTO.class);
        lotteryResourceDTO.setWinLotteryId(partakeLogId);

        //消耗抽奖次数
        ActivityUserRelatedDO activityUserRelatedDO = new ActivityUserRelatedDO();
        activityUserRelatedDO.setId(activityUserRelated.getId());
        activityUserRelated.getLimits().put(ActivityTemplateNumberEnum.DRAW_NUMBER.getId(),(int)drawNumber - 1);
        activityUserRelatedDO.setLimits(activityUserRelated.getLimits());
        activityUserRelatedDAO.updateById(activityUserRelatedDO);
        return lotteryResourceDTO;
    }

    @Override
    public PrizeListAndSurplusNumberDTO prizeList(LotteryRequestDTO query) throws ExecutionException {

        PromotionActivityDetailDTO result = promotionActivityManager.getCacheActivityById(query.getActivityId());
        if (Objects.isNull(result)){
            return this.getPrizeList(query);
        }

        PromotionActivityDO byId = result.clone(PromotionActivityDO.class);
        byId.setName(result.getActivityName());
        byId.setId(result.getActivityId());

        //查询活动关联奖品及奖品规则
//        List<PromotionActivityLimitDO> promotionActivityLimits = promotionActivityLimitDAO.selectByActivityId(query.getActivityId());

        /**
         * 活动规则配置完全可以本地缓存
         */
        List<PromotionActivityLimitDO> promotionActivityLimits = activityLimitCache.get(query.getActivityId());
        if (CollectionUtil.isEmpty(promotionActivityLimits)){
            throw new ApplicationException("活动规则未配置");
        }

        PromotionActivityResponseDTO clone = byId.clone(PromotionActivityResponseDTO.class);
        clone.setExt(byId.getExt());
        //查询奖品列表
        PrizeListAndSurplusNumberDTO prizeListAndSurplusNumber = new PrizeListAndSurplusNumberDTO();
        prizeListAndSurplusNumber.setPromotionActivity(clone);
        //List<PromotionHisResourceDO> promotionHisResource = this.getPromotionHisResource(query.getActivityId());
        prizeListAndSurplusNumber.setPrizeList(ObjectCloneUtils.convertList(result.getPrizeList(),PromotionHisResourceDTO.class));

        //查询剩余抽奖次数
        ActivityPartakeRequest partake = query.clone(ActivityPartakeRequest.class);
        ActivityUserRelatedDTO activityUserRelated = promotionActivityService.getActivityUserRelated(partake,promotionActivityLimits,byId.getPaTemplateId().toString());
        prizeListAndSurplusNumber.setDrawNumber((int)activityUserRelated.getLimits().get(ActivityTemplateNumberEnum.DRAW_NUMBER.getId()));
        prizeListAndSurplusNumber.setShareNumber((int)activityUserRelated.getLimits().get(ActivityTemplateNumberEnum.SHARE_NUMBER.getId()));

        //是否显示切换按钮
        int switchNumber = (int) activityUserRelated.getLimits().get(ActivityTemplateNumberEnum.SWITCH_NUMBER.getId());
        Boolean showSwitchCheck = this.isShowSwitchCheck(switchNumber, query.getActivityId(),result.getProjectInfoList());
        if (showSwitchCheck){
            if (!activityUserRelated.getLimits().containsKey("projectId") || Objects.isNull(activityUserRelated.getLimits().get("projectId"))){
                showSwitchCheck = Boolean.FALSE;
            }
        }
        prizeListAndSurplusNumber.setIsShowSwitch(showSwitchCheck);

//        PromotionActivityLimitDO limitDO = promotionActivityLimits
//                .stream().filter(e -> e.getType().equals(PATemplateBaseEnum.LUCKYDRAW.getId()))
//                .findFirst()
//                .orElse(null);
//        PromotionActivityLimitDO limitDO = result.getLuckyDrawLimit();
//        if (limitDO == null){
//            throw new ApplicationException("活动规则未配置");
//        }
        if(CollectionUtil.isEmpty(result.getLuckyDrawLimit())){
            throw new ApplicationException("活动规则未配置");
        }
        List<BaseActivityDTO> numberList = result.getLuckyDrawLimit();//JSON.parseArray(limitDO.getLimits(), BaseActivityDTO.class);
        prizeListAndSurplusNumber.setShareDrawNumber(LuckyDrawConverter.elLimitConverter(numberList, ActivityTemplateNumberEnum.SHARE_DRAW_NUMBER.getId()));


        //界面活动页配置
        //prizeListAndSurplusNumber.setActivityPageDTO(Optional.ofNullable(activityPageDAO.getByActivity(query.getActivityId(), ActivityTypeEnum.ACTIVITY.getId())).map(e->e.clone(ActivityPageDTO.class)).orElse(null));
        prizeListAndSurplusNumber.setActivityPageDTO(result.getActivityPageVO().clone(ActivityPageDTO.class));

        //界面分享页配置
        //prizeListAndSurplusNumber.setActivityPageShareDTO(Optional.ofNullable(activityPageShareDAO.getByActivity(query.getActivityId(), ActivityTypeEnum.ACTIVITY.getId())).map(e->e.clone(ActivityPageShareDTO.class)).orElse(null));
        prizeListAndSurplusNumber.setActivityPageShareDTO(result.getActivityPageShareVO().clone(ActivityPageShareDTO.class));

        prizeListAndSurplusNumber.setProjectId(Optional.ofNullable(activityUserRelated.getLimits().get("projectId")).map(String::valueOf).orElse(null));
        prizeListAndSurplusNumber.setProjectName(Optional.ofNullable(activityUserRelated.getLimits().get("projectName")).map(String::valueOf).orElse(null));
        prizeListAndSurplusNumber.setProjectCity(Optional.ofNullable(activityUserRelated.getLimits().get("projectCity")).map(String::valueOf).orElse(null));
        return prizeListAndSurplusNumber;
    }


    private PrizeListAndSurplusNumberDTO getPrizeList(LotteryRequestDTO query) {

        PromotionActivityDO byId = promotionActivityDAO.getById(query.getActivityId());
        if (byId == null){
            throw new ApplicationException("活动不存在");
        }


        //查询活动关联奖品及奖品规则
        List<PromotionActivityLimitDO> promotionActivityLimits = promotionActivityLimitDAO.selectByActivityId(query.getActivityId());
        if (CollectionUtil.isEmpty(promotionActivityLimits)){
            throw new ApplicationException("活动规则未配置");
        }

        PromotionActivityResponseDTO clone = byId.clone(PromotionActivityResponseDTO.class);
        clone.setExt(byId.getExt());
        //查询奖品列表
        PrizeListAndSurplusNumberDTO prizeListAndSurplusNumber = new PrizeListAndSurplusNumberDTO();
        prizeListAndSurplusNumber.setPromotionActivity(clone);
        List<PromotionHisResourceDO> promotionHisResource = this.getPromotionHisResource(query.getActivityId());
        prizeListAndSurplusNumber.setPrizeList(ObjectCloneUtils.convertList(promotionHisResource,PromotionHisResourceDTO.class));

        //查询剩余抽奖次数
        ActivityPartakeRequest partake = query.clone(ActivityPartakeRequest.class);
        ActivityUserRelatedDTO activityUserRelated = promotionActivityService.getActivityUserRelated(partake,promotionActivityLimits,byId.getPaTemplateId().toString());
        prizeListAndSurplusNumber.setDrawNumber((int)activityUserRelated.getLimits().get(ActivityTemplateNumberEnum.DRAW_NUMBER.getId()));
        prizeListAndSurplusNumber.setShareNumber((int)activityUserRelated.getLimits().get(ActivityTemplateNumberEnum.SHARE_NUMBER.getId()));

        //是否显示切换按钮
        int switchNumber = (int) activityUserRelated.getLimits().get(ActivityTemplateNumberEnum.SWITCH_NUMBER.getId());
        Boolean showSwitchCheck = this.isShowSwitchCheck(switchNumber, query.getActivityId(),null);
        if (showSwitchCheck){
            if (!activityUserRelated.getLimits().containsKey("projectId") || Objects.isNull(activityUserRelated.getLimits().get("projectId"))){
                showSwitchCheck = Boolean.FALSE;
            }
        }
        prizeListAndSurplusNumber.setIsShowSwitch(showSwitchCheck);

        PromotionActivityLimitDO limitDO = promotionActivityLimits
                .stream().filter(e -> e.getType().equals(PATemplateBaseEnum.LUCKYDRAW.getId()))
                .findFirst()
                .orElse(null);
        if (limitDO == null){
            throw new ApplicationException("活动规则未配置");
        }
        List<BaseActivityDTO> numberList = JSON.parseArray(limitDO.getLimits(), BaseActivityDTO.class);
        prizeListAndSurplusNumber.setShareDrawNumber(LuckyDrawConverter.elLimitConverter(numberList, ActivityTemplateNumberEnum.SHARE_DRAW_NUMBER.getId()));


        //界面活动页配置
        prizeListAndSurplusNumber.setActivityPageDTO(Optional.ofNullable(activityPageDAO.getByActivity(query.getActivityId(), ActivityTypeEnum.ACTIVITY.getId())).map(e->e.clone(ActivityPageDTO.class)).orElse(null));

        //界面分享页配置
        prizeListAndSurplusNumber.setActivityPageShareDTO(Optional.ofNullable(activityPageShareDAO.getByActivity(query.getActivityId(), ActivityTypeEnum.ACTIVITY.getId())).map(e->e.clone(ActivityPageShareDTO.class)).orElse(null));
        prizeListAndSurplusNumber.setProjectId(Optional.ofNullable(activityUserRelated.getLimits().get("projectId")).map(String::valueOf).orElse(null));
        prizeListAndSurplusNumber.setProjectName(Optional.ofNullable(activityUserRelated.getLimits().get("projectName")).map(String::valueOf).orElse(null));
        prizeListAndSurplusNumber.setProjectCity(Optional.ofNullable(activityUserRelated.getLimits().get("projectCity")).map(String::valueOf).orElse(null));
        return prizeListAndSurplusNumber;
    }

    @Override
    public Boolean isShowSwitchCheck(Integer switchNumber, Long activityId, List<ActivityParticipationVO> projectInfoList) {
        //是否显示切换项目按钮
        //最多可切换3次项目，超出则隐藏切换项目栏目
        if (switchNumber >= switchNumberMax){
            log.info("最多可切换3次项目activityId：{}",activityId);
            return Boolean.FALSE;
        }

        //非关联多个项目隐藏切换项目栏目
        List<ActivityParticipationDO> activityParticipationList = Lists.newArrayList();
        if(CollectionUtil.isEmpty(projectInfoList)){
            activityParticipationList = activityParticipationDAO.listByActivityId(activityId);
        }else{
            activityParticipationList = ObjectCloneUtils.convertList(projectInfoList, ActivityParticipationDO.class);
        }
        if (CollectionUtil.isNotEmpty(activityParticipationList) && activityParticipationList.size() == 1) {
            log.info("活动之关联了一个项目activityId：{}",activityId);
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    @Override
    public List<WinningRecordResponseDTO> winningRecord(PartakeLogQuery query) {

        PromotionActivityDO promotionActivityDO = promotionActivityDAO.getById(query.getActivityId());
        List<WinningRecordResponseDTO>  contentList = Lists.newArrayList();
        if(ObjectUtil.equal(promotionActivityDO.getPaTemplateId().toString(), StrategyGroupEnum.HF_FISSION_REDUCEPRICE_ACT.getId())){

            PageBean<ActivityPartakeLogResponseDTO> page = activityPartakeLogDAO.getSuccessRecord(query);
            PageBean<WinningRecordResponseDTO> winningRecordResponsePageBean = ObjectCloneUtils.convertPageBean(page, WinningRecordResponseDTO.class);
            List<WinningRecordResponseDTO> content = winningRecordResponsePageBean.getContent();
            if (CollectionUtil.isEmpty(content)){
                return contentList;
            }
            content.forEach(e->{
                if(StringUtils.isBlank(e.getNickName())){
                    e.setNickName("***");
                }else if(StringUtils.isNotBlank(e.getNickName()) && e.getNickName().length() >= 2) {
                    e.setNickName(e.getNickName().substring(0,2) + "***");
                }else{
                    e.setNickName(e.getNickName().charAt(0) + "***");
                }
            });
            return content;
        }else if(ObjectUtil.equal(promotionActivityDO.getPaTemplateId().toString(), StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId())){
            //奖品
            List<ActivityPartakeLogResponseDTO> assistSuccessRecordList = activityPartakeLogDAO.getAssistSuccessRecord(query.getActivityId(), query.getFissonStatus());
            if(assistSuccessRecordList.size() < 5){//小于5个不显示
                return contentList;
            }
            //批量获取参与者的奖品及奖品名称
            List<Long> partakeLogIds = assistSuccessRecordList.stream().map(SuperExtVO::getId).collect(Collectors.toList());
            Map<Long, List<ActivityFissionAssistResourceDO>> collect = activityFissionAssistResourceDAO.lambdaQuery().in(ActivityFissionAssistResourceDO::getPartakeLogId, partakeLogIds).list().stream().collect(Collectors.groupingBy(ActivityFissionAssistResourceDO::getPartakeLogId));
            Map<Long, PromotionHisResourceDO> resourceDOMap = promotionHisResourceDAO.findByActivityId(query.getActivityId()).stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));

            for (ActivityPartakeLogResponseDTO activityPartakeLogDO : assistSuccessRecordList){
                //一个助力活动可能有多个阶梯奖品
                List<ActivityFissionAssistResourceDO> activityFissionAssistResourceList = collect.get(activityPartakeLogDO.getId());
                if(CollectionUtil.isNotEmpty(activityFissionAssistResourceList)){
                    activityFissionAssistResourceList.forEach(item ->{
                        WinningRecordResponseDTO winningRecordResponseDTO = new WinningRecordResponseDTO();

                        //用户昵称
                        String nickName = activityPartakeLogDO.getNickName();
                        if(StringUtils.isBlank(nickName)){
                            winningRecordResponseDTO.setNickName("***");
                        }else if(StringUtils.isNotBlank(nickName) && nickName.length() >= 2) {
                            winningRecordResponseDTO.setNickName(nickName.substring(0,2) + "***");
                        }else{
                            winningRecordResponseDTO.setNickName(nickName.charAt(0) + "***");
                        }
                        PromotionHisResourceDO resourceDO = resourceDOMap.get(item.getResourceId());
                        if (resourceDO != null) {
                            winningRecordResponseDTO.setPrizeResult(resourceDO.getName());
                        }
                        contentList.add(winningRecordResponseDTO);
                    });
                }
            }
        }

        return contentList;
    }

    @Override
    public PageBean<ActivityPartakeLogResponseDTO> findCertificatesList(LuckyDrawPartakeLogQuery query) {
        PageBean<ActivityPartakeLogResponseDTO> page = activityPartakeLogDAO.findCertificatesList(query);
        List<ActivityPartakeLogResponseDTO> content = page.getContent();

        if(CollectionUtil.isNotEmpty(content)){
            //提取资源id
            Set<Long> resourceIds = content.stream().map(ActivityPartakeLogResponseDTO::getResourceId).collect(Collectors.toSet());

            Map<Long, PromotionHisResourceDO> resourceMap = Maps.newHashMap();

            QueryWrapper<PromotionHisResourceDO> queryWrapperResource = new QueryWrapper<>();
            queryWrapperResource.lambda().in(PromotionHisResourceDO::getId,resourceIds);
            List<PromotionHisResourceDO> resourceList = promotionHisResourceDAO.list(queryWrapperResource);
            if (CollectionUtil.isNotEmpty(resourceList)){
                resourceMap = resourceList.stream().collect(Collectors.toMap(PromotionHisResourceDO::getId, Function.identity()));
            }
            Map<Long, PromotionHisResourceDO> finalResourceMap = resourceMap;
            content.forEach(item ->{
                PromotionHisResourceDO promotionHisResourceDO = finalResourceMap.get(item.getResourceId());
                item.setResourceHisDetailResponseDTO(Objects.nonNull(promotionHisResourceDO) ? promotionHisResourceDO.clone(ResourceHisDetailResponseDTO.class):null);
            });
        }

        return page;
    }

    @Override
    public List<ProjectListResponseDTO> projectList(Long id) {
        List<ActivityParticipationDO> activityParticipationList = activityParticipationDAO.listByActivityId(id);
        if (CollectionUtil.isEmpty(activityParticipationList)){
            return Lists.newArrayList();
        }
        List<ActivityParticipationDTO> activityParticipations = ObjectCloneUtils.convertList(activityParticipationList, ActivityParticipationDTO.class);
        Map<String, List<ActivityParticipationDTO>> activityParticipationMap = activityParticipations.stream().collect(Collectors.groupingBy(ActivityParticipationDTO::getAreaName));
        List<ProjectListResponseDTO> projectListResponseList = Lists.newArrayList();
        activityParticipationMap.forEach((areaName,project)->{
            ProjectListResponseDTO projectListResponse = new ProjectListResponseDTO();
            projectListResponse.setAreaName(areaName);
            projectListResponse.setProjectList(project);
            projectListResponseList.add(projectListResponse);
        });
        return projectListResponseList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean receiveNow(ReceiveNowDTO dto) {
        if (dto.getResourceId() < 0){
            throw new ApplicationException("未中奖");
        }

        CustomerFeedbackQuery query = new CustomerFeedbackQuery();
        query.setActivityId(dto.getActivityId());
        //query.setUserId(dto.getUserId());
        query.setPhone(dto.getPhone());
        query.setResourceId(dto.getId());
        List<ActivityFormFeedbackDO> formFeedbackList = customerFeedbackDAO.pageList(query).getContent();
        if (CollectionUtil.isEmpty(formFeedbackList)){
            log.info("未填写信息登记，req={}",JsonUtil.bean2JsonString(query));
            throw new ApplicationException(CommonExceptionCode.NOT_ACTIVITY_FORM,"请填写信息登记");
        }

        //查询用户抽奖记录
        ActivityPartakeLogDO byId = activityPartakeLogDAO.getPartakeLogById(dto.getWinLotteryId());
        if (ObjectUtils.isEmpty(byId)){
            log.info("活动奖品信息丢失,中奖id={}",dto.getWinLotteryId());
            throw new ApplicationException("中奖信息不存在");
        }

        if (byId.getGetTime() != null){
            log.info("奖品已领取,中奖id={}",dto.getWinLotteryId());
            throw new ApplicationException("奖品已领取");
        }

        try{
            //扣减缓存库存---参与的时候扣除
            /*boolean flag = promotionActivityManager.decrRedisQty(dto.getId(),dto.getActivityId());
            if(!flag){
                throw new ApplicationException(CommonExceptionCode.INVALIDATION,"库存不足，领取失败");
            }*/

            //没有选择项目或者完成信息登记，领取不成功，奖品数量不扣减，消耗抽奖次数
            //补全生效抽奖信息
            ActivityPartakeLogDO activityPartakeLogDO = LuckyDrawConverter.partakeLogUpdateConverter(byId, dto);
            activityPartakeLogDAO.updateActivityPartakeLogById(activityPartakeLogDO);

            PromotionHisResourceDO hisResource = promotionHisResourceDAO.getById(byId.getResourceId());
            if(PromotionResourceTypeEnum.THIRD_PARTY.getId().equals(hisResource.getType())){
                PromotionActivityDO promotionActivity = promotionActivityDAO.getById(query.getActivityId());
                if (promotionActivity == null){
                    throw new ApplicationException("活动不存在");
                }
                if (ThirdCategoryEnum.PHONE_FEE.getId().equals(hisResource.getThirdCategory())){

                    //话费充值
                    incentiveService.rechargeMobileAsync(LuckyDrawConverter.rechargeMobileConverter(dto,promotionActivity.getName(),hisResource,byId.getCode()));
                }else if(ThirdCategoryEnum.CASH_BAG.getId().equals(hisResource.getThirdCategory())){

                    //发送红包激励
                    //取资源配置的项目信息
                    dto.setProjectId(hisResource.getProjectId());
                    dto.setProjectName(hisResource.getProjectName());
                    incentiveService.sendIncentiveAsync(LuckyDrawConverter.sendIncentiveConverter(dto,promotionActivity.getName(),hisResource,byId.getCode(),SendTemplateNewsRequestDTO.MP_FROM_ZYT));
                }
            }else{
                //保存核销信息
                Map<String, Object> projectInfo = new HashMap<>();
                if(StringUtil.isNotEmpty(activityPartakeLogDO.getProjectId()) && StringUtil.isNotEmpty(activityPartakeLogDO.getProjectName())){
                    projectInfo.put("projectId",activityPartakeLogDO.getProjectId());
                    projectInfo.put("projectName",activityPartakeLogDO.getProjectName());
                }
                ActivityOrderDO activityOrderDO = new ActivityOrderDO();
                activityOrderDO.setType(dto.getType());
                activityOrderDO.setActivityId(dto.getActivityId());
                activityOrderDO.setUserId(dto.getUserId());
                activityOrderDO.setId(0L);
                activityOrderDO.setExt(projectInfo);
                activityOrderDO.setPayTime(DateUtils.now());
                ActivityFormFeedbackDTO formFeedback = formFeedbackList.get(0).clone(ActivityFormFeedbackDTO.class);
                ActivityVerifyDO activityVerifyDO = ActivityInfoConverter.verifyConverter(activityOrderDO,formFeedback, hisResource, byId.getCode(), VerifyStatusEnum.NO_VERIFY.getId(), WhetherEnum.NO.getId());
                activityVerifyDAO.save(activityVerifyDO);
            }
            //查询或更新用户关联信息
            this.updateActivityUserRelated(dto.getActivityId(),dto.getPhone(),dto.getProjectId(),dto.getProjectName(),dto.getProjectCity());


            //发送短信通知
            promotionActivityService.templateSend(byId.getPhone(), byId.getUserName(), hisResource.getName(), byId.getCode(),huafaConstantConfig.WINNING_NOTICE_TEMPLATE_ID);

            //小程序订阅消息
            int[] timeByCalendar = DateUtils.getTimeByCalendar(byId.getCreatedTime());
            Map<String,Object> templateParam = Maps.newHashMap();
            templateParam.put("thing1",hisResource.getName());
            templateParam.put("time2"," "+timeByCalendar[0]+ "年" + timeByCalendar[1] + "月" + timeByCalendar[2] + "日 " + timeByCalendar[3] + ":"+ timeByCalendar[4]);
            templateParam.put("thing3","请到我的礼品栏目查看详情");
            promotionActivityService.minSubscribeNews(JSON.toJSONString(templateParam),byId.getUserId(),byId.getUserName(),huafaConstantConfig.MINI_WINNING_NOTICE_TEMPLATE_ID);

            //获取分布式锁并扣减数据库库存
            return Boolean.TRUE;
        }catch (Exception ex){
            log.error("领取失败，报错原因：{}",ex);
            //返还库存--参与的时候就处理
            //promotionActivityManager.incrRedisQty(dto.getId(),dto.getActivityId());
            throw new ApplicationException("系统异常，领取失败");
        }

    }

    private void updateActivityUserRelated(Long activityId, String phone, String projectId, String projectName, String projectCity) {
        ActivityUserRelatedDTO activityUserRelatedDTO = new ActivityUserRelatedDTO();
        activityUserRelatedDTO.setActivityId(activityId);
        //activityUserRelatedDTO.setUserId(userId);
        activityUserRelatedDTO.setPhone(phone);
        ActivityUserRelatedDTO activityUserRelated = activityUserRelatedDAO.getActivityUserRelated(activityUserRelatedDTO);
        if (activityUserRelated == null || activityUserRelated.getLimits().isEmpty()){
            throw new ApplicationException("用户信息异常");
        }
        Map<String, Object> limits = activityUserRelated.getLimits();
        if (limits.containsKey("projectId") && Objects.nonNull(limits.get("projectId"))){
            return;
        }
        limits.put("projectId",projectId);
        limits.put("projectName",projectName);
        limits.put("projectCity",projectCity);
        ActivityUserRelatedDO userRelatedDO = new ActivityUserRelatedDO();
        userRelatedDO.setId(activityUserRelated.getId());
        userRelatedDO.setLimits(limits);
        activityUserRelatedDAO.updateById(userRelatedDO);
    }

    @Override
    public Boolean initNumberDrawsDay() {
        List<ActivityUserRelatedDTO> initActivityUserList = activityUserRelatedDAO.getInitActivityUserList();
        if (CollectionUtil.isEmpty(initActivityUserList)){
            return null;
        }
        //提取活动id
        List<Long> activityIdList = initActivityUserList.stream().map(ActivityUserRelatedDTO::getActivityId).distinct().collect(Collectors.toList());

        //查询活动规则
        List<PromotionActivityLimitDO> promotionActivityLimitList = promotionActivityLimitDAO.selectByActivityIds(activityIdList);
        if (CollectionUtil.isEmpty(promotionActivityLimitList)){
            return null;
        }

        //转map
        Map<Long, PromotionActivityLimitDO> hisResourceMap = promotionActivityLimitList
                .stream()
                .filter(e -> e.getType().equals(PATemplateBaseEnum.LUCKYDRAW.getId()))
                .collect(Collectors.toMap(PromotionActivityLimitDO::getActivityId, his -> his));

        //新增数据
        List<ActivityUserRelatedDO> collect = initActivityUserList.stream().map(user -> {
            PromotionActivityLimitDO limit = hisResourceMap.get(user.getActivityId());
            ActivityUserRelatedDO related = user.clone(ActivityUserRelatedDO.class);
            if (limit != null){
                List<BaseActivityDTO> numberList = JSON.parseArray(limit.getLimits(), BaseActivityDTO.class);
                Integer dailyDrawNumber = numberList.stream()
                        .filter(number -> number.getId().equals(ActivityTemplateNumberEnum.DRAW_NUMBER.getId()))
                        .map(number -> Integer.parseInt(number.getValue())).findFirst().orElse(0);

                Integer dailyShareNumber = numberList.stream()
                        .filter(number -> number.getId().equals(ActivityTemplateNumberEnum.DAILY_SHARE_NUMBER.getId()))
                        .map(number -> Integer.parseInt(number.getValue())).findFirst().orElse(0);

                related.getLimits().put(ActivityTemplateNumberEnum.DRAW_NUMBER.getId(),dailyDrawNumber);
                related.getLimits().put(ActivityTemplateNumberEnum.SHARE_NUMBER.getId(),dailyShareNumber);
                related.setUpdatedTime(DateUtils.now());
            }else{
                related.setCreatedDate(DateUtils.toDateText(DateUtils.now()));
                related.setCreatedTime(DateUtils.now());
                related.setUpdatedTime(DateUtils.now());
            }
            return related;
        }).collect(Collectors.toList());

        return activityUserRelatedDAO.saveOrUpdateBatch(collect);
    }

    /**
     * 1、第一次进入活动领取奖品后，选择了项目（A）后，就固定项目，后续再次领取奖品时，不显示项目选择弹出框，默认归属项目（A）
     * 2、切换项目弹出框，以颜色标识当前项目A，且不允许A项目切换至A项目
     * 3、切换项目A至项目B之后，再领取的奖品，不展示项目选择弹出框，默认归属为项目B
     * 4、多个项目，没有超出切换次数，仅助力砍价抽奖可以切
     *
     * 2021、7、15 切换项目需求变更 3.0
     * 切换项目不改变任何记录，也不再有任何限制，只修改关联的项目信息
     * @param dto
     * @return
     */
    @Override
    public Boolean switchProject(SwitchProjectDTO dto) {
        //查询剩余抽奖次数
        LotteryRequestDTO query = new LotteryRequestDTO();
        query.setActivityId(dto.getActivityId());
        //query.setUserId(dto.getUserId());
        query.setPhone(dto.getPhone());
        ActivityUserRelatedDTO activityUserRelated = this.getActivityUserRelated(query);
        int switchNumber = (int) activityUserRelated.getLimits().get(ActivityTemplateNumberEnum.SWITCH_NUMBER.getId());
//        Boolean showSwitchCheck = this.isShowSwitchCheck(switchNumber, dto.getActivityId(),dto.getPhone());
//        if(!showSwitchCheck){
//            log.info("《抽奖》不符合切换项目条件");
//            throw new ApplicationException("不符合切换项目条件");
//        }
//
//        if (activityUserRelated.getLimits().containsKey("projectId") && Objects.nonNull(activityUserRelated.getLimits().get("projectId"))){
//            String projectId = activityUserRelated.getLimits().get("projectId").toString();
//            if (projectId.equals(dto.getProjectId())){
//                throw new ApplicationException("切换的项目不能与之前相同！");
//            }
//        }else{
//            log.info("《抽奖》项目id为空");
//            throw new ApplicationException("不符合切换项目条件");
//        }
//
//        //切换项目时清空未核销的礼品券抽奖明细
//        List<ActivityPartakeLogDO> partakeLogList = activityPartakeLogDAO.findUnGiftVouchers(dto.getActivityId(),dto.getPhone());
//        if (CollectionUtil.isNotEmpty(partakeLogList)) {
//            List<Long> LogIds = partakeLogList.stream().map(ActivityPartakeLogDO::getId).collect(Collectors.toList());
//            activityPartakeLogDAO.removeByIds(LogIds);
//        }
//        //查询未核销的礼品券
//        List<ActivityVerifyInResponseDTO> verifyInResponseList = activityVerifyDAO.findUnGiftVouchers(dto.getActivityId(),dto.getPhone());
//
//
//        if (CollectionUtil.isNotEmpty(verifyInResponseList)) {
//
//            List<Long> verifyIds = verifyInResponseList.stream().map(ActivityVerifyInResponseDTO::getId).collect(Collectors.toList());
//            //获得未核销的礼品券清空
//            activityVerifyDAO.removeGiftVoucher(verifyIds);
//        }

        //累加切换项目次数,清空抽奖次数，添加项目信息
        ActivityUserRelatedDO activityUserRelatedDO = new ActivityUserRelatedDO();
        activityUserRelatedDO.setId(activityUserRelated.getId());
        activityUserRelated.getLimits().put(ActivityTemplateNumberEnum.SWITCH_NUMBER.getId(),switchNumber + 1);
//        activityUserRelated.getLimits().put(ActivityTemplateNumberEnum.DRAW_NUMBER.getId(),getDailyDrawNumber(dto.getActivityId()));
        activityUserRelated.getLimits().put("projectId",dto.getProjectId());
        activityUserRelated.getLimits().put("projectName",dto.getProjectName());
        activityUserRelated.getLimits().put("projectCity",dto.getProjectCity());
        activityUserRelatedDO.setLimits(activityUserRelated.getLimits());
        return activityUserRelatedDAO.updateById(activityUserRelatedDO);
    }

    private Integer getDailyDrawNumber(Long activityId) {
        List<PromotionActivityLimitDO> promotionActivityLimits = promotionActivityLimitDAO.selectByActivityId(activityId);
        if (CollectionUtil.isEmpty(promotionActivityLimits)){
            throw new ApplicationException("活动规则未配置");
        }

        PromotionActivityLimitDO limitDO = promotionActivityLimits
                .stream().filter(e -> e.getType().equals(PATemplateBaseEnum.LUCKYDRAW.getId()))
                .findFirst()
                .orElse(null);
        if (limitDO == null){
            throw new ApplicationException("活动规则未配置");
        }

        //活动规则
        List<BaseActivityDTO> numberList = JSON.parseArray(limitDO.getLimits(), BaseActivityDTO.class);
        return LuckyDrawConverter.elLimitConverter(numberList, ActivityTemplateNumberEnum.DRAW_NUMBER.getId());
    }

    @Override
    public void exportUserLotteryRecordExcel(HttpServletResponse response, UserLotteryRecordQuery query) {
        query.setPage(-1);
        query.setSize(99999);
        List<ActivityPartakeLogResponseDTO> content = activityPartakeLogDAO.findPage(query).getContent();
        //添加用户登记明细
        this.getCustomerFeedbackList(content,query.getActivityId());

        ExcelExportUtil util = new ExcelExportUtil();
        util.setTitle("用户抽奖记录");
        String[] heardList = new String[]{"序号","昵称","手机号","抽奖时间","中奖结果","核销码","领取时间","发放方式","姓名","联系方式","所选房源","到访日期","到访时间段"};
        String[] headerKey = new String[]{"","nickName","phone","createdTime","prizeResult","code","getTime","grantWay","userName","phone2","houseName","visitDate","visitTime"};
        List<Map<String, String>> dataList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(content)){
            for (int i = 0; i < content.size(); i++) {
                Map<String, String> data = Maps.newHashMap();
                ActivityPartakeLogResponseDTO dto = content.get(i);
                data.put(headerKey[0], (i+1) + "");
                data.put(headerKey[1], dto.getNickName());
                data.put(headerKey[2], dto.getPhone());
                data.put(headerKey[3], DateUtils.format(dto.getCreatedTime(),DateUtils.DEFAULT_DATE_TIME_FORMAT));
                data.put(headerKey[4], dto.getPrizeResult());
                data.put(headerKey[5], dto.getCode());
                data.put(headerKey[6], dto.getGetTime());
                data.put(headerKey[7], dto.getGrantWay());
//                FeedbackVO feedback = dto.getFeedback();
//                data.put(headerKey[8], feedback!=null?feedback.getUserName():"");
//                data.put(headerKey[9], feedback!=null?feedback.getPhone():"");
//                data.put(headerKey[10],feedback!=null? feedback.getHouseName():"");
//                data.put(headerKey[11],feedback!=null? feedback.getVisitDate():"");
//                data.put(headerKey[12],feedback!=null? feedback.getVisitTime():"");
                dataList.add(data);
            }
            util.setHeardKey(headerKey);
            util.setHeardList(heardList);
            util.setData(dataList);
            util.setFileName("用户抽奖记录");
            util.setSheetName("用户抽奖记录");
            try {
                util.exportExport(response);
            } catch (Exception ex) {
                ex.printStackTrace();
            }

        }
    }

    /**
     * 奖品规则校验
     * @param activityId 活动id
     * @param phone     用户id
     * @param hisResourceList 奖品列表
     */
    @Override
    public void prizeConfig(Long activityId, String phone, List<PromotionHisResourceDO> hisResourceList) {

        RMap<String, String> totalCountRMap = redissonClient.getMap("pc_activityId_" + activityId);
        totalCountRMap.expire(3600 * 3 + new Random().nextInt(100), TimeUnit.SECONDS);
        if(totalCountRMap.isEmpty()) {
            //查询核记录
            List<ActivityVerifyCountDTO> verifyList = activityVerifyDAO.findCountByActivityIdUserId(activityId,null);
            if (CollectionUtil.isNotEmpty(verifyList)) {
                verifyList.forEach(verifyDTO -> {
                    totalCountRMap.put("dayCount_" + verifyDTO.getResourceId(),String.valueOf(verifyDTO.getDayCount()));
                });
            }
        }
        RMap<String, String> userCountRMap = redissonClient.getMap("pc_activityId_" + activityId + "_phone_" + phone);
        userCountRMap.expire(3600 * 3  + new Random().nextInt(100), TimeUnit.SECONDS);
        if(userCountRMap.isEmpty()) {
            List<ActivityVerifyCountDTO> partakeLogList = activityPartakeLogDAO.findCountByActivityIdUserId(activityId,phone);
            if (CollectionUtil.isNotEmpty(partakeLogList)) {
                partakeLogList.forEach(partakeLogDTO -> {
                    userCountRMap.put("userCount_" + partakeLogDTO.getResourceId(),String.valueOf(partakeLogDTO.getUserCount()));
                });
            }
        }

        //找到谢谢参与的奖项，把不符合的奖励概率加到它身上
        PromotionHisResourceDO thanksHisResourceDO = hisResourceList.stream().filter(his -> his.getResourceId() < 0).findFirst().get();

        hisResourceList.forEach(his->{
            if (his.getResourceId() < 0){
                return;
            }

            String dayCountStr = totalCountRMap.get("dayCount_" + his.getId());
            Integer dayCount = dayCountStr == null ? null : Integer.parseInt(dayCountStr);

            String userCountStr = userCountRMap.get("userCount_" + his.getId());
            Integer userCount = userCountStr == null ? null : Integer.parseInt(userCountStr);

            //奖品发放数量已达到奖品设置的资源发放数量 概率修改为0
            if (his.getRemainingQuantity() != null && 0 >= his.getRemainingQuantity()){
                log.info("《抽奖》奖品发放数量已达到奖品设置的资源发放数量 概率修改为0");
                thanksHisResourceDO.setOddsOfWinning(thanksHisResourceDO.getOddsOfWinning() + his.getOddsOfWinning());
                his.setOddsOfWinning(0d);
                return;
            }

            //抽取活动奖品当天发放数量    当天发放数量 >= 每天发放上限 概率修改为0
            if (dayCount != null && his.getLimitTimes() != null && dayCount >= his.getLimitTimes()){
                log.info("《抽奖》当天发放数量已达到奖品设置的每天发放上限 概率修改为0");
                thanksHisResourceDO.setOddsOfWinning(thanksHisResourceDO.getOddsOfWinning() + his.getOddsOfWinning());
                his.setOddsOfWinning(0d);
                return;
            }

            //抽取活动该用户参与奖品记录      配置限制用户中奖1次并且用户已经有抽奖记录 概率修改为0
            if (userCount != null && his.getLimitType() != null && his.getLimitType().equals(1) && userCount > 0) {
                log.info("《抽奖》配置限制用户中奖1次并且用户已经有抽奖记录 概率修改为0");
                thanksHisResourceDO.setOddsOfWinning(thanksHisResourceDO.getOddsOfWinning() + his.getOddsOfWinning());
                his.setOddsOfWinning(0d);
            }
        });
        log.info("《抽奖》参与抽奖的最终概率：{}",JsonUtil.bean2JsonString(hisResourceList));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object share(ActivityUserRelatedDTO activityUserRelated,List<PromotionActivityLimitDO> promotionActivityLimits) {

        PromotionActivityLimitDO limitDO = promotionActivityLimits
                .stream().filter(e -> e.getType().equals(PATemplateBaseEnum.LUCKYDRAW.getId()))
                .findFirst()
                .orElse(null);
        if (limitDO == null){
            throw new ApplicationException("活动规则未配置");
        }

        List<BaseActivityDTO> numberList = JSON.parseArray(limitDO.getLimits(), BaseActivityDTO.class);

        //分享可额外抽奖次数
        Integer shareDrawNumber = LuckyDrawConverter.elLimitConverter(numberList, ActivityTemplateNumberEnum.SHARE_DRAW_NUMBER.getId());

        //每日/每人抽奖次数  + 额外次数
        int dailyDrawNumber = (int)activityUserRelated.getLimits().get(ActivityTemplateNumberEnum.DRAW_NUMBER.getId());
        activityUserRelated.getLimits().put(ActivityTemplateNumberEnum.DRAW_NUMBER.getId(),dailyDrawNumber + shareDrawNumber);
        return Boolean.TRUE;
    }

    /**
     * 给某个用户增加抽奖次数
     * @param dto
     * @param drawNumber
     * @return
     */
    public Boolean addDrawNumber(AddDrawNumberDTO dto,Integer drawNumber) {
        PromotionActivityDetailDTO promotionActivity = promotionActivityService.getActivityById(dto.getActivityId());
        Assert.isTrue(promotionActivity.getStatus() != 3 ,"活动已结束");
        //查询活动关联奖品及奖品规则
        List<PromotionActivityLimitDO> promotionActivityLimits = promotionActivityLimitDAO.selectByActivityId(dto.getActivityId());
        Assert.notEmpty(promotionActivityLimits,"活动规则未配置");

        ActivityPartakeRequest query = dto.clone(ActivityPartakeRequest.class);
        ActivityUserRelatedDTO activityUserRelated = promotionActivityService.getActivityUserRelated(query, promotionActivityLimits, promotionActivity.getPaTemplateId().toString());
        if (activityUserRelated == null) {
            return null;
        }
        addDrawNumber(activityUserRelated,drawNumber);
        return Boolean.TRUE;
    }

    @Override
    public Integer addDrawNumber(AddDrawNumberDTO dto) {
        //如果有缓存时间内，则不处理
        RBucket<Object> bucket = redissonClient.getBucket(RedisConstants.LUCKY_DRAW_ADD_DRAW_NUMBER + dto.getActivityId() + dto.getPhone());
        if (bucket.get() != null){
            return null;
        }
        PromotionActivityDetailDTO promotionActivity = promotionActivityService.getActivityById(dto.getActivityId());
        if (promotionActivity.getStatus() == 3) {//已结束则返回
            return null;
        }
        //先查看是否有参与记录，没有则返回
        ActivityUserRelatedDTO activityUserRelated = activityUserRelatedDAO.getActivityUserRelated(new ActivityUserRelatedDTO(dto.getActivityId(),dto.getUserId(),dto.getPhone()));
        if(activityUserRelated == null){
            return null;
        }
        //看活动是否有配置增加抽奖次数的配置（注册经纪人，推荐，推荐且成交）,如果没有则返回
        List<BaseActivityDTO> luckyDrawLimit = promotionActivity.getLuckyDrawLimit();
        Integer perGetDrawNumberAgent = LuckyDrawConverter.elLimitConverter(luckyDrawLimit, PATemplateLuckyDrawEnum.PER_GET_DRAW_NUMBER.getId() + PATemplateLuckyDrawEnum.GET_DRAW_NUMBER_REG_AGENT.getId());
        Integer perGetDrawNumberReport = LuckyDrawConverter.elLimitConverter(luckyDrawLimit, PATemplateLuckyDrawEnum.PER_GET_DRAW_NUMBER.getId() + PATemplateLuckyDrawEnum.GET_DRAW_NUMBER_REPORT.getId());
        Integer perGetDrawNumberDeal = LuckyDrawConverter.elLimitConverter(luckyDrawLimit, PATemplateLuckyDrawEnum.PER_GET_DRAW_NUMBER.getId() + PATemplateLuckyDrawEnum.GET_DRAW_NUMBER_DEAL.getId());
        if(perGetDrawNumberAgent == 0 && perGetDrawNumberReport == 0 && perGetDrawNumberDeal == 0){
            return 0;
        }
        //根据手机号查询经纪人
        AgentInfoDTO agentInfo = huaFaHmacAuthUtil.getAgentInfo(dto.getPhone());
        if(agentInfo == null){
            return 0;
        }
        //获取到活动参与身份
        List<BaseActivityDTO> userLimit = promotionActivity.getUserLimit();
        List<String> agentTypes = userLimit.stream().map(BaseActivityDTO::getValue).collect(Collectors.toList());
        //经纪人身份判定：基础配置中参与对象-不限身份时，任意经纪人身份注册、推荐、推荐成交都可额外获得抽奖次数；基础配置中参与对象-全国业主/活动项目业主时，仅业主身份经纪人注册、推荐、推荐成交才可获得抽奖次数；基础配置中参与对象-经纪人时，仅选中的经纪人身份注册、推荐、推荐成交客户可获得额外抽奖次数
        String userType = agentTypes.get(0);
        if (Objects.equals(userType, MemberTypeEnum.ALL.getId())){
            agentTypes = MemberTypeEnum.getAgentList();
        } else if (Objects.equals(userType, MemberTypeEnum.OWNER.getId()) || Objects.equals(userType, MemberTypeEnum.PROJECT_OWNER.getId())){
            agentTypes = ListUtil.toList(MemberTypeEnum.HF_HOUSE_HOST.getId());
        }
        List<String> agentIds = new ArrayList<>();
        //是否符合新注册用户
        if (agentTypes.contains(agentInfo.getAgentTypeNo())
                && DateUtil.isIn(agentInfo.getCreateTime(), promotionActivity.getStartTime(), promotionActivity.getEndTime())) {
            agentIds.add(agentInfo.getAgentId());
        }
        //是否有符合的报备记录
        List<String> reportIds = new ArrayList<>();
        List<String> dealReportIds = new ArrayList<>();
        List<CustomerReportDTO> customers = huaFaHmacAuthUtil.getReportCustomers(agentInfo.getAgentId());
        if (CollUtil.isNotEmpty(customers)) {
            //查当前经纪人类型报备记录且看最新的报备时间是否在活动期
            for (CustomerReportDTO customer : customers) {
                if (agentTypes.contains(customer.getAgentTypeNo())
                        && agentInfo.getAgentId().equals(customer.getAgentId())
                        && DateUtil.isIn(customer.getReportDate(), promotionActivity.getStartTime(), promotionActivity.getEndTime())) {
                    reportIds.add(customer.getReportId());
                    //如果是成交状态80
                    if ("80".contains(customer.getReportStatus())) {
                        dealReportIds.add(customer.getReportId());
                    }
                }
            }
        }

        int addedDrawNumber = 0;
        addedDrawNumber += getAddedDrawNumber(luckyDrawLimit,activityUserRelated, PATemplateLuckyDrawEnum.GET_DRAW_NUMBER_REG_AGENT, agentIds);
        addedDrawNumber += getAddedDrawNumber(luckyDrawLimit,activityUserRelated, PATemplateLuckyDrawEnum.GET_DRAW_NUMBER_REPORT, reportIds);
        addedDrawNumber += getAddedDrawNumber(luckyDrawLimit,activityUserRelated, PATemplateLuckyDrawEnum.GET_DRAW_NUMBER_DEAL, dealReportIds);

        if (addedDrawNumber > 0) {
            addDrawNumber(activityUserRelated, addedDrawNumber);
        }
        bucket.set(addedDrawNumber, 90, TimeUnit.SECONDS);
        return addedDrawNumber;
    }

    private void addDrawNumber(ActivityUserRelatedDTO activityUserRelated, int addedDrawNumber) {
        ActivityUserRelatedDO activityUserRelatedDO = new ActivityUserRelatedDO();
        activityUserRelatedDO.setId(activityUserRelated.getId());
        int dailyDrawNumber = (int) activityUserRelated.getLimits().get(ActivityTemplateNumberEnum.DRAW_NUMBER.getId());
        activityUserRelated.getLimits().put(ActivityTemplateNumberEnum.DRAW_NUMBER.getId(),dailyDrawNumber + addedDrawNumber);
        activityUserRelatedDO.setLimits(activityUserRelated.getLimits());
        activityUserRelatedDAO.updateById(activityUserRelatedDO);
    }

    private int getAddedDrawNumber(List<BaseActivityDTO> luckyDrawLimit,ActivityUserRelatedDTO activityUserRelated, PATemplateLuckyDrawEnum type, List<String> idList) {
        int addedDrawNumber = 0;
        if (idList.isEmpty()) {
            return addedDrawNumber;
        }
        Integer perGetDrawNumberAgent = LuckyDrawConverter.elLimitConverter(luckyDrawLimit, PATemplateLuckyDrawEnum.PER_GET_DRAW_NUMBER.getId() + type.getId());
        Integer totalGetNumberAgent = LuckyDrawConverter.elLimitConverter(luckyDrawLimit, PATemplateLuckyDrawEnum.TOTAL_GET_NUMBER.getId() + type.getId());
        //如果可以增加且没有超过总次数
        List<String> items = (List<String>) activityUserRelated.getLimits().computeIfAbsent("addDrawItems" + type.getId(), k -> new ArrayList<>());
        for (String id : idList) {
            if (items.size() < totalGetNumberAgent && !items.contains(id)) {
                items.add(id);
                addedDrawNumber += perGetDrawNumberAgent;
            }
        }
        return addedDrawNumber;
    }


    @Override
    public List<PromotionHisResourceDO> getPromotionHisResource(Long activityId) {
        PromotionHisResourceQuery hisResourceQuery = new PromotionHisResourceQuery();
        hisResourceQuery.setActivityId(activityId);
        List<PromotionHisResourceDO> content = promotionHisResourceDAO.findPage(hisResourceQuery).getContent();
        if (CollectionUtil.isEmpty(content)){
            throw new ApplicationException("活动奖品未配置，请联系管理员！");
        }
        return content;
    }

    private void lotteryCheck(LotteryRequestDTO query) {
        PromotionActivityDO activity = promotionActivityDAO.getById(query.getActivityId());
        if (activity == null){
            throw new ApplicationException("活动不存在");
        }
        if (activity.getStatus().equals(Integer.parseInt(ActivityStatusEnum.UN_START.getId()))){
            throw new ApplicationException("活动尚未开始，请耐心等待！");
        }else if (!activity.getStatus().equals(Integer.parseInt(ActivityStatusEnum.IN_PROGRESS.getId()))){
            throw new ApplicationException("来晚啦，活动已结束！");
        }
    }

    /**
     * 查询活动用户信息
     * @param query
     * @return
     */
    @Override
    public ActivityUserRelatedDTO getActivityUserRelated(LotteryRequestDTO query) {
        ActivityUserRelatedDTO activityUserRelatedDTO = new ActivityUserRelatedDTO();
        activityUserRelatedDTO.setActivityId(query.getActivityId());
        //activityUserRelatedDTO.setUserId(query.getUserId());
        activityUserRelatedDTO.setPhone(query.getPhone());
        ActivityUserRelatedDTO activityUserRelated = activityUserRelatedDAO.getActivityUserRelated(activityUserRelatedDTO);
        if (activityUserRelated == null){
            List<PromotionActivityLimitDO> promotionActivityLimits = promotionActivityLimitDAO.selectByActivityId(query.getActivityId());
            if (CollectionUtil.isEmpty(promotionActivityLimits)){
                throw new ApplicationException("活动规则未配置");
            }
            ActivityPartakeRequest partakeRequest = query.clone(ActivityPartakeRequest.class);

            PromotionActivityLimitDO limitDO = promotionActivityLimits
                    .stream().filter(e -> e.getType().equals(PATemplateBaseEnum.LUCKYDRAW.getId()))
                    .findFirst()
                    .orElse(null);
            if (limitDO == null){
                throw new ApplicationException("活动规则未配置");
            }
            ActivityUserRelatedDO relatedDO = LuckyDrawConverter.activityUserConverter(partakeRequest, promotionActivityLimits,null);
            activityUserRelatedDAO.save(relatedDO);
            activityUserRelated = relatedDO.clone(ActivityUserRelatedDTO.class);
            log.info("活动用户信息补全信息：" + JsonUtil.bean2JsonString(activityUserRelated));
        }
        return activityUserRelated;
    }

    /**
     * 拼装用户登记明细和 礼品券发放方式
     * @param content
     * @param activityId
     */
    private void getCustomerFeedbackList(List<ActivityPartakeLogResponseDTO> content,Long activityId) {
        if (CollectionUtil.isEmpty(content)){
            return;
        }
        //提取用户id
        //Set<String> userIds = content.stream().map(ActivityPartakeLogResponseDTO::getUserId).collect(Collectors.toSet());

        Set<String> phoneList = content.stream().map(ActivityPartakeLogResponseDTO::getPhone).collect(Collectors.toSet());

        //提取资源id
        Set<Long> resourceIds = content.stream().map(ActivityPartakeLogResponseDTO::getResourceId).collect(Collectors.toSet());

        QueryWrapper<ActivityFormFeedbackDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ActivityFormFeedbackDO::getActivityId,activityId);
        //queryWrapper.lambda().in(ActivityFormFeedbackDO::getUserId,userIds);
        queryWrapper.lambda().in(ActivityFormFeedbackDO::getPhone,phoneList);
        queryWrapper.lambda().in(ActivityFormFeedbackDO::getDeleted,0);
        List<ActivityFormFeedbackDO> list = customerFeedbackDAO.list(queryWrapper);

        Map<String, ActivityFormFeedbackDO> formFeedbackMap = Maps.newHashMap();
        Map<Long, PromotionResourceDO> resourceMap = Maps.newHashMap();

        if (CollectionUtil.isNotEmpty(list)){
            formFeedbackMap = list.stream().collect(Collectors.toMap(ActivityFormFeedbackDO::getPhone, formFeedbackDO -> formFeedbackDO));
        }

        QueryWrapper<PromotionResourceDO> queryWrapperResource = new QueryWrapper<>();
        queryWrapperResource.lambda().in(PromotionResourceDO::getId,resourceIds);
        List<PromotionResourceDO> resourceList = promotionResourceDAO.list(queryWrapperResource);
        if (CollectionUtil.isNotEmpty(resourceList)){
            resourceMap = resourceList.stream().collect(Collectors.toMap(PromotionResourceDO::getId, PromotionResourceDO->PromotionResourceDO));
        }

        Map<Long, PromotionResourceDO> finalResourceMap = resourceMap;
        Map<String, ActivityFormFeedbackDO> finalFormFeedbackMap = formFeedbackMap;
        content.forEach(log->{
            ActivityFormFeedbackDO activityFormFeedbackDO = finalFormFeedbackMap.get(log.getPhone());
            PromotionResourceDO grantWay = finalResourceMap.get(log.getResourceId());
            if (activityFormFeedbackDO != null){
//                FeedbackVO feedbackVO = JSON.parseObject(JSON.toJSONString(activityFormFeedbackDO.getLimits()),FeedbackVO.class);
                //feedbackVO.setHouseName(grantWay==null?"":grantWay.getHouseName());
                log.setFeedback(activityFormFeedbackDO.clone(ActivityFormFeedbackDTO.class, CloneDirection.OPPOSITE));
            }
            if (grantWay != null){
                log.setGrantWay(ResourceGrantWayEnum.getValueById(grantWay.getGrantWay()));
            }
        });
    }

    /**
     * 查询活动规则
     * @param id 活动id
     * @return
     */
    private RuleConfigVO getActivityLimit(Long id) {

        //查询活动规则信息
        List<PromotionActivityLimitDO> promotionActivityLimit = promotionActivityLimitDAO.selectByActivityId(id);
        if (CollectionUtil.isEmpty(promotionActivityLimit)){
            return new RuleConfigVO();
        }
//        return JsonUtil.json2Bean(promotionActivityLimit.get(0).getExt(),RuleConfigVO.class);
        List<BaseActivityDTO> numberList = JSON.parseArray(promotionActivityLimit.get(0).getLimits(), BaseActivityDTO.class);
        if (CollectionUtil.isEmpty(numberList)){
            return null;
        }
        RuleConfigVO configVO = new RuleConfigVO();
        configVO.setDailyDrawNumber(LuckyDrawConverter.elLimitConverter(numberList,ActivityTemplateNumberEnum.DRAW_NUMBER.getId()));
        configVO.setJoinType(LuckyDrawConverter.elLimitConverter(numberList,ActivityTemplateNumberEnum.JOIN_TYPE.getId()));
        configVO.setShareDrawNumber(LuckyDrawConverter.elLimitConverter(numberList,ActivityTemplateNumberEnum.SHARE_DRAW_NUMBER.getId()));
        configVO.setDailyShareNumber(LuckyDrawConverter.elLimitConverter(numberList,ActivityTemplateNumberEnum.DAILY_SHARE_NUMBER.getId()));
        String feedbackInfo = LuckyDrawConverter.elLimitConverterToStr(numberList, ActivityTemplateNumberEnum.FEEDBACK_INFO.getId());
        if (StringUtils.isNotEmpty(feedbackInfo)) {
            configVO.setFeedbackInfo(JSON.parseArray(feedbackInfo, EnrollmentInfoVO.class));
        }

        //--------------------砍价活动相关规则-------------------
        configVO.setHelperNumber(LuckyDrawConverter.elLimitConverter(numberList,ActivityTemplateNumberEnum.HELPER_NUMBER.getId()));
        configVO.setLaunchTimes(LuckyDrawConverter.elLimitConverter(numberList,ActivityTemplateNumberEnum.LAUNCH_TIMES.getId()));
        configVO.setLowestPrice(LuckyDrawConverter.elLimitConverterToBigDecimal(numberList,ActivityTemplateNumberEnum.LOWEST_PRICE.getId()));
        configVO.setValidPeriod(LuckyDrawConverter.elLimitConverter(numberList,ActivityTemplateNumberEnum.VALID_PERIOD.getId()));
        configVO.setHelpTimes(LuckyDrawConverter.elLimitConverter(numberList,ActivityTemplateNumberEnum.HELP_TIMES.getId()));
        return configVO;
    }
}
