package com.deepexi.dxp.marketing.domain.marketing.dto;

import com.deepexi.dxp.marketing.common.base.dto.SuperExtDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/12
 */
@Data
public class MarketingAutoDTO extends SuperExtDTO {

    /**
     * 自动任务名称
     */
    private String name;


    /**
     * 任务编码
     */
    private String code;


    /**
     * 任务状态(1:草稿，2:未开始，3:运行中，4:暂停，5:结束)
     */
    private Integer status;


    /**
     * 执行时机
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date executeTime;


    /**
     * 行动模版ID
     */
    private Long actionTemplateId;


    /**
     * 描述
     */
    private String description;

    /**
     * 0 立即执行 1 延后执行
     */
    private Integer executeNow;

    /**
     * 条件触发后时
     */
    private Long executeAfter;


    /**
     * 执行时间单位 0 秒 1 分 2时 3日 4月 5年
     */
    private Integer executeUnit;


}
