package com.deepexi.dxp.marketing.domain.marketing.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2024/12/12 11:46
 */
@Data
@NoArgsConstructor
public class CancelOrderRequest {
    private Long orderId;
    @NotBlank
    @ApiModelProperty(value = "订单编号")
    private String code;
    @NotBlank
    @ApiModelProperty(value = "取消原因")
    private String reason;
    @ApiModelProperty(value = "是否管理员操作")
    private boolean isAdmin = false;

    public CancelOrderRequest(String code) {
        this.code = code;
    }
}
