package com.deepexi.dxp.marketing.manager.promotion.impl;

import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityCommodityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityConfigDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.TemplateLimitDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityParamsDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityResponseParamsDTO;
import com.deepexi.dxp.marketing.manager.promotion.Calculate;
import com.deepexi.dxp.marketing.manager.promotion.CalculateResult;
import com.deepexi.dxp.middle.promotion.util.Arith;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> xinjian.yao
 * @date 2019/11/26 11:35
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public abstract class BaseStrategy implements Calculate {

    /**
     * TemplateLimitDTO 活动模板
     * Activity 活动配置的值
     * ActivityParamsDTO 活动当前的信息值
     */
    private TemplateLimitDTO templateLimitDTO;
    private ActivityConfigDTO activityConfigDTO;
    private ActivityParamsDTO params;

    private ActivityResponseParamsDTO activityResponseParamsDTO;

    /**
     * 对商品原始价格进行汇总
     */
    public static final Function<ActivityCommodityDTO, Double> DETAIL_PRICE_ALL = val -> Arith.mul(val.getDetailPrice().doubleValue(), Double.valueOf(val.getSkuAmount().toString()));
    /**
     * 对商品总优惠价格汇总
     */
    public static final Function<ActivityCommodityDTO, Double> DISCOUNT_PRICE_ALL = val -> val.getDiscountsPriceAll().doubleValue();
    /**
     * 对商品优惠了多少钱 进行汇总
     */
    public static final Function<ActivityCommodityDTO, Double> SUBTRACT_PRICE_ALL = val -> val.getSubtractPriceAll().doubleValue();

    @Override
    public CalculateResult calculateAndReturn() {
        CalculateResult result = new CalculateResult();
        result.setResult(calculate());
        return result;
    }

    /**
     * @param discountsCommodityList 商品list
     * @return 返回商品的具有统计
     */
    protected double getAllPrice(List<ActivityCommodityDTO> discountsCommodityList, Function<ActivityCommodityDTO, Double> mapper) {

        return discountsCommodityList
                .stream()
                .map(mapper)
                .reduce(0.00, Double::sum);

    }

    /**
     * 处理一个n元n件活动下的统计信息
     *
     * @param activityResponseParamsDTO 活动的返回结果
     */
    protected void collectDiscounts(ActivityResponseParamsDTO activityResponseParamsDTO) {
        List<ActivityCommodityDTO> activityCommodityDTOList = activityResponseParamsDTO.getActivityCommodityDTOList();
        // 总原始价格
        double allDetailPrice = getAllPrice(activityCommodityDTOList, DETAIL_PRICE_ALL);
        // 总优惠后价格
        double allDiscountsPrice = getAllPrice(activityCommodityDTOList, DISCOUNT_PRICE_ALL);
        // 总优惠金额
        double allSubtractPrice = getAllPrice(activityCommodityDTOList, SUBTRACT_PRICE_ALL);

        activityResponseParamsDTO.setDetailPrice(BigDecimal.valueOf(allDetailPrice));
        activityResponseParamsDTO.setDiscountsPrice(BigDecimal.valueOf(allDiscountsPrice));
        activityResponseParamsDTO.setSubtractPrice(BigDecimal.valueOf(allSubtractPrice));

    }

    /**
     * 优惠金额分摊
     *
     * @param discountsCommodityList 商品List
     * @param discoutPrice           优惠金额分摊
     */
    protected void amountOfShareCalculate(List<ActivityCommodityDTO> discountsCommodityList, Double discoutPrice) {


        // 获取所有商品的总价 进行比例的计算需要用到
        double allPrice = getAllPrice(discountsCommodityList, DETAIL_PRICE_ALL);
        //先排序取金额最大的第一位，避免最终剩余分摊额比第一个商品原价还大而导致出现负数
        discountsCommodityList = discountsCommodityList.stream().sorted(Comparator.comparing(ActivityCommodityDTO::getDetailPrice).reversed()).collect(Collectors.toList());
        // 第一商品 有总优惠减去其他的商品的优惠
        ActivityCommodityDTO firstCommodity = discountsCommodityList.get(0);
        // 积累的优惠金额
        double accumulatePrice = 0;

        // 从第一个开始 计算优惠比例
        for (int i = 1; i < discountsCommodityList.size(); i++) {
            ActivityCommodityDTO temp = discountsCommodityList.get(i);
            // 总优惠,除之后保留2位小数，0就是0
            BigDecimal commodityDiscounts = temp.getDetailPrice()
                    .multiply(new BigDecimal(temp.getSkuAmount().toString()))
                    .setScale(2, BigDecimal.ROUND_FLOOR)
                    .divide(new BigDecimal(Double.toString(allPrice)), 2, BigDecimal.ROUND_DOWN)
                    .multiply(new BigDecimal(discoutPrice.toString())
                            .setScale(2, BigDecimal.ROUND_FLOOR)
                    )
                    .setScale(2, BigDecimal.ROUND_FLOOR);
            temp.setDiscountsPriceAll(temp
                    .getDetailPrice()
                    .multiply(new BigDecimal(temp.getSkuAmount().toString()))
                    .subtract(commodityDiscounts)
                    .setScale(2, BigDecimal.ROUND_FLOOR)
            );
            temp.setSubtractPriceAll(commodityDiscounts);
            // 累计优惠了多少钱
            accumulatePrice = Arith.add(accumulatePrice, commodityDiscounts.doubleValue());
        }

        // 处理第一个优惠 第一个优惠可以获取多少钱
        Double firstDiscount = Arith.sub(discoutPrice, accumulatePrice);
        // 优惠后总价格
        firstCommodity.setDiscountsPriceAll(firstCommodity.getDetailPrice()
                .multiply(new BigDecimal(firstCommodity.getSkuAmount().toString()))
                .subtract(new BigDecimal(firstDiscount.toString()))
                .setScale(2, BigDecimal.ROUND_FLOOR)
        );
        // 优惠了多少钱
        firstCommodity.setSubtractPriceAll(new BigDecimal(firstDiscount.toString())
                .setScale(2, BigDecimal.ROUND_FLOOR)
        );


    }

}
