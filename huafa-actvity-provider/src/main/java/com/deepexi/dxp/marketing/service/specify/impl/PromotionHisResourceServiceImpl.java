package com.deepexi.dxp.marketing.service.specify.impl;

import com.deepexi.dxp.marketing.domain.marketing.request.specify.HisResourceRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.PromotionHisResourceDTO;
import com.deepexi.dxp.marketing.domain.promotion.query.specify.PromotionHisResourceQuery;
import com.deepexi.dxp.marketing.service.specify.PromotionHisResourceService;
import com.deepexi.dxp.middle.promotion.dao.specify.PromotionHisResourceDAO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.PromotionHisResourceDO;
import com.deepexi.util.pageHelper.PageBean;
import com.deepexi.util.pojo.CloneDirection;
import com.deepexi.util.pojo.ObjectCloneUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class PromotionHisResourceServiceImpl implements PromotionHisResourceService {

    @Autowired
    private PromotionHisResourceDAO promotionHisResourceDAO;

    @Override
    public PageBean<PromotionHisResourceDTO> findPage(HisResourceRequestDTO query) {
        PromotionHisResourceQuery promotionHisResourceQuery = query.clone(PromotionHisResourceQuery.class);
        PageBean<PromotionHisResourceDO> page = promotionHisResourceDAO.findPage(promotionHisResourceQuery);
        return ObjectCloneUtils.convertPageBean(page,PromotionHisResourceDTO.class, CloneDirection.OPPOSITE);
    }
}
