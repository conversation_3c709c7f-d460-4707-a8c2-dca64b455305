//package com.deepexi.dxp.marketing.engine.processor;
//
//
//import com.deepexi.dxp.marketing.engine.MarketingTaskCallback;
//import com.deepexi.dxp.marketing.engine.event.MarketingTaskEvent;
//import com.deepexi.dxp.marketing.engine.listener.MarketingTaskListener;
//import com.deepexi.dxp.marketing.engine.task.MetaDataTask;
//import lombok.extern.slf4j.Slf4j;
//
///**
// * 营销任务继续执行处理事件源
// *
// * <AUTHOR>
// * @date 2020/3/18 18:55
// */
//@Slf4j
//public class MarketingTaskGoOnProcessor extends AbstractTaskProcessor {
//
//    public MarketingTaskGoOnProcessor(MetaDataTask metaDataTask) {
//        super(metaDataTask);
//    }
//
//    public MarketingTaskGoOnProcessor(MetaDataTask metaDataTask, MarketingTaskCallback callback) {
//        super(metaDataTask, callback);
//    }
//
//    @Override
//    protected void doProcess(MarketingTaskListener listener, MarketingTaskEvent event) {
//        try {
//            log.info("task-goon-start-营销任务继续执行开始");
//            listener.goOn(event);
//        } finally {
//            event.semaphoreDown();
//            log.info("task-goon-end-营销任务继续执行结束");
//        }
//    }
//
//    @Override
//    public void callBack() {
//
//    }
//}
