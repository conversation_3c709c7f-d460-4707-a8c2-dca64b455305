package com.deepexi.dxp.marketing.domain.marketing.dto;

import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @Class: FlowCanvasProcinstDTO
 * @Description:
 * @Author: zht
 * @Date: 2020/7/20
 */
@Data
public class FlowCanvasProcinstDTO extends SuperDTO {
    /**
     * 流程画布定义id
     */
    private Long fcDefId;

    /**
     * 流程画布定义CODE,用于根据版本区分的同一个画布
     */
    private String fcDefCode;

    /**
     * flink job id
     */
    private String jobId;

    /**
     * 查询状态id
     */
    private String triggerId;

    /**
     * 备份路径
     */
    private String savePointPath;

    /**
     * 执行开始时间 (单次和触发的存定时执行时间，周期重复的存定义的开始时间)
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date excTime;

    /**
     * 任务状态(1:运行中，2:失败，3:已暂停，4:已结束)
     */
    private Integer status;

    /**
     * 实例圈选的总人数
     */
    private Integer lassoUserNums;
}
