package com.deepexi.dxp.marketing.service.promotion;


import com.deepexi.dxp.marketing.domain.promotion.request.activity.OrderLockRequest;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.PreSaleTailMoneyVO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.PromotionActivityLoggerListDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.StatisticsUserActivityCountVO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.OrderLockVO;
import com.deepexi.dxp.marketing.domain.promotion.query.activity.PreSaleTailMoneyQuery;
import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityLoggerQuery;
import com.deepexi.dxp.marketing.domain.promotion.query.activity.StatisticsQuery;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.PreSalesActivityLockPostRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityLoggerCreatePostRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityLoggerUpdatePostRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.ReturnSalesRequest;
import com.deepexi.dxp.middle.promotion.domain.dto.ActivityLogDTO;
import com.deepexi.dxp.middle.promotion.domain.query.PromotionActivityLogCountQuery;
import com.deepexi.util.pageHelper.PageBean;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> xinjian.yao
 * @date 2019/11/21 16:16
 */
@Service
public interface PromotionActivityLoggerMiddleService {


    /**
     * 创建活动日志
     *
     * @param dto 入参dto
     * @return 出参dto
     */
    Boolean create(PromotionActivityLoggerCreatePostRequest dto);

    /**
     * 根据id 修改id 修好
     *
     * @param id  id
     * @param dto dto
     * @return
     */
    Boolean update(Long id, PromotionActivityLoggerUpdatePostRequest dto);

    /**
     * 根据活动id 删除日志
     *
     * @param activityIds
     * @return
     */
    Boolean deleted(List<String> activityIds);

    /**
     * 根据id删除
     *
     * @param ids
     * @return
     */

    Boolean deletedByIds(List<Long> ids);

    /**
     * 根据dto 开发
     *
     * @param dto 入参dto
     * @return DO List
     */
    List<PromotionActivityLoggerListDTO> select(PromotionActivityLoggerQuery dto);

    /**
     * 批量创建活动日志
     *
     * @param dto dto
     * @return dto
     */
    OrderLockVO createBatch(OrderLockRequest dto);

    /**
     * 批量根据订单信息修改活动日志
     *
     * @param dtoList dtoList
     * @return 是否成功
     */
    Boolean updateBatchByOrderId(List<PromotionActivityLoggerUpdatePostRequest> dtoList);

    /**
     * 退货
     *
     * @param returnSalesRequest
     * @return
     */
    Boolean returnSales(List<ReturnSalesRequest> returnSalesRequest);

    /**
     * 分页查询接口
     *
     * @param query 入参
     * @return 出参
     */
    PageBean<PromotionActivityLoggerListDTO> findPage(PromotionActivityLoggerQuery query);


    /**
     * 根据订单id 租户id appId  修改关于预售活动的一些信息
     *
     * @param preSalesList
     * @return
     */
    Boolean updateByOrder(List<PreSalesActivityLockPostRequest> preSalesList);

    /**
     * 统计用户参与活动类型的次数
     *
     * @param query
     * @return
     */
    StatisticsUserActivityCountVO statisticsUserActivityCount(StatisticsQuery query);

    /**
     * 尾款时间到了 退还定金接口
     *
     * @param query
     * @return
     */
    PreSaleTailMoneyVO retornPreSalesTailMoney(PreSaleTailMoneyQuery query);

    /**
     * 查询日志次数记录
     * @param query
     * @return
     */
    ActivityLogDTO getLogNumCount(PromotionActivityLogCountQuery query);
}





