package com.deepexi.dxp.marketing.domain.marketing.vo;

import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 主动营销任务VO
 * @Author: HuangBo.
 * @Date: 2020/3/16 18:37
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Api(value = "营销任务VO")
public class MarketingActiveVO extends SuperVO {


    /**
     * 主动营销任务名称
     */
    @ApiModelProperty(value = "主动营销任务名称")
    private String name;

    /**
     * 是否是实验任务(0:否 1:是)
     */
    @ApiModelProperty(value = "是否是实验任务(0:否 1:是)")
    private boolean experiment;

    /**
     * 任务目标名称
     */
    @ApiModelProperty(value = "主动营销任务编码")
    private String code;

    /**
     * 分组标识，用于实验室创建多个对照分组资源时
     */
    @ApiModelProperty(value = "分组标识，用于实验室创建多个对照分组资源时")
    private String groupCode;

    /**
     * 任务状态(1:草稿，2:未开始，3:运行中，4:暂停，5:结束)
     */
    @ApiModelProperty(value = "任务状态(1:草稿，2:未开始，3:运行中，4:暂停，5:已完成，6:已终止)")
    private int status;

    @ApiModelProperty(value = "任务触发时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date executeTime;

    @ApiModelProperty(value = "受众预估人数")
    private int estimateMemberNums;

    @ApiModelProperty(value = "预估成交总额")
    private Long estimateTurnoverAmount;

    @ApiModelProperty(value = "实际圈取人数")
    private int estimateToucheMemberNums;

    /**
     * 任务发送时根据标签客群会员圈取的人数
     */
    @ApiModelProperty(value = "任务发送时根据标签客群会员圈取的人数")
    private Integer tagsMemberNums;

    @ApiModelProperty(value = "成功触达人数")
    private int successTouchNums;

    @ApiModelProperty(value = "预估订单提交量下限值")
    private int estimateOrderSubmittedNumsLower;

    @ApiModelProperty(value = "预估订单提交量上限值")
    private int estimateOrderSubmittedNumsUp;

    @ApiModelProperty(value = "预估订单成交量下限值")
    private int estimateOrderTransactionNumsLower;

    @ApiModelProperty(value = "预估订单成交量上限值")
    private int estimateOrderTransactionNumsUp;

    @ApiModelProperty(value = "预估核销率下限值")
    private double estimateWriteOffRateLower;

    @ApiModelProperty(value = "预估核销率上限值")
    private double estimateWriteOffRateUp;

    @ApiModelProperty(value = "预估投放成本下限值")
    private Long estimateResourceCostLower;

    @ApiModelProperty(value = "预估投放成本上限值")
    private Long estimateResourceCostUp;

    @ApiModelProperty(value = "预估客单价下限值")
    private int estimateMemberUnitPriceLower;

    @ApiModelProperty(value = "预估客单价上限值")
    private int estimateMemberUnitPriceUp;

    @ApiModelProperty(value = "任务目的")
    private String purposeName;

    @ApiModelProperty(value = "任务目的类型(1:营收；2:商品；3:用户)")
    private Integer purposeType;

    @ApiModelProperty(value = "资源分类")
    private String resourceTypeName;

    @ApiModelProperty(value = "资源类别")
    private String resourceCategoryName;

    @ApiModelProperty(value = "资源名称")
    private String resourceName;

    @ApiModelProperty(value = "触达渠道")
    private String sendChannel;

    @ApiModelProperty(value = "触达方式")
    private String sendWay;

    /**
     * 营销任务开始时间
     */
    @ApiModelProperty(value = "营销任务开始时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date taskStartTime;

    /**
     * 营销任务结束时间
     */
    @ApiModelProperty(value = "营销任务结束时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date taskEndTime;

    /**
     * 营销任务资源有效期开始时间
     */
    @ApiModelProperty(value = "营销任务资源有效期开始时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date validityStartTime;

    /**
     * 营销任务资源有效期结束时间
     */
    @ApiModelProperty(value = "营销任务资源有效期结束时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date validityEndTime;

    /**
     * 自动/主动任务映射DTO
     */
    private MarketingTaskResourceMappingVO taskMappingDTO;
    /**
     * 营销任务目的
     */
    private MarketingActivePurposeVO purpose;

    /**
     * 营销任务受众目标群体
     */
    private MarketingActiveTargetVO targetMembers;

    /**
     * 营销任务资源
     */
    private List<MarketingActiveResourcesVO> resourcesList;

    /**
     * 营销任务触达策略
     */
    private List<MarketingActiveTouchWayVO> touchWayList;

    /**
     * 营销任务触发时间
     */
    private MarketingActiveTouchTimeVO touchTime;

    /**
     * 营销任务安全过滤
     */
    private List<MarketingTaskSecurityFilterVO> securityFilterList;

    @ApiModelProperty(value = "营销任务总数")
    private int taskTotalNums;

    @ApiModelProperty(value = "未开始任务数量")
    private int taskNotStartNums;

    @ApiModelProperty(value = "草稿任务数量")
    private int taskDraftNums;

    @ApiModelProperty(value = "暂停中任务数量")
    private int taskSuspendNums;

    @ApiModelProperty(value = "执行中任务数量")
    private int taskOperationNums;

    @ApiModelProperty(value = "已结束任务数量")
    private int taskFinishedNums;

    /**
     * 主动营销对应的映射主表(marketing_task_resource_mapping)ID
     */
    @ApiModelProperty(value = "主动营销对应的映射主表ID")
    private Long taskMappingId;

    /**
     * 指标组ID
     */
    @ApiModelProperty(value = "指标组ID")
    private Long kpiGroupId;

}
