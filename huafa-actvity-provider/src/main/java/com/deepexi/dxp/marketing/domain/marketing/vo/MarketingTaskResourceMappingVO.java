package com.deepexi.dxp.marketing.domain.marketing.vo;

import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 任务映射表
 * <AUTHOR>
 * @version 1.0
 * @date 2020-04-22 14:46
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MarketingTaskResourceMappingVO extends SuperVO {
    /**
     * 任务id
     */
    @ApiModelProperty(value = "主动或自动任务ID")
    private Long taskId;

    /**
     * 任务类型1:主动 2：自动
     */
    @ApiModelProperty(value = "任务类型1:主动 2：自动")
    private Integer taskType;

    /**
     * 资源id
     */
    @ApiModelProperty(value = "资源id")
    private Long resourceId;

    /**
     * 资源渠道
     */
    @ApiModelProperty(value = "资源渠道")
    private String resourceChannel;

    /**
     * 短链接
     */
    @ApiModelProperty(value = "短链接")
    private String shortURL;
}
