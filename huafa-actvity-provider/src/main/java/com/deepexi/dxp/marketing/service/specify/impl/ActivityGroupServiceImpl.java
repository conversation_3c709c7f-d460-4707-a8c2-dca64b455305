package com.deepexi.dxp.marketing.service.specify.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.deepexi.dxp.marketing.constant.TradeConstants;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityAnalysisQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityGroupQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityGroupRelatedQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.EventsReportQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.*;
import com.deepexi.dxp.marketing.domain.marketing.response.*;
import com.deepexi.dxp.marketing.domain.promotion.query.specify.PromotionHisResourceQuery;
import com.deepexi.dxp.marketing.enums.activity.strategy.StrategyGroupEnum;
import com.deepexi.dxp.marketing.enums.specify.*;
import com.deepexi.dxp.marketing.extension.HuafaRuntimeEnv;
import com.deepexi.dxp.marketing.service.specify.*;
import com.deepexi.dxp.middle.marketing.common.base.SuperEntity;
import com.deepexi.dxp.middle.promotion.converter.specify.ActivityGroupConverter;
import com.deepexi.dxp.middle.promotion.dao.PromotionActivityDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.*;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.*;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.DateUtils;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import com.deepexi.util.pojo.ObjectCloneUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ActivityGroupServiceImpl implements ActivityGroupService {

    @Resource
    private ActivityGroupDAO activityGroupDAO;

    @Resource
    private ActivityGroupRelatedDAO activityGroupRelatedDAO;

    @Resource
    private PromotionActivityDAO promotionActivityDAO;

    @Resource
    private ActivityPageDAO activityPageDAO;

    @Resource
    private ActivityPageShareDAO activityPageShareDAO;

    @Resource
    private ActivityVerifyDAO activityVerifyDAO;

    @Resource
    private SensorsService sensorsService;

    @Autowired
    private ActivityPromotionChannelService activityPromotionChannelService;

    @Autowired
    private PromotionHisResourceDAO promotionHisResourceDAO;

    @Autowired
    private MiniProgramService miniProgramService;

    @Autowired
    private PromotionActivityService promotionActivityService;

    @Override
    @Transactional(rollbackFor = ApplicationException.class)
    public Boolean save(ActivityGroupRequestDTO requestDTO) {

        this.check(requestDTO.getName());
        //保存活动组信息
        ActivityGroupDO activityGroupDO = ActivityGroupConverter.groupConverter(requestDTO);

        activityGroupDAO.save(activityGroupDO);

        //保存活动组关联信息
        return activityGroupRelatedDAO.saveBatch(ActivityGroupConverter.relatedConverter(requestDTO,activityGroupDO.getId()));
    }

    @Override
    @Transactional(rollbackFor = ApplicationException.class)
    public Boolean update(ActivityGroupRequestDTO requestDTO) {
        if (requestDTO.getId() == null){
            throw new ApplicationException("id不能为空");
        }

        ActivityGroupResponseDTO byId = activityGroupDAO.findById(requestDTO.getId());
        if (byId == null){
            throw new ApplicationException("活动分组不存在！");
        }

        if (!requestDTO.getName().equals(byId.getName())){
            this.check(requestDTO.getName());
        }

        //修改活动组信息
        activityGroupDAO.updateById(ActivityGroupConverter.groupConverter(requestDTO));

        UpdateWrapper<ActivityGroupRelatedDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(ActivityGroupRelatedDO::getGroupId,requestDTO.getId());
        activityGroupRelatedDAO.remove(updateWrapper);

        return activityGroupRelatedDAO.saveBatch(ActivityGroupConverter.relatedConverter(requestDTO,requestDTO.getId()));
    }

    @Override
    public PageBean<ActivityGroupResponseDTO> pageList(ActivityGroupQuery query) {
        return activityGroupDAO.pageList(query);
    }

    @Override
    public ActivityGroupResponseDTO detail(Long id) {
        ActivityGroupDO byId = activityGroupDAO.getById(id);
        if (byId == null){
            return null;
        }
        ActivityGroupResponseDTO groupResponse = byId.clone(ActivityGroupResponseDTO.class);

        if (byId.getType().equals(ActivityGroupEnum.TOPIC.getId())){
            groupResponse.setActivityPageVO(activityPageDAO.getByActivity(id, ActivityTypeEnum.TOPIC.getId()).clone(ActivityPageVO.class));
            groupResponse.setActivityShareVO(activityPageShareDAO.getByActivity(id, ActivityTypeEnum.TOPIC.getId()).clone(ActivityPageShareVO.class));
        }
        return groupResponse;
    }

    @Override
    public PageBean<ActivityGroupRelatedResponseDTO> activityList(ActivityGroupQuery query) {
        ActivityGroupRelatedQuery relatedQuery = new ActivityGroupRelatedQuery();
        relatedQuery.setGroupId(query.getId());
        relatedQuery.setPage(query.getPage());
        relatedQuery.setSize(query.getSize());
        PageBean<ActivityGroupRelatedDO> activityGroupRelatedPageBean = activityGroupRelatedDAO.pageList(relatedQuery);
        return this.getActivityGroupRelated(activityGroupRelatedPageBean);
    }

    @Override
    public Boolean delete(Long id) {
        //删除活动组前 校验是否关联活动专题
        Integer count= activityGroupRelatedDAO.removeGroupCheck(id);
        if (count > 0){
            throw new ApplicationException("已关联活动专题的活动组不可删除！");
        }
        return activityGroupDAO.removeById(id);
    }

    @Override
    public PageBean<ActivityAndActivityGroupResponseDTO> activityAndActivityGroupList(ActivityGroupQuery query) {
        query.setUserId(HuafaRuntimeEnv.getUserId());
        List<Long> activityIds = promotionActivityService.getActivityIdsByUserId(query.getUserId());
        query.setActivityIdList(activityIds);
        PageBean<ActivityAndActivityGroupResponseDTO> activityAndActivityGroupList = activityGroupDAO.getActivityAndActivityGroupList(query);
        Optional.ofNullable(activityAndActivityGroupList.getContent()).ifPresent(list->{
            list.forEach(e->{
                e.setTId(e.getId() +"+"+ e.getType());
            });
        });

        return activityAndActivityGroupList;
    }

    @Override
    @Transactional(rollbackFor = ApplicationException.class)
    public Boolean saveTopic(ActivityTopicRequestDTO requestDTO) {

        this.check(requestDTO.getName());

        ActivityGroupDO activityGroupDO = ActivityGroupConverter.topicConverter(requestDTO);
        activityGroupDO.setUpdatedBy(activityGroupDO.getCreatedBy());
        activityGroupDAO.save(activityGroupDO);

        Long id = activityGroupDO.getId();




        //保存活动页
        activityPageDAO.save(ActivityGroupConverter.activityPageConverter(requestDTO,activityGroupDO.getId()));

        //保存分享页
        activityPageShareDAO.save(ActivityGroupConverter.activityShareConverter(requestDTO,activityGroupDO.getId()));

        //创建默认推广渠道
        //activityPromotionChannelService.createDefaults(activityGroupDO.getId(), ActivityPromotionTypeEnum.TOPIC.getType(), null);
        boolean result = activityGroupRelatedDAO.saveBatch(ActivityGroupConverter.topicRelatedConverter(requestDTO, activityGroupDO.getId()));

        if(result){
            ActivityGroupDO byId = activityGroupDAO.getById(id);
            miniProgramService.aSynCreateOneCodeScene(byId,MiniOrgTypeEnum.PROJECT.getId(),null, ActivityPromotionTypeEnum.TOPIC.getType());
        }

        return result;
    }

    private void check(String name) {
        if (StringUtils.isEmpty(name)){
            throw new ApplicationException("名称不能为空！");
        }
        QueryWrapper<ActivityGroupDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ActivityGroupDO::getName,name);
        queryWrapper.lambda().eq(ActivityGroupDO::getDeleted, SuperEntity.DR_NORMAL);
        List<ActivityGroupDO> content = activityGroupDAO.list(queryWrapper);
        if (CollectionUtil.isNotEmpty(content)){
            throw new ApplicationException("名称已存在！");
        }
    }

    @Override
    @Transactional(rollbackFor = ApplicationException.class)
    public Boolean updateTopic(ActivityTopicRequestDTO requestDTO) {
        if (requestDTO.getId() == null){
            throw new ApplicationException("id不能为空");
        }

        ActivityGroupResponseDTO byId = activityGroupDAO.findById(requestDTO.getId());
        if (byId == null){
            throw new ApplicationException("活动主题不存在！");
        }

        if (!requestDTO.getName().equals(byId.getName())){
            this.check(requestDTO.getName());
        }

        ActivityGroupDO activityGroupDO = ActivityGroupConverter.topicConverter(requestDTO);
        activityGroupDAO.updateById(activityGroupDO);
        //如果场景码为空时进行更新
        if(StringUtils.isEmpty(byId.getSceneCode())){
           /* String oneCodeScene = miniProgramService.createOneCodeScene(requestDTO.getName()+"_"+activityGroupDO.getId(), MiniOrgTypeEnum.NATIONAL.getId(), null);
            activityGroupDO.setSceneCode(oneCodeScene);*/
            miniProgramService.aSynCreateOneCodeScene(activityGroupDO,MiniOrgTypeEnum.PROJECT.getId(),null,ActivityPromotionTypeEnum.TOPIC.getType());
        }else{
            miniProgramService.aSynUpdateOneCodeScene(byId,MiniOrgTypeEnum.PROJECT.getId(),null, ActivityPromotionTypeEnum.TOPIC.getType());
        }

        //修改活动页
        UpdateWrapper<ActivityPageDO> wrapperPage = new UpdateWrapper<>();
        wrapperPage.lambda().eq(ActivityPageDO::getActivityId,activityGroupDO.getId());
        wrapperPage.lambda().eq(ActivityPageDO::getType, ActivityTypeEnum.TOPIC.getId());
        wrapperPage.lambda().eq(ActivityPageDO::getDeleted, SuperEntity.DR_NORMAL);
        activityPageDAO.update(ActivityGroupConverter.activityPageConverter(requestDTO,activityGroupDO.getId()),wrapperPage);

        //修改分享页
        UpdateWrapper<ActivityPageShareDO> wrapperShare = new UpdateWrapper<>();
        wrapperShare.lambda().eq(ActivityPageShareDO::getActivityId,activityGroupDO.getId());
        wrapperShare.lambda().eq(ActivityPageShareDO::getType, ActivityTypeEnum.TOPIC.getId());
        wrapperShare.lambda().eq(ActivityPageShareDO::getDeleted, SuperEntity.DR_NORMAL);
        activityPageShareDAO.update(ActivityGroupConverter.activityShareConverter(requestDTO,activityGroupDO.getId()),wrapperShare);

        //先删除活动主题关联配置
        QueryWrapper<ActivityGroupRelatedDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ActivityGroupRelatedDO::getGroupId,activityGroupDO.getId());
        activityGroupRelatedDAO.remove(queryWrapper);
        return activityGroupRelatedDAO.saveBatch(ActivityGroupConverter.topicRelatedConverter(requestDTO,activityGroupDO.getId()));
    }

    @Override
    public ActivityTopicAnalysisResponseDTO analysisOverview(Long id) {
        ActivityAnalysisQuery query = new ActivityAnalysisQuery();
        query.setPage(-1);
        query.setSize(9999);
        query.setId(id);
        PageBean<ActivityTopicAnalysisActivityInfoResponseDTO> promotionActivityResponsePageBean = promotionActivityDAO.analysisActivityInfo(query);
        List<ActivityTopicAnalysisActivityInfoResponseDTO> content = promotionActivityResponsePageBean.getContent();
        Long visitorsNumber = 0L;
        Long visitorsPeopleNumber = 0L;
        Long shareNumber = 0L;
        Long sharePeopleNumber = 0L;
        if (CollectionUtil.isNotEmpty(content)){
            String activityId = content.stream().map(e->("'"+e.getId()+"'")).collect(Collectors.joining(TradeConstants.DOT));

            //查询活动访问次数
            EventsReportQuery eventsReportQuery = new EventsReportQuery();
            eventsReportQuery.setEventName(SensorsEventEnum.ACTIVITY_PAGE_VIEW.getCode());
            eventsReportQuery.setSqlIn(activityId);
            visitorsNumber = sensorsService.findActivityFrequencySqlQuery(eventsReportQuery);

            //查询活动访问人数
            visitorsPeopleNumber = sensorsService.findActivityPeopleSqlQuery(eventsReportQuery);

            //查询活动分享次数
            eventsReportQuery.setEventName(SensorsEventEnum.PAGE_SHARE.getCode());
            shareNumber = sensorsService.findActivityFrequencySqlQuery(eventsReportQuery);

            //查询活动分享人数
            eventsReportQuery.setEventName(SensorsEventEnum.PAGE_SHARE.getCode());
            sharePeopleNumber = sensorsService.findActivityPeopleSqlQuery(eventsReportQuery);
        }
        ActivityTopicAnalysisResponseDTO topicAnalysisResponse = new ActivityTopicAnalysisResponseDTO();
        topicAnalysisResponse.setShareNumber(shareNumber);
        topicAnalysisResponse.setSharePeopleNumber(sharePeopleNumber);
        topicAnalysisResponse.setVisitorsNumber(visitorsNumber);
        topicAnalysisResponse.setVisitorsPeopleNumber(visitorsPeopleNumber);
        return topicAnalysisResponse;
    }

    @Override
    public PageBean<ActivityTopicAnalysisActivityInfoResponseDTO> analysisActivityInfo(ActivityAnalysisQuery query) {
        PageBean<ActivityTopicAnalysisActivityInfoResponseDTO> promotionActivityResponsePageBean = promotionActivityDAO.analysisActivityInfo(query);
        List<ActivityTopicAnalysisActivityInfoResponseDTO> content = promotionActivityResponsePageBean.getContent();
        this.getSensorsInfo(content);
        return promotionActivityResponsePageBean;
    }

    private void getSensorsInfo(List<ActivityTopicAnalysisActivityInfoResponseDTO> content) {
        if (CollectionUtil.isNotEmpty(content)){
            String activityId = content.stream().map(e->("'"+e.getId()+"'")).collect(Collectors.joining(TradeConstants.DOT));

            //查询活动页面浏览人数
            EventsReportQuery eventsReportQuery = new EventsReportQuery();
            eventsReportQuery.setSqlIn(activityId);
            eventsReportQuery.setEventName(SensorsEventEnum.ACTIVITY_PAGE_VIEW.getCode());
            List<ActivityPageViewVisitorsDTO> activityPageViewSqlInQuery = sensorsService.findActivityPeopleSqlInQuery(eventsReportQuery);
            Map<String, Long> pageViewMap = Maps.newHashMap();
            if (CollectionUtil.isNotEmpty(activityPageViewSqlInQuery)){
                pageViewMap = activityPageViewSqlInQuery.stream().collect(Collectors.toMap(ActivityPageViewVisitorsDTO::getActivityId, ActivityPageViewVisitorsDTO::getQuantity));
            }

            //查询活动分享人数
            Map<String, Long> shareMap = Maps.newHashMap();
            eventsReportQuery.setEventName(SensorsEventEnum.PAGE_SHARE.getCode());
            List<ActivityPageViewVisitorsDTO> activitySharePeopleSqlInQuery = sensorsService.findActivityPeopleSqlInQuery(eventsReportQuery);
            if (CollectionUtil.isNotEmpty(activitySharePeopleSqlInQuery)){
                shareMap = activityPageViewSqlInQuery.stream().collect(Collectors.toMap(ActivityPageViewVisitorsDTO::getActivityId, ActivityPageViewVisitorsDTO::getQuantity));
            }

            Map<String, Long> finalPageViewMap = pageViewMap;
            Map<String, Long> finalShareMap = shareMap;
            content.forEach(e->{
                Long visitorsPeopleNumber = finalPageViewMap.get(e.getId().toString());
                Long sharePeopleNumber = finalShareMap.get(e.getId().toString());
                e.setVisitorsPeopleNumber(visitorsPeopleNumber == null ? 0L : visitorsPeopleNumber);
                e.setSharePeopleNumber(sharePeopleNumber == null ? 0L : sharePeopleNumber);
            });
        }
    }

    @Override
    public PageBean<ActivityTopicAnalysisResourceResponseDTO> analysisResourceInfo(ActivityAnalysisQuery query) {
        return activityVerifyDAO.analysisResourceInfo(query);
    }

    private PageBean<ActivityGroupRelatedResponseDTO> getActivityGroupRelated(PageBean<ActivityGroupRelatedDO> activityGroupRelatedPageBean) {
        if (activityGroupRelatedPageBean == null){
            return new PageBean<>();
        }
        PageBean<ActivityGroupRelatedResponseDTO> activityGroupRelatedResponsePage = ObjectCloneUtils.convertPageBean(activityGroupRelatedPageBean, ActivityGroupRelatedResponseDTO.class);
        List<ActivityGroupRelatedDO> content = activityGroupRelatedPageBean.getContent();
        if (CollectionUtil.isEmpty(content)){
            return new PageBean<>();
        }

        //提取活动ids
        List<Long> activityIds = content.stream().map(ActivityGroupRelatedDO::getRelatedActivityId)
                .filter(relatedGroupId -> 0 != relatedGroupId).collect(Collectors.toList());
        //提取活动组ids
        List<Long> groupIds = content.stream().map(ActivityGroupRelatedDO::getRelatedGroupId)
                .filter(relatedGroupId -> 0 != relatedGroupId).collect(Collectors.toList());

        Map<Long, PromotionActivityDO> activityMap = Maps.newHashMap();
        Map<Long, ActivityGroupDO> groupMap = Maps.newHashMap();
        Map<Long, List<PromotionHisResourceDO>> activityHisResourceMap = Maps.newHashMap();
        //批量查询活动详情
        if (CollectionUtil.isNotEmpty(activityIds)) {
            List<PromotionActivityDO> promotionActivityList = promotionActivityDAO.listByIds(activityIds);
            if (CollectionUtil.isNotEmpty(promotionActivityList)) {
                activityMap = promotionActivityList.stream().collect(Collectors.toMap(PromotionActivityDO::getId, activity -> activity));

                //提取优惠券，秒杀活动id 查询优惠券，秒杀活动奖品信息
                List<Long> ids = promotionActivityList.stream()
                        .filter(e ->
                                String.valueOf(e.getPaTemplateId()).equals(StrategyGroupEnum.HF_COUPON_ACT.getId()) ||
                                        String.valueOf(e.getPaTemplateId()).equals(StrategyGroupEnum.HF_SECKILL_ACT.getId()))
                        .map(PromotionActivityDO::getId)
                        .collect(Collectors.toList());
                activityHisResourceMap = this.getActivityHisResource(ids);
            }
        }

        //批量查询活动组详情
        if (CollectionUtil.isNotEmpty(groupIds)) {

            List<ActivityGroupDO> activityGroupList = activityGroupDAO.listByIds(groupIds);
            if (CollectionUtil.isNotEmpty(activityGroupList)){
                groupMap = activityGroupList.stream().collect(Collectors.toMap(ActivityGroupDO::getId, activity -> activity));
            }
        }

        Map<Long, PromotionActivityDO> finalActivityMap = activityMap;
        Map<Long, ActivityGroupDO> finalGroupMap = groupMap;
        Map<Long, List<PromotionHisResourceDO>> finalActivityHisResourceMap = activityHisResourceMap;
        activityGroupRelatedResponsePage.setContent(content.stream().map(related -> {
            PromotionActivityDO promotionActivityDO = finalActivityMap.get(related.getRelatedActivityId());
            ActivityGroupDO activityGroupDO = finalGroupMap.get(related.getRelatedGroupId());
            List<PromotionHisResourceDO> promotionHisResourceList = finalActivityHisResourceMap.get(related.getRelatedActivityId());

            ActivityGroupRelatedResponseDTO relatedResponse = new ActivityGroupRelatedResponseDTO();
            relatedResponse.setId(related.getId());
            relatedResponse.setActivityId(related.getRelatedActivityId());
            relatedResponse.setSort(related.getSort());
            relatedResponse.setImageUrl(related.getImageUrl());
            if (promotionActivityDO != null) {
                relatedResponse.setName(promotionActivityDO.getName());
                relatedResponse.setPaTemplateId(promotionActivityDO.getPaTemplateId());
                relatedResponse.setTemplateName(StrategyGroupEnum.getValueById(promotionActivityDO.getPaTemplateId().toString()));
                relatedResponse.setStartTime(promotionActivityDO.getStartTime()!=null?DateUtils.format(promotionActivityDO.getStartTime(),DateUtils.DEFAULT_DATE_TIME_FORMAT):"");
                relatedResponse.setEndTime(promotionActivityDO.getEndTime()!=null?DateUtils.format(promotionActivityDO.getEndTime(),DateUtils.DEFAULT_DATE_TIME_FORMAT):"");
                relatedResponse.setStatus(promotionActivityDO.getStatus());
                relatedResponse.setRemark(promotionActivityDO.getRemark());
                if (promotionActivityDO.getExt() != null) {
                    Object isPopular = promotionActivityDO.getExt().get("isPopular");
                    if (Objects.nonNull(isPopular)) {
                        relatedResponse.setIsPopular(Integer.parseInt(isPopular.toString()));
                    }
                }
            }
            if (activityGroupDO != null){
                relatedResponse.setName(activityGroupDO.getName());
            }
            if (related.getType().equals(0)){
                relatedResponse.setTId(related.getRelatedGroupId() +"+"+related.getType());
                relatedResponse.setRelatedId(related.getRelatedGroupId());
            }else{
                relatedResponse.setTId(related.getRelatedActivityId() +"+"+related.getType());
                relatedResponse.setRelatedId(related.getRelatedActivityId());
            }
            if (promotionHisResourceList != null){
                relatedResponse.setPrizeList(ObjectCloneUtils.convertList(promotionHisResourceList, PromotionHisResourceDTO.class));
            }
            relatedResponse.setType(related.getType());
            return relatedResponse;
        }).collect(Collectors.toList()));
        return activityGroupRelatedResponsePage;
    }

    private Map<Long,List<PromotionHisResourceDO>> getActivityHisResource(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)){
            return Maps.newHashMap();
        }
        PromotionHisResourceQuery hisResourceQuery = new PromotionHisResourceQuery();
        hisResourceQuery.setActivityIds(ids);
        hisResourceQuery.setSize(1000);
        List<PromotionHisResourceDO> content = promotionHisResourceDAO.findPage(hisResourceQuery).getContent();
        if (CollectionUtil.isNotEmpty(content)){
            return content.stream().collect(Collectors.groupingBy(PromotionHisResourceDO::getActivityId));
        }
        return Maps.newHashMap();
    }
}
