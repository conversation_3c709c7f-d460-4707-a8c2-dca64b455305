//package com.deepexi.dxp.marketing.engine.handler;
//
//import com.alibaba.fastjson.JSONArray;
//import com.deepexi.dxp.marketing.config.RocketMQConfig;
//import com.deepexi.dxp.marketing.domain.tags.dto.UserFromEsDTO;
//import com.deepexi.dxp.marketing.engine.event.MarketingTaskEvent;
//import com.deepexi.dxp.marketing.engine.listener.MarketingTaskListener;
//import com.deepexi.dxp.marketing.engine.processor.AbstractTaskProcessor;
//import com.deepexi.dxp.marketing.engine.task.MetaDataTask;
//import com.deepexi.dxp.marketing.enums.tipic.RocketMQTopicTagEnum;
//import com.deepexi.dxp.marketing.enums.tipic.RocketMqTopicEnum;
//import com.deepexi.dxp.marketing.mq.rocket.SendMetaDataProducer;
//import com.deepexi.dxp.marketing.utils.SpringContextHolder;
//import com.deepexi.dxp.middle.marketing.domain.dto.TaskSendDataDTO;
//import com.deepexi.util.exception.ApplicationException;
//import com.google.common.collect.Queues;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
//import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
//import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
//import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
//import org.apache.rocketmq.common.message.MessageExt;
//
//import java.nio.charset.StandardCharsets;
//import java.util.List;
//import java.util.Objects;
//import java.util.Queue;
//import java.util.UUID;
//import java.util.stream.Collectors;
//
///**
// * 营销任务发送时相关规则元数据组装器
// *
// * <AUTHOR>
// * 2020/3/18 19:10
// */
//@Slf4j
//public class MetaDataHandler extends MarketingTaskListener<MarketingTaskEvent> {
//
//    private DefaultMQPushConsumer esMemberConsumer;
//
//    // 任务开启时传入的元数据对象
//    private MetaDataTask startMetaDataTask;
//
//    public MetaDataTask getStartMetaDataTask() {
//        return startMetaDataTask;
//    }
//
//    // TODO 发送失败后的结果放入到重试集合里，进行相关重发
//    private final Queue<TaskSendDataDTO> reTryQueue = Queues.newLinkedBlockingQueue();
//
//    @Override
//    public void start(MarketingTaskEvent event) {
//        try {
//            String uuid = UUID.randomUUID().toString().replaceAll("-", "");
//            String instanceName = "App-" + uuid;
//
//            startMetaDataTask = event.getMetaDataTask();
//            ESTaskMembersListener listener = new ESTaskMembersListener(event.getMetaDataTask());
//            String groupName = MarketingTaskEvent.GROUP_META_ES_PREFIX
//                    + event.getTopicTagName();
//            String topicName = RocketMqTopicEnum.APPLICATION_TOPIC.getName();
//            String tag = RocketMQTopicTagEnum.MTA_MEMBER_FOR_ES + "_" + event.getTopicTagName();
//            esMemberConsumer = SpringContextHolder.getBean(RocketMQConfig.class)
//                    .taskActiveConsumerTemplate(topicName, tag, listener, groupName, instanceName);
//            log.info("任务【id：{}】【参数组装器】开始执行，消费端已成功启动等待消费################", event.getMetaDataTask().getKey());
//
//        } catch (Exception e) {
//            log.error("从MQ里获取营销任务[key: " + ((AbstractTaskProcessor) event
//                    .getSource()).getMetaDataTask().getKey() + "]会员信息发送错误", e);
//            throw new ApplicationException("获取营销任务会员信息发送错误", e);
//        } finally {
//            // TODO 处理其他动作，
//            //  1、如将consumer放入到容器中进行本次任务的生命周期管理
//            //  2、如通知其他组件进行相关处理
//            // 暂时不论怎样都释放信号量
////            event.semaphoreDown();
//        }
//    }
//
//    @Override
//    public void suspend(MarketingTaskEvent event) {
//        log.info("MetaDataHandler【暂停】执行主动营销任务【Key：{}】【开始】..."
//                , event.getMetaDataTask().getKey());
//
//        if (esMemberConsumer != null) {
//            esMemberConsumer.suspend();
//        }
//        log.info("MetaDataHandler【暂停】执行主动营销任务【Key：{}】【结束】..."
//                , event.getMetaDataTask().getKey());
//    }
//
//    @Override
//    public void goOn(MarketingTaskEvent event) {
//        try {
//            log.info("MetaDataHandler【继续执行】主动营销任务【Key：{}】【开始】..."
//                    , event.getMetaDataTask().getKey());
//            if (Objects.isNull(esMemberConsumer)) {
//                log.info("发送引擎组装器消费者不存在.");
//                throw new ApplicationException("发送引擎组装器消费者不存在.");
//            }
//            esMemberConsumer.resume();
//            log.info("MetaDataHandler【继续执行】主动营销任务【Key：{}】【结束】..."
//                    , event.getMetaDataTask().getKey());
//        } catch (Exception e) {
//            log.error("继续启动消费【MetaDataHandler】失败", e);
//            throw new ApplicationException("继续执行主动营销任务【Key：{}】失败", e);
//        }
//    }
//
//    @Override
//    public void breakOff(MarketingTaskEvent event) {
//        end(event);
//    }
//
//    @Override
//    public void end(MarketingTaskEvent event) {
//        log.info("MetaDataHandler【已完成】执行主动营销任务【Key：{}】【开始】..."
//                , event.getMetaDataTask().getKey());
//        // 关闭消费者
//        if (esMemberConsumer != null) {
//            esMemberConsumer.shutdown();
//        }
//        log.info("MetaDataHandler【已完成】执行主动营销任务【Key：{}】【结束】..."
//                , event.getMetaDataTask().getKey());
//    }
//
//    /**
//     * ES会员MQ信息消费者监听器
//     */
//    public class ESTaskMembersListener implements MessageListenerConcurrently {
//
//        private final MetaDataTask metaDataTask;
//        private final SendMetaDataProducer producer = SpringContextHolder.getBean(SendMetaDataProducer.class);
//
//        public ESTaskMembersListener(MetaDataTask metaDataTask) {
//            this.metaDataTask = metaDataTask;
//        }
//
//        @Override
//        public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgList
//                , ConsumeConcurrentlyContext consumeConcurrentlyContext) {
//
//            List<List<UserFromEsDTO>> members = msgList.stream().map(
//                    msg -> {
//                        String data = new String(msg.getBody(), StandardCharsets.UTF_8);
//                        log.info("消费的数据为：{}", data);
//                        return JSONArray.parseArray(data, UserFromEsDTO.class);
//                    }
//            ).collect(Collectors.toList());
//
//            // 组装会员、资源、发送渠道等信息并将其发送到MQ，供发送器消费
//            producer.assembleToMQ(members, MetaDataHandler.this);
//            log.info("##########【消费ES会员数据 {} 条成功】###########", members.size());
//
//            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
//        }
//    }
//}
