package com.deepexi.dxp.marketing.domain.marketing.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @Class: DolphinSchedulerProcessInstanceExecuteDTO
 * @Description: DolphinScheduler 流程实例-执行操作 DTO
 * @Author: lizhong<PERSON>
 * @Date: 2020/3/31
 **/
@Data
public class DolphinSchedulerProcessInstanceExecuteDTO implements Serializable {

    /**
     * 流程实例ID
     */
    private Integer processInstanceId;

    /**
     * 执行类型,可用值:NONE,REPEAT_RUNNING,RECOVER_SUSPENDED_PROCESS,START_FAILURE_TASK_PROCESS,STOP,PAUSE
     */
    private String executeType;

}
