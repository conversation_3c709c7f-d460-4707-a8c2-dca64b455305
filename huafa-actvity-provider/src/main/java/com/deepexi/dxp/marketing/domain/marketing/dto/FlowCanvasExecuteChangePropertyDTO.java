package com.deepexi.dxp.marketing.domain.marketing.dto;

import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import lombok.Data;

/**
 * 流程画布-执行-更换属性
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-07 16:03
 */
@Data
public class FlowCanvasExecuteChangePropertyDTO extends SuperDTO {

    /**
     * 画布ID
     */
    private Long flowId;

    /**
     * 画布节点ID
     */
    private String nodeId;

    /**
     * 属性code
     */
    private String propertyCode;

    /**
     * 属性名称
     */
    private String propertyName;

    /**
     * 更换的属性值
     */
    private String propertyValue;

    public FlowCanvasExecuteChangePropertyDTO() {
    }

    public FlowCanvasExecuteChangePropertyDTO(String nodeId, String propertyCode, String propertyName, String propertyValue) {
        this.nodeId = nodeId;
        this.propertyCode = propertyCode;
        this.propertyName = propertyName;
        this.propertyValue = propertyValue;
    }
}
