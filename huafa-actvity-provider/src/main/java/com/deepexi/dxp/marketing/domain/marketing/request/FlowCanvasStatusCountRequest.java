package com.deepexi.dxp.marketing.domain.marketing.request;

import com.deepexi.dxp.marketing.common.base.request.SuperRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/12
 */
@Data
public class FlowCanvasStatusCountRequest extends SuperRequest {
    @ApiModelProperty(value = "状态列表", required = true)
    @NotNull(message = "状态列表不能为空")
    private List<Integer> statusList;
}
