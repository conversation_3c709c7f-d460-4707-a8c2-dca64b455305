//package com.deepexi.dxp.marketing.manager.promotion.impl;
//
//import com.deepexi.dxp.marketing.domain.promotion.dto.PromotionActivityDetailDTO;
//import com.deepexi.dxp.marketing.domain.promotion.dto.activity.ActivityRuleDTO;
//import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
//import com.deepexi.dxp.marketing.domain.promotion.dto.activity.PromotionActivityListPostVO;
//import com.deepexi.dxp.marketing.domain.promotion.dto.activity.PromotionActivityStatusUpdateRequest;
//import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityQuery;
//import com.deepexi.dxp.marketing.domain.promotion.request.PromotionActivityCreateRequest;
//import com.deepexi.dxp.marketing.domain.promotion.request.PromotionActivityUpdateRequest;
//import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityCreatePostRequest;
//import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityUpdatePostRequest;
//import com.deepexi.dxp.marketing.enums.status.ActivityStatus;
//import com.deepexi.dxp.marketing.manager.promotion.PromotionActivityService;
//import com.deepexi.dxp.marketing.service.promotion.PromotionActivityLoggerMiddleService;
//import com.deepexi.dxp.marketing.service.promotion.PromotionActivityMiddleService;
//import com.deepexi.dxp.middle.promotion.domain.dto.ActivityLogCountDTO;
//import com.deepexi.dxp.middle.promotion.domain.dto.ActivityLogDTO;
//import com.deepexi.dxp.middle.promotion.domain.query.PromotionActivityLogCountQuery;
//import com.deepexi.util.exception.ApplicationException;
//import com.deepexi.util.pageHelper.PageBean;
//import com.deepexi.util.pojo.CloneDirection;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//import java.util.Map;
//import java.util.Optional;
//
///**
// * 活动管理
// *
// * <AUTHOR>
// * @version 1.0
// * @date 2020-08-21 14:44
// */
//@Service
//@Slf4j
//public class PromotionActivityServiceImpl implements PromotionActivityService {
//
//    @Autowired
//    private PromotionActivityMiddleService promotionActivityMiddleService;
//
//    @Autowired
//    private PromotionActivityLoggerMiddleService promotionActivityLoggerMiddleService;
//
//    @Override
//    public PageBean<PromotionActivityListPostVO> findPage(PromotionActivityQuery query) {
//        return promotionActivityMiddleService.findPage(query);
//    }
//
//    @Override
//    public Long create(PromotionActivityCreateRequest dto) {
//        List<ActivityRuleDTO> activityRuleDTOList = dto.getActivityRuleDTOList();
//        activityRuleDTOList.forEach(vals -> {
//            String sortValue = Optional.of(vals)
//                    .map(ActivityRuleDTO::getCondition)
//                    .map(val -> val.get(0))
//                    .map(BaseActivityDTO::getValue)
//                    .orElse("-1");
//            vals.setSort(sortValue);
//        });
//        String paTemplateId = dto.getPaTemplateId().toString();
//
//        Long activityId = promotionActivityMiddleService.create(dto.clone(PromotionActivityCreatePostRequest.class, CloneDirection.OPPOSITE));
//
//
//        return activityId;
//    }
//
//
//
//
//
//    @Override
//    public PromotionActivityDetailDTO getActivityById(Long id) {
//        //活动基本数据
//        PromotionActivityListPostVO activityDetail= promotionActivityMiddleService.getActivityById(id);
//        if (activityDetail == null){
//            return new PromotionActivityDetailDTO();
//        }
//        PromotionActivityDetailDTO result = activityDetail.clone(PromotionActivityDetailDTO.class, CloneDirection.OPPOSITE);
//        //活动参与次数
//        PromotionActivityLogCountQuery logCountQuery = new PromotionActivityLogCountQuery();
//        logCountQuery.setActivityId(id);
//        ActivityLogDTO activityLogDTO = promotionActivityLoggerMiddleService.getLogNumCount(logCountQuery);
//        Map<Long, ActivityLogCountDTO> activityLogCountMap = activityLogDTO.getActivityLogCountMap();
//        Integer activityJoinQty = 0;
//        if (activityLogCountMap.containsKey(id)){
//            activityJoinQty = activityLogCountMap.get(id).getCountData();
//        }
//        result.setActivityJoinQty(activityJoinQty);
//        return result;
//    }
//
//    @Override
//    public Boolean updateActivityById(Long id, PromotionActivityUpdateRequest dto) {
//        dto.setActivityId(id);
//
//        PromotionActivityListPostVO activityByIdDetail = promotionActivityMiddleService.getActivityById(id);
//
//        if (activityByIdDetail == null){
//            throw new ApplicationException("查找不到该活动：{}",id);
//        }
//
//        PromotionActivityUpdatePostRequest cloneUpdateDTO = dto.clone(PromotionActivityUpdatePostRequest.class,
//                CloneDirection.FORWARD);
//        cloneUpdateDTO.setActivityId(id);
//        return promotionActivityMiddleService.updateActivityById(id, cloneUpdateDTO);
//    }
//
//    @Override
//    public Long updateActivityStatus(com.deepexi.dxp.marketing.domain.promotion.request.PromotionActivityStatusUpdateRequest dto) {
//        PromotionActivityStatusUpdateRequest params = dto.clone(PromotionActivityStatusUpdateRequest.class);
//        Long aLong = promotionActivityMiddleService.updateActivityStatus(params);
//
//        if (!dto.getStatus().equals(Integer.valueOf(ActivityStatus.STOP.getId()))) {
//            return aLong;
//        }
//
//        return aLong;
//    }
//
//    @Override
//    public Boolean delete(List<Long> ids) {
//        return promotionActivityMiddleService.delete(ids);
//    }
//
//}
