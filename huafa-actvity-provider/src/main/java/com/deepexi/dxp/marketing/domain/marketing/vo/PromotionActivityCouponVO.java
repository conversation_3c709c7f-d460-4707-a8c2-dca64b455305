package com.deepexi.dxp.marketing.domain.marketing.vo;

import com.deepexi.dxp.marketing.domain.promotion.dto.activity.ActivityRuleDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


/**
 * 活动优惠券VO
 * <AUTHOR>
 * @Date 2020/3/19
 */
@Data
@ApiModel
public class PromotionActivityCouponVO implements Serializable {

    @ApiModelProperty(value = "活动ID")
    private Long activityId;

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty("活动开始时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date startTime;

    @ApiModelProperty("活动结束时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date endTime;

    @ApiModelProperty("活动策略的规则")
    private List<ActivityRuleDTO> activityRuleDTOList;

    @ApiModelProperty(value = "优惠券ID")
    private Long couponId;

    @ApiModelProperty("优惠券类型")
    private String couponType;

    @ApiModelProperty("代金券面值|折扣券折扣")
    private BigDecimal couponValue;

    @ApiModelProperty("优惠券名称")
    private String couponName;

    @ApiModelProperty("活动类型")
    private Integer paTemplateId;
}
