package com.deepexi.dxp.marketing.controller.specify.adminapi;

import com.deepexi.common.extension.AppRuntimeEnv;
import com.deepexi.dxp.marketing.api.specify.MarketingKpiOpenApi;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.MarketingKpiItemGroupQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.MarketingKpiQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.*;
import com.deepexi.dxp.marketing.service.specify.MarketingKpiService;
import com.deepexi.dxp.marketing.utils.ListUtils;
import com.deepexi.dxp.middle.marketing.domain.dto.MarketingKpiDTO;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import com.deepexi.util.pojo.CloneDirection;
import com.deepexi.util.pojo.ObjectCloneUtils;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 指标管理原生/派生指标Controller
 * @Author: HuangBo.
 * @Date: 2020/5/15 16:29
 */
@Validated
@RestController
@RequestMapping("/admin-api/v1/kpi")
@Slf4j
@Api(description = "指标管理", tags = "MarketingKpiSdk")
public class MarketingKpiOpenApiController implements MarketingKpiOpenApi {

    @Autowired
    private MarketingKpiService kpiItemsService;


    @PostMapping("/save")
    @ApiOperation(value = "指标管理—新增修改", nickname = "marketingKipSave")
    public Data<MarketingKpiVO> save(@RequestBody @Valid MarketingKpiVO kpiItemsVO) {
        try {
            MarketingKpiDTO kpiItemsDTO = kpiItemsVO.clone(MarketingKpiDTO.class, CloneDirection.OPPOSITE);
            boolean res = kpiItemsService.save(kpiItemsDTO);
            return new Data<>(kpiItemsDTO.clone(MarketingKpiVO.class, CloneDirection.OPPOSITE));
        } catch (Exception e) {
            log.error("【保存指标】发生错误,{}", ExceptionUtils.getStackTrace(e));
            if (e instanceof ApplicationException) {
                throw e;
            }
            return new Data<MarketingKpiVO>(null, "10001", "保存失败.");
        }
    }


    @GetMapping("/original/query-page-list")
    @ApiOperation(value = "指标管理—原生指标分页查询", nickname = "marketingKipOriginalQueryPageList")
    public Data<PageBean<MarketingKpiVO>> originalPageList(@Valid MarketingKpiQuery query) {
        query.setType(MarketingKpiQuery.ORIGINAL_TYPE);

        return pageList(query);
    }

    @PostMapping("/original/query-page-list-post")
    @ApiOperation(value = "指标管理—原生指标分页查询", nickname = "marketingKipOriginalQueryPageList")
    public Data<PageBean<MarketingKpiVO>> originalPageListPost(@RequestBody @Valid MarketingKpiQuery query) {
        query.setType(MarketingKpiQuery.ORIGINAL_TYPE);

        return pageList(query);
    }

    @GetMapping("/derivative/query-page-list")
    @ApiOperation(value = "指标管理—派生指标分页查询", notes = "派生指标—派生指标分页查询", nickname = "marketingKipDerivativeQueryPageList")
    public Data<PageBean<MarketingKpiVO>> derivativePageList(@Valid MarketingKpiQuery query) {
        query.setType(MarketingKpiQuery.DERIVATIVE_TYPE);
        return pageList(query);
    }

    @PostMapping("/derivative/query-page-list-post")
    @ApiOperation(value = "指标管理—派生指标分页查询", notes = "派生指标—派生指标分页查询", nickname = "marketingKipDerivativeQueryPageList")
    public Data<PageBean<MarketingKpiVO>> derivativePageListPost(@RequestBody @Valid MarketingKpiQuery query) {
        query.setType(MarketingKpiQuery.DERIVATIVE_TYPE);
        return pageList(query);
    }

    @GetMapping("/query-page-list")
    @ApiOperation(value = "指标管理—分页查询", notes = "指标管理—分页查询", nickname = "marketingKipQueryPageList")
    public Data<PageBean<MarketingKpiVO>> pageList(@Valid MarketingKpiQuery query){
        try {
            PageBean<MarketingKpiDTO> kpiItemsDTOPageBean = kpiItemsService.pageList(query);
            PageBean<MarketingKpiVO> result = ObjectCloneUtils.convertPageBean(
                    kpiItemsDTOPageBean, MarketingKpiVO.class, CloneDirection.OPPOSITE);
            return new Data<>(result);
        } catch (Exception e) {
            log.error("【查询指标】发生错误,{}", ExceptionUtils.getStackTrace(e));
            if(e instanceof ApplicationException){
                throw e;
            }
            return new Data<>(null,"10001", "查询失败.");
        }
    }

    @PostMapping("/query-page-list-post")
    @ApiOperation(value = "指标管理—分页查询", notes = "指标管理—分页查询", nickname = "marketingKipQueryPageList")
    public Data<PageBean<MarketingKpiVO>> pageListPost(@RequestBody @Valid MarketingKpiQuery query){
        try {
            PageBean<MarketingKpiDTO> kpiItemsDTOPageBean = kpiItemsService.pageList(query);
            PageBean<MarketingKpiVO> result = ObjectCloneUtils.convertPageBean(
                    kpiItemsDTOPageBean, MarketingKpiVO.class, CloneDirection.OPPOSITE);
            return new Data<>(result);
        } catch (Exception e) {
            log.error("【查询指标】发生错误,{}", ExceptionUtils.getStackTrace(e));
            if(e instanceof ApplicationException){
                throw e;
            }
            return new Data<>(null,"10001", "查询失败.");
        }
    }

    @PostMapping("/query-list")
    @ApiOperation(value = "指标管理—列表查询", notes = "指标管理—列表查询", nickname = "marketingKipQueryList")
    public Data<List<MarketingKpiVO>> queryList(@RequestBody @Valid MarketingKpiQuery query){
        try {
            List<MarketingKpiDTO> dtos = kpiItemsService.queryList(query,false);
            List<MarketingKpiVO> result = ObjectCloneUtils.convertList(dtos, MarketingKpiVO.class, CloneDirection.OPPOSITE);
            return new Data<>(result);
        } catch (Exception e) {
            log.error("【查询指标】发生错误,{}", ExceptionUtils.getStackTrace(e));
            if(e instanceof ApplicationException){
                throw e;
            }
            return new Data<>(null,"10001", "查询失败.");
        }
    }

    @GetMapping("/select/data")
    @ApiOperation(value = "指标管理—获取下拉框数据" , notes = "指标管理—获取下拉框数据", nickname = "marketingKipSelectData")
    public Data<Map<String, Set<Object>>> selectData(MarketingKpiSelectDataRequestDTO request) {
        try {
            Map<String,Set<Object>> resMap = Maps.newHashMap();
            MarketingKpiQuery query = new MarketingKpiQuery();
            query.setType(request.getType());

            // 查询所有单位
            query.setQueryFieldName("unitName");
            resMap.put("unitNameList", Sets.newHashSet(kpiItemsService.queryByColumn(query)));

            // 查询所有更新频率
            query.setQueryFieldName("frequency");
            resMap.put("frequencyList",  Sets.newHashSet(kpiItemsService.queryByColumn(query)));

            return new Data<>(resMap);
        } catch (Exception e) {
            log.error("【查询指标页面所有下拉框数据】发生错误,{}", ExceptionUtils.getStackTrace(e));
            if(e instanceof ApplicationException){
                throw e;
            }
            return new Data<>(null,"10001", "查询指标页面所有下拉框数据失败.");
        }
    }
    @PostMapping("/select/dataPost")
    @ApiOperation(value = "指标管理—获取下拉框数据" , notes = "指标管理—获取下拉框数据", nickname = "marketingKipSelectData")
    public Data<Map<String, Set<Object>>> selectDataPost(@RequestBody MarketingKpiSelectDataRequestDTO request) {
        try {
            Map<String,Set<Object>> resMap = Maps.newHashMap();
            MarketingKpiQuery query = new MarketingKpiQuery();
            query.setType(request.getType());

            // 查询所有单位
            query.setQueryFieldName("unitName");
            resMap.put("unitNameList", Sets.newHashSet(kpiItemsService.queryByColumn(query)));

            // 查询所有更新频率
            query.setQueryFieldName("frequency");
            resMap.put("frequencyList",  Sets.newHashSet(kpiItemsService.queryByColumn(query)));

            return new Data<>(resMap);
        } catch (Exception e) {
            log.error("【查询指标页面所有下拉框数据】发生错误,{}", ExceptionUtils.getStackTrace(e));
            if(e instanceof ApplicationException){
                throw e;
            }
            return new Data<>(null,"10001", "查询指标页面所有下拉框数据失败.");
        }
    }

    @GetMapping("/detail")
    @ApiOperation(value = "指标管理——详情", notes = "指标管理——详情", nickname = "marketingKipDetail")
    public Data<MarketingKpiVO> detail(@RequestParam Long id) {
        try {
            MarketingKpiQuery query = new MarketingKpiQuery();
            query.setId(id);
            MarketingKpiDTO kpiItemsDTO = kpiItemsService.queryById(query);

            if(Objects.isNull(kpiItemsDTO)){
                return new Data<>(null,"10001", "指标不存在.");
            }

            MarketingKpiVO kpiItemsVO = kpiItemsDTO.clone(MarketingKpiVO.class);
            // 设置派生指标公式
            if(MarketingKpiQuery.DERIVATIVE_TYPE.equals(kpiItemsVO.getType())){
                List<MarketingKpiFormulaVO> kpiItemsFormulaVOList = ListUtils.copyListDTO2VO(
                        MarketingKpiFormulaVO.class, kpiItemsDTO.getFormulaList());
                kpiItemsVO.setFormulaList(kpiItemsFormulaVOList);
            }
            return new Data<>(kpiItemsVO);
        } catch (Exception e) {
            log.error("【通过ID查询指标】发生错误,{}", ExceptionUtils.getStackTrace(e));
            if(e instanceof ApplicationException){
                throw e;
            }
            return new Data<>(null,"10001", "查询失败.");
        }
    }

    @PutMapping("/process/status")
    @ApiOperation(value = "指标管理——启用禁用", notes = "指标管理——启用禁用", nickname = "marketingKipProcessStatus")
    public Data<Boolean> processStatus(@RequestParam Integer status, @RequestParam Long id) {
        try {
            return new Data<Boolean>(kpiItemsService.processStatus(id, status));
        } catch (Exception e) {
            log.error("【禁用/启用指标】发生错误,{}", ExceptionUtils.getStackTrace(e));
            if(e instanceof ApplicationException){
                throw e;
            }
            return new Data<>(null,"10001", "操作指标失败.");
        }
    }

    @GetMapping("/check/name")
    @ApiOperation(value = "指标管理—同名检查", notes = "指标管理—同名检查", nickname = "marketingKipCheckName")
    public Data<Boolean> checkName(MarketingKpiCheckNameRequestDTO request){
        return new Data<Boolean>(kpiItemsService.existByName(AppRuntimeEnv.getTenantId(), request.getName(), request.getOldName()));
    }

    @PostMapping("/check/namePost")
    @ApiOperation(value = "指标管理—同名检查", notes = "指标管理—同名检查", nickname = "marketingKipCheckName")
    public Data<Boolean> checkNamePost(@RequestBody MarketingKpiCheckNameRequestDTO request){
        return new Data<>(kpiItemsService.existByName(AppRuntimeEnv.getTenantId(), request.getName(), request.getOldName()));
    }

    @GetMapping("/query-operation-list")
    @ApiOperation(value = "指标管理—查询运算列表" , notes = "指标管理—查询运算列表", nickname = "marketingKpiQueryOperationList")
    public Data<List<KpiOperationDTO>> queryOperationList(@RequestParam Long id){
        return new Data<>(kpiItemsService.queryOperationList(id, AppRuntimeEnv.getTenantId()));
    }

    @GetMapping("/query-by-item-group")
    @ApiOperation(value = "指标管理-指标组—根据指标组ID获取原生/派生指标集合", notes = "指标管理-指标组—根据类型查询核心指标", nickname = "marketingKipQueryByKpiItemGroup")
    public Data<List<MarketingKpiExtDTO>> queryByKpiItemGroup(MarketingKpiItemGroupQuery query){
        return new Data<>(ObjectCloneUtils.convertList(
                kpiItemsService.queryByKpiItemGroup(query), MarketingKpiExtDTO.class));
    }
    @PostMapping("/query-by-item-group-post")
    @ApiOperation(value = "指标管理-指标组—根据指标组ID获取原生/派生指标集合", notes = "指标管理-指标组—根据类型查询核心指标", nickname = "marketingKipQueryByKpiItemGroup")
    public Data<List<MarketingKpiExtDTO>> queryByKpiItemGroupPost(@RequestBody MarketingKpiItemGroupQuery query){
        return new Data<>(ObjectCloneUtils.convertList(
                kpiItemsService.queryByKpiItemGroup(query), MarketingKpiExtDTO.class));
    }

    @Override
    @DeleteMapping("/delete")
    public Data<Boolean> deleteByKpi(@RequestParam Long id) {

        return new Data<>(kpiItemsService.deleteByKpi(id));
    }
}
