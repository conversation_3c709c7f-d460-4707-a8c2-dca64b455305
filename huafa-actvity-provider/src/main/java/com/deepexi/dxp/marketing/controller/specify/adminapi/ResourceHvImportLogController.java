package com.deepexi.dxp.marketing.controller.specify.adminapi;

import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.response.ExcelExportResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.ResourceHvImportLogResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.query.specify.ResourceHvImportLogQuery;
import com.deepexi.dxp.marketing.domain.promotion.request.specify.PromotionResourceImportRequestDTO;
import com.deepexi.dxp.marketing.service.specify.ResourceHvImportLogService;
import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@Slf4j
@RequestMapping("/admin-api/v1/promotion-importLog")
@Api(description = "房源券导入管理", value = "房源券导入管理")
public class ResourceHvImportLogController {

    @Autowired
    private ResourceHvImportLogService resourceHvImportLogService;

    /**
     * 房源导入列表
     * @param query
     * @return
     */
    @GetMapping("/list")
    @ApiOperation(value="房源导入日志列表", notes = "房源券导入日志列表")
    public Data<PageBean<ResourceHvImportLogResponseDTO>> list(ResourceHvImportLogQuery query) {
        return new Data<>(resourceHvImportLogService.findPage(query));
    }
    /**
     * 房源导入列表
     * @param query
     * @return
     */
    @PostMapping("/listPost")
    @ApiOperation(value="房源导入日志列表", notes = "房源券导入日志列表")
    public Data<PageBean<ResourceHvImportLogResponseDTO>> listPost(@RequestBody ResourceHvImportLogQuery query) {
        return new Data<>(resourceHvImportLogService.findPage(query));
    }


    /**
     * 房源导入
     * @param promotionResourceImportRequestDTO
     * @return
     */
    @PostMapping("/import")
    @ApiOperation(value="房源券导入", notes = "房源券导入")
    public Data<Boolean> resourceImport(@RequestBody PromotionResourceImportRequestDTO promotionResourceImportRequestDTO){
        return new Data<>(resourceHvImportLogService.importResource(promotionResourceImportRequestDTO));
    }

    @ApiOperation(value="房源券导入失败记录导出", notes = "房源券导入失败记录导出")
    @GetMapping("/exportExcel")
    public Data<Boolean> exportExcel(HttpServletResponse response,Long id){
        resourceHvImportLogService.exportExcel(response,id);
        return new Data<>(true);
    }

    @ApiOperation(value="获取房源券导入失败记录数据", notes = "获取房源券导入失败记录数据")
    @GetMapping("/getExportExcelData")
    public ExcelExportResponseDTO getExportExcelData(Long id){
        return resourceHvImportLogService.getExportExcelData(id);
    }

    @ApiOperation("下载导入模板")
    @GetMapping("/exportTemplate")
    public void exportTemplate(Integer type,
                               HttpServletRequest request, HttpServletResponse response) {
        resourceHvImportLogService.exportTemplate(type,request,response);
    }
}
