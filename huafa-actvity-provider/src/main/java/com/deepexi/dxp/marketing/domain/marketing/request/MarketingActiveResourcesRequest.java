package com.deepexi.dxp.marketing.domain.marketing.request;

import com.deepexi.dxp.marketing.common.base.request.SuperRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 主动营销资源(包含模板、H5、优惠券)
 * @Author: HuangBo.
 * @Date: 2020/3/14 17:55
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MarketingActiveResourcesRequest extends SuperRequest {

  /**
   * 主键ID
   */
  @ApiModelProperty(value = "主键ID")
  private Long id;

  /**
   * 主动营销任务
   */
  @ApiModelProperty(value = "主动营销任务ID")
  @NotNull(message = "主动营销任务ID不能为空")
  private Long activeId;

  /**
   * 资源ID
   */
  @ApiModelProperty(value = "资源ID")
  @NotNull(message = "营销任务资源ID不能为空")
  private Long resourceId;

  /**
   * 资源编码
   */
  @ApiModelProperty(value = "资源编码")
  private String resourceCode;

  /**
   * 资源名称
   */
  @ApiModelProperty(value = "资源名称")
  private String resourceName;

  /**
   * 资源信息
   */
  @ApiModelProperty(value = "资源信息")
  private String resourceInfo;

  /**
   * 资源类型编码(1:只发送通知；2:赠送优惠券；3:推送H5)
   */
  @ApiModelProperty(value = "资源类型编码(1:只发送通知；2:赠送优惠券；3:推送H5)")
  @NotNull(message = "资源类型编码不能为空")
  private String resourceTypeCode;

  /**
   * 资源类型名称
   */
  @ApiModelProperty(value = "资源类型名称(1:只发送通知；2:赠送优惠券；3:推送H5)")
  @NotNull(message = "资源类型名称不能为空")
  private String resourceTypeName;

  /**
   * 资源类别编码
   */
  @ApiModelProperty(value = "资源类别编码")
  @NotNull(message = "资源类别编码不能为空")
  private String resourceCategoryCode;

  /**
   * 资源类别名称
   */
  @ApiModelProperty(value = "资源类别名称")
  @NotNull(message = "资源类别名称不能为空")
  private String resourceCategoryName;

  /**
   * 资源渠道
   */
  @ApiModelProperty(value = "资源渠道(当前默认传DR)")
  @NotNull(message = "资源渠道不能为空")
  private String resourceChannel;

  /**
   * 资源详情URL
   */
  @ApiModelProperty(value = "资源详情URL")
  private String resourceDetailURL;

  /**
   * 营销任务资源有效期开始时间
   */
  @ApiModelProperty(value = "资源有效期开始时间")
  private Date validityStartTime;

  /**
   * 营销任务资源有效期结束时间
   */
  @ApiModelProperty(value = "资源有效期结束时间")
  private Date validityEndTime;

}
