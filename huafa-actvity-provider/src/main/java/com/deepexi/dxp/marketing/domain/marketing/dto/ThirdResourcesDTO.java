package com.deepexi.dxp.marketing.domain.marketing.dto;


import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * 资源备份表
 *
 * <AUTHOR>
 * @Date 2020/4/8
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class ThirdResourcesDTO extends SuperDTO {

    /**
     * 渠道
     */
    @ApiModelProperty(value = "渠道")
    private String resourceChannel;
    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID")
    private String resourceId;
    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String resourceName;
    /**
     * 活动类型ID
     */
    @ApiModelProperty(value = "活动类型ID")
    private String resourceTypeId;
    /**
     * 活动类型名称
     */
    @ApiModelProperty(value = "活动类型名称")
    private String resourceTypeName;
    /**
     * 任务ID
     */
    @ApiModelProperty(value = "任务ID")
    private Long taskId;
    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date activityStartTime;
    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date activityEndTime;
    /**
     * 活动状态：0=草稿，1=未开始，2=进行中，3=已完成，4=已终止
     */
    @ApiModelProperty(value = "活动状态：0=草稿，1=未开始，2=进行中，3=已完成，4=已终止")
    private Integer activityStatus;

    /**
     * 资源url
     */
    @ApiModelProperty(value = "资源url")
    private String resourceDetailUrl;

    /**
     * 资源自身更新时间
     */
    @ApiModelProperty(value = "资源自身更新时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date resourceUpdateTime;
}

