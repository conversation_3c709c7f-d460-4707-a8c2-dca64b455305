package com.deepexi.dxp.marketing.schedule;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSON;
import com.deepexi.common.extension.AppRuntimeEnv;
import com.deepexi.dxp.marketing.constant.PayConstant;
import com.deepexi.dxp.marketing.constant.RedisConstants;
import com.deepexi.dxp.marketing.domain.marketing.request.CancelOrderRequest;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityOrderResponseVO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityVerifyInResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.PromotionActivityDetailDTO;
import com.deepexi.dxp.marketing.enums.resource.ActivityStatusEnum;
import com.deepexi.dxp.marketing.enums.specify.OrderStatusEnum;
import com.deepexi.dxp.marketing.enums.specify.PromotionResourceValidTimeTypeEnum;
import com.deepexi.dxp.marketing.manager.promotion.PromotionActivityManager;
import com.deepexi.dxp.marketing.service.specify.ActivityOrderService;
import com.deepexi.dxp.marketing.service.specify.ActivityVerifyService;
import com.deepexi.dxp.marketing.service.specify.LuckyDrawService;
import com.deepexi.dxp.marketing.service.specify.PromotionActivityService;
import com.deepexi.dxp.marketing.service.specify.impl.SignUpService;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.util.CollectionUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 活动定时计划
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityScheduledTask {

	@Resource
	private PromotionActivityService promotionActivityService;

	@Resource
	private LuckyDrawService luckyDrawService;

	@Resource
	private ActivityOrderService activityOrderService;

	@Autowired
	private PromotionActivityManager promotionActivityManager;

	@Autowired
	private RedissonClient redissonClient;

	@Autowired
	private ActivityVerifyService activityVerifyService;
	@Resource
	private SignUpService signUpService;


//	@Scheduled(cron="0 */1 * * * ?")
	@XxlJob("updateStatusByStartTime")
	public ReturnT<String> updateStatusByStartTime(String param) {
		//开始已到达启动时间的活动
		List<PromotionActivityDO> promotionActivityDOList =  promotionActivityService.getListByShouldStart();
		Boolean aBooleanStart = promotionActivityService.updateStatusByStartTime();
		log.info("开始已到达启动时间的活动结果：" + aBooleanStart);
		if(aBooleanStart){
			//缓存数据到redis
			for(PromotionActivityDO promotionActivityDO:promotionActivityDOList){
				promotionActivityManager.forceCacheActInfo(promotionActivityDO.getId());
			}
			//清空列表缓存
			promotionActivityManager.clearAllRunningListCache();
		}
		return ReturnT.SUCCESS;
	}

//	@Scheduled(cron="0 */1 * * * ?")
	@XxlJob("updateStatusByEndTime")
	public ReturnT<String> updateStatusByEndTime(String param) {
		//完成已到达结束时间的活动
		List<PromotionActivityDO> promotionActivityDOList =  promotionActivityService.getListByShouldFinish();
		Boolean aBooleanEnd = promotionActivityService.updateStatusByEndTime();
		log.info("完成已到达结束时间的活动结果：" + aBooleanEnd);
		if(aBooleanEnd){
			for(PromotionActivityDO promotionActivityDO:promotionActivityDOList){
				//助力活动系统发放礼品
				promotionActivityService.grantResource(promotionActivityDO);
				//活动结束砍价活动是否完成
				promotionActivityService.checkBargainEndTime(promotionActivityDO);
				PromotionActivityDetailDTO redisActivityDetailDTO = JSON.parseObject(String.valueOf(redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO+promotionActivityDO.getId()).get()),PromotionActivityDetailDTO.class);
				if(Objects.nonNull(redisActivityDetailDTO)){
					redisActivityDetailDTO.setStatus(Integer.parseInt(ActivityStatusEnum.FINISH.getId()));
					redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO+promotionActivityDO.getId()).set(JSON.toJSONString(redisActivityDetailDTO));
				}
			}
			//清空列表缓存
			promotionActivityManager.clearAllRunningListCache();
		}
		return ReturnT.SUCCESS;
	}


//	@Scheduled(cron="0 0 0 * * ? ")
	@XxlJob("numberDrawsDay")
	public ReturnT<String> numberDrawsDay(String param) {
		//初始化每日抽奖次数
		Boolean initBoolean = luckyDrawService.initNumberDrawsDay();
		log.info("初始化每日抽奖次数结果：" + initBoolean);
		return ReturnT.SUCCESS;
	}

	/**
	 * 更新订单状态（关闭订单）
	 */
//	@Scheduled(cron="0 0/1 * * * ?")
//	public ReturnT<String> updateOrderStatusToClose(){
	@XxlJob("updateOrderStatusToClose")
	public ReturnT<String> updateOrderStatusToClose(String param) {
		List<ActivityOrderResponseVO> list = activityOrderService.findList(OrderStatusEnum.UNPAY.getId());
		if(CollectionUtil.isNotEmpty(list)){
			list.forEach(order ->{
				DateTime now = DateTime.now();
				Calendar nowTime = Calendar.getInstance();
				nowTime.setTime(order.getCreatedTime());//订阐创建时间
				nowTime.add(Calendar.MINUTE, PayConstant.EXPIREMINUTES);
				Date closeTime = nowTime.getTime();
				if(now.after(closeTime)){//超时
					// 还需要请求微信接口，取消订单
					//调用支付平台取消订单
					CancelOrderRequest dto = new CancelOrderRequest(order.getCode());
					dto.setReason("订单支付超时关闭");
					activityOrderService.closeOrder(dto);
				}
			});
		}
		return ReturnT.SUCCESS;
	}

	/**
	 * 更新订单状态（查询订单是否支付成功）
	 */
//	@Scheduled(cron="0 0/1 * * * ?")
	@XxlJob("updateOrderStatusToFinish")
	public ReturnT<String> updateOrderStatusToFinish(String param) {
		List<ActivityOrderResponseVO> list = activityOrderService.findList(OrderStatusEnum.UNPAY.getId());
		if(CollectionUtil.isNotEmpty(list)){
			list.forEach(order ->{
				DateTime now = DateTime.now();
				Calendar nowTime = Calendar.getInstance();
				nowTime.setTime(order.getCreatedTime());//订阐创建时间
				nowTime.add(Calendar.MINUTE, PayConstant.EXPIREMINUTES);
				Date closeTime = nowTime.getTime();
				if(now.before(closeTime)){//未超时
					//调用支付平台查询订单
					activityOrderService.checkUnPayOrder(order.getWxOrderNo(),order.getId());
				}
			});
		}
		return ReturnT.SUCCESS;
	}

	/**
	 * 对于需要检查活动的，如报名活动人数不够要取消
	 * @param param
	 * @return
	 */
	//	@Scheduled(cron="0 0/1 * * * ?")
	@XxlJob("checkActivity")
	public ReturnT<String> checkActivity(String param) {
		//定时检查报名活动人数是否够
		List<PromotionActivityDO> list = promotionActivityService.getSignUpActivityCheckList();
		AppRuntimeEnv.setTenantId("hfb981fd0e654f7c90470bc865d83690");
		AppRuntimeEnv.setAppId(1001L);
		if(CollectionUtil.isNotEmpty(list)){
			list.forEach(item -> signUpService.cancelActivity(item));
		}
		return ReturnT.SUCCESS;
	}
	/**
	 * 过期优惠券核销记录将核销记录状态改成已过期 一个小时执行一次
	 */
//	@Scheduled(cron="0 0 */1 * * ?")
	@XxlJob("overdueVerify")
	public ReturnT<String> overdueVerify(String param){
		List<ActivityVerifyInResponseDTO> list = activityVerifyService.findOverList();

		if(CollectionUtil.isNotEmpty(list)){
			list.forEach(activityVerifyDO ->{
				if(PromotionResourceValidTimeTypeEnum.DESIGNATED_TIME.getId().equals(activityVerifyDO.getValidTimeType())
                        && activityVerifyDO.getValidEndTime().before(DateTime.now())){
                    activityVerifyService.activityVerifyToOver(activityVerifyDO);
                }
                if(PromotionResourceValidTimeTypeEnum.EFFECTIVE_DAYS.getId().equals(activityVerifyDO.getValidTimeType())
                        ){
                    DateTime now = DateTime.now();
                    Calendar nowTime = Calendar.getInstance();
                    nowTime.setTime(activityVerifyDO.getPayTime());//购买或领取时间
                    nowTime.add(Calendar.DATE, activityVerifyDO.getValidDay());
                    Date overTime = nowTime.getTime();
                    if(overTime.before(now)){
                        activityVerifyService.activityVerifyToOver(activityVerifyDO);
                    }
                }
			});
		}
		return ReturnT.SUCCESS;
	}

	/**
	 * 有效期截止后，满足至少一个阶梯，即为助力成功，反之即为助力失败
	 */
//	@Scheduled(cron="0 */3 * * * ?")
	@XxlJob("assistSuccessStatus")
	public ReturnT<String> assistSuccessStatus(String param){
		promotionActivityManager.udpateAssistSuccessStatus();
		return ReturnT.SUCCESS;
	}
}
