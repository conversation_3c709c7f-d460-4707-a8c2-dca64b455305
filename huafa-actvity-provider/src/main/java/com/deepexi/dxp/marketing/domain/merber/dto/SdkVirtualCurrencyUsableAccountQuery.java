package com.deepexi.dxp.marketing.domain.merber.dto;

import com.deepexi.util.domain.dto.BaseDTO;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Class: SdkVirtualCurrencyUsableAccountQuery
 * @Description:
 * @Author: lizhong<PERSON>
 * @Date: 2020/7/28
 **/
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class SdkVirtualCurrencyUsableAccountQuery extends BaseDTO {
    @SerializedName("status")
    private Integer status;
    @SerializedName("用户ID")
    private Long userId;
    @SerializedName("用户类型")
    private Integer userType;
}
