//package com.deepexi.dxp.marketing.controller.promotion.middleapi;
//
//import com.deepexi.dxp.marketing.api.promotion.PromotionActivityLimitMiddleApi;
//import com.deepexi.dxp.marketing.domain.promotion.dto.activity.PromotionActivityLimitFindVO;
//import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityLimitQuery;
//import com.deepexi.dxp.middle.promotion.util.PageUtil;
//import com.deepexi.dxp.marketing.service.promotion.PromotionActivityLimitMiddleService;
//import com.deepexi.util.config.Payload;
//import com.deepexi.util.pageHelper.PageBean;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.validation.Valid;
//import java.util.List;
//
///**
// * <AUTHOR> xinjian.yao
// * @date 2020/3/11 15:56
// */
//@RestController
//@Slf4j
//@Api(value = "活动限制", tags = "活动限制相关接口")
//public class PromotionActivityLimitMiddleApiController
//        implements PromotionActivityLimitMiddleApi {
//
//
//    @Autowired
//    private PromotionActivityLimitMiddleService promotionActivityLimitMiddleService;
//
//    @Override
//    @ApiOperation("促销活动限制查询")
//    public Payload<PageBean<PromotionActivityLimitFindVO>> findPage(@Valid @RequestBody PromotionActivityLimitQuery query) {
//        List<PromotionActivityLimitFindVO> pageList = promotionActivityLimitMiddleService.findPage(query);
//
//        PageUtil<PromotionActivityLimitFindVO> responsePageUtil = new PageUtil<>();
//        return new Payload<>(responsePageUtil.pageUtil(query.getPage(), query.getSize(), pageList));
//    }
//}
