//package com.deepexi.dxp.marketing.engine.handler;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.deepexi.dm.bury.point.api.BuryPointService;
//import com.deepexi.dm.bury.point.domain.BuryPointEvent;
//import com.deepexi.dxp.marketing.engine.listener.MarketingTaskListenerFactory;
//import com.deepexi.dxp.marketing.enums.contact.MessageTypeEnum;
//import com.deepexi.dxp.marketing.enums.marketing.MarketingTaskExecuteStatusEnum;
//import com.deepexi.dxp.marketing.enums.marketing.MarketingTaskTypeEnum;
//import com.deepexi.dxp.marketing.enums.marketing.TaskShortNameEnum;
//import com.deepexi.dxp.marketing.enums.redis.RedisPrefixEnum;
//import com.deepexi.dxp.marketing.mq.rocket.SendMetaDataProducer;
//import com.deepexi.dxp.marketing.service.marketing.MarketingTaskTouchDetailService;
//import com.deepexi.dxp.marketing.utils.SpringContextHolder;
//import com.deepexi.dxp.middle.marketing.domain.dto.MarketingTaskTouchDetailCreateDTO;
//import com.deepexi.dxp.middle.marketing.domain.dto.TaskSendDataDTO;
//import com.deepexi.redis.service.RedisService;
//import com.deepexi.util.CollectionUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
//import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
//import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
//import org.apache.rocketmq.client.producer.DefaultMQProducer;
//import org.apache.rocketmq.common.message.MessageExt;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.nio.charset.StandardCharsets;
//import java.util.*;
//import java.util.stream.Collectors;
//
///**
// * 消息发送成功失败接收器；
// * 1、处理成功后的记录
// * 2、处理失败后的降级动作
// *
// * <AUTHOR>
// * @date 2020/3/21 15:34
// */
//@Slf4j
//@Component
//public class ResultHandler implements MessageListenerConcurrently {
//
//    @Autowired
//    private MarketingTaskTouchDetailService marketingTaskTouchDetailService;
//
//    @Autowired
//    private DefaultMQProducer rocketProducerTemplate;
//
//    @Autowired
//    private RedisService redisService;
//
//    @Autowired
//    private BuryPointService buryPointService;
//
//    @Override
//    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
//        for (MessageExt messageExt : msgs) {
//            TaskSendDataDTO taskSendDataDto = JSONObject.parseObject(new String(messageExt.getBody(), StandardCharsets.UTF_8), TaskSendDataDTO.class);
//            try {
//                if (redisService.get( RedisPrefixEnum.SEND_MQ_MSG_ID.getKey() + ":" + messageExt.getMsgId()) != null) {
//                    log.info("执行结果处理器参数：消息已被其他机器消费：{}", JSON.toJSONString(taskSendDataDto));
//                    continue;
//                } else {
//                    redisService.set(RedisPrefixEnum.SEND_MQ_MSG_ID.getKey() + ":" + messageExt.getMsgId(), null, 600);
//                }
//            } catch (Exception e) {
//                log.error("Redis Throwable", e);
//            }
//            try {
//                log.info("执行结果处理器参数：{}", JSON.toJSONString(taskSendDataDto));
//
//                //触达明细初始类
//                MarketingTaskTouchDetailCreateDTO marketingTaskTouchDetailCreateDto = new MarketingTaskTouchDetailCreateDTO();
//
//                //任务
//                TaskSendDataDTO.TaskContent taskContent = taskSendDataDto.getTaskContent();
//
//                //会员
//                TaskSendDataDTO.MemberContent memberContent = taskSendDataDto.getMemberContent();
//
//                //资源
//                TaskSendDataDTO.ResourceContent resourceContent = taskSendDataDto.getResourceContent();
//
//                //触达规则列表
//                List<TaskSendDataDTO.TouchRuleContent> touchRuleContentList = taskSendDataDto.getTouchRuleContentList();
//
//                //触达规则排序列表
//                List<Integer> sortList = touchRuleContentList.stream().map(TaskSendDataDTO.TouchRuleContent::getSort).collect(Collectors.toList());
//
//                //封装记录数据
//                if (CollectionUtil.isNotEmpty(touchRuleContentList)) {
//                    for (int i = 0; i < touchRuleContentList.size(); i++) {
//                        //如果为当前执行的触达规则
//                        if (touchRuleContentList.get(i).getRuleExecuteStatus()) {
//                            TaskSendDataDTO.TouchRuleContent touchRuleContent = touchRuleContentList.get(i);
//                            marketingTaskTouchDetailCreateDto.setTenantId(taskSendDataDto.getTenantId());
//                            marketingTaskTouchDetailCreateDto.setAppId(taskSendDataDto.getAppId());
//                            marketingTaskTouchDetailCreateDto.setMemberId(memberContent.getMemberId());
//                            marketingTaskTouchDetailCreateDto.setMemberName(memberContent.getMemberName());
//                            if (memberContent.getPhone() != null) {
//                                marketingTaskTouchDetailCreateDto.setMemberPhone(memberContent.getPhone());
//                            }
//                            if (null != resourceContent) {
//                                marketingTaskTouchDetailCreateDto.setResourceId(resourceContent.getResourceId());
//                                marketingTaskTouchDetailCreateDto.setResourceCategoryCode(resourceContent.getResourceCategoryCode());
//                                marketingTaskTouchDetailCreateDto.setResourceName(resourceContent.getResourceName());
//                                marketingTaskTouchDetailCreateDto.setResourceChannel(resourceContent.getResourceChannel());
//                            }
//                            //短信状态先给一个中间状态
//                            if (MessageTypeEnum.SMS.getCode() == touchRuleContentList.get(i).getSendRuleChannel()) {
//                                marketingTaskTouchDetailCreateDto.setStatus(MarketingTaskExecuteStatusEnum.WAIT.getValue());
//                            } else {
//                                marketingTaskTouchDetailCreateDto.setStatus(taskContent.getExecuteStatus() ? MarketingTaskExecuteStatusEnum.SUCCESS.getValue() : MarketingTaskExecuteStatusEnum.FAILURE.getValue());
//                            }
//                            marketingTaskTouchDetailCreateDto.setTaskType(taskContent.getTaskType());
//                            marketingTaskTouchDetailCreateDto.setMappingId(taskContent.getTaskMappingId());
//                            marketingTaskTouchDetailCreateDto.setTaskId(taskContent.getTaskId());
//
//                            marketingTaskTouchDetailCreateDto.setSendRuleChannel(touchRuleContent.getSendRuleChannel());
//                            marketingTaskTouchDetailCreateDto.setSendRuleType(touchRuleContent.getSendRuleType());
//                            marketingTaskTouchDetailCreateDto.setSendTemplateId(touchRuleContent.getSendTemplateId());
//
//                            //记录触达明细
//                            //mysql消费标记
//                            marketingTaskTouchDetailCreateDto.setRecord(taskSendDataDto.getTenantId() + "_" + taskContent.getTaskType() + "_" + taskContent.getTaskId() + "_" + memberContent.getMemberId() + "_" + touchRuleContent.getSendRuleChannel());
//                            marketingTaskTouchDetailService.create(marketingTaskTouchDetailCreateDto);
//
//
//                            //转换正在执行的触达规则
//                            touchRuleContentList.get(i).setRuleExecuteStatus(false);
//                            // 记录触达人数
//                            RedisService redisService = SpringContextHolder.getBean("redisService");
//
//                            //redis消费记录
//                            List<String> memberIds = new ArrayList<>();
//                            memberIds.add(memberContent.getMemberId().toString());
//
//
//                            //任务失败的时候降级
//                            if (taskContent.getExecuteStatus()) {
//                                try {
//                                    redisService.incr(taskSendDataDto.getRedisConsumeMemberNums(), 1L);
//                                    redisService.sadd( RedisPrefixEnum.TASK_RECORD.getKey() + ":" + taskSendDataDto.getTenantId() + "_" + taskContent.getTaskType() + "_" + taskContent.getTaskId(), memberIds);
//                                } catch (Exception e) {
//                                    log.info("主动任务记录消费失败：{}", taskSendDataDto);
//                                }
//                                //埋点
//                                try {
//                                    buryPoint(taskSendDataDto.getTenantId(), memberContent.getMemberId(), taskContent.getTaskTypeShortName(), taskContent.getTaskMappingId());
//                                } catch (Exception e) {
//                                    log.info("主动任务触达埋点失败：{}", taskSendDataDto);
//                                }
//                            } else if (!taskContent.getExecuteStatus()) {
//                                //当前规则排序
//                                Integer currentSort = touchRuleContentList.get(i).getSort();
//
//                                //该规则不为最后一条降级,转换下一条执行的规则状态
//                                if (currentSort < Collections.max(sortList)) {
//                                    Integer nextSort = touchRuleContentList.get(i + 1).getSort();
//                                    if (nextSort == currentSort + 1) {
//                                        touchRuleContentList.get(i + 1).setRuleExecuteStatus(true);
//                                        //修改任务缩写
//                                        taskSendDataDto.getTaskContent().setTaskTypeShortName(TaskShortNameEnum.getValueById(touchRuleContentList.get(i + 1).getSendRuleChannel()));
//                                    }
//                                    //数据封装器
//                                    MetaDataHandler marketingTaskListener = (MetaDataHandler) MarketingTaskListenerFactory
//                                            .getMetaDataHandlerListener(taskSendDataDto.getTagName());
//                                    SendMetaDataProducer producer = SpringContextHolder.getBean(SendMetaDataProducer.class);
//                                    producer.sendMQ(marketingTaskListener, taskSendDataDto, rocketProducerTemplate, taskSendDataDto.getTagName(), true);
//                                    break;
//                                } else {
//                                    try {
//                                        redisService.incr(taskSendDataDto.getRedisConsumeMemberNums(), 1L);
//                                        String key = RedisPrefixEnum.TASK_RECORD.getKey() + ":" + taskSendDataDto.getTenantId() + "_" + taskContent.getTaskType() + "_" + taskContent.getTaskId();
//                                        log.info("redis存入consumeRecord, key{}", key);
//                                        redisService.sadd(key, memberIds.toArray());
//                                    } catch (Exception e) {
//                                        log.info("主动任务记录消费失败：{}", taskSendDataDto);
//                                    }
//                                }
//                            }
//                        }
//                    }
//                }
//
//            } catch (Throwable t) {
//                log.error("RocketMQ Consume Throwable", t);
//            }
//        }
//
//        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
//    }
//
//    /**
//     * DM主动添加埋点事件
//     * @param tenantId
//     * @param memberId
//     * @param touchType
//     * @param activityId
//     */
//    private void buryPoint(String tenantId, Long memberId, String touchType, Long activityId) {
//        BuryPointEvent buryPointEvent = new BuryPointEvent();
//        buryPointEvent.setTenantId(tenantId);
//        buryPointEvent.setOneId(memberId.toString());
//        buryPointEvent.setType("activity_touch");
//        buryPointEvent.setTime(System.currentTimeMillis());
//        buryPointEvent.setSystemId("dxp-domamin-marketing");
//        buryPointEvent.setAccType("member");
//        buryPointEvent.setChannel("wx".equals(touchType) ? "weixin" : "H5");
//        Map args = new HashMap<>();
//        args.put("activity_id", activityId.toString());
//        args.put("task_type", MarketingTaskTypeEnum.INITIATIVE.getValue());
//        //微信wx 短信sms
//        args.put("touch_type", "wx".equals(touchType) ? "wx_tpl" : touchType);
//        buryPointEvent.setArgs(args);
//        buryPointService.buryPoint(buryPointEvent);
//    }
//
//}
//
