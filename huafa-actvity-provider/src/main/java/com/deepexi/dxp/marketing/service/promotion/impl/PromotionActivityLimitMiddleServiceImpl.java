//package com.deepexi.dxp.marketing.service.promotion.impl;
//
//import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
//import com.deepexi.dxp.marketing.domain.promotion.dto.activity.PromotionActivityLimitFindVO;
//import com.deepexi.dxp.marketing.domain.promotion.dto.activity.PromotionActivityListPostVO;
//import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityConfigDTO;
//import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityLimitQuery;
//import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityQuery;
//import com.deepexi.dxp.middle.promotion.common.base.SuperEntity;
//import com.deepexi.dxp.middle.promotion.converter.ActivityConfigConverter;
//import com.deepexi.dxp.middle.promotion.converter.PromotionActivityListPostResponseDtoConverter;
//import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
//import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityLimitDO;
//import com.deepexi.dxp.middle.promotion.domain.entity.PromotionStrategyDO;
//import com.deepexi.dxp.middle.promotion.mapper.PromotionActivityLimitMapper;
//import com.deepexi.dxp.middle.promotion.mapper.PromotionActivityMapper;
//import com.deepexi.dxp.middle.promotion.mapper.PromotionStrategyMapper;
//import com.deepexi.dxp.marketing.service.promotion.PromotionActivityLimitMiddleService;
//import com.deepexi.util.CollectionUtil;
//import com.deepexi.util.pojo.CloneDirection;
//import com.deepexi.util.pojo.ObjectCloneUtils;
//import com.google.common.collect.Lists;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//import java.util.Objects;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR> xinjian.yao
// * @date 2020/3/11 15:58
// */
//@Service
//public class PromotionActivityLimitMiddleServiceImpl implements PromotionActivityLimitMiddleService {
//
//
//    @Autowired
//    private PromotionActivityLimitMapper promotionActivityLimitMapper;
//    @Autowired
//    private PromotionActivityMapper promotionActivityMapper;
//    @Autowired
//    private PromotionStrategyMapper promotionStrategyMapper;
//
//    @Override
//    public List<PromotionActivityLimitFindVO> findPage(PromotionActivityLimitQuery query) {
//
//        PromotionActivityQuery queryClone = query.clone(PromotionActivityQuery.class, CloneDirection.FORWARD);
//        queryClone.setStatusList(query.getActivityStatusList());
//        queryClone.setPaTemplateList(query.getPaTemplateList());
//        List<PromotionActivityDO> all = promotionActivityMapper.findAll(queryClone);
//        // 获取活动下的策略信息并以活动分组
//        Map<Long, List<PromotionStrategyDO>> activityStrategyMap = getAllPromotionStrategyList(all);
//        // 获取活动下的限制信息以活动分组
//        Map<Long, List<PromotionActivityLimitDO>> activityLimitMap = getAllPromotionActivityLimitList(all);
//
//        //存储返回的结果
//        List<PromotionActivityDO> resultListDTO = new ArrayList<>();
//
//        all.forEach(activity -> {
//            // 组装一个活动的配置信息
//            ActivityConfigDTO activityConfig = getActivityConfig(activity, activityStrategyMap, activityLimitMap);
//            // 获取用户限制
//            List<BaseActivityDTO> userLimitList = activityConfig.getUserLimit();
//            List<Boolean> matchResultList = new ArrayList<>();
//            if (!Objects.isNull(query.getUserLimit()) && CollectionUtil.isNotEmpty(userLimitList)) {
//                boolean result = userLimitList.stream()
//                        .anyMatch(userLimit -> userLimit.getId().equals(query.getUserLimit()));
//                matchResultList.add(result);
//            }
//            // 领券活动会员领取方式过滤
//            if (!Objects.isNull(query.getCouponLimit()) && CollectionUtil.isNotEmpty(activityConfig.getActivityRuleDTOList())) {
//                activityConfig.getActivityRuleDTOList().forEach(rule -> {
//                    List<BaseActivityDTO> conditionList = rule.getCondition();
//                    if (CollectionUtil.isEmpty(conditionList)) {
//                        return;
//                    }
//                    boolean result = conditionList.stream().anyMatch(condition -> condition.getId().equals(query.getCouponLimit()));
//                    matchResultList.add(result);
//                });
//            }
//
//            boolean matchResult = matchResultList.stream().anyMatch(val -> val.equals(Boolean.FALSE));
//            if (matchResult) {
//                return;
//            }
//            resultListDTO.add(activity);
//        });
//        List<PromotionActivityListPostVO> resultDTOList = new ArrayList<>();
//        resultListDTO.forEach(promotionActivityDo -> {
//            PromotionActivityListPostVO dto1 = transform(promotionActivityDo, activityStrategyMap, activityLimitMap);
//            resultDTOList.add(dto1);
//        });
//
//        return ObjectCloneUtils.convertList(resultDTOList, PromotionActivityLimitFindVO.class, CloneDirection.OPPOSITE);
//    }
//
//    private PromotionActivityListPostVO transform(PromotionActivityDO promotionActivityDo,
//                                                  Map<Long, List<PromotionStrategyDO>> activityStrategyMap,
//                                                  Map<Long, List<PromotionActivityLimitDO>> activityLimitMap) {
//        //组装limit
//        List<PromotionActivityLimitDO> limitDoList = activityLimitMap.get(promotionActivityDo.getId());
//        //组装strategy
//        List<PromotionStrategyDO> strategyDoList = activityStrategyMap.get(promotionActivityDo.getId());
//
//        return PromotionActivityListPostResponseDtoConverter
//                .converter(promotionActivityDo, strategyDoList, limitDoList);
//    }
//
//    private ActivityConfigDTO getActivityConfig(PromotionActivityDO promotionActivityDo,
//                                                Map<Long, List<PromotionStrategyDO>> activityStrategyMap,
//                                                Map<Long, List<PromotionActivityLimitDO>> activityLimitMap) {
//
//        Long activityId = promotionActivityDo.getId();
//        List<PromotionStrategyDO> strategyList = activityStrategyMap.get(activityId);
//        List<PromotionActivityLimitDO> limitList = activityLimitMap.get(activityId);
//        return ActivityConfigConverter.converter(promotionActivityDo, strategyList, limitList);
//    }
//
//    private Map<Long, List<PromotionStrategyDO>> getAllPromotionStrategyList(List<PromotionActivityDO> promotionActivityDOList) {
//        List<Long> collect = promotionActivityDOList.stream()
//                .map(SuperEntity::getId)
//                .distinct()
//                .collect(Collectors.toList());
//
//        List<PromotionStrategyDO> doList = promotionStrategyMapper.selectAllByActivityIdList(collect);
//        return doList.stream()
//                .collect(Collectors.groupingBy(PromotionStrategyDO::getActivityId));
//
//
//    }
//
//    private Map<Long, List<PromotionActivityLimitDO>> getAllPromotionActivityLimitList(List<PromotionActivityDO> promotionActivityDOList) {
//        List<Long> collect = promotionActivityDOList.stream()
//                .map(SuperEntity::getId)
//                .distinct()
//                .collect(Collectors.toList());
//        List<PromotionActivityLimitDO> doList = Lists.newArrayList();
//        if(CollectionUtil.isNotEmpty(collect)){
//            doList = promotionActivityLimitMapper.selectAllByActivityIdList(collect);
//        }
//
//        return doList.stream()
//                .collect(Collectors.groupingBy(PromotionActivityLimitDO::getActivityId));
//    }
//}
