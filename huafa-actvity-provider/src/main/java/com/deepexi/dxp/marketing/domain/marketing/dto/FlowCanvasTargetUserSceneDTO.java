package com.deepexi.dxp.marketing.domain.marketing.dto;
import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import lombok.Data;


/**
 * 流程画布-圈人配置-场景
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-07 16:03
 */
@Data
public class FlowCanvasTargetUserSceneDTO extends SuperDTO {

    /**
     * 流程画布圈选用户配置表ID
     */
    private Long targetUserId;

    /**
     * 场景ID
     */
    private Long sceneId;

    /**
     * 场景分类：1：交易场景，2：权益场景，3、服务场景
     */
    private Integer sceneType;

    /**
     * 场景名称
     */
    private String sceneName;

    public FlowCanvasTargetUserSceneDTO(){}

    public FlowCanvasTargetUserSceneDTO(Long sceneId, Integer sceneType, String sceneName){
        this.sceneId = sceneId;
        this.sceneType = sceneType;
        this.sceneName = sceneName;
    }
}
