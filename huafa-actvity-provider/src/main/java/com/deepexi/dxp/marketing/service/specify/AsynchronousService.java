package com.deepexi.dxp.marketing.service.specify;

import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityOrderDO;

/**
 * <AUTHOR>
 */
public interface AsynchronousService {
    void sensorsBuriedPoint(ActivityOrderDO activityOrderDO);

    void sensorsBuriedPointFission(Long id, String eventName);

    void projectIsJumpable(Long id);

    void asyncCacheNewActInfo(Long activityId);

}
