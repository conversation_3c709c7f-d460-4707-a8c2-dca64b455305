package com.deepexi.dxp.marketing.controller.specify.adminapi;

import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.service.specify.CacheService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/admin-api/v1/cache")
@Api(value = "缓存相关接口", description = "缓存相关接口", tags = {"缓存相关接口"})
public class CacheController {

    @Autowired
    private CacheService cacheService;

    @GetMapping("/updateCacheActInfo")
    @ApiOperation(value="更新所有进行中的活动缓存", notes = "更新所有进行中的活动缓存")
    public Data<Object> updateCacheActInfo(Long id) {
        return new Data<>(cacheService.updateCacheActInfo(id));
    }

}
