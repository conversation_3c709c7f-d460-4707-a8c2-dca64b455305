package com.deepexi.dxp.marketing.service.specify.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.deepexi.dxp.marketing.common.base.utils.CommonExceptionCode;
import com.deepexi.dxp.marketing.config.HuafaConstantConfig;
import com.deepexi.dxp.marketing.constant.MkPermitApiConstant;
import com.deepexi.dxp.marketing.constant.PayConstant;
import com.deepexi.dxp.marketing.domain.marketing.response.CommonOrgResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.CommonUserInfoResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.MenuPermissionDTO;
import com.deepexi.dxp.marketing.service.specify.CommonSystemService;
import com.deepexi.dxp.marketing.utils.HTTPClientUtils;
import com.deepexi.dxp.marketing.utils.HuaFaHmacAuthUtil;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.StringUtil;
import com.deepexi.util.exception.ApplicationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CommonSystemServiceImpl implements CommonSystemService {

    @Autowired
    private HuaFaHmacAuthUtil huaFaHmacAuthUtil;

    @Autowired
    private HuafaConstantConfig huafaConstantConfig;

    @Override
    public List<CommonOrgResponseDTO> getOrg(String userId) {
        Map<String, Object> map = new HashMap<>();
        map.put("userId",userId);
        try{
            String result = huaFaHmacAuthUtil.request(0,1,MkPermitApiConstant.ORG_GET_URL,map);
            log.info("用户组织查询结果:{}",result);
            JSONObject response = JSONUtil.parseObj(result);
            String code = response.getStr("code");
            if(!CommonExceptionCode.SUCCESS.equals(code)) {
                throw new ApplicationException(response.getStr("msg"));
            }
            JSONArray data = response.getJSONArray("data");
            List<CommonOrgResponseDTO> commonOrgResponseDTOS = data.toList(CommonOrgResponseDTO.class);
            return commonOrgResponseDTOS;
        }catch(Exception e){
            log.error("获取用户组织异常",e);
            throw new ApplicationException(e.getMessage());
        }
    }

    @Override
    public JSONObject getOrgTree(String orgIds) {
        try{
            String url = huafaConstantConfig.PAY_BASE_URL+"/"+PayConstant.ORG_TREE_URL+"?orgIds="+orgIds;
            String result = HTTPClientUtils.get(url);
            log.info("组织权限树列表查询结果:{}",result);
            JSONObject response = JSONUtil.parseObj(result);
            return response;
        }catch(Exception e){
            log.error("获取组织权限树列表异常");
            throw new ApplicationException("获取组织权限树列表异常.");
        }
    }

    @Override
    public JSONObject getOrgTreeByUserAlias(String userId) {
        List<CommonOrgResponseDTO> list = this.getOrg(userId);
        if(CollectionUtil.isNotEmpty(list)){
            String orgIdstr = list.stream().filter(item -> StringUtil.isNotEmpty(item.getOrgId()) && StringUtil.isNotEmpty(item.getOrgId().trim())).map(CommonOrgResponseDTO::getOrgId).distinct().collect(Collectors.joining(","));
            return StringUtil.isNotEmpty(orgIdstr) ? this.getOrgTree(orgIdstr) : null;
        }
        return null;
    }

    @Override
    public CommonUserInfoResponseDTO.CommonUserInfoDTO getUserInfo(String userId) {

        Map<String, Object> map = new HashMap<>();
        map.put("userId",userId);
//        headers.put("Content-Type","application/x-www-form-urlencoded");
//        headers.put("Accept","application/json;charset=UTF-8");
//        headers.put("Authorization",authorization);
        //String url = MkPermitApiConstant.BASE_URL+MkPermitApiConstant.USER_INFO_URL+"?userAlias="+userAlias;
        try{
            String result = huaFaHmacAuthUtil.request(0,1,MkPermitApiConstant.USER_INFO_URL,map);
            //String result = HTTPClientUtils.get(url,null,headers);
            log.info("用户信息查询结果:{}",result);

            CommonUserInfoResponseDTO commonUserInfoResponseDTO = JSONUtil.toBean(result, CommonUserInfoResponseDTO.class);
            if(Objects.nonNull(commonUserInfoResponseDTO) && "200".equals(commonUserInfoResponseDTO.getCode())){
               return commonUserInfoResponseDTO.getData();
            }
        }catch(Exception e){
            log.error("获取用户信息异常",e);
            //throw new ApplicationException(e.getMessage());
        }
        return null;
    }

    @Override
    public CommonUserInfoResponseDTO.CommonUserInfoDTO getUserInfoByUserId(String userId) {
        Map<String, Object> map = new HashMap<>();
        map.put("userId",userId);
        try{
            String result = huaFaHmacAuthUtil.request(0,1,MkPermitApiConstant.USER_INFO_NO_LOGIN,map);
            log.info("免登录获取用户信息查询结果:{}",result);
            CommonUserInfoResponseDTO commonUserInfoResponseDTO = JSONUtil.toBean(result, CommonUserInfoResponseDTO.class);
            if(Objects.nonNull(commonUserInfoResponseDTO) && "200".equals(commonUserInfoResponseDTO.getCode())){
                return commonUserInfoResponseDTO.getData();
            }
        }catch(Exception e){
            log.error("免登录获取用户信息异常",e);
        }
        return null;
    }

    @Override
    public List<MenuPermissionDTO> getUserAllMenuPermission(String userId, String clientCode, String menuId) {
        Map<String, Object> map = new HashMap<>();
        map.put("userId",userId);
        map.put("clientCode",clientCode);
        map.put("menuId",menuId);
        try{
            String result = huaFaHmacAuthUtil.request(0,1,MkPermitApiConstant.MENU_PERMISSION_GET_URL,map);
            log.info("用户所有菜单权限查询结果:{}",result);
            JSONObject response = JSONUtil.parseObj(result);
            String code = response.getStr("code");
            if(!CommonExceptionCode.SUCCESS.equals(code)) {
                throw new ApplicationException(response.getStr("msg"));
            }
            JSONArray data = response.getJSONArray("data");
            List<MenuPermissionDTO> commonOrgResponseDTOS = data.toList(MenuPermissionDTO.class);
            return commonOrgResponseDTOS;
        }catch(Exception e){
            log.error("用户所有菜单权限查询异常",e);
            throw new ApplicationException(e.getMessage());
        }
    }


}
