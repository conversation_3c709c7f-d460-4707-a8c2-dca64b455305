package com.deepexi.dxp.marketing.domain.marketing.query;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @description：
 * @author: ch<PERSON><PERSON><PERSON>
 * @version: 1.0.0
 * @date: 2021-03-26 19:44
 */
@Data
public class MarketingActiveCountNumsQuery extends SuperQuery {

    @ApiModelProperty("开始时间")
    private Date startTime;

    @ApiModelProperty("结束时间")
    private Date endTime;


}
