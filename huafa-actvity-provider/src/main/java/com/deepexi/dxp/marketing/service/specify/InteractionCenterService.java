package com.deepexi.dxp.marketing.service.specify;

import com.deepexi.dxp.marketing.domain.marketing.request.specify.MessageInformRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.SendTemplateNewsRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.TemplateSendRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.WxTemplateSendRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.InteractiveResponseDTO;

/**
 * 交互中心
 * <AUTHOR>
 */
public interface InteractionCenterService {

    /**
     * 模板短信发送接口
     * @param requestDTO
     * @return
     */
    void templateSend(TemplateSendRequestDTO requestDTO);

    /**
     * 小程序模板消息发送
     * @param dto
     * @return
     */
    InteractiveResponseDTO miniTemplateNews(SendTemplateNewsRequestDTO dto);

}
