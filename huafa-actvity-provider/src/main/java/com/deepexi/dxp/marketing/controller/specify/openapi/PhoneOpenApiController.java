package com.deepexi.dxp.marketing.controller.specify.openapi;

import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.service.specify.IncentiveService;
import com.deepexi.dxp.marketing.service.specify.PhoneService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 手机号码登录注册controller
 *
 * <AUTHOR>
 * @date  2019/10/24 14:54
 * @see [相关类/方法]
 * @since 1.0
 */
@RestController
@RequestMapping("/open-api/v1/phone")
@Api(value = "移动端-手机验证码模块-",tags = {"移动端-手机验证码模块"})
public class PhoneOpenApiController {

    @Resource
    private PhoneService phoneService;
    @Resource
    private IncentiveService incentiveService;
    /**
     * 发送短信验证码
     * phone参数是AES加密串
     */
    @ApiOperation("发送短信验证码")
    @PostMapping("smsCode")
    public Data<Boolean> sendSmsCode(@RequestParam String phone,@RequestParam String smsCode) {
        return new Data<>(phoneService.sendSmsCode(phone,smsCode));
    }

    /**
     * 校验手机验证码
     * @param phone 手机号
     * @param smsCode 手机验证码
     * @return 结果
     */
    @ApiOperation("手机验证码校验")
    @PostMapping("checkSmsCode")
    public Data<Boolean> checkSmsCode(@RequestParam String phone,@RequestParam String smsCode) {
        return new Data<>(phoneService.checkSmsCode(phone,smsCode));
    }
}
