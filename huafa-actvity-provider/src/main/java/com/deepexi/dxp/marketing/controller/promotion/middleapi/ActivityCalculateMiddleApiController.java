//package com.deepexi.dxp.marketing.controller.promotion.middleapi;
//
//import com.deepexi.dxp.marketing.api.promotion.ActivityCalculateMiddleApi;
//import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
//import com.deepexi.dxp.marketing.domain.promotion.dto.activity.CommodityActivityVO;
//import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.*;
//import com.deepexi.dxp.marketing.domain.promotion.dto.coupon.PromotionCouponLoggerListPostResponseDTO;
//import com.deepexi.dxp.marketing.domain.promotion.request.activity.CommodityActivityRequest;
//import com.deepexi.dxp.marketing.domain.promotion.request.calculate.*;
//import com.deepexi.dxp.marketing.service.promotion.ActivityCalculateMiddleService;
//import com.deepexi.util.config.Payload;
//import com.deepexi.util.pageHelper.PageBean;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.util.List;
//
///**
// * <AUTHOR> xinjian.yao
// * @date 2019/11/26 17:34
// */
//@RestController
//@Slf4j
//@Api(value = "活动计算", tags = "活动计算相关接口")
//public class ActivityCalculateMiddleApiController implements ActivityCalculateMiddleApi {
//
//    @Autowired
//    private ActivityCalculateMiddleService activityCalculateMiddleService;
//
//
//    @Override
//    @ApiOperation(value = "活动下的商品信息")
//    public Payload<List<ActivityConfigDTO>> getCommodityByActivityId(@RequestBody List<Long> activityIds) {
//
//        return new Payload<>(activityCalculateMiddleService.getCommodityByActivityId(activityIds));
//    }
//
//    @Override
//    @ApiOperation(value = "商品可参与活动")
//    public Payload<CommodityActivityVO> commodityActivity(@RequestBody CommodityActivityRequest commodityActivityRequest) {
//        return new Payload<>(activityCalculateMiddleService.commodityActivity(commodityActivityRequest));
//    }
//
//    @Override
//    @ApiOperation(value = "多个商品可参与活动")
//    public Payload<List<CommodityActivityVO>> commodityListActivity(@RequestBody CommodityListActivityRequest commodityListActivityRequest) {
//        return new Payload<>(activityCalculateMiddleService.commodityListActivity(commodityListActivityRequest));
//    }
//
//    @Override
//    @ApiOperation(value = "购物车计算金额")
//    public Payload<ShoppingCartVO> shoppingCart(@RequestBody ActivityParamsRequest activityParamsRequest) {
//        return new Payload<>(activityCalculateMiddleService.calculate(activityParamsRequest));
//    }
//
//    @Override
//    @ApiOperation(value = "购物车计算（门店维度）")
//    public Payload<ShoppingCartShopAspectVO> shoppingCartShopAspect(@RequestBody ActivityParamsRequest activityParamsRequest) {
//        return new Payload<>(activityCalculateMiddleService.shoppingCartShopAspect(activityParamsRequest));
//    }
//
//    @Override
//    @ApiOperation(value = "订单计算")
//    public Payload<OrderEditResponseDTO> orderCalculate(@RequestBody ActivityOrderParamsRequest activityRequestParams) {
//        return new Payload<>(activityCalculateMiddleService.orderCalculate(activityRequestParams));
//    }
//
//    @Override
//    @ApiOperation(value = "积分兑换活动")
//    public Payload<PageBean<ActivityResponseParamsDTO>> showJfdh(@RequestBody ActivityParamsRequest activityParamsRequest,
//                                                                 @RequestParam(value = "page") Integer page,
//                                                                 @RequestParam(value = "size") Integer size) {
//        return new Payload<>(activityCalculateMiddleService.showJFDH(activityParamsRequest, page, size));
//    }
//
//    @Override
//    @ApiOperation(value = "进行积分兑换商品")
//    public Payload<ActivityResponseParamsDTO> exchange(@RequestBody ActivityParamsRequest activityRequestParams) {
//        return new Payload<>(activityCalculateMiddleService.calculateJFDH(activityRequestParams));
//    }
//
//    @Override
//    @ApiOperation(value = "查询优惠券列表")
//    public Payload<PageBean<CouponResponseDTO>> couponList(@RequestBody ActivityParamsRequest activityParamsRequest,
//                                                           @RequestParam(value = "page") Integer page,
//                                                           @RequestParam(value = "size") Integer size) {
//        return new Payload<>(activityCalculateMiddleService.couponList(activityParamsRequest, page, size));
//    }
//
//    @Override
//    @ApiOperation(value = "用户可用优惠券列表")
//    public Payload<PageBean<PromotionCouponLoggerListPostResponseDTO>> usableCouponList(@RequestBody ActivityParamsRequest activityParamsRequest) {
//        return new Payload<>(activityCalculateMiddleService.usableCouponList(activityParamsRequest));
//    }
//
//    @Override
//    @ApiOperation(value = "获取购物券")
//    public Payload<BaseActivityDTO> getCoupon(@RequestBody CouponActivityParamsRequest activityRequestParams) {
//        return new Payload<>(activityCalculateMiddleService.getCoupon(activityRequestParams));
//    }
//
//    @Override
//    @ApiOperation(value = "积分兑换次数活动计算", tags = "积分兑换次数活动计算")
//    public Payload<Integer> getJFDHTimes(@RequestParam Long appId,
//                                         @RequestParam String tenantId,
//                                         @RequestParam Long userId,
//                                         @RequestParam String userType) {
//        return new Payload<>(activityCalculateMiddleService.getJFDHTimes(appId, tenantId, userId, userType));
//    }
//}
