package com.deepexi.dxp.marketing.domain.merber.dto;

import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;


/**
 * SdkSetMobileMemberCountRequestDTO
 *
 * <AUTHOR>
 * @Date 2020/7/27
 */
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class SdkMemberPageDTO extends SuperDTO {

    @ApiModelProperty("创建开始时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createdTimeFrom;

    @ApiModelProperty("创建结束时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createdTimeTo;

    @ApiModelProperty("更新开始时间")
    private String updatedTimeFrom;

    @ApiModelProperty("更新结束时间")
    private String updatedTimeTo;

}
