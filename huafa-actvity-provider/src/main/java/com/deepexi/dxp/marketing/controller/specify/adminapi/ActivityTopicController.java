package com.deepexi.dxp.marketing.controller.specify.adminapi;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityAnalysisQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityGroupQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityTopicRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.*;
import com.deepexi.dxp.marketing.enums.specify.ActivityGroupEnum;
import com.deepexi.dxp.marketing.extension.HuafaRuntimeEnv;
import com.deepexi.dxp.marketing.service.specify.ActivityGroupService;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Objects;

/**
 * 活动专题模块controller
 *
 * <AUTHOR>
 * @date  2019/10/24 14:54
 * @see [相关类/方法]
 * @since 1.0
 */
@RestController
@RequestMapping("/admin-api/v1/topic")
@Api(value = "活动专题模块-",tags = {"活动专题模块"})
public class ActivityTopicController {

    @Resource
    private ActivityGroupService activityGroupService;

    @PostMapping("/ActivityAndActivityGroupList")
    @ApiOperation(value = "活动和活动组列表查询", notes = "活动和活动组列表查询")
    public Data<PageBean<ActivityAndActivityGroupResponseDTO>> ActivityAndActivityGroupList(@RequestBody ActivityGroupQuery query) {
        return new Data<>(activityGroupService.activityAndActivityGroupList(query));
    }

    @PostMapping("/save")
    @ApiOperation(value = "活动专题新增", notes = "活动专题新增")
    public Data<Boolean> save(@RequestBody @Valid ActivityTopicRequestDTO requestDTO) {
        if (Objects.isNull(requestDTO)){
            throw new ApplicationException("入参对象不能为空");
        }
        return new Data<>(activityGroupService.saveTopic(requestDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "活动专题编辑", notes = "活动组编辑")
    public Data<Boolean> update(@RequestBody @Valid ActivityTopicRequestDTO requestDTO) {
        if (Objects.isNull(requestDTO)){
            throw new ApplicationException("入参对象不能为空");
        }
        return new Data<>(activityGroupService.updateTopic(requestDTO));
    }


    @PostMapping("/pageList")
    @ApiOperation(value = "活动专题列表", notes = "活动组列表")
    public Data<PageBean<ActivityGroupResponseDTO>> pageList(@RequestBody ActivityGroupQuery query) {
        query.setType(ActivityGroupEnum.TOPIC.getId());
        query.setUserId(HuafaRuntimeEnv.getUserId());
        return new Data<>(activityGroupService.pageList(query));
    }


    @PostMapping("/detail")
    @ApiOperation(value = "活动专题详情", notes = "活动组详情")
    public Data<ActivityGroupResponseDTO> detail(@RequestParam Long id) {
        return new Data<>(activityGroupService.detail(id));
    }


    @PostMapping("/activityList")
    @ApiOperation(value = "活动专题详情-选择活动列表", notes = "选择活动列表")
    public Data<PageBean<ActivityGroupRelatedResponseDTO>> activityList(@RequestBody ActivityGroupQuery query) {
        return new Data<>(activityGroupService.activityList(query));
    }


    @DeleteMapping("/delete")
    @ApiOperation(value = "活动专题-删除", notes = "活动专题-删除")
    public Data<Boolean> delete(@RequestParam Long id) {
        return new Data<>(activityGroupService.delete(id));
    }

    @PostMapping("/analysisOverview")
    @ApiOperation(value = "活动专题分析-数据概览", notes = "活动专题分析-数据概览")
    public Data<ActivityTopicAnalysisResponseDTO> analysisOverview(@RequestParam Long id) {
        return new Data<>(activityGroupService.analysisOverview(id));
    }

    @PostMapping("/analysisActivityInfo")
    @ApiOperation(value = "活动专题分析-活动明细", notes = "活动专题分析-活动明细")
    public Data<PageBean<ActivityTopicAnalysisActivityInfoResponseDTO>> analysisActivityInfo(@RequestBody ActivityAnalysisQuery query) {
        return new Data<>(activityGroupService.analysisActivityInfo(query));
    }


    @PostMapping("/analysisResourceInfo")
    @ApiOperation(value = "活动专题分析-资源数据", notes = "活动专题分析-资源数据")
    public Data<PageBean<ActivityTopicAnalysisResourceResponseDTO>> analysisResourceInfo(@RequestBody ActivityAnalysisQuery query) {
        return new Data<>(activityGroupService.analysisResourceInfo(query));
    }

}
