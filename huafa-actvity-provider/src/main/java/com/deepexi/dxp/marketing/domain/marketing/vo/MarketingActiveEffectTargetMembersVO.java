package com.deepexi.dxp.marketing.domain.marketing.vo;


import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 营销任务受众目标VO
 * @Author: HuangBo.
 * @Date: 2020/3/13 17:18
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MarketingActiveEffectTargetMembersVO extends SuperVO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "templateId")
    private Long templateId;

    /**
     * 主动营销任务
     */
    @ApiModelProperty(value = "主动营销任务ID")
    private Long activeId;


    /**
     * 是否全部用户(0:否，1:是)
     */
    @ApiModelProperty(value = "是否选取全部用户(0:否，1:是)")
    @NotNull(message = "是否选取全部用户必填")
    private boolean allUsers;

    /**
     * 模型精选(0:不精选，1:模型单独精选，2:模型综合精选)
     */
    @ApiModelProperty(value = "模型精选(0:不精选，1:单个模型结果分别筛选，2:多模型综合结果筛选)")
    @NotNull(message = "模型精选必填")
    private int byModel;

    /**
     * 筛选后预估人数
     */
    @ApiModelProperty(value = "筛选后预估人数")
    private int exactModelNums;

    /**
     * 营销任务投放标签客群
     */
    private List<MarketingActiveTargetTagsVO> groupTagVOList;

    /**
     * 客户信息列表
     */
    private List<MarketingActiveTargetUsersVO> membersInfoVOList;

    /**
     * 模型信息列表
     */
    private List<MarketingActiveTargetModelVO> modelVOList;

    /**
     * 资源id
     */
    private Long resourceId;

    /**
     * 资源类型
     */
    private Integer resourceType;

    /**
     * 资源值
     */
    private Double resourceValue;
}
