package com.deepexi.dxp.marketing.domain.marketing.dto;
import com.baomidou.mybatisplus.annotation.TableField;
import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import lombok.Data;


/**
 * 流程画布-圈人配置
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-07 16:03
 */
@Data
public class FlowCanvasTargetUserDTO extends SuperDTO {

    /**
     * 画布ID
     */
    private Long flowId;

    /**
     * 画布节点ID
     */
    @TableField("node_id")
    private String nodeId;

    /**
     * 类型：1.综合选择 2.其他
     */
    private Integer type;

    /**
     * 是否全部用户(0:否，1:是)
     */
    private Boolean allUser;

    /**
     * 是否选择会员(0:否，1:是)
     */
    private Boolean byMembers;

    /**
     * 是否选择游客(0:否，1:是)
     */
    private Boolean byVisitor;
    /**
     * 是否有按标签选取(0:否，1:是)
     */
    private Boolean byTags;

    /**
     * 是否有按客群选取(0:否，1:是)
     */
    private Boolean byGroups;
    /**
     * 是否按模型精选(0:否，1:是)
     */
    private Boolean byModel;
    /**
     * 是否选择场景：0：否 1：是'
     */
    private Boolean byScene;

    /**
     * 模型综合精选人数
     */
    private Integer exactModelNums;


    public FlowCanvasTargetUserDTO(){

    }

    public FlowCanvasTargetUserDTO(String nodeId, Integer type, Boolean allUser, Boolean byMembers, Boolean byVisitor, Boolean byTags, Boolean byGroups, Boolean byModel, Boolean byScene){
        this.nodeId = nodeId;
        this.type = type;
        this.allUser = allUser;
        this.byMembers = byMembers;
        this.byVisitor = byVisitor;
        this.byTags = byTags;
        this.byGroups = byGroups;
        this.byModel = byModel;
        this.byScene = byScene;
    }
}
