package com.deepexi.dxp.marketing.service.specify.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.deepexi.dxp.marketing.config.HuafaConstantConfig;
import com.deepexi.dxp.marketing.constant.SensorsConstant;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityAnalysisQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.EventsReportQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.EventsReportSqlQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.SensorsBuriedPointDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.*;
import com.deepexi.dxp.marketing.enums.specify.ActivityAnalysisDateTypeEnum;
import com.deepexi.dxp.marketing.service.specify.SensorsService;
import com.deepexi.dxp.marketing.utils.HTTPClientUtils;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityPromotionChannelDO;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.DateUtils;
import com.deepexi.util.JsonUtil;
import com.deepexi.util.StringUtil;
import com.deepexi.util.exception.ApplicationException;
import com.google.common.collect.Lists;
import com.sensorsdata.analytics.javasdk.SensorsAnalytics;
import com.sensorsdata.analytics.javasdk.bean.EventRecord;
import com.sensorsdata.analytics.javasdk.bean.SuperPropertiesRecord;
import com.sensorsdata.analytics.javasdk.consumer.BatchConsumer;
import com.sensorsdata.analytics.javasdk.exceptions.InvalidArgumentException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.shaded.com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SensorsServiceImpl implements SensorsService {

    @Autowired
    private HuafaConstantConfig huafaConstantConfig;
    @Override
    public Long findActivityFrequencySqlQuery(EventsReportQuery query) {
        String sql = "select count(1) as quantity from events where event = '" +query.getEventName()+ "'";
        if (StringUtil.isNotBlank(query.getSqlIn())){
            sql = sql + " and activity_id in ("+query.getSqlIn() +")";
        }
        sql = sql + addSql();
        return getQuantity(sql);
    }

    @Override
    public Long findActivityPeopleSqlQuery(EventsReportQuery query) {
        String sql = "select count(DISTINCT distinct_id) as quantity from events where event = '" +query.getEventName()+ "' ";
        if (StringUtil.isNotBlank(query.getSqlIn())){
            sql = sql + "and activity_id in ("+query.getSqlIn() +")";
        }
        sql = sql + addSql();
        return getQuantity(sql);
    }

    @Override
    public List<ActivityPageViewVisitorsDTO> findActivityFrequencySqlInQuery(EventsReportQuery query) {
        String sql = "select activity_id as activityId,count(activity_id) as quantity from events where event = '" +query.getEventName()+ "' " +
                "and activity_id in ("+query.getSqlIn() +") " ;
        sql = sql + addSql();
        sql = sql +" group by activity_id";
        return getQuantityList(sql,ActivityPageViewVisitorsDTO.class);
    }

    @Override
    public List<ActivityPageViewVisitorsDTO> findActivityPeopleSqlInQuery(EventsReportQuery query) {
        String sql = "select activity_id as activityId,count(DISTINCT distinct_id) as quantity from events where event = '" +query.getEventName()+ "' " +
                "and activity_id in ("+query.getSqlIn() +") ";
        sql = sql + addSql();
        sql = sql + " group by activity_id";
        return getQuantityList(sql,ActivityPageViewVisitorsDTO.class);
    }

    @Override
    public ActivityTodayYesterdayVisitorsDTO todayYesterdayPeopleNumber(EventsReportQuery query) {
        //丹丹：用户数，同一个人昨天浏览，今天浏览，今天就不增加了
//        String sql = "select count(DISTINCT distinct_id) as totalNumber, " +
//                " count(DISTINCT case when date = CURRENT_DATE() then distinct_id else null end) as todayNumber, " +
//                " count(DISTINCT case when date = CURRENT_DATE() - INTERVAL '1' DAY then distinct_id else null end) as yesterdayNumber  " +
//                "from events " +
//                "where event = '" + query.getEventName()+ "' " +
//                " and activity_id = "+ query.getActivityId() +"";
//        if (StringUtil.isNotBlank(query.getStartTime()) && StringUtil.isNotBlank(query.getEndTime())) {
//            sql += " and date >= " + query.getStartTime() + " and date <= " + query.getEndTime();
//        }
        String sql = "select count(distinct_id) as totalNumber, " +
                " count(case when min_date = CURRENT_DATE() then distinct_id else null end) as todayNumber, " +
                " count(case when min_date = CURRENT_DATE() - INTERVAL '1' DAY then distinct_id else null end) as yesterdayNumber  " +
                "from (" +
                " select distinct_id,min(date) as min_date " +
                " from events " +
                " where event = '" + query.getEventName()+ "' " +
                " and activity_id = '"+ query.getActivityId() +"'" ;
        sql = sql + addSql();
        sql = sql +  " group by distinct_id " +
                ") a";

        EventsReportSqlQuery sqlQuery = new EventsReportSqlQuery();
        sqlQuery.setSql(sql);
        return JsonUtil.json2Bean(sqlQuery(sqlQuery), ActivityTodayYesterdayVisitorsDTO.class);
    }

    public List<ActivityTodayYesterdayVisitorsDTO> todayYesterdayPeopleNumberBatch(EventsReportQuery query) {
        //丹丹：用户数，同一个人昨天浏览，今天浏览，今天就不增加了
        String sql = "select event, count(distinct_id) as totalPeopleNumber, " +
                "count(case when min_date = CURRENT_DATE() then distinct_id else null end) as todayPeopleNumber," +
                "count(case when min_date = CURRENT_DATE() - INTERVAL '1' DAY then distinct_id else null end) as yesterdayPeopleNumber," +
                "sum(total) totalNumber," +
                "sum(todayTotal) todayNumber," +
                "sum(yesterdayTotal) yesterdayNumber" +
                " from (" +
                "select event,distinct_id,min(date) as min_date,count(distinct_id) total," +
                "count(case when date = CURRENT_DATE() then distinct_id else null end) as todayTotal," +
                "count(case when date = CURRENT_DATE() - INTERVAL '1' DAY then distinct_id else null end) as yesterdayTotal" +
                " from events e" +
                " where time between '" + query.getStartTime()+ "' and '" + query.getEndTime() + "'" +
                " and activity_id = '"+ query.getActivityId() + "'";
        sql = sql + addSql();
        sql = sql +  " group by distinct_id,event " +
                ") a group by event ";

        return getQuantityList(sql,ActivityTodayYesterdayVisitorsDTO.class);
    }

    @Override
    public ActivityTodayYesterdayVisitorsDTO todayYesterdayNumber(EventsReportQuery query) {
        String sql = "select count(distinct_id) as totalNumber, " +
                " count(case when date = CURRENT_DATE() then distinct_id else null end) as todayNumber, " +
                " count(case when date = CURRENT_DATE() - INTERVAL '1' DAY then distinct_id else null end) as yesterdayNumber  " +
                "from events " +
                "where event = '" + query.getEventName()+ "' " +
                " and activity_id = '"+ query.getActivityId() +"'";
        sql = sql + addSql();
        EventsReportSqlQuery sqlQuery = new EventsReportSqlQuery();
        sqlQuery.setSql(sql);
        return JsonUtil.json2Bean(sqlQuery(sqlQuery), ActivityTodayYesterdayVisitorsDTO.class);
    }

    @Override
    public List<ActivityTodayYesterdayVisitorsDTO> todayYesterdayNumberGroupByEvent(EventsReportQuery query) {
        String sql = "select event,count(distinct_id) as totalNumber, " +
                " count(case when date = CURRENT_DATE() then distinct_id else null end) as todayNumber, " +
                " count(case when date = CURRENT_DATE() - INTERVAL '1' DAY then distinct_id else null end) as yesterdayNumber,  " +

                "  count(DISTINCT distinct_id) as totalPeopleNumber, " +
                "  count(DISTINCT case when date = CURRENT_DATE() then distinct_id else null end) as todayPeopleNumber, " +
                "  count(DISTINCT case when date = CURRENT_DATE() - INTERVAL '1' DAY then distinct_id else null end) as yesterdayPeopleNumber " +
                "from events " +
                "where event in ('ActivityPageView','ActivityParticipation')  " +
                " and activity_id = '"+ query.getActivityId() +"'  " ;
        sql = sql + addSql();
        sql = sql + " group by event ";

        return getQuantityList(sql,ActivityTodayYesterdayVisitorsDTO.class);
    }

    @Override
    public List<ActivityTrendResponseDTO> findActivityTrendsPageView(EventsReportQuery query) {
        String sql;
        if (query.getDateType().equals(ActivityAnalysisDateTypeEnum.BY_DAY.getId())){
            String dateDay = query.getDate();
            sql = "select count(DISTINCT distinct_id) as peopleNumber,trunc(time, 'HH24') as hh24, extract(hour from time) AS formatTime " +
                    "from events " +
                    "where event = '" +query.getEventName()+ "' and date = '" + dateDay + "' and activity_id = '"+ query.getActivityId()+"'";
            sql = sql + addSql();
            sql = sql + " group by hh24,formatTime " +
                    " order by formatTime asc ";
        }else{
            String dateDay = query.getDate() + "-01 00:00:00.0";
            sql = "select count(DISTINCT distinct_id) as peopleNumber,extract(day from time) AS formatTime " +
                    "from events " +
                    "where event = '" +query.getEventName()+ "' and trunc(date,'MM') = '" + dateDay + "' and activity_id = '"+ query.getActivityId()+"'";
            sql = sql + addSql();
            sql = sql +  " group by date,formatTime " +
                    " order by formatTime asc ";
        }

        List<String> strings = this.sqlQuerySplit(sql);
        if (CollectionUtil.isNotEmpty(strings)){
            return this.getActivityTrendResponseDTO(query,strings);
        }
        return Lists.newArrayList();
    }

    @Override
    public List<ChannelDistributionResponseDTO> sensorsChannelDistribution(EventsReportQuery query) {
        String sql = "select channel_name as channelName," +
                "count(DISTINCT case when event = 'ActivityPageView' then distinct_id else null end) as accessPeopleNumber,\n" +
                "count(DISTINCT case when event = 'ActivityParticipation' then distinct_id else null end) as joinPeopleNumber,\n" +
                "count(DISTINCT case when event = 'EnrollmentInfo' then distinct_id else null end) as lzPeopleNumber\n" +
                "from events \n" +
                "where event in ('ActivityPageView','EnrollmentInfo','ActivityParticipation') " +
                " and time between '" + query.getStartTime()+ "' and '" + query.getEndTime() + "'" +
                " and activity_id = '"+ query.getActivityId() + "' and channel_name is not null ";
        sql = sql + addSql();
        sql = sql +  "group by channel_name order by accessPeopleNumber desc,joinPeopleNumber desc,lzPeopleNumber desc";

        return getQuantityList(sql,ChannelDistributionResponseDTO.class);
    }

    @Override
    public List<ActivityPageViewVisitorsDTO> findCityDistributionList(EventsReportQuery query) {
        String sql = "select $province as province,count(DISTINCT distinct_id) as quantity from events where" +
                " event = '" + query.getEventName()+ "' " +
                " and time between '" + query.getStartTime()+ "' and '" + query.getEndTime() + "'" +
                " and activity_id = '"+ query.getActivityId() +"' " +
                " and $province != '保留IP' " ;
        sql = sql + addSql();
        sql = sql + " group by $province  " ;
        sql = sql +  " order by quantity asc";
        return getQuantityList(sql,ActivityPageViewVisitorsDTO.class);
    }

    @Override
    public Long findGoToViewPeopleNumber(EventsReportQuery query) {
        String sql = "select count(DISTINCT distinct_id) as quantity" +
                "from ( " +
                "select a.distinct_id " +
                "  from ( " +
                "  select distinct_id,date from events where activity_id = '"+ query.getActivityId() + "' event = '" + query.getEventName()+ "' ";
        if (query.getDateType() != null){
            if(query.getDateType() == 1){
                sql += " date = CURRENT_DATE()";
            }else{
                sql += " date =  CURRENT_DATE() - INTERVAL '1' DAY";
            }
        }
        sql += ") a " +
                "join ( " +
                "  select distinct_id,date from events where event = '" + query.getEventName()+ "' ";
        if (query.getDateType() != null){
            if(query.getDateType() == 1){
                sql += " date = CURRENT_DATE()";
            }else{
                sql += " date =  CURRENT_DATE() - INTERVAL '1' DAY";
            }
        }
        sql +="  ) b on a.distinct_id = b.distinct_id " +
                "  ) c " +
                "  ";
        return getQuantity(sql);
    }

    @Override
    public List<ActivityAnalysisResponseDTO> sensorsActivityAnalysisStatistics(String activityId) {
        String sql = "select activity_id as id,\n" +
                "count(case when event = 'ActivityPageView' then distinct_id else null end) as accessNumber,\n" +
                "count(case when event = 'ActivityParticipation' then distinct_id else null end) as joinNumber,\n" +
                "count(case when event = 'PageShare' then distinct_id else null end) as shareNumber,\n" +
                "count(DISTINCT case when event = 'ActivityPageView' then distinct_id else null end) as accessPeopleNumber,\n" +
                "count(DISTINCT case when event = 'ActivityParticipation' then distinct_id else null end) as joinPeopleNumber,\n" +
                "count(DISTINCT case when event = 'PageShare' then distinct_id else null end) as sharePeopleNumber,\n" +
                "count(DISTINCT case when event = 'EnrollmentInfo' then distinct_id else null end) as lzPeopleNumber\n" +
                "from events \n" +
                "where event in ('ActivityPageView','PageShare','EnrollmentInfo','ActivityParticipation') and activity_id in ("+ activityId +")\n" ;
        sql = sql + addSql();
        sql = sql + "group by activity_id ";

        return getQuantityList(sql,ActivityAnalysisResponseDTO.class);
    }

    @Override
    public List<ActivityAnalysisResponseDTO> sensorsActivityAnalysisStatisticsJoinNumber(String activityId) {
        String sql = "select activity_id as id,\n" +
                "count(DISTINCT case when event = 'ActivityParticipation' then distinct_id else null end) as joinPeopleNumber\n" +
                "from events \n" +
                "where event in ('ActivityPageView','PageShare','EnrollmentInfo','ActivityParticipation') and activity_id in ("+ activityId +")\n" ;
        sql = sql + addSql();
        sql = sql + "group by activity_id ";

        return getQuantityList(sql,ActivityAnalysisResponseDTO.class);
    }

    @Override
    public IndexAnalysisDTO joinUserAnalysis(Long id) {
        String sql = "select " +
                "count(distinct_id) as totalNumber, " +
                " count(case when date = CURRENT_DATE() then distinct_id else null end) as todayNumber, " +
                " count(case when date = CURRENT_DATE() - INTERVAL '1' DAY then distinct_id else null end) as yesterdayNumber,  " +

                "count(distinct_id) as totalPeopleNumber, " +
                " count(case when date = CURRENT_DATE() then distinct_id else null end) as todayPeopleNumber, " +
                " count(case when date = CURRENT_DATE() - INTERVAL '1' DAY then distinct_id else null end) as yesterdayPeopleNumber  " +
                "from events " +
                "where event = 'ActivityParticipation' " +
                " and activity_id = '"+ id +"'";

        EventsReportSqlQuery sqlQuery = new EventsReportSqlQuery();
        sqlQuery.setSql(sql);
        return JsonUtil.json2Bean(sqlQuery(sqlQuery), IndexAnalysisDTO.class);
    }

    @Override
    public void sensorsBuriedPoint(SensorsBuriedPointDTO sensorsBuriedPoint) {
        SensorsAnalytics sa = new SensorsAnalytics(new BatchConsumer(huafaConstantConfig.SENSORS_BURIED_POINT, 1, true));

        String localHost = this.getLocalHost();
        try {

            //设置公共属性,以后上传的每一个事件都附带该属性
            SuperPropertiesRecord propertiesRecord = SuperPropertiesRecord.builder()
                    .addProperty("$os", "Windows")
                    .addProperty("$os_version", "8.1")
                    .addProperty("$ip", localHost)
                    .build();
            sa.registerSuperProperties(propertiesRecord);

            // 2. 用户注册登录之后，系统分配的注册ID
            String registerId = sensorsBuriedPoint.getRegisterId();
            //使用trackSignUp关联用户匿名ID和登录ID
            sa.trackSignUp(registerId, sensorsBuriedPoint.getCookieId());

            EventRecord payRecord = EventRecord.builder().setDistinctId(sensorsBuriedPoint.getRegisterId()).isLoginId(Boolean.TRUE)
                    .setEventName(sensorsBuriedPoint.getEventName())
                    .addProperties(sensorsBuriedPoint.getSuperProperties())
                    .build();

            sa.track(payRecord);
        } catch (InvalidArgumentException e) {
            e.printStackTrace();
            log.error("神策埋点异常：" + e.getMessage());
        }
    }

    private String getLocalHost() {
        InetAddress ip4;
        try {
            ip4 = Inet4Address.getLocalHost();
        } catch (UnknownHostException e) {
            e.printStackTrace();
            return "";
        }
        return ip4.getHostAddress();
    }

    @Override
    public Long channelCountByActivityId(ActivityPromotionChannelDO promotionChannelDO) {
        if (StringUtil.isBlank(promotionChannelDO.getChannelName()) || promotionChannelDO.getActivityId() == null){
            throw new ApplicationException("参数异常");
        }
        String sql = StrUtil.format("select count(1) as quantity from events where activity_id = '{}' and channel_name = '{}' and time > '{}'",
                promotionChannelDO.getActivityId(),promotionChannelDO.getChannelName(), DateUtil.formatDateTime(promotionChannelDO.getCreatedTime()));
        sql = sql + addSql();
        return getQuantity(sql);
    }

    @Override
    public List<ActivityAnalysisResponseDTO> posterChannelStatistics(ActivityAnalysisQuery query) {
        String sql = "    select max(activity_id) as id," +
                "         max(activity_name) as activityName,share_phone as phone,max(user_name) as nickName,max(time) as createTime," +
                "         count(DISTINCT case when event = 'ActivityPageView' then distinct_id else null end) as accessPeopleNumber,\n" +
                "         count(DISTINCT case when event = 'ActivityParticipation' then distinct_id else null end) as joinPeopleNumber,\n" +
                "         count(DISTINCT case when event = 'EnrollmentInfo' then distinct_id else null end) as lzPeopleNumber\n" +
                "         from events \n" +
                "         where activity_id = '"+ query.getId() +"' and event in ('ActivityPageView','ActivityParticipation','EnrollmentInfo') and poster_channel = '海报渠道'";
        if (StringUtil.isNotBlank(query.getNickNameOrPhone())){
            sql += " and (regexp_like(share_phone,'"+query.getNickNameOrPhone()+"') or regexp_like(user_name,'"+query.getNickNameOrPhone()+"')) ";
        }
        sql += " and time between '" + query.getStartTime()+ "' and '" + query.getEndTime() + "'";
        sql += "   group by share_phone  order by createTime desc limit "+query.getPage()+","+query.getSize()+"";
        return getQuantityList(sql,ActivityAnalysisResponseDTO.class);
    }
    @Override
    public Long posterChannelStatisticsCount(ActivityAnalysisQuery query) {
        String sql = " select count(1) as quantity from (   select max(activity_id) as id" +
                "         from events \n" +
                "         where activity_id = '"+ query.getId() +"' and event in ('ActivityPageView','ActivityParticipation','EnrollmentInfo') and poster_channel = '海报渠道'";
        if (StringUtil.isNotBlank(query.getNickNameOrPhone())){
            sql += " and (regexp_like(share_phone,'"+query.getNickNameOrPhone()+"') or regexp_like(user_name,'"+query.getNickNameOrPhone()+"')) ";
        }
        sql = sql + "   group by share_phone ) as tb";
        return getQuantity(sql);
    }

    private List<ActivityTrendResponseDTO> getActivityTrendResponseDTO(EventsReportQuery query,List<String> strings) {
        Map<Integer, Long> collect = strings.stream().map(e -> JsonUtil.json2Bean(e, SensorsActivityTrend.class))
                .collect(Collectors.toMap(SensorsActivityTrend::getFormatTime, SensorsActivityTrend::getPeopleNumber));

        List<ActivityTrendResponseDTO> activityTrendList = Lists.newArrayList();

        int date = 24;
        if (query.getDateType().equals(ActivityAnalysisDateTypeEnum.BY_MONTH.getId())){
            //获取查询月份的最后一天
            String endMonth = DateUtils.getEndDateOfMonth(query.getDate() + "-01").substring(8, 10);
            date = Integer.parseInt(endMonth);
        }
        ActivityTrendResponseDTO activityTrendResponseDTO;
        for (int i = 1 ; i <= date ; i++){
            int index = i;
            activityTrendResponseDTO = new ActivityTrendResponseDTO();
            Long aLong = collect.get(i);
            if (query.getDateType().equals(ActivityAnalysisDateTypeEnum.BY_DAY.getId()) && i == 24){
                aLong = collect.get(0);
                index = 0;
            }

            if (aLong == null){
                activityTrendResponseDTO.setPeopleNumber(0L);
            }else{
                activityTrendResponseDTO.setPeopleNumber(aLong);
            }
            activityTrendResponseDTO.setCreatedTime(query.getDate());
            activityTrendResponseDTO.setDate(index);
            activityTrendResponseDTO.setIndexPath("1");
            activityTrendList.add(activityTrendResponseDTO);
        }
        return activityTrendList.stream().sorted(Comparator.comparing(ActivityTrendResponseDTO::getDate)).collect(Collectors.toList());
    }

    private List<String> sqlQuerySplit(String sql) {
        EventsReportSqlQuery sqlQuery = new EventsReportSqlQuery();
        sqlQuery.setSql(sql);
        String str = sqlQuery(sqlQuery);
        if (StringUtil.isBlank(str)){
            return new ArrayList<>();
        }
        String[] split = str.split("\n");
        return Arrays.asList(split);
    }


    private Long getQuantity(String sql){
        EventsReportSqlQuery sqlQuery = new EventsReportSqlQuery();
        sqlQuery.setSql(sql);
        ActivityPageViewVisitorsDTO activityPageViewVisitorsDTO = JsonUtil.json2Bean(sqlQuery(sqlQuery), ActivityPageViewVisitorsDTO.class);
        return activityPageViewVisitorsDTO.getQuantity();
    }
    private <T> List<T> getQuantityList(String sql, Class<T> clazz){
        List<String> strings = this.sqlQuerySplit(sql);
        if (CollectionUtil.isNotEmpty(strings)){
            return strings.stream().map(e-> JsonUtil.json2Bean(e,clazz)).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    public String sqlQuery(EventsReportSqlQuery query) {
        Map<String,String> map = new HashMap<>(2);
        map.put("q",query.getSql());
        map.put("format","json");
        Map<String,String> header = Maps.newHashMap();
        header.put("content-type","application/x-www-form-urlencoded");
        String url = huafaConstantConfig.DATA_URL + SensorsConstant.SQL_QUERY +"?token="+ huafaConstantConfig.SENSORS_API_SECRET +"&project="+huafaConstantConfig.PROJECT;
        String s = HTTPClientUtils.doPost(url, header, map);
        log.info("------请求神策sql：" + query.getSql());
        log.info("------请求神策响应：" + s);
        return s;
    }

    @Data
    public static class SensorsActivityTrend{
        private Long peopleNumber;
        private Integer formatTime;
    }

    public Map<String, Object> registerSuperProperties(SuperPropertiesRecord propertiesRecord) {
        if (propertiesRecord == null){
            return Maps.newHashMap();
        }
        Map<String, Object> superProperties = Maps.newHashMap();
        for (String key : propertiesRecord.getPropertyMap().keySet()) {
            superProperties.put(key, propertiesRecord.getPropertyMap().get(key));
        }
        return superProperties;
    }

    private String addSql(){
        String sql = "";
        if (StringUtil.isNotBlank(huafaConstantConfig.getTIME_LIMIT())){
            sql = " and time >= '"+ huafaConstantConfig.getTIME_LIMIT() +"' and poster_channel is null ";
        }
        return sql;
    }
}
