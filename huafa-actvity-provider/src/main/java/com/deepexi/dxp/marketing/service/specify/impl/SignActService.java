package com.deepexi.dxp.marketing.service.specify.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import com.deepexi.common.extension.AppRuntimeEnv;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPartakeLogDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPartakeRequest;
import com.deepexi.dxp.marketing.domain.marketing.response.SignResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.ResourceHisDetailResponseDTO;
import com.deepexi.dxp.marketing.enums.activity.limit.PATemplateBaseEnum;
import com.deepexi.dxp.marketing.enums.activity.limit.PATemplateNumberEnum;
import com.deepexi.dxp.marketing.enums.specify.FissonStatusEnum;
import com.deepexi.dxp.marketing.manager.promotion.PromotionActivityManager;
import com.deepexi.dxp.marketing.utils.LotteryUtil;
import com.deepexi.dxp.middle.promotion.converter.specify.BargainingConverter;
import com.deepexi.dxp.middle.promotion.dao.PromotionActivityLimitDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityFissionAssistResourceDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityFissionLogDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityPartakeLogDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.PromotionHisResourceDAO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityLimitDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityFissionAssistResourceDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityFissionLogDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityPartakeLogDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.PromotionHisResourceDO;
import com.deepexi.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 签到活动模板
 * @date 2022/10/24 16:47
 */
@Service
@Slf4j
public class SignActService {

    @Resource
    private ActivityPartakeLogDAO activityPartakeLogDAO;

    @Resource
    private ActivityFissionLogDAO activityFissionLogDAO;

    @Resource
    private PromotionHisResourceDAO promotionHisResourceDAO;

    @Resource
    private ActivityFissionAssistResourceDAO activityFissionAssistResourceDAO;

    @Resource
    private PromotionActivityManager promotionActivityManager;

    @Resource
    private PromotionActivityLimitDAO promotionActivityLimitDAO;

    @Transactional
    public SignResponseDTO partakeAct(ActivityPartakeRequest requestVo, List<PromotionActivityLimitDO> promotionActivityLimitList,
                                      List<ResourceHisDetailResponseDTO> prizeList) {
        SignResponseDTO result = new SignResponseDTO();
        //更新参与数据
        ActivityPartakeLogDO partakeLogDO = doActivityPartake(requestVo, promotionActivityLimitList);
        //处理是否中奖
        PromotionHisResourceDO hisDetailResponse = checkLottery(partakeLogDO,prizeList);
        //增加签到记录
        addSignLog(requestVo, partakeLogDO,hisDetailResponse);
        if (hisDetailResponse != null) {
            result.setResourceId(hisDetailResponse.getResourceId() > 0 ? hisDetailResponse.getId() : hisDetailResponse.getResourceId()).setType(1).setName(hisDetailResponse.getName()).setUrl(hisDetailResponse.getUrl());
        }
        return result;
    }

    private PromotionHisResourceDO checkLottery(ActivityPartakeLogDO partakeLogDO, List<ResourceHisDetailResponseDTO> prizeList) {
        //检查当前累计签到次数是否等于某一个阶梯的值，有则说明可以抽这个奖，需要检查是否有剩于库存，是否有中过奖
        ResourceHisDetailResponseDTO hitResource = null;
        for (ResourceHisDetailResponseDTO hisDetailResponseDTO : prizeList) {
            if (hisDetailResponseDTO.getFissonCount() == partakeLogDO.getCurrentFissonCount()) {
                hitResource = hisDetailResponseDTO;
            }
        }
        if (hitResource == null){//没有达到中奖条件
            return null;
        }

        //抽奖逻辑
        PromotionHisResourceDO promotionHisResourceDO = doLottery(partakeLogDO,hitResource);
        return promotionHisResourceDO;
    }

    private PromotionHisResourceDO doLottery(ActivityPartakeLogDO partakeLogDO,ResourceHisDetailResponseDTO hitResource) {

        List<PromotionHisResourceDO> hisResourceList = promotionHisResourceDAO.findByActivityId(partakeLogDO.getActivityId());
        //查询每个奖品当天发送的数量
        int dayCount = activityFissionAssistResourceDAO.countResourceByToday(partakeLogDO.getActivityId(), hitResource.getId());

        //查询当前用户中奖的情况
        ActivityFissionAssistResourceDO gotResourceDO = activityFissionAssistResourceDAO.getByLadderSort(null, partakeLogDO.getId(), hitResource.getId());

        PromotionHisResourceDO noResource = null,hitResourceDO = null;
        for (PromotionHisResourceDO his : hisResourceList) {
            if (his.getResourceId() < 0){
                noResource = his;
                continue;
            }
            if (Objects.equals(his.getId(),hitResource.getId())) {
                hitResourceDO = his;
                //奖品发放数量已达到奖品设置的资源发放数量 概率修改为0
                if (his.getRemainingQuantity() != null && 0 >= his.getRemainingQuantity()){
                    log.info("《签到》奖品发放数量已达到奖品设置的资源发放数量 概率修改为0");
                    his.setOddsOfWinning(0d);
                }

                //抽取活动奖品当天发放数量    当天发放数量 >= 每天发放上限 概率修改为0
                if (his.getLimitTimes() != null && dayCount >= his.getLimitTimes()){
                    log.info("《签到》当天发放数量已达到奖品设置的每天发放上限 概率修改为0");
                    his.setOddsOfWinning(0d);
                }

                //抽取活动该用户参与奖品记录      配置限制用户中奖1次并且用户已经有抽奖记录 概率修改为0
                if (gotResourceDO != null && his.getLimitType() != null && his.getLimitType().equals(1)) {
                    log.info("《签到》配置限制用户中奖1次并且用户已经有抽奖记录 概率修改为0");
                    his.setOddsOfWinning(0d);
                }
            }
        }
        //把剩余的概率分给 谢谢参与
        noResource.setOddsOfWinning(100 - hitResourceDO.getOddsOfWinning());
        List<PromotionHisResourceDO> lotteryList = ListUtil.toList(noResource,hitResourceDO);

        log.info("《签到》参与抽奖的最终概率：{}", JsonUtil.bean2JsonString(lotteryList));
        //根据概率随机抽取奖品
        List<Double> collect = lotteryList.stream().map(PromotionHisResourceDO::getOddsOfWinning).collect(Collectors.toList());
        int lottery = LotteryUtil.lottery(collect);
        PromotionHisResourceDO promotionHisResourceDO = lotteryList.get(lottery);

        //如果有抽中，扣减缓存库存
        if (promotionHisResourceDO.getResourceId() > 0) {//非谢谢参与
            boolean flag = promotionActivityManager.decrRedisQty(promotionHisResourceDO.getId(), promotionHisResourceDO.getActivityId());
            if (!flag) {//抽中了，但是没有库存了，也返回没中奖
                return noResource;
            }
            promotionActivityManager.decrRemainQty(promotionHisResourceDO.getId(), 1L);
            //添加可领取记录
            activityFissionAssistResourceDAO.save(BargainingConverter.activityFissionAssistResourceConverter(partakeLogDO, promotionHisResourceDO));
        }
        return promotionHisResourceDO;
    }

    private void addSignLog(ActivityPartakeRequest requestVo, ActivityPartakeLogDO partakeLogDO, PromotionHisResourceDO hisDetailResponse) {
        //增加签到记录，先检查当天是否已签到
        ActivityFissionLogDO fissionLogDO = activityFissionLogDAO.getByPhone(requestVo.getActivityId(), requestVo.getPhone());
        Assert.isTrue(fissionLogDO == null || !DateUtil.isSameDay(fissionLogDO.getCreatedTime(), new Date()), "一天只能签到一次");
        fissionLogDO = BargainingConverter.activityFissionLogConverter(requestVo, 0, partakeLogDO);
        if (hisDetailResponse != null) {//有抽奖记录，则记录奖品id,包含谢谢参与
            fissionLogDO.setAssistResourceId(hisDetailResponse.getId());
        }
        activityFissionLogDAO.save(fissionLogDO);
    }

    private ActivityPartakeLogDO doActivityPartake(ActivityPartakeRequest requestVo, List<PromotionActivityLimitDO> promotionActivityLimitList) {
        //先检查是否有参与，如果没有，需要传项目id,如果有的话，则直接增加次数及签到记录，每天只能签到一次
        ActivityPartakeLogDO partakeLogDO = activityPartakeLogDAO.getByPhone(requestVo.getActivityId(), requestVo.getPhone());
        if (partakeLogDO == null) {
            Assert.notEmpty(requestVo.getProjectId(), "首次签到，项目信息不能为空");
            partakeLogDO = partakeLogConverter(requestVo, promotionActivityLimitList);
        }
        partakeLogDO.setCurrentFissonCount(partakeLogDO.getCurrentFissonCount() + 1);
        Assert.isTrue(partakeLogDO.getCurrentFissonCount() <= partakeLogDO.getNeedFissonCount(), "签到次数已达到最大，不能再签到");

        activityPartakeLogDAO.saveOrUpdate(partakeLogDO);
        return partakeLogDO;
    }

    public static ActivityPartakeLogDO partakeLogConverter(ActivityPartakeRequest requestVo, List<PromotionActivityLimitDO> promotionActivityLimitList) {
        // 获取活动规则
        List<BaseActivityDTO> numberList = BargainingConverter.getPromotionActivityLimit(promotionActivityLimitList, PATemplateBaseEnum.NUMBER.getId());
        // 获取总签到数量
        Integer total = BargainingConverter.elLimitConverter(numberList, PATemplateNumberEnum.ZCS.getId());

        ActivityPartakeLogDO activityPartakeLog = requestVo.clone(ActivityPartakeLogDO.class);
        activityPartakeLog.setAppId(AppRuntimeEnv.getAppId());
        activityPartakeLog.setTenantId(AppRuntimeEnv.getTenantId());
        activityPartakeLog.setCreatedBy(requestVo.getUserName());
        activityPartakeLog.setUpdatedBy(requestVo.getUserName());
        activityPartakeLog.setCurrentFissonCount(0);
        activityPartakeLog.setFissonStatus(FissonStatusEnum.PROCESSING.getId());
        activityPartakeLog.setNeedFissonCount(total);
        return activityPartakeLog;
    }

    public void homeInfo(ActivityPartakeLogDTO partakeLogDTO) {
        if (partakeLogDTO.getId() == null) {//还没参与，设置下初始值
            List<PromotionActivityLimitDO> promotionActivityLimitList = promotionActivityLimitDAO.selectByActivityId(partakeLogDTO.getActivityId());
            List<BaseActivityDTO> numberList = BargainingConverter.getPromotionActivityLimit(promotionActivityLimitList, PATemplateBaseEnum.NUMBER.getId());
            Integer total = BargainingConverter.elLimitConverter(numberList, PATemplateNumberEnum.ZCS.getId());
            partakeLogDTO.setNeedFissonCount(total);
            partakeLogDTO.setCurrentFissonCount(0);
            partakeLogDTO.setTodayActCnt(0);
        } else {
            ActivityFissionLogDO fissionLogDO = activityFissionLogDAO.getByPhone(partakeLogDTO.getActivityId(), partakeLogDTO.getPhone());
            if (fissionLogDO != null && DateUtil.isSameDay(fissionLogDO.getCreatedTime(), new Date())) {
                partakeLogDTO.setTodayActCnt(1);
            }
        }
    }
}
