package com.deepexi.dxp.marketing.controller.promotion.middleapi;

import com.deepexi.dxp.marketing.api.promotion.PromotionActivityTemplateMiddleApi;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.PromotionActivityTemplateDetailPostDTO;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityTemplateCreatePostRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityTemplateDeletedPostRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityTemplateUpdatePostRequest;
import com.deepexi.dxp.marketing.service.promotion.PromotionActivityTemplateMiddleService;
import com.deepexi.util.config.Payload;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @date 2019/11/13 15:07
 */
@RestController
@Slf4j
@Api(value = "活动模板", tags = "活动模板相关接口")
public class PromotionActivityTemplateMiddleApiController implements PromotionActivityTemplateMiddleApi {


    @Autowired
    private PromotionActivityTemplateMiddleService promotionActivityTemplateMiddleService;

    @Override
    @ApiOperation(value = "根据查看详情")
    public Payload<PromotionActivityTemplateDetailPostDTO> detail(@RequestParam Long id) {
        return new Payload<>(promotionActivityTemplateMiddleService.detail(id));
    }

    @Override
    @ApiOperation(value = "更新活动模板")
    public Payload<Boolean> update(@RequestParam Long id, @RequestBody PromotionActivityTemplateUpdatePostRequest dto) {
        return new Payload<>(promotionActivityTemplateMiddleService.update(id, dto));
    }

    @Override
    @ApiOperation(value = "创建活动模板")
    public Payload<Boolean> create(@RequestBody PromotionActivityTemplateCreatePostRequest dto) {
        return new Payload<>(promotionActivityTemplateMiddleService.create(dto));
    }

    @Override
    @ApiOperation(value = "删除活动模板")
    public Payload<Boolean> deleted(@RequestBody List<PromotionActivityTemplateDeletedPostRequest> dtoList) {
        return new Payload<>(promotionActivityTemplateMiddleService.deleted(dtoList));
    }


}