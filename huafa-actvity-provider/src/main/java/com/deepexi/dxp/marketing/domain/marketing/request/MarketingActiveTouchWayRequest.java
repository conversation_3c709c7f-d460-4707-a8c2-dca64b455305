package com.deepexi.dxp.marketing.domain.marketing.request;


import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 主动营销触达方式
 * @Author: HuangBo.
 * @Date: 2020/3/16 11:35
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MarketingActiveTouchWayRequest extends SuperVO {

  /**
   * 主键ID
   */
  @ApiModelProperty(value = "主键ID")
  private Long id;

  /**
   * 主动营销任务
   */
  @ApiModelProperty(value = "主动营销任务ID")
  private Long activeId;

  /**
   * 发送规则渠道: 1.发送微信  2.发送短信
   */
  @ApiModelProperty(value = "发送规则渠道: 1.发送微信  2.发送短信")
  @NotNull(message = "发送规则渠道不能为空")
  private Integer sendRuleChannel;

  /**
   * 发送规则类型: 1.主触达  2.降级触达
   */
  @ApiModelProperty(value = "发送规则类型: 1.主触达  2.降级触达")
  @NotNull(message = "发送规则类型不能为空")
  private Integer sendRuleType;

  /**
   * 是否抽样测试  0：抽样测试 1：不抽样测试
   */
  @ApiModelProperty(value = "是否抽样测试  0：抽样测试 1：不抽样测试")
  private boolean sampleTest;

  /**
   * 排序，用于主触达有多个配置时执行顺序
   */
  @ApiModelProperty(value = "触达有多个配置时执行排序")
  private int sort;

  /**
   * 抽样数量
   */
  @ApiModelProperty(value = "抽样数量")
  private int sampleTestQty;

  /**
   * 短信或微信模板id
   */
  @ApiModelProperty(value = "短信或微信模板id")
  @NotNull(message = "短信或微信模板id")
  private Long sendTemplateId;

  /**
   * 短信或微信模板名称
   */
  @ApiModelProperty(value = "短信或微信模板名称")
  private String sendTemplateName;

}
