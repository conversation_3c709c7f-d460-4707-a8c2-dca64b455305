package com.deepexi.dxp.marketing.manager.promotion.impl.strategy;

import com.deepexi.dxp.marketing.domain.promotion.dto.activity.ActivityRuleDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.UpIdDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityCommodityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityConfigDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.TemplateLimitDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityParamsDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityResponseParamsDTO;
import com.deepexi.dxp.marketing.enums.activity.strategy.StrategyGroupEnum;
import com.deepexi.dxp.marketing.enums.activity.strategy.operation.PTOperationEnum;
import com.deepexi.dxp.marketing.enums.status.ActivityInventory;
import com.deepexi.dxp.marketing.manager.promotion.impl.BaseStrategy;
import com.deepexi.dxp.middle.promotion.enums.MiddlePromotionResultEnum;
import com.deepexi.dxp.middle.promotion.util.Arith;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.exception.ApplicationException;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 秒杀活动策略
 * <AUTHOR>
 * @date 2020.09.21
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel
@Data
@Slf4j
public class SecKillStrategy extends BaseStrategy {

    private List<SecKillStrategyEnumsCalculate> calculateHelper = new ArrayList<>(30);


    public SecKillStrategy(TemplateLimitDTO templateLimitDTO, ActivityConfigDTO activityConfigDTO, ActivityParamsDTO params, ActivityResponseParamsDTO activityResponseParamsDTO) {
        super(templateLimitDTO, activityConfigDTO, params, activityResponseParamsDTO);
    }

    /**
     * 秒杀的枚举类处理
     */
    private interface SecKillStrategyEnumsCalculate {
        /**
         * @param activityRuleDTOList       活动的优惠rule
         * @param params                    活动的参数
         * @param activityResponseParamsDTO 优惠结果返回类
         */
        void calculate(List<ActivityRuleDTO> activityRuleDTOList, ActivityParamsDTO params, ActivityResponseParamsDTO activityResponseParamsDTO);
    }

    private SecKillStrategy.SecKillStrategyEnumsCalculate strategyDz(List<ActivityRuleDTO> activityRuleDTOList) {
        //json例子：[{"sort": "", "condition": "", "operation": [{"id": "activityPrice", "flag": "==", "upId": "313", "value": "9"}, {"id": "activityPrice", "flag": "==", "upId": "233", "value": "0.9"}, {"id": "activityPrice", "flag": "==", "upId": "212", "value": "39"}, {"id": "activityPrice", "flag": "==", "upId": "454", "value": "3"}], "strategyType": "2"}]
        return (activityRuleList, params, activityResponseParams) -> {
            boolean enumsFlag = activityResponseParams.getPaTemplateId().equals(StrategyGroupEnum.SECKILL.getId());
            if (!enumsFlag) {
                return;
            }
            if (CollectionUtil.isNotEmpty(activityRuleDTOList)) {
                //获取所有商品
                List<ActivityCommodityDTO> commodities = activityResponseParams.getActivityCommodityDTOList();
                //获取原商品总价
                BigDecimal totalDetailMoney = getTotalDetailMoney(commodities);
                //获取折扣条件
                if (CollectionUtil.isEmpty(activityRuleList)) {
                    log.error("秒杀策略规则为空：{}", getActivityConfigDTO().getActivityId());
                    throw new ApplicationException("秒杀策略规则为空！");
                }
                //计算每件商品的优惠金额
                setCommodity(commodities, activityRuleList);
                //设置返回
                setActivityResponseParams(commodities, totalDetailMoney, activityResponseParams);
            }
        };
    }

    //获取原价总额
    private BigDecimal getTotalDetailMoney(List<ActivityCommodityDTO> commodities) {
        return commodities.stream()
                .map(activityCommodity -> Arith.transformToBigDecimal(activityCommodity.getDetailPrice().multiply(new BigDecimal(activityCommodity.getSkuAmount())), 2))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    //设置活动汇总
    private void setActivityResponseParams(List<ActivityCommodityDTO> commodities, BigDecimal totalDetailMoney, ActivityResponseParamsDTO activityResponseParamsDTO) {
        //各个商品的优惠价总和
        BigDecimal totalDiscountsPrice = commodities.stream().map(ActivityCommodityDTO::getDiscountsPriceAll).reduce(BigDecimal.ZERO, BigDecimal::add);
        //优惠了多少金额
        BigDecimal totalSubtractPrice = totalDetailMoney.subtract(totalDiscountsPrice);
        activityResponseParamsDTO.setDetailPrice(totalDetailMoney);
        activityResponseParamsDTO.setDiscountsPrice(totalDiscountsPrice);
        activityResponseParamsDTO.setSubtractPrice(totalSubtractPrice);
    }

    //计算每个商品秒杀价格
    private void setCommodity(List<ActivityCommodityDTO> list, List<ActivityRuleDTO> activityRuleDTOList) {
        List<UpIdDTO> operationList = activityRuleDTOList.get(0).getOperation();
        list.forEach(sku -> {
            //获取商品秒杀单价
             String value = operationList.stream().filter(ope -> ope.getId().equals(PTOperationEnum.HDJG.getId()) && Long.parseLong(ope.getUpId()) == sku.getUpId())
                    .findFirst()
                    .map(BaseActivityDTO::getValue).orElse("0.00");
            BigDecimal seckillPrice = new BigDecimal(value);

            if (sku.getDetailPrice().compareTo(seckillPrice) < 0) {
                log.info("秒杀价格设置不合理 商品{} 原价{} 少于秒杀价{} ", sku.getUpId(), sku.getDetailPrice(), seckillPrice);
                throw new ApplicationException(MiddlePromotionResultEnum.SERVICE_PRICE_OPERATION);
            }
            //计算商品总额
            BigDecimal totalSkuMoney = Arith.transformToBigDecimal(sku.getDetailPrice().multiply(new BigDecimal(sku.getSkuAmount())), 2);
            log.info("商品:{} 原价{} * 数量{} = 原价总额{}", sku.getUpId(), sku.getDetailPrice(), sku.getSkuAmount(), totalSkuMoney);
            //计算商品优惠后总价
            BigDecimal discountsPriceAll = Arith.transformToBigDecimal(seckillPrice.multiply(new BigDecimal(sku.getSkuAmount())), 2);
            log.info("商品:{} 秒杀单价{} * 数量{} = 秒杀总额{}", sku.getUpId(), seckillPrice, sku.getSkuAmount(), discountsPriceAll);
            sku.setDiscountsPriceAll(discountsPriceAll);
            //计算商品优惠总额
            sku.setSubtractPriceAll(totalSkuMoney.subtract(discountsPriceAll));
            sku.setStockType(ActivityInventory.ACTIVITY_INVENTORY.getId());
            if (BigDecimal.ZERO.equals(sku.getSubtractPriceAll())) {
                sku.setStockType(ActivityInventory.SALES_INVENTORY.getId());
            }
        });
    }


    private void init(List<ActivityRuleDTO> activityRuleDTOList) {
        calculateHelper.add(strategyDz(activityRuleDTOList));
    }

    @Override
    public Boolean calculate() {
        // 获取活动的策略
        List<ActivityRuleDTO> activityStrategiesList = super.getActivityConfigDTO().getActivityRuleDTOList();
        // 获取活动的参数
        ActivityParamsDTO params = super.getParams();
        // 活动返回的参数
        ActivityResponseParamsDTO activityResponseParamsDTO = super.getActivityResponseParamsDTO();
        init(activityStrategiesList);
        startCalculate(activityStrategiesList, params, activityResponseParamsDTO);
        return true;
    }

    private void startCalculate(List<ActivityRuleDTO> activityStrategiesList, ActivityParamsDTO params, ActivityResponseParamsDTO activityResponseParamsDTO) {

        for (SecKillStrategyEnumsCalculate strategy : calculateHelper) {
            strategy.calculate(activityStrategiesList, params, activityResponseParamsDTO);
        }
    }
}
