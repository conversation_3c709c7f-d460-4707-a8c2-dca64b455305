package com.deepexi.dxp.marketing.manager.promotion.impl.strategy;

import com.deepexi.dxp.marketing.domain.promotion.dto.activity.ActivityRuleDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityCommodityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityConfigDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.TemplateLimitDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityParamsDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityResponseParamsDTO;
import com.deepexi.dxp.marketing.enums.activity.strategy.StrategyGroupEnum;
import com.deepexi.dxp.marketing.enums.activity.strategy.condition.JFDHConditionEnum;
import com.deepexi.dxp.marketing.manager.promotion.impl.BaseStrategy;
import com.deepexi.dxp.middle.promotion.util.Arith;
import com.deepexi.util.StringUtil;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> ming.zhong
 * @date created in 14:37 2019/11/27
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel
@Data
@Slf4j
public class JFDHStrategy extends BaseStrategy {

    private List<JFDHStrategyEnumsCalculate> calculateHelper = new ArrayList<>(30);

    public JFDHStrategy(TemplateLimitDTO templateLimitDTO, ActivityConfigDTO activityConfigDTO, ActivityParamsDTO params, ActivityResponseParamsDTO activityResponseParamsDTO) {
        super(templateLimitDTO, activityConfigDTO, params, activityResponseParamsDTO);
    }

    /**
     * 总价任选的枚举类处理
     */
    private interface JFDHStrategyEnumsCalculate {

        /**
         * 计算接口
         *
         * @param activityRuleDTOList       活动的优惠rule
         * @param params                    活动的参数
         * @param activityResponseParamsDTO 优惠结果返回类
         */
        void calculate(List<ActivityRuleDTO> activityRuleDTOList, ActivityParamsDTO params, ActivityResponseParamsDTO activityResponseParamsDTO);
    }

    /**
     * @return 返回 积分兑换的结果
     */
    private JFDHStrategyEnumsCalculate strategyJFDH() {
        return (activityRuleList, params, activityResponseParams) -> {
            if (!StrategyGroupEnum.JFDH_G.getId().equals(activityResponseParams.getPaTemplateId())) {
                return;
            }
            List<ActivityCommodityDTO> commodities = activityResponseParams.getActivityCommodityDTOList();
            List<BaseActivityDTO> baseActivityDtoList = activityRuleList.get(0).getCondition();
            int integral = 0;
            BigDecimal money = BigDecimal.ZERO;
            for (BaseActivityDTO condition : baseActivityDtoList) {
                if (JFDHConditionEnum.JF.getId().equals(condition.getId())) {
                    integral = Integer.parseInt(condition.getValue());
                }
                if (JFDHConditionEnum.JE.getId().equals(condition.getId())) {
                    if (null == condition.getValue() || StringUtil.isBlank(condition.getValue())) {
                        money = new BigDecimal("0");
                        break;
                    }
                    money = Arith.transformToBigDecimal(new BigDecimal(condition.getValue()), 2);
                }
            }
            //设置单个商品属性
            for (ActivityCommodityDTO sku : commodities) {
                //设置单个需要积分
                Integer amount = sku.getSkuAmount() == null ? 1 : sku.getSkuAmount();
                sku.setSkuAmount(amount);
                sku.setIntegral(integral);
                sku.setDiscountsPriceAll(money.multiply(new BigDecimal(amount.toString())));
                if (Objects.nonNull(sku.getDetailPrice())) {
                    sku.setSubtractPriceAll(sku.getDetailPrice()
                            .multiply(new BigDecimal(sku.getSkuAmount().toString()))
                            .subtract(sku.getDiscountsPriceAll())
                            .setScale(2, BigDecimal.ROUND_HALF_UP));
                }

            }
            //设置返回属性
            //需要金额
            BigDecimal totalDiscountsPrice = commodities.stream().map(ActivityCommodityDTO::getDiscountsPriceAll).reduce(BigDecimal.ZERO, BigDecimal::add);

            activityResponseParams.setDiscountsPrice(totalDiscountsPrice);
            //需要总积分
            Integer integralW = commodities.stream().map(sku -> sku.getIntegral() * sku.getSkuAmount()).reduce(0, Integer::sum);
            activityResponseParams.setIntegralAll(integralW);
        };
    }

    private void init() {
        calculateHelper.add(strategyJFDH());
    }

    @Override
    public Boolean calculate() {
        // 获取活动的策略
        List<ActivityRuleDTO> activityStrategiesList = super.getActivityConfigDTO().getActivityRuleDTOList();
        // 获取活动的参数
        ActivityParamsDTO params = super.getParams();
        // 活动返回的参数
        ActivityResponseParamsDTO activityResponseParamsDTO = super.getActivityResponseParamsDTO();
        init();
        DZCXCalculate(activityStrategiesList, params, activityResponseParamsDTO);
        return true;
    }

    private void DZCXCalculate(List<ActivityRuleDTO> activityStrategiesList, ActivityParamsDTO params, ActivityResponseParamsDTO activityResponseParamsDTO) {

        for (JFDHStrategyEnumsCalculate strategy : calculateHelper) {
            strategy.calculate(activityStrategiesList, params, activityResponseParamsDTO);
        }
    }
}
