package com.deepexi.dxp.marketing.domain.promotion.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel
public class DataPermissionDTO {
    @ApiModelProperty(value = "区域id",hidden = true)
    private List<String> areaIdList;
    @ApiModelProperty(value = "城市公司id",hidden = true)
    private List<String> cityIdList;
    @ApiModelProperty(value = "项目id",hidden = true)
    private List<String> projectIdList;
}
