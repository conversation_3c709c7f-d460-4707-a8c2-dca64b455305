//package com.deepexi.dxp.marketing.controller.promotion.openapi;
//
//import com.deepexi.common.extension.AppRuntimeEnv;
//import com.deepexi.dxp.marketing.domain.promotion.dto.activity.*;
//import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityCouponQuery;
//import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityLimitQuery;
//import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityQuery;
//import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionSeckillActivityQuery;
//import com.deepexi.dxp.marketing.domain.promotion.request.activity.ActivityCommoditValidateRequest;
//import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityCreatePostRequest;
//import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityUpdatePostRequest;
//import com.deepexi.dxp.marketing.enums.activity.strategy.StrategyGroupEnum;
//import com.deepexi.dxp.marketing.enums.status.ActivityStatus;
//import com.deepexi.dxp.middle.promotion.util.PageUtil;
//import com.deepexi.dxp.marketing.service.promotion.PromotionActivityLimitMiddleService;
//import com.deepexi.dxp.marketing.service.promotion.PromotionActivityMiddleService;
//import com.deepexi.util.config.Payload;
//import com.deepexi.util.exception.ApplicationException;
//import com.deepexi.util.pageHelper.PageBean;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import io.swagger.annotations.ApiParam;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
//import javax.validation.Valid;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Optional;
//
///**
// * <AUTHOR> xinjian.yao
// * @date 2019/12/5 20:19
// */
//@RestController
//@Slf4j
//@RequestMapping("/open-api/v1/promotion-activity")
//@Api(description = "促销活动管理", value = "促销活动管理", tags = "PromotionActivitySdk")
//public class PromotionActivityOpenApiController {
//
//    @Autowired
//    private PromotionActivityMiddleService promotionActivityMiddleService;
//
//    @Autowired
//    private PromotionActivityLimitMiddleService promotionActivityLimitMiddleService;
//
//    /**
//     * 判断商品是否有被活动关联到
//     *
//     * @param upIds 商品的上架id
//     * @return 是否被关联到
//     */
//    @PostMapping("/sku-join-activity")
//    @ApiOperation(value = "商品是否有被活动关联", nickname = "promotionActivitySkuJoinActivity")
//    public Payload<Boolean> skuJoinActivity(@RequestBody List<Long> upIds) {
//        PromotionActivityQuery query = new PromotionActivityQuery();
//        query.setStatus(Integer.valueOf(ActivityStatus.IN_PROGRESS.getId()));
//        query.setPaTemplateId(Integer.valueOf(StrategyGroupEnum.PTHD_G.getId()));
//        List<PromotionActivityListPostVO> ptActivityList
//                = promotionActivityMiddleService.findAll(query);
//        query.setStatus(Integer.valueOf(ActivityStatus.UN_START.getId()));
//        List<PromotionActivityListPostVO> ptActivityList1
//                = promotionActivityMiddleService.findAll(query);
//
//        query.setPaTemplateId(Integer.valueOf(StrategyGroupEnum.JFDH_G.getId()));
//        List<PromotionActivityListPostVO> activityList
//                = promotionActivityMiddleService.findAll(query);
//        query.setStatus(Integer.valueOf(ActivityStatus.IN_PROGRESS.getId()));
//        List<PromotionActivityListPostVO> activityList1
//                = promotionActivityMiddleService.findAll(query);
//        activityList.addAll(ptActivityList);
//        activityList.addAll(ptActivityList1);
//        activityList.addAll(activityList1);
//
//        activityList.forEach(activity -> {
//            List<SkuCodeBaseDTO> skuList = Optional.ofNullable(activity.getCommodity())
//                    .map(ComdityLimitDTO::getSkuList)
//                    .orElse(new ArrayList<>());
//            skuList.forEach(sku -> {
//                Long upId = Long.valueOf(sku.getUpId());
//                if (upIds.contains(upId)) {
//                    ApplicationException applicationException = new ApplicationException(upId + "该上架id已经被关联活动,下架请先停止活动");
//                    applicationException.setCode("400");
//                    throw applicationException;
//                }
//            });
//        });
//        return new Payload<>(Boolean.TRUE);
//    }
//
//    /**
//     * 分页查询活动的信息
//     *
//     * @param query 查询的信息
//     * @return 返回查询到的信息
//     */
//    @GetMapping("/page")
//    @ApiOperation(value = "分页查询活动", nickname = "promotionActivityFindPage")
//    public Payload<PageBean<PromotionActivityListPostVO>> findPage(@ApiParam(name = "query", required = true) PromotionActivityQuery query) {
//        PageBean<PromotionActivityListPostVO> resultList = promotionActivityMiddleService.findPage(query);
//        return new Payload<>(resultList);
//
//    }
//
//    /**
//     * 查询活动 顺便过滤一些限制信息
//     *
//     * @param query 入参
//     * @return 出参
//     */
//    @PostMapping("/find-page-from-limit")
//    @ApiOperation(value = "查询活动并且过滤一些限制", nickname = "findPageFromLimit")
//    public Payload<PageBean<PromotionActivityLimitFindVO>> findPageFromLimit(@RequestBody PromotionActivityLimitQuery query) {
//
//        List<PromotionActivityLimitFindVO> pageList = promotionActivityLimitMiddleService.findPage(query);
//        PageUtil<PromotionActivityLimitFindVO> responsePageUtil = new PageUtil<>();
//        PageBean<PromotionActivityLimitFindVO> page = responsePageUtil.pageUtil(query.getPage(), query.getSize(), pageList);
//        return new Payload<>(page);
//    }
//
//    /**
//     * 批量查询活动的信息
//     *
//     * @param query 活动的查询信息
//     * @return 查询出来的活动信息
//     */
//    @GetMapping("/list")
//    @ApiOperation(value = "查询列表", nickname = "promotionActivityFindAll")
//    public Payload<List<PromotionActivityListPostVO>> findAll(@ApiParam(name = "query", required = true) @ModelAttribute PromotionActivityQuery query) {
//        List<PromotionActivityListPostVO> resultList = promotionActivityMiddleService.findAll(query);
//        return new Payload<>(resultList);
//    }
//
//    /**
//     * 根据id查询活动
//     *
//     * @param id 活动的id
//     * @return 查询出来的活动信息
//     */
//    @GetMapping("/find-activity-by-id")
//    @ApiOperation(value = "根据id查询活动", nickname = "promotionActivityFindActivityById")
//    public Payload<PromotionActivityListPostVO> findActivityById(@RequestParam Long id) {
//        PromotionActivityListPostVO result = promotionActivityMiddleService.getActivityById(id);
//        return new Payload<>(result);
//    }
//
//
//    /**
//     * 创建一个促销活动
//     *
//     * @param vo 创建活动需要的vo
//     * @return 返回创建成功的主键id
//     */
//    @PostMapping("/create")
//    @ApiOperation(value = "创建活动", nickname = "promotionActivityCreate")
//    public Payload<Long> create(@RequestBody PromotionActivityCreatePostRequest vo) {
//        vo.setAppId(AppRuntimeEnv.getAppId());
//        return new Payload<>(promotionActivityMiddleService.create(vo));
//    }
//
//    /**
//     * 修改活动
//     *
//     * @param id 修改活动的id
//     * @param vo 修改活动需要的vo
//     * @return 是否修改到数据
//     */
//    @PutMapping("/update")
//    @ApiOperation(value = "修改活动", nickname = "promotionActivityUpdate")
//    public Payload<Boolean> update(@RequestParam Long id, @RequestBody PromotionActivityUpdatePostRequest vo) {
//        vo.setAppId(AppRuntimeEnv.getAppId());
//        vo.setActivityId(id);
//        return new Payload<>(promotionActivityMiddleService.updateActivityById(id, vo));
//
//    }
//
//    /**
//     * 删除活动
//     *
//     * @param ids 活动的id数据
//     * @return 是否删除到数据
//     */
//    @DeleteMapping("/delete")
//    @ApiOperation(value = "删除活动", nickname = "promotionActivityDelete")
//    public Payload<Boolean> delete(@RequestBody List<Long> ids) {
//        return new Payload<>(promotionActivityMiddleService.delete(ids));
//    }
//
//    /**
//     * 修改活动的主表数据
//     *
//     * @param vo 修改活动需要的vo
//     * @return 是否修改到数据
//     */
//    @PutMapping("/update-activity-status")
//    @ApiOperation(value = "修改活动状态", nickname = "promotionActivityUpdateActivityStatus")
//    public Payload<Long> updateActivityStatus(@RequestBody PromotionActivityStatusUpdateRequest vo) {
//        return new Payload<>(promotionActivityMiddleService.updateActivityStatus(vo));
//    }
//
//    /**
//     * 查询秒杀活动列表
//     *
//     * @param dto 查询参数
//     * @return 分页响应
//     */
//    @PostMapping("/seckill/page")
//    @ApiOperation(value = "查询秒杀活动分页", nickname = "promotionActivityFindSeckillPage")
//    public Payload<PageBean<PromotionSeckillActivityVO>> findSeckillPage(@RequestBody @Valid PromotionSeckillActivityQuery dto) {
//        PageBean<PromotionSeckillActivityVO> page = promotionActivityMiddleService.findSeckillPage(dto);
//        return new Payload<>(page);
//    }
//
//    /**
//     * 校验商品列表是否已参与活动
//     *
//     * @param requestDTO 查询参数
//     * @return 响应
//     */
//    @PostMapping("/commodity-list-validate")
//    @ApiOperation(value = "校验商品列表是否已参与活动", nickname = "promotionActivityCommodityListValidate")
//    public Payload<ActivityCommodityValidateVO> commodityListValidate(@Valid @RequestBody ActivityCommoditValidateRequest requestDTO) {
//        ActivityCommodityValidateVO responseDTO = promotionActivityMiddleService.validateCommodityList(requestDTO);
//        return new Payload<>(responseDTO);
//    }
//
//    /**
//     * 查询活动-优惠券分页
//     * @param query 促销活动优惠券查询
//     * @return  活动优惠券分页结果
//     */
//    @PostMapping("/activity-coupon/page")
//    @ApiOperation(value = "查询活动-优惠券分页", nickname = "promotionActivityFindActivityCouponPage")
//    public Payload<PageBean<PromotionActivityCouponVO>> findActivityCouponPage(@RequestBody @Valid PromotionActivityCouponQuery query) {
//        return new Payload<>(promotionActivityMiddleService.findActivityCouponPage(query));
//    }
//}
