package com.deepexi.dxp.marketing.service.specify;

import com.deepexi.dxp.marketing.domain.marketing.query.specify.UserProjectQuery;
import com.deepexi.dxp.marketing.domain.marketing.response.UserProjectResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityPageQuery;
import com.deepexi.util.pageHelper.PageBean;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UserProjectService {
    PageBean<UserProjectResponseDTO> getUserProjectNoLogin(UserProjectQuery query);
    PageBean<UserProjectResponseDTO> getUserProjectNoLoginV2(UserProjectQuery query);

    /**
     * 用户可选择项目列表
     * @param query
     * @return
     */
    List<UserProjectResponseDTO> getCanCheckList(UserProjectQuery query);

    /**
     * 项目详情
     * @param projectId
     * @return
     */
    Boolean getProjectPageByHfProjectId(String projectId);

    /**
     * 通过获取pageId
     * @param projectId
     * @return
     */
    Integer getProjectPageId(String projectId);

    /**
     * 用户区域
     * @param query
     * @return
     */
    List<UserProjectResponseDTO> getUserArea(UserProjectQuery query);

    /**
     * 用户城市
     * @param query
     * @return
     */
    List<UserProjectResponseDTO> getUserRealCityByAreaId(UserProjectQuery query);


    /**
     * 获取数据并将数据存到redis
     * @param userId
     * @return
     */
    List<UserProjectResponseDTO> saveToRedis(String userId);

    void initCommunityDataPermission(PromotionActivityPageQuery query);
}
