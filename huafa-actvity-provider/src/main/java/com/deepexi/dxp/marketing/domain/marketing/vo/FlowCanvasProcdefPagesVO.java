package com.deepexi.dxp.marketing.domain.marketing.vo;

import com.deepexi.util.domain.vo.BaseExtVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/11
 */
@Data
@ApiModel
public class FlowCanvasProcdefPagesVO extends BaseExtVO {
    /**
     * 画布id
     */
    @ApiModelProperty(value = "画布id")
    private Long id;
    /**
     * 流程画布任务名称
     */
    @ApiModelProperty(value = "流程画布任务名称")
    private String name;

    /**
     * 流程画布定义编码,用于根据版本区分的同一个画布
     */
    @ApiModelProperty(value = "流程画布定义编码")
    private String code;

    /**
     * 类型（2：模板 1：流程画布）
     */
    @ApiModelProperty(value = "类型（2：模板 1：流程画布）")
    private Integer type;

    /**
     * 执行类型（once:单次，fixed：周期重复， event：事件触发）
     */
    @ApiModelProperty(value = "执行类型（once:单次，fixed：周期重复， event：事件触发）")
    private String executeType;

    /**
     * '立即执行 right_now/定时执行 scheduled /指定时间重复执行 fixed_at_spec_time / 每隔一段时间重复 fixed_at_spec_period / 事件触发 event'
     */
    @ApiModelProperty(value = "'立即执行 right_now/定时执行 scheduled /指定时间重复执行 fixed_at_spec_time / 每隔一段时间重复 fixed_at_spec_period / 事件触发 event'")
    private String runType;

    /**
     * 任务状态(1:未发布，2:运行中，3:暂停中，4:已停止)
     */
    @ApiModelProperty(value = "任务状态(1:未发布，2:运行中，3:暂停中，4:已停止)")
    private Integer status;

    /**
     * 前端发起的一个状态操作是否正在操作中，
     * 当前端发起一个操作，系统立马将该字段改为1，表示正在操作中，
     * 操作完成后，将该字段改为0
     */
    @ApiModelProperty(value = "是否操作中")
    private Boolean inOperation;

    /**
     * cron表达式
     */
    @ApiModelProperty(value = "cron表达式")
    private String cronExpression;

    /**
     * 执行开始时间 (单次和触发的存定时执行时间，周期重复的存定义的开始时间)
     */
    @ApiModelProperty(value = "执行开始时间 (单次和触发的存定时执行时间，周期重复的存定义的开始时间)")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date excTime;

    /**
     * 流程画布与前端约定的协议json字符串
     */
    @ApiModelProperty(value = "流程画布与前端约定的协议json字符串")
    private String contentJson;

    /**
     * 引用的模板id
     */
    @ApiModelProperty(value = "引用的模板id")
    private Long flowTemplateId;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createdBy;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createdTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updatedTime;


    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private Long version;

    /**
     * 执行次数
     */
    @ApiModelProperty(value = "触发次数")
    private Integer excNums;
}
