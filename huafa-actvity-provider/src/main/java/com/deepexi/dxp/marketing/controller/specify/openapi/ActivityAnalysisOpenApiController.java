package com.deepexi.dxp.marketing.controller.specify.openapi;

import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityAnalysisQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityTrendsQuery;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityAnalysisResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityTrendResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.IndexPathResponseDTO;
import com.deepexi.dxp.marketing.service.specify.ActivityAnalysisService;
import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/open-api/v1/open/promotion-activity")
@Api(value = "活动分析", description = "活动分析接口", tags = {"活动分析接口"})
public class ActivityAnalysisOpenApiController {

    @Autowired
    private ActivityAnalysisService promotionActivityService;

    @PostMapping("/analysis")
    @ApiOperation(value="活动分析", notes = "活动分析")
    public Data<PageBean<ActivityAnalysisResponseDTO>> analysisList(@RequestBody ActivityAnalysisQuery query) throws Exception {
        return new Data<>(promotionActivityService.analysisList(query));
    }

    @GetMapping("/getActPartTotalNumber")
    @ApiOperation(value="获取活动参与人数", notes = "获取活动参与人数")
    public Data<Long> getActPartTotalNumber(@RequestParam Long activityId) {
        return new Data<>(promotionActivityService.getActPartTotalNumber(activityId));
    }

    @PostMapping("/findActivityTrend")
    @ApiOperation(value="活动趋势查询", notes = "活动趋势查询")
    public Data<List<ActivityTrendResponseDTO>> findActivityTrends(@RequestBody @Valid ActivityTrendsQuery query) {
        return new Data<>(promotionActivityService.findActivityTrends(query));
    }

    @PostMapping("/findOverviewOfIndicators")
    @ApiOperation(value="指标概览自定义查询", notes = "指标概览自定义查询")
    public Data<List<IndexPathResponseDTO>> findOverviewOfIndicators(@RequestBody @Valid ActivityAnalysisQuery query) {
        return new Data<>(promotionActivityService.findOverviewOfIndicators(query));
    }

    @ApiOperation(value = "海报分享统计")
    @PostMapping("/posterChannelStatistics")
    public PageBean<ActivityAnalysisResponseDTO> posterChannelStatistics(@RequestBody @Valid ActivityAnalysisQuery query) {
        return promotionActivityService.posterChannelStatistics(query);
    }
}
