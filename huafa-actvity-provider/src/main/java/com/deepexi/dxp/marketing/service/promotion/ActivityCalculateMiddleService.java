package com.deepexi.dxp.marketing.service.promotion;

import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.ComdityLimitDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.CommodityActivityVO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.*;
import com.deepexi.dxp.marketing.domain.promotion.dto.coupon.PromotionCouponLoggerListPostResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.CommodityActivityRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.calculate.*;
import com.deepexi.util.pageHelper.PageBean;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> xinjian.yao
 * @date 2019/11/28 11:38
 */
@Service
public interface ActivityCalculateMiddleService {
    /**
     * 购物车计算接口
     *
     * @param activityParamsRequest 购物车入参数
     * @return 出参数
     */
    ShoppingCartVO calculate(ActivityParamsRequest activityParamsRequest);

    /**
     * 积分兑换计算接口
     *
     * @param activityParamsRequest 积分兑换入参数
     * @return 出参数
     */
    ActivityResponseParamsDTO calculateJFDH(ActivityParamsRequest activityParamsRequest);

    /**
     * 积分兑换可兑换商品
     *
     * @param activityParamsRequest 积分兑换入参数
     * @return 出参数
     */
    PageBean<ActivityResponseParamsDTO> showJFDH(ActivityParamsRequest activityParamsRequest, Integer page, Integer size);

    /**
     * 领券活动页面展示
     *
     * @param activityParamsRequest 领券活动入参数
     * @return 出参数
     */
    PageBean<CouponResponseDTO> couponList(ActivityParamsRequest activityParamsRequest, Integer page, Integer size);

    /**
     * 领券(创建并返回结果，特殊类型【1：会员权益优惠券，6：第三方权益优惠券】的优惠券返回null)
     *
     * @param activityRequestParams
     */
    BaseActivityDTO getCoupon(CouponActivityParamsRequest activityRequestParams);

    /**
     * 购物车进入订单页面接口
     *
     * @param activityRequestParams
     * @return
     */
    OrderEditResponseDTO orderCalculate(ActivityOrderParamsRequest activityRequestParams);


    /**
     * 查询商品的可用活动
     *
     * @param commodityActivityRequest 商品的信息
     * @return 活动的信息
     */
    CommodityActivityVO commodityActivity(CommodityActivityRequest commodityActivityRequest);

    /**
     * 多个商品查询促销活动
     *
     * @param commodityListActivityRequest
     * @return
     */
    List<CommodityActivityVO> commodityListActivity(CommodityListActivityRequest commodityListActivityRequest);

    /**
     * 查询活动下的商品信息
     *
     * @param activityIds 活动ids
     * @return 活动信息
     */
    List<ActivityConfigDTO> getCommodityByActivityId(List<Long> activityIds);


    /**
     * 当前可用优惠券列表
     *
     * @param activityParamsRequest
     * @return
     */
    PageBean<PromotionCouponLoggerListPostResponseDTO> usableCouponList(ActivityParamsRequest activityParamsRequest);

    /**
     * 返回优惠券可用商品
     *
     * @param couponId 优惠劵id
     * @return
     */
    ComdityLimitDTO returnCouponCommodityLimit(Long couponId);

    /**
     * 获得积分兑换次数
     *
     * @param appId
     * @param tenantId
     * @param userId
     * @param userType
     * @return
     */
    Integer getJFDHTimes(Long appId, String tenantId, Long userId, String userType);

    /**
     * 购物车计算 以门店维度返回
     *
     * @param activityParamsRequest
     * @return
     */
    ShoppingCartShopAspectVO shoppingCartShopAspect(ActivityParamsRequest activityParamsRequest);
}
