package com.deepexi.dxp.marketing.domain.marketing.dto;

import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import lombok.Data;


/**
 * 流程画布-执行-打/移标签
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-07 16:03
 */
@Data
public class FlowCanvasExecuteTagsDTO extends SuperDTO {
    /**
     * 画布ID
     */
    private Long flowId;

    /**
     * 画布节点ID
     */
    private String nodeId;

    /**
     * 类型
     */
    private String type;

    /**
     * 标签ID
     */
    private Long tagId;

    public FlowCanvasExecuteTagsDTO(){

    }

    public FlowCanvasExecuteTagsDTO(String nodeId, String type, Long tagId){
        this.nodeId = nodeId;
        this.tagId = tagId;
        this.type = type;
    }
}
