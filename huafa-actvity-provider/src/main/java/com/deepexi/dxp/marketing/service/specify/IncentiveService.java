package com.deepexi.dxp.marketing.service.specify;

import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPartakeRequest;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.RechargeMobileRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.SendIncentiveCallBackRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.SendIncentiveRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.IncentiveResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.RechargeMobileResponseDTO;
import com.deepexi.dxp.marketing.domain.specify.dto.ActivityFormFeedbackDTO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.PromotionHisResourceDO;

/**
 * 激励核销（红包，话费请求接口）
 * <AUTHOR>
 */
public interface IncentiveService {
    /**
     * 发送红包激励
     * @param requestDTO
     * @return
     */
    IncentiveResponseDTO sendIncentive(SendIncentiveRequestDTO requestDTO);
    /**
     * 异步发送红包激励
     * @param requestDTO
     * @return
     */
    void sendIncentiveAsync(SendIncentiveRequestDTO requestDTO);

    /**
     * 话费充值
     * @param requestDTO
     * @return
     */
    RechargeMobileResponseDTO rechargeMobile(RechargeMobileRequestDTO requestDTO);
    /**
     * 异步话费充值
     * @param requestDTO
     * @return
     */
    void rechargeMobileAsync(RechargeMobileRequestDTO requestDTO);

    /**
     * 小程序红包回调
     * @param requestDTO
     * @return
     */
    Boolean sendIncentiveCallBack(SendIncentiveCallBackRequestDTO requestDTO);

    /**
     * 话费充值回调方法
     * @param reqNo
     * @param result
     * @param state
     * @return
     */
    Boolean moneyCallBack(String reqNo, String result, String state);

    String sendTransfer(SendIncentiveRequestDTO requestDTO) throws Exception;

    void thirdVerify(ActivityPartakeRequest activityPartakeLogDO, PromotionHisResourceDO promotionHisResourceDO,
                     ActivityFormFeedbackDTO activityFormFeedbackDTO, String activityName, String resourceCode, String mpFrom);
}
