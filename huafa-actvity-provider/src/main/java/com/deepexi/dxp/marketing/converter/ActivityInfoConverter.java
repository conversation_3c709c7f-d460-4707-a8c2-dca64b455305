package com.deepexi.dxp.marketing.converter;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.deepexi.common.extension.AppRuntimeEnv;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPageShareVO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPageVO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPartakeLogDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.FeedbackInfoVO;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityCreatePostRequest;
import com.deepexi.dxp.marketing.domain.specify.dto.ActivityFormFeedbackDTO;
import com.deepexi.dxp.marketing.enums.resource.BottomBtnTypeEnum;
import com.deepexi.dxp.marketing.enums.specify.PromotionResourceCouponCategoryEnum;
import com.deepexi.dxp.marketing.enums.specify.PromotionResourceTypeEnum;
import com.deepexi.dxp.marketing.enums.specify.VerifyRefundStatusEnum;
import com.deepexi.dxp.marketing.enums.specify.VerifyTypeEnum;
import com.deepexi.dxp.marketing.utils.MapBeanUtil;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.*;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.DateUtils;
import com.deepexi.util.StringUtil;
import com.deepexi.util.exception.ApplicationException;
import com.google.common.collect.Lists;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2021/5/31 10:32
 */
public class ActivityInfoConverter {

    /**
     * 活动页信息转换
     * @param requestDTO
     * @return
     */
    public static ActivityPageDO activityPageConverter(PromotionActivityCreatePostRequest requestDTO) {

        //默认一个tenantId appId 开始
        AppRuntimeEnv.setTenantId("hfb981fd0e654f7c90470bc865d83690");
        AppRuntimeEnv.setAppId(1001L);
        //默认一个tenantId appId 开始

        ActivityPageDO activityPageDO = new ActivityPageDO();
        activityPageDO.setAppId(AppRuntimeEnv.getAppId());
        activityPageDO.setTenantId(AppRuntimeEnv.getTenantId());
        activityPageDO.setCreatedBy(requestDTO.getCreatedBy());
        activityPageDO.setUpdatedBy(requestDTO.getUpdatedBy());
        ActivityPageVO activityPageVO = requestDTO.getActivityPageVO();
        if (Objects.nonNull(activityPageVO)){
            activityPageDO.setActivityIconUrl(activityPageVO.getActivityIconUrl());
            activityPageDO.setActivityDiagram(activityPageVO.getActivityDiagram());
            activityPageDO.setActivityRulesUrl(activityPageVO.getActivityRulesUrl());
            List<Integer> bottomBtnTypes = activityPageVO.getBottomBtnTypes();
            if(CollectionUtil.isNotEmpty(bottomBtnTypes)){
                String collect = bottomBtnTypes.stream().map(String::valueOf).collect(Collectors.joining(","));
                activityPageDO.setBottomBtnType(collect);
                if (collect.contains(BottomBtnTypeEnum.CONTACT_NUMBER.getId().toString()) && StringUtil.isBlank(activityPageVO.getPhone())){
                    throw new ApplicationException("底部按钮联系电话不能为空");
                }
                if (collect.contains(BottomBtnTypeEnum.CONTACT_ONLINE.getId().toString()) && StringUtil.isBlank((String)activityPageVO.getExt().get("btn6Url"))){
                    throw new ApplicationException("底部按钮在线咨询图片不能为空");
                }
            }
            activityPageDO.setPhone(activityPageVO.getPhone());
            activityPageDO.setType(1);
            activityPageDO.setIsShowRanklist(activityPageVO.getIsShowRanklist());
            activityPageDO.setRanklistUrl(activityPageVO.getRanklistUrl());
            activityPageDO.setInviteAssistBtnText(activityPageVO.getInviteAssistBtnText());
            activityPageDO.setInviteColorType(activityPageVO.getInviteColorType());
            activityPageDO.setBackGroundUrl(activityPageVO.getBackGroundUrl());
            activityPageDO.setBackGroundColor(activityPageVO.getBackGroundColor());
            activityPageDO.setButtonColor(activityPageVO.getButtonColor());
            activityPageDO.setButtonLineColor(activityPageVO.getButtonLineColor());
            activityPageDO.setButtonTextColor(activityPageVO.getButtonTextColor());
            activityPageDO.setCardLotteryImg(activityPageVO.getCardLotteryImg());
            activityPageDO.setCardBackImg(activityPageVO.getCardBackImg());
            activityPageDO.setMergeBackgroundImg(activityPageVO.getMergeBackgroundImg());
            activityPageDO.setMergeCardImg(activityPageVO.getMergeCardImg());
            activityPageDO.setMergeTextColor(activityPageVO.getMergeTextColor());
            activityPageDO.setMergeButtonImg(activityPageVO.getMergeButtonImg());
            activityPageDO.setHeadUrl(activityPageVO.getHeadUrl());
            activityPageDO.setExt(activityPageVO.getExt());
        }
        return activityPageDO;
    }

    /**
     * 分享也信息转换
     * @param requestDTO
     * @return
     */
    public static ActivityPageShareDO activityShareConverter(PromotionActivityCreatePostRequest requestDTO) {

        //默认一个tenantId appId 开始
        AppRuntimeEnv.setTenantId("hfb981fd0e654f7c90470bc865d83690");
        AppRuntimeEnv.setAppId(1001L);
        //默认一个tenantId appId 开始

        ActivityPageShareDO activityPageShareDO = new ActivityPageShareDO();
        activityPageShareDO.setAppId(AppRuntimeEnv.getAppId());
        activityPageShareDO.setTenantId(AppRuntimeEnv.getTenantId());
        activityPageShareDO.setCreatedBy(requestDTO.getCreatedBy());
        activityPageShareDO.setUpdatedBy(requestDTO.getUpdatedBy());
        ActivityPageShareVO activityPageShareVO = requestDTO.getActivityPageShareVO();
        if (Objects.nonNull(activityPageShareVO)){
            //如果分享标题，海报标题，分享海报默认设置
            activityPageShareDO.setShareTitle(StrUtil.blankToDefault(activityPageShareDO.getShareTitle(),requestDTO.getActivityName()));
            activityPageShareDO.setShareContent(activityPageShareVO.getShareContent());
            activityPageShareDO.setShareIconUrl(activityPageShareVO.getShareIconUrl());
            activityPageShareDO.setPosterTitle(StrUtil.blankToDefault(activityPageShareDO.getPosterTitle(),requestDTO.getActivityName()));
            activityPageShareDO.setSharePosterUrl(StrUtil.blankToDefault(activityPageShareDO.getSharePosterUrl(),requestDTO.getActivityPageVO().getActivityIconUrl()));
            activityPageShareDO.setType(1);
        }
        return activityPageShareDO;
    }

    /**
     * 项目参与活动中间表
     * @param requestDTO
     * @return
     */
    public static List<ActivityParticipationDO>  participationConverter(PromotionActivityCreatePostRequest requestDTO, Long activityId) {

        //默认一个tenantId appId 开始
        AppRuntimeEnv.setTenantId("hfb981fd0e654f7c90470bc865d83690");
        AppRuntimeEnv.setAppId(1001L);
        //默认一个tenantId appId 开始

        List<ActivityParticipationDO> activityParticipationList = Lists.newArrayList();

        if (CollectionUtil.isNotEmpty(requestDTO.getProjectIds())){
            requestDTO.getProjectIds().forEach(project->{
                ActivityParticipationDO activityParticipationDO = new ActivityParticipationDO();
                activityParticipationDO.setAppId(AppRuntimeEnv.getAppId());
                activityParticipationDO.setTenantId(AppRuntimeEnv.getTenantId());
                activityParticipationDO.setCreatedBy(requestDTO.getCreatedBy());
                activityParticipationDO.setUpdatedBy(requestDTO.getUpdatedBy());
                activityParticipationDO.setActivityId(activityId);
                activityParticipationDO.setProjectId(project.getProjectId());
                activityParticipationDO.setAreaId(project.getAreaId());
                activityParticipationDO.setAreaName(project.getAreaName());
                activityParticipationDO.setOrgId(project.getOrgId());
                activityParticipationDO.setOrgName(project.getOrgName());
                activityParticipationDO.setPorjectPeriodList(JSON.toJSONString(project.getPorjectPeriodList()));
                activityParticipationDO.setPositionId(project.getPositionId());
                activityParticipationDO.setProjectName(project.getProjectName());
                activityParticipationDO.setProjectNumber(project.getProjectNumber());
                activityParticipationDO.setSaleOrgId(project.getSaleOrgId());
                activityParticipationDO.setSaleOrgName(project.getSaleOrgName());
                activityParticipationDO.setUserAlias(project.getUserAlias());
                activityParticipationDO.setUserName(project.getUserName());
                activityParticipationDO.setCityId(project.getCityId());
                activityParticipationDO.setCityName(project.getCityName());
                activityParticipationDO.setRealCityId(project.getRealCityId());
                activityParticipationDO.setRealCityName(project.getRealCityName());
                activityParticipationDO.setRealAreaId(project.getRealAreaId());
                activityParticipationDO.setRealAreaName(project.getRealAreaName());
                activityParticipationList.add(activityParticipationDO);
            });
        }
        return activityParticipationList;
    }

    /**
     * 用户参与活动记录数据转换
     * @return
     */
    public static ActivityPartakeLogDTO partakeLogConverter(ActivityOrderDO activityOrderDO, ActivityFormFeedbackDTO activityFormFeedbackDTO, PromotionHisResourceDO promotionHisResourceDO, String resourceCode) {

        //默认一个tenantId appId 开始
        AppRuntimeEnv.setTenantId("hfb981fd0e654f7c90470bc865d83690");
        AppRuntimeEnv.setAppId(1001L);
        //默认一个tenantId appId 开始

        ActivityPartakeLogDTO activityPartakeLog = new ActivityPartakeLogDTO();
        activityPartakeLog.setAppId(AppRuntimeEnv.getAppId());
        activityPartakeLog.setTenantId(AppRuntimeEnv.getTenantId());

        activityPartakeLog.setOrderId(activityOrderDO.getId());
        activityPartakeLog.setOrderNo(activityOrderDO.getCode());
        activityPartakeLog.setType(activityOrderDO.getType());
        activityPartakeLog.setPayTime(activityOrderDO.getPayTime());
        activityPartakeLog.setPayMoney(activityOrderDO.getPayMoney());
        activityPartakeLog.setCreatedTime(DateTime.now());

        if(Objects.nonNull(activityFormFeedbackDTO)){
            activityPartakeLog.setUserId(activityFormFeedbackDTO.getUserId());
            activityPartakeLog.setUserName(activityFormFeedbackDTO.getUserName());
            activityPartakeLog.setPhone(activityFormFeedbackDTO.getPhone());
            activityPartakeLog.setNickName(activityFormFeedbackDTO.getNickName());
            activityPartakeLog.setUnionId(activityFormFeedbackDTO.getUnionId());
            activityPartakeLog.setActivityId(activityFormFeedbackDTO.getActivityId());
            if(Objects.nonNull(activityFormFeedbackDTO.getLimits())){
                FeedbackInfoVO feedbackInfoVO = (FeedbackInfoVO)MapBeanUtil.map2Object(activityFormFeedbackDTO.getLimits(),FeedbackInfoVO.class);
                activityPartakeLog.setIdCard(feedbackInfoVO.getIdCard());
                activityPartakeLog.setDetaAddress(feedbackInfoVO.getHouseNum());
                activityPartakeLog.setArea(feedbackInfoVO.getCityName());
                activityPartakeLog.setGetTime(DateUtils.getCurrentTime());
            }
        } else if (activityOrderDO.getId() > 0){
            activityPartakeLog.setUserId(activityOrderDO.getUserId());
            activityPartakeLog.setPhone(activityOrderDO.getPhone());
            activityPartakeLog.setUserName((String) activityOrderDO.getExt().get("userName"));
            activityPartakeLog.setNickName((String) activityOrderDO.getExt().get("nickName"));
            activityPartakeLog.setUnionId(activityOrderDO.getUnionId());
            activityPartakeLog.setActivityId(activityOrderDO.getActivityId());
            //如果有报名人数则赋值
            activityPartakeLog.setNeedFissonCount((Integer) activityOrderDO.getExt().get("total"));
            Map<String, Object> ext = new HashMap<>();
            ext.put("items", activityOrderDO.getExt().get("items"));
            ext.put("userRole", activityOrderDO.getExt().get("userRole"));
            activityPartakeLog.setExt(ext);
        }

        if(Objects.nonNull(promotionHisResourceDO)){
            activityPartakeLog.setPrizeResult(promotionHisResourceDO.getName());
            activityPartakeLog.setResourceId(promotionHisResourceDO.getId());
        }
        activityPartakeLog.setCode(resourceCode);

        return activityPartakeLog;
    }

    public static ActivityVerifyDO verifyConverter(ActivityOrderDO activityOrderDO,ActivityFormFeedbackDTO activityFormFeedbackDTO, PromotionHisResourceDO promotionHisResourceDO, String resourceCode, Integer verifyStatus, Integer isPay) {

        //默认一个tenantId appId 开始
        AppRuntimeEnv.setTenantId("hfb981fd0e654f7c90470bc865d83690");
        AppRuntimeEnv.setAppId(1001L);
        //默认一个tenantId appId 开始

        ActivityVerifyDO activityVerifyDO = new ActivityVerifyDO();
        activityVerifyDO.setAppId(AppRuntimeEnv.getAppId() == null?1001L:AppRuntimeEnv.getAppId());
        activityVerifyDO.setTenantId(StringUtil.isNotEmpty(AppRuntimeEnv.getTenantId())?AppRuntimeEnv.getTenantId():"hfb981fd0e654f7c90470bc865d83690");
        activityVerifyDO.setResourceId(promotionHisResourceDO.getId());
        activityVerifyDO.setUserId(activityOrderDO.getUserId());
        activityVerifyDO.setNickName(activityFormFeedbackDTO != null ? activityFormFeedbackDTO.getNickName() : (String)activityOrderDO.getExt().get("nickName"));
        activityVerifyDO.setUserName(activityFormFeedbackDTO != null ? activityFormFeedbackDTO.getUserName() : (String)activityOrderDO.getExt().get("userName"));
        activityVerifyDO.setPhone(activityFormFeedbackDTO != null ? activityFormFeedbackDTO.getPhone() : activityOrderDO.getPhone());
        activityVerifyDO.setName(promotionHisResourceDO.getName());
        //核销
        if (activityOrderDO.getExt() != null && activityOrderDO.getExt().size() > 0) {
            activityVerifyDO.setProjectId(activityOrderDO.getExt().get("projectId") != null ?
                    activityOrderDO.getExt().get("projectId").toString() : "");
            activityVerifyDO.setProjectName(activityOrderDO.getExt().get("projectName") != null ?
                    activityOrderDO.getExt().get("projectName").toString() : "");
        }
        activityVerifyDO.setUnionId(activityFormFeedbackDTO != null ? activityFormFeedbackDTO.getUnionId() : activityOrderDO.getUnionId());
        activityVerifyDO.setType(activityOrderDO.getType());
        activityVerifyDO.setCreatedTime(new Date());
        activityVerifyDO.setUpdatedBy(activityFormFeedbackDTO != null ? activityFormFeedbackDTO.getUserName() : (String)activityOrderDO.getExt().get("userName"));
        activityVerifyDO.setCreatedBy(activityVerifyDO.getCreatedBy());
        activityVerifyDO.setIsPay(isPay);
        activityVerifyDO.setValidTimeType(promotionHisResourceDO.getValidTimeType());
        activityVerifyDO.setValidStartTime(promotionHisResourceDO.getValidStartTime());
        activityVerifyDO.setValidEndTime(promotionHisResourceDO.getValidEndTime());
        activityVerifyDO.setValidDay(promotionHisResourceDO.getValidDay());
        activityVerifyDO.setPayTime(activityOrderDO.getPayTime());
        activityVerifyDO.setCode(resourceCode);
        activityVerifyDO.setRefundStatus(VerifyRefundStatusEnum.NO_REFUND.getId());
        activityVerifyDO.setVerifyStatus(verifyStatus);

        //判断是优惠券还是礼品券
        if(PromotionResourceTypeEnum.COUPON.getId().equals(promotionHisResourceDO.getType()) && Objects.nonNull(promotionHisResourceDO.getCouponCategory())){
            if(
                PromotionResourceCouponCategoryEnum.REAL_ESTATE_COUPON.getId().equals(promotionHisResourceDO.getCouponCategory()) ||
                PromotionResourceCouponCategoryEnum.ORDINARY_COUPON.getId().equals(promotionHisResourceDO.getCouponCategory())
            ){
                activityVerifyDO.setVerifyType(VerifyTypeEnum.COUPON.getId());

            } else if(PromotionResourceCouponCategoryEnum.COMMODITY_COUPON.getId().equals(promotionHisResourceDO.getCouponCategory())){
                activityVerifyDO.setVerifyType(VerifyTypeEnum.GIFT.getId());
            }
        } else if(PromotionResourceTypeEnum.THIRD_PARTY.getId().equals(promotionHisResourceDO.getType())){
            activityVerifyDO.setVerifyType(VerifyTypeEnum.THIRD.getId());
            //第三方资源核销项目取资源
            activityVerifyDO.setProjectId(promotionHisResourceDO.getProjectId());
            activityVerifyDO.setProjectName(promotionHisResourceDO.getProjectName());
        }

        activityVerifyDO.setOrderId(activityOrderDO.getId());
        activityVerifyDO.setActivityId(activityOrderDO.getActivityId());
        //activityVerifyDO.setProjectName(activityFormFeedbackDTO.getProjectName());
        return activityVerifyDO;
    }

}

