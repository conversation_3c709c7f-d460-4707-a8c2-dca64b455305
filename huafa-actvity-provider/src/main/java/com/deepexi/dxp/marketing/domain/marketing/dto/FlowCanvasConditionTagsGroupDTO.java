package com.deepexi.dxp.marketing.domain.marketing.dto;

import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import lombok.Data;

/**
 * 流程画布-条件节点—标签客群配置
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-07 16:03
 */
@Data
public class FlowCanvasConditionTagsGroupDTO extends SuperDTO {

    /**
     * 画布ID
     */
    private Long flowId;

    /**
     * 画布节点ID
     */
    private String nodeId;

    /**
     * 标签或客群，1:标签;2:客群
     */
    private Integer type;

    /**
     * 判断条件名称
     */
    private String name;

    /**
     * 计算符号
     */
    private String symbols;

    /**
     * 标签ID或客群ID
     */
    private Long tagId;

    /**
     * 标签或客群名称
     */
    private String tagName;
}
