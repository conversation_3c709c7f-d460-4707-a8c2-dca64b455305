package com.deepexi.dxp.marketing.domain.merber.dto;

import com.deepexi.util.domain.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Class: SdkMemberLevelConfigFindAllRequestDTO
 * @Description:
 * @Author: lizhongbao
 * @Date: 2020/7/28
 **/
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class SdkMemberLevelConfigFindAllRequestDTO extends BaseDTO {
    @ApiModelProperty("等级描述")
    private String description;
    @ApiModelProperty("有效期")
    private String effectTime;
    @ApiModelProperty("有效类型")
    private Integer effectType;
    @ApiModelProperty("表达式")
    private String express;
    @ApiModelProperty("图片")
    private String iconUrl;
    @ApiModelProperty("主键")
    private Long id;
    @ApiModelProperty("等级编码")
    private String levelCode;
    @ApiModelProperty("等级组ID")
    private Long levelGroupId;
    @ApiModelProperty("等级名称")
    private String levelName;
    @ApiModelProperty("排序")
    private Integer sort;
    @ApiModelProperty("状态")
    private Integer status;
    @ApiModelProperty("类型")
    private Integer type;
}
