package com.deepexi.dxp.marketing.domain.marketing.dto;

import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * 会员等级
 *
 * <AUTHOR>
 * @since 2020年06月05日 10:34
 */
@Data
public class MemberLevelListDTO extends SuperDTO implements Serializable {

    private static final long serialVersionUID = -387159476864683268L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 图标
     */
    private String iconUrl;

    /**
     * 覆盖用户数
     */
    private Long coverMemberNum;

    /**
     * 所需成长值
     */
    private String growthValue;

    /**
     * 升级礼包数量
     */
    private Long upgradePackageNum;

    /**
     * 专属权益数量
     */
    private Long exclusiveRightsNum;

    /**
     * 状态(0-禁用、1-启用)
     */
    private Integer status;

    /**
     * 等级的排序
     */
    private Integer sort;

    /**
     * 有效期值
     */
    private Integer expiredValue;

    /**
     * 有效期单位
     */
    private Integer unit;

    /**
     * 有效类型（0永久 1限期）
     */
    private Integer effectType;
}
