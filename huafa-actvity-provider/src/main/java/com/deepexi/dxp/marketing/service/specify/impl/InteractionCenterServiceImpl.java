package com.deepexi.dxp.marketing.service.specify.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.cnhuafas.common.service.GatewayService;
import com.deepexi.dxp.marketing.constant.InteractionConstant;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.SendTemplateNewsRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.TemplateSendRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.InteractiveResponseDTO;
import com.deepexi.dxp.marketing.service.specify.InteractionCenterService;
import com.deepexi.util.JsonUtil;
import com.deepexi.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 交互中心
 * <AUTHOR>
 */
@Service
@Slf4j
public class InteractionCenterServiceImpl implements InteractionCenterService {

    @Resource
    private GatewayService gatewayService;

    @Override
    @Async
    public void templateSend(TemplateSendRequestDTO requestDTO) {
        try{
            log.info("=====================发送短信通知====================");
            String postFormData = gatewayService.getInteractionServiceClient().doPost(InteractionConstant.MESSAGE_TEMPLATE_SEND_URL, JSON.toJSONString(requestDTO));
            log.info("发送短信通知返回：{}",postFormData);
            if(StringUtil.isEmpty(postFormData)){
                log.info("发送短信通知失败：{}",postFormData);
            }
        }catch (Exception e){
            log.info("发送短信通知异常请求参数:{}", JsonUtil.bean2JsonString(requestDTO));
            log.error("发送短信通知异常:",e);
        }
    }

    @Override
    @Async
    public InteractiveResponseDTO miniTemplateNews(SendTemplateNewsRequestDTO requestDTO) {
        try{
            log.info("=====================小程序模板消息发送====================");
            if (SendTemplateNewsRequestDTO.MP_FROM_ZYT.equals(requestDTO.getMpFrom())) {
                requestDTO.setJumpUrl(InteractionConstant.MINI_TEMPLATE_NEWS_JUMP_URL);
            }
            String postFormData = gatewayService.getInteractionServiceClient().doPost(InteractionConstant.MINI_TEMPLATE_NEWS_URL, JSON.toJSONString(requestDTO));
            log.info("小程序模板消息发送返回：{}",postFormData);
            if(StringUtil.isEmpty(postFormData)){
                log.error("小程序模板消息发送发送失败:{}", JsonUtil.bean2JsonString(requestDTO));
                return null;
            }
            return JSONUtil.toBean(postFormData, InteractiveResponseDTO.class);
        }catch (Exception e){
            log.info("小程序模板消息发送发送异常请求参数:{}", JsonUtil.bean2JsonString(requestDTO));
            log.error("小程序模板消息发送发送异常:",e);
            InteractiveResponseDTO incentiveResponseDTO = new InteractiveResponseDTO();
            incentiveResponseDTO.setCode("500");
            incentiveResponseDTO.setMsg(e.getMessage());
            incentiveResponseDTO.setData(null);
            return incentiveResponseDTO;
        }
    }

}
