package com.deepexi.dxp.marketing.service.specify;

import com.deepexi.dxp.marketing.domain.marketing.query.specify.LuckyDrawPartakeLogQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.PartakeLogQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.UserLotteryRecordQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.VerifyInfoQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.*;
import com.deepexi.dxp.marketing.domain.marketing.response.*;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityLimitDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.PromotionHisResourceDO;
import com.deepexi.util.pageHelper.PageBean;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutionException;

/**
 * 抽奖活动service
 * <AUTHOR>
 */
public interface LuckyDrawService {

    /**
     * 保存抽奖活动信息
     * @param requestDTO 活动详情
     * @return
     */
    @Deprecated
    boolean save(LuckyDrawRequestDTO requestDTO);

    /**
     * 修改抽奖活动信息
     * @param id 活动id
     * @param dto 活动详情
     * @return
     */
    @Deprecated
    Boolean updateActivityById(Long id, LuckyDrawRequestDTO dto);

    /**
     * 逻辑删除活动
     * @param ids
     * @return
     */
    @Deprecated
    Boolean delete(List<Long> ids);

    /**
     * 活动详情
     * @param id 活动id
     * @return
     */
    LuckyDrawResponseDTO detail(Long id);

    /**
     * 用户抽奖记录
     * @param query
     * @return
     */
    @Deprecated
    PageBean<ActivityPartakeLogResponseDTO> userLotteryRecord(UserLotteryRecordQuery query);

    /**
     * 查询中奖结果的奖品列表
     * @param id
     * @return
     */
    Set<String> prizeResultList(Long id);

    /**
     * 奖品领取/核销明细
     * @param query
     * @return
     */
    List<VerifyInfoResponseDTO> verifyInfoList(VerifyInfoQuery query);

    /**
     * 用户抽奖
     * @param query
     * @return
     */
    @Deprecated
    LotteryResourceDTO lottery(LotteryRequestDTO query);

    /**
     * 抽奖活动-奖品列表
     * @param query
     * @return
     */
    PrizeListAndSurplusNumberDTO prizeList(LotteryRequestDTO query) throws ExecutionException;

    @Deprecated
    List<ProjectListResponseDTO> projectList(Long id);

    /**
     * 立即领取
     * @param dto
     * @return
     */
    Boolean receiveNow(ReceiveNowDTO dto);

    /**
     * 初始化每日抽奖次数
     * @return
     */
    Boolean initNumberDrawsDay();

    /**
     * 切换项目
     * @param dto
     * @return
     */
    Boolean switchProject(SwitchProjectDTO dto);

    /**
     * 用户抽奖记录导出
     * @param response
     * @param query
     */
    @Deprecated
    void exportUserLotteryRecordExcel(HttpServletResponse response, UserLotteryRecordQuery query);

    /**
     * 获取奖品列表
     * @param activityId
     * @return List<PromotionHisResourceDO>
     */
    List<PromotionHisResourceDO> getPromotionHisResource(Long activityId);

    /**
     * 奖品规则校验
     * @param activityId
     * @param userId
     * @param hisResourceList
     */
    void prizeConfig(Long activityId, String phone, List<PromotionHisResourceDO> hisResourceList);

    Object share(ActivityUserRelatedDTO activityUserRelated,List<PromotionActivityLimitDO> promotionActivityLimits);

    Integer addDrawNumber(AddDrawNumberDTO dto);
    Boolean addDrawNumber(AddDrawNumberDTO dto,Integer drawNumber);

    ActivityUserRelatedDTO getActivityUserRelated(LotteryRequestDTO query);

    Boolean isShowSwitchCheck(Integer switchNumber, Long activityId,List<ActivityParticipationVO> projectInfoList);

    /**
     * 查询活动中奖记录
     * @param requestDTO
     * @return
     */
    List<WinningRecordResponseDTO> winningRecord(PartakeLogQuery requestDTO);


    /**
     * 抽奖活动未领取记录
     * @param query
     * @return
     */
    PageBean<ActivityPartakeLogResponseDTO> findCertificatesList(LuckyDrawPartakeLogQuery query);
}
