package com.deepexi.dxp.marketing.manager.promotion.impl;

import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityConfigDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.TemplateLimitDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityParamsDTO;
import com.deepexi.dxp.marketing.manager.promotion.Calculate;
import com.deepexi.dxp.marketing.manager.promotion.CalculateResult;

/**
 * <AUTHOR> xinjian.yao
 * @date 2019/11/23 14:08
 */
public abstract class BaseLimit implements Calculate {

    /**
     *  TemplateLimitDTO 活动模板
     *  Activity 活动配置的值
     *  ActivityParamsDTO 活动单前的信息值
     */
    private TemplateLimitDTO templateLimitDTO;
    private ActivityConfigDTO activity;
    private ActivityParamsDTO params;

    public BaseLimit(TemplateLimitDTO templateLimitDTO, ActivityConfigDTO activity, ActivityParamsDTO params) {
        this.templateLimitDTO = templateLimitDTO;
        this.activity = activity;
        this.params = params;
    }

    public TemplateLimitDTO getTemplateLimitDTO() {
        return templateLimitDTO;
    }

    public void setTemplateLimitDTO(TemplateLimitDTO templateLimitDTO) {
        this.templateLimitDTO = templateLimitDTO;
    }

    public ActivityConfigDTO getActivity() {
        return activity;
    }

    public void setActivity(ActivityConfigDTO activity) {
        this.activity = activity;
    }

    public ActivityParamsDTO getParams() {
        return params;
    }

    public void setParams(ActivityParamsDTO params) {
        this.params = params;
    }

    @Override
    public abstract Boolean calculate() ;

    @Override
    public CalculateResult calculateAndReturn() {
        CalculateResult result = new CalculateResult();
        result.setResult(calculate());
        return result;
    }
}
