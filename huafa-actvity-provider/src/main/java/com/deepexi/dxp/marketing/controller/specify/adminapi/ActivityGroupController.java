package com.deepexi.dxp.marketing.controller.specify.adminapi;

import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityGroupQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityGroupRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityGroupRelatedResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityGroupResponseDTO;
import com.deepexi.dxp.marketing.enums.specify.ActivityGroupEnum;
import com.deepexi.dxp.marketing.extension.HuafaRuntimeEnv;
import com.deepexi.dxp.marketing.service.specify.ActivityGroupService;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Objects;

/**
 * 活动组模块controller
 *
 * <AUTHOR>
 * @date  2019/10/24 14:54
 * @see [相关类/方法]
 * @since 1.0
 */
@RestController
@RequestMapping("/admin-api/v1/group")
@Api(value = "活动组模块",tags = {"活动组模块"})
public class ActivityGroupController {

    @Resource
    private ActivityGroupService activityGroupService;

    @PostMapping("/save")
    @ApiOperation(value = "活动组保存", notes = "活动组保存")
    public Data<Boolean> save(@RequestBody @Valid ActivityGroupRequestDTO requestDTO) {
        if (Objects.isNull(requestDTO)){
            throw new ApplicationException("入参对象不能为空");
        }
        return new Data<>(activityGroupService.save(requestDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "活动组编辑", notes = "活动组编辑")
    public Data<Boolean> update(@RequestBody @Valid ActivityGroupRequestDTO requestDTO) {
        if (Objects.isNull(requestDTO)){
            throw new ApplicationException("入参对象不能为空");
        }
        return new Data<>(activityGroupService.update(requestDTO));
    }


    @PostMapping("/pageList")
    @ApiOperation(value = "活动组列表", notes = "活动组列表")
    public Data<PageBean<ActivityGroupResponseDTO>> pageList(@RequestBody ActivityGroupQuery query) {
        query.setType(ActivityGroupEnum.GROUP.getId());
        query.setUserId(HuafaRuntimeEnv.getUserId());
        return new Data<>(activityGroupService.pageList(query));
    }


    @PostMapping("/detail")
    @ApiOperation(value = "活动组详情", notes = "活动组详情")
    public Data<ActivityGroupResponseDTO> detail(@RequestParam Long id) {
        return new Data<>(activityGroupService.detail(id));
    }


    @PostMapping("/activityList")
    @ApiOperation(value = "活动组详情-活动列表", notes = "选择活动列表")
    public Data<PageBean<ActivityGroupRelatedResponseDTO>> activityList(@RequestBody ActivityGroupQuery query) {
        return new Data<>(activityGroupService.activityList(query));
    }

    @DeleteMapping("/delete")
    @ApiOperation(value = "活动组详情-删除", notes = "活动组详情-删除")
    public Data<Boolean> delete(@RequestParam Long id) {
        return new Data<>(activityGroupService.delete(id));
    }


}
