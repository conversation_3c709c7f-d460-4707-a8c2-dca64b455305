package com.deepexi.dxp.marketing.manager.promotion.impl.strategy;

import com.deepexi.dxp.marketing.domain.promotion.dto.activity.ActivityRuleDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityCommodityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityConfigDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.TemplateLimitDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityParamsDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityResponseParamsDTO;
import com.deepexi.dxp.marketing.enums.activity.strategy.StrategyGroupEnum;
import com.deepexi.dxp.marketing.enums.activity.strategy.condition.ZJRXConditionEnum;
import com.deepexi.dxp.marketing.enums.activity.strategy.operation.ZJRXOperationEnum;
import com.deepexi.dxp.marketing.enums.status.ActivityInventory;
import com.deepexi.dxp.marketing.manager.promotion.impl.BaseStrategy;
import com.deepexi.dxp.middle.promotion.util.Arith;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.exception.ApplicationException;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * 总价任选
 *
 * <AUTHOR> xinjian.yao
 * @date 2019/11/26 11:39
 */
@EqualsAndHashCode(callSuper = true)
@Slf4j
public class ZJRXStrategy extends BaseStrategy {


    private List<ZJRXStrategyEnumsCalculate> calculateHelper = new ArrayList<>();


    public ZJRXStrategy(TemplateLimitDTO templateLimitDTO, ActivityConfigDTO activityConfigDTO, ActivityParamsDTO params, ActivityResponseParamsDTO activityResponseParamsDTO) {
        super(templateLimitDTO, activityConfigDTO, params, activityResponseParamsDTO);
    }

    /**
     * 总价任选的枚举类处理
     */
    private interface ZJRXStrategyEnumsCalculate {
        /**
         * @param activityRuleDTOList       活动的优惠rule
         * @param params                    活动的参数
         * @param activityResponseParamsDTO 优惠结果返回类
         */
        void calculate(List<ActivityRuleDTO> activityRuleDTOList, ActivityParamsDTO params, ActivityResponseParamsDTO activityResponseParamsDTO);
    }

    /**
     * @return 返回 n元n件的处理结果
     */
    private ZJRXStrategyEnumsCalculate strategyNN() {
        return (activityRuleList, params, activityResponseParams) -> {
            boolean enumsFlag = activityResponseParams.getPaTemplateId().equals(StrategyGroupEnum.ZJRX_G.getId());
            if (!enumsFlag) {
                return;
            }
            // 订单商品数量
            Integer commodityNumber = getOrderNumber(activityResponseParams);
            if (Objects.isNull(commodityNumber) || commodityNumber.equals(0)) {
                return;
            }
            // 可以享受优惠的商品记录
            List<ActivityCommodityDTO> discountsCommodityList = new ArrayList<>();
            // 需要从订单里面删除可以享受优惠的商品
            List<ActivityCommodityDTO> deletedFlag = new ArrayList<>();

            // 获取订单商品 并且按价格排序
            List<ActivityCommodityDTO> activityCommodityDTOList = activityResponseParams.getActivityCommodityDTOList();
            activityCommodityDTOList.sort(Comparator.comparing(ActivityCommodityDTO::getDetailPrice).reversed());

            if (activityRuleList.get(0).getSort() == null) {
                activityRuleList.forEach(val -> val.setSort(val.getCondition().get(0).getValue()));
            }
            // 规则排序

            activityRuleList.sort(Comparator.comparing((ActivityRuleDTO val) -> Integer.valueOf(val.getSort())).reversed());

            // 优惠金额记录
            double discoutPrice = 0;
            for (ActivityRuleDTO activityRuleDTO : activityRuleList) {
                // 获取n元n件策略下 一条规则的条件的限制信息
                Integer conditionValue = getConditionValue(activityRuleDTO);
                // 订单剩余商品数量小于单前规则条件数量 进行下一条规则匹配
                if (commodityNumber < conditionValue) {
                    continue;
                }

                // 优惠金额累计
                String discuntsStr = activityRuleDTO.getOperation().stream()
                        .filter(val -> ZJRXOperationEnum.HDJG.getId().equals(val.getId()))
                        .findFirst()
                        .map(BaseActivityDTO::getValue)
                        .get();
                discoutPrice = Arith.add(discoutPrice, Double.valueOf(discuntsStr));

                // 订单数量大于配置数量 分配
                for (ActivityCommodityDTO commodity : activityCommodityDTOList) {

                    // 删除表示里面的商品就不重复计算了
                    if (deletedFlag.stream().anyMatch(val -> val.getUpId().equals(commodity.getUpId()))) {
                        continue;
                    }
                    // 判断是非已经存在优惠商品里面了
                    ActivityCommodityDTO activityCommodityDTO = discountsCommodityList.stream()
                            .filter(val -> val.getUpId().equals(commodity.getUpId()))
                            .findFirst()
                            .orElse(null);
                    if (activityCommodityDTO != null) {
                        if (commodity.getSkuAmount() <= conditionValue) {
                            activityCommodityDTO.setSkuAmount(activityCommodityDTO.getSkuAmount() + commodity.getSkuAmount());
                            deletedFlag.add(commodity);
                            conditionValue = conditionValue - commodity.getSkuAmount();
                            commodityNumber = commodityNumber - commodity.getSkuAmount();
                            continue;
                        }
                        activityCommodityDTO.setSkuAmount(activityCommodityDTO.getSkuAmount() + conditionValue);
                        commodity.setSkuAmount(commodity.getSkuAmount() - conditionValue);
                        conditionValue = 0;
                        commodityNumber = commodityNumber - conditionValue;
                        continue;
                    }
                    // 当前sku数量可以直接满足
                    if (commodity.getSkuAmount() <= conditionValue) {
                        discountsCommodityList.add(commodity);
                        deletedFlag.add(commodity);
                        conditionValue = conditionValue - commodity.getSkuAmount();
                        commodityNumber = commodityNumber - commodity.getSkuAmount();
                        continue;
                    }

                    // 配置数量小于单前sku商品的数量 需要对单前的sku进行拆分
                    ActivityCommodityDTO temp = commodity.clone(ActivityCommodityDTO.class);
                    temp.setSkuAmount(conditionValue);
                    commodity.setSkuAmount(commodity.getSkuAmount() - conditionValue);
                    discountsCommodityList.add(temp);
                    commodityNumber = commodityNumber - conditionValue;
                    conditionValue = 0;

                }
            }

            if (discoutPrice == 0) {
                super.getActivityResponseParamsDTO().getActivityCommodityDTOList().forEach(val -> val.setStockType(ActivityInventory.SALES_INVENTORY.getId()));
            }
            // 在订单里面删除可以享受优惠的商品
            activityCommodityDTOList.removeAll(deletedFlag);
            // 计算没有优惠的商品的总价
            activityCommodityDTOList.forEach(commodity -> {
                commodity.setSubtractPriceAll(BigDecimal.ZERO);
                commodity.setDiscountsPriceAll(commodity.getDetailPrice().multiply(new BigDecimal(commodity.getSkuAmount().toString())));
                commodity.setStockType(ActivityInventory.SALES_INVENTORY.getId());
            });
            // 金额分摊

            // 获取所有商品的总价 进行比例的计算需要用到
            double allPrice = getAllPrice(discountsCommodityList, DETAIL_PRICE_ALL);

            if (CollectionUtil.isEmpty(discountsCommodityList)) {
                // 添加优惠信息的商品到订单商品
                activityCommodityDTOList.addAll(discountsCommodityList);
                // 优惠信息汇总
                super.collectDiscounts(activityResponseParams);
                return;
            }
            // 第一商品 有总优惠减去其他的商品的优惠
            ActivityCommodityDTO firstCommodity = discountsCommodityList.get(0);
            // 积累的优惠金额
            double accumulatePrice = 0;

            // 从第一个开始 计算优惠比例
            for (int i = 1; i < discountsCommodityList.size(); i++) {
                ActivityCommodityDTO temp = discountsCommodityList.get(i);
                // 单前商品价格优惠
                BigDecimal commodityDiscounts = temp.getDetailPrice()
                        .multiply(new BigDecimal(temp.getSkuAmount().toString()))
                        .setScale(2, BigDecimal.ROUND_HALF_UP)
                        .divide(BigDecimal.valueOf(allPrice), 2)
                        .multiply(BigDecimal.valueOf(discoutPrice));
                temp.setDiscountsPriceAll(commodityDiscounts);
                temp.setSubtractPriceAll(temp
                        .getDetailPrice()
                        .multiply(new BigDecimal(temp.getSkuAmount().toString()))
                        .subtract(commodityDiscounts));
                // 累计优惠了多少钱
                accumulatePrice = Arith.add(accumulatePrice, commodityDiscounts.doubleValue());
            }

            // 处理第一个优惠 第一个优惠可以获取多少钱
            Double firstDiscount = Arith.sub(discoutPrice, accumulatePrice);
            // 优惠后总价格
            firstCommodity.setSubtractPriceAll(firstCommodity.getDetailPrice()
                    .multiply(new BigDecimal(firstCommodity.getSkuAmount().toString()))
                    .subtract(new BigDecimal(firstDiscount.toString())));
            // 优惠了多少钱
            firstCommodity.setDiscountsPriceAll(new BigDecimal(firstDiscount.toString()));

            // 添加优惠信息的商品到订单商品
            activityCommodityDTOList.addAll(discountsCommodityList);
            // 优惠信息汇总
            super.collectDiscounts(activityResponseParams);
        };
    }


    /**
     * @param activityRuleDTO 规则
     * @return 返回规则下设计的值
     */
    private Integer getConditionValue(ActivityRuleDTO activityRuleDTO) {
        String conditionStr = activityRuleDTO.getCondition()
                .stream()
                .filter(val -> ZJRXConditionEnum.GMDSL.getId().equals(val.getId()))
                .findFirst()
                .map(BaseActivityDTO::getValue)
                .orElseThrow(() -> new ApplicationException("N元N建条件参数错误"));
        return Integer.valueOf(conditionStr);
    }


    /**
     * @param activityResponseParamsDTO 活动信息
     * @return 活动下订单的商品数量
     */
    private Integer getOrderNumber(ActivityResponseParamsDTO activityResponseParamsDTO) {

        return activityResponseParamsDTO
                .getActivityCommodityDTOList()
                .stream()
                .map(ActivityCommodityDTO::getSkuAmount)
                .reduce(0, Integer::sum);

    }


    private void init() {
        calculateHelper.add(strategyNN());
    }

    @Override
    public Boolean calculate() {
        // 获取活动的策略
        List<ActivityRuleDTO> activityStrategiesList = super.getActivityConfigDTO().getActivityRuleDTOList();
        // 获取活动的参数
        ActivityParamsDTO params = super.getParams();
        // 活动返回的参数
        ActivityResponseParamsDTO activityResponseParamsDTO = super.getActivityResponseParamsDTO();
        init();
        calculateZJRX(activityStrategiesList, params, activityResponseParamsDTO);
        return true;
    }

    private void calculateZJRX(List<ActivityRuleDTO> activityStrategiesList, ActivityParamsDTO params, ActivityResponseParamsDTO activityResponseParamsDTO) {

        for (ZJRXStrategyEnumsCalculate strategy : calculateHelper) {
            strategy.calculate(activityStrategiesList, params, activityResponseParamsDTO);
        }
    }
}
