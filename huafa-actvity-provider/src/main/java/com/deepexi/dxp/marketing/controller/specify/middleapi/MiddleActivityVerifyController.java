package com.deepexi.dxp.marketing.controller.specify.middleapi;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityVerifyRejectLogQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.MyVerifyQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.VerifyPageQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityVerifyRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityVerifyInResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityVerifyRejectLogResponseDTO;
import com.deepexi.dxp.marketing.service.specify.ActivityVerifyRejectLogService;
import com.deepexi.dxp.marketing.service.specify.ActivityVerifyService;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("/middle-api/v1/verify")
@Api(value = "middle-核销管理-",tags = {"middle-核销管理模块"})
public class MiddleActivityVerifyController {

    @Autowired
    private ActivityVerifyService activityVerifyService;

    @Autowired
    private ActivityVerifyRejectLogService activityVerifyRejectLogService;

    @PostMapping("/list")
    @ApiOperation(value="核销列表", notes = "核销列表")
    public Data<PageBean<ActivityVerifyInResponseDTO>> list(@RequestBody @Valid VerifyPageQuery query) {
        return new Data<>(activityVerifyService.findPage(query));
    }

    @GetMapping("/detail")
    @ApiOperation(value="核销详情", notes = "核销详情")
    public Data<ActivityVerifyInResponseDTO> detail(@RequestParam Long id) {
        return new Data<>(activityVerifyService.getDetail(id));
    }

    @PostMapping("/rejectLogList")
    @ApiOperation(value="驳回列表", notes = "驳回列表")
    public Data<PageBean<ActivityVerifyRejectLogResponseDTO>> rejectLogList(@RequestBody @Valid ActivityVerifyRejectLogQuery query) {
        return new Data<>(activityVerifyRejectLogService.findPage(query));
    }

    @ApiOperation(value = "核销")
    @PutMapping("/wiped")
    public Data<Boolean> wiped(@RequestBody @Valid ActivityVerifyRequestDTO requestDTO) {
        return new Data<>(activityVerifyService.wiped(requestDTO));
    }


    @ApiOperation(value = "驳回")
    @PutMapping("/reject")
    public Data<Boolean> reject(@RequestBody @Valid ActivityVerifyRequestDTO requestDTO) {
        if (requestDTO.getId() == null){
            throw new ApplicationException("核销id不能为空");
        }
        return new Data<>(activityVerifyService.reject(requestDTO));
    }

    @ApiOperation(value = "退款")
    @PutMapping("/refunds")
    public Data<Boolean> refunds(@RequestBody @Valid ActivityVerifyRequestDTO requestDTO) {//reason 退款原因
        if (requestDTO.getId() == null){
            throw new ApplicationException("核销id不能为空");
        }
        return new Data<>(activityVerifyService.refunds(requestDTO));
    }

    @PostMapping("/myList")
    @ApiOperation(value="个人核销列表", notes = "个人核销列表")
    public Data<PageBean<ActivityVerifyInResponseDTO>> myList(@RequestBody @Valid MyVerifyQuery query) {
        return new Data<>(activityVerifyService.myList(query));
    }

    @GetMapping("/getQrCode")
    @ApiOperation(value="核销二维码", notes = "核销二维码")
    public Data<String> getQrCode(@RequestParam String code) {
        return new Data<>(activityVerifyService.getQrCode(code));
    }

    @GetMapping("/sendIncentiveManual")
    @ApiOperation(value="手动发送红包激励", notes = "手动发送红包激励")
    public Data<Boolean> sendIncentiveManual(@RequestParam Long verifyId) {
        return new Data<>(activityVerifyService.sendIncentiveManual(verifyId));
    }
}
