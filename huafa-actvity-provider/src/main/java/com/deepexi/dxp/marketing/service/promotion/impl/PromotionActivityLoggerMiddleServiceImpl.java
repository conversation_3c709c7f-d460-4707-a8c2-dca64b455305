package com.deepexi.dxp.marketing.service.promotion.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.*;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.OrderLockVO;
import com.deepexi.dxp.marketing.domain.promotion.query.activity.PreSaleTailMoneyQuery;
import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityLoggerQuery;
import com.deepexi.dxp.marketing.domain.promotion.query.activity.StatisticsQuery;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.*;
import com.deepexi.dxp.marketing.enums.activity.strategy.LoggerCommodityEnum;
import com.deepexi.dxp.marketing.enums.activity.strategy.StrategyGroupEnum;
import com.deepexi.dxp.marketing.service.promotion.PromotionActivityLoggerMiddleService;
import com.deepexi.dxp.middle.promotion.converter.PromotionActivityLoggerConverter;
import com.deepexi.dxp.middle.promotion.domain.dto.ActivityLogCountDTO;
import com.deepexi.dxp.middle.promotion.domain.dto.ActivityLogDTO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityLoggerDO;
import com.deepexi.dxp.middle.promotion.domain.query.PromotionActivityLogCountQuery;
import com.deepexi.dxp.middle.promotion.mapper.PromotionActivityLoggerMapper;
import com.deepexi.dxp.middle.promotion.mapper.PromotionActivityMapper;
import com.deepexi.dxp.middle.promotion.util.CollectionsUtil;
//import com.deepexi.dxp.middle.promotion.util.DistributeIdUtil;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import com.deepexi.util.pojo.ObjectCloneUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.*;

/**
 * <AUTHOR> xinjian.yao
 * @date 2019/11/21 16:16
 */
@Service
@Slf4j
public class PromotionActivityLoggerMiddleServiceImpl implements PromotionActivityLoggerMiddleService {

    @Autowired
    private PromotionActivityLoggerMapper promotionActivityLoggerMapper;
    @Autowired
    private PromotionActivityMapper promotionActivityMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean create(PromotionActivityLoggerCreatePostRequest dto) {
        Map<String, Object> orderDetailJson = dto.getOrderDetailJson();
        String jsonString = JSON.toJSONString(orderDetailJson);
        dto.setOrderDetail(jsonString);

        PromotionActivityLoggerDO loggerDo = dto.clone(PromotionActivityLoggerDO.class);

        return promotionActivityLoggerMapper.insert(loggerDo) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(Long id, PromotionActivityLoggerUpdatePostRequest dto) {

        PromotionActivityLoggerDO promotionActivityLoggerDO = dto.clone(PromotionActivityLoggerDO.class);
        promotionActivityLoggerDO.setId(id);
        return promotionActivityLoggerMapper.updateById(promotionActivityLoggerDO) > 0;


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleted(List<String> orderIds) {
        return promotionActivityLoggerMapper.deleteByActivityIds(orderIds) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deletedByIds(List<Long> ids) {
        return promotionActivityLoggerMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<PromotionActivityLoggerListDTO> select(PromotionActivityLoggerQuery query) {
        PromotionActivityLoggerDO valDo = query.clone(PromotionActivityLoggerDO.class);
        List<PromotionActivityLoggerDO> poList = promotionActivityLoggerMapper.selectByAll(valDo);
        return ObjectCloneUtils.convertList(poList, PromotionActivityLoggerListDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderLockVO createBatch(OrderLockRequest dto) {
        Boolean resultFlag = Boolean.TRUE;
        //活动日志处理
        if (CollectionUtil.isNotEmpty(dto.getActivityList())) {
            if (CollectionUtil.isNotEmpty(dto.getCouponList())) {

                dto.getActivityList().forEach(e -> {
                    e.setCouponFlag("1");
                });
            }
            dto.getActivityList().forEach(val -> {
                Map<String, Object> orderDetailJson =
                        val.getOrderDetailJson();
                val.setOrderDetail(JSON.toJSONString(orderDetailJson));
                if (Objects.isNull(val.getCode())) {
//                    val.setCode(DistributeIdUtil.generateId());
                }
            });
            List<PromotionActivityLoggerDO> doList
                    = ObjectCloneUtils.convertList(dto.getActivityList(), PromotionActivityLoggerDO.class);
            doList.forEach(val -> {
//                val.setCode(DistributeIdUtil.generateId());
            });
            resultFlag = promotionActivityLoggerMapper.insertList(doList) > 0;
        }

        OrderLockVO responseDTO = new OrderLockVO();
        responseDTO.setResultFlag(resultFlag);

        return responseDTO;
    }

    @Override
    public Boolean updateBatchByOrderId(List<PromotionActivityLoggerUpdatePostRequest> dtoList) {
        dtoList.forEach(val -> {
            promotionActivityLoggerMapper.updateStatusByOrderNumber(val.getStatus(), val.getOrderNumber());
        });
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean returnSales(List<ReturnSalesRequest> returnSalesRequest) {

        //活动处理
        returnSalesRequest.forEach(val -> {
            PromotionActivityLoggerDO clone = val.clone(PromotionActivityLoggerDO.class);
            clone.setFollowNumber(null);
            List<PromotionActivityLoggerDO> promotionActivityLoggerDOList = promotionActivityLoggerMapper.selectByAll(clone);
            if (CollectionUtil.isEmpty(promotionActivityLoggerDOList)) {
                return;
            }
            log.info("promotionActivityLoggerDOList:" + promotionActivityLoggerDOList.size());
            // 查询出来最多一条数据
            PromotionActivityLoggerDO promotionActivityLoggerDO = promotionActivityLoggerDOList.get(0);
            String orderDetail = promotionActivityLoggerDO.getOrderDetail();
            Integer orderQty = promotionActivityLoggerDO.getOrderQty();

            Map<String, Map<String, Integer>> skuNumberMap = JSON.parseObject(orderDetail, TreeMap.class);
            List<ReturnSalesCommodityDTO> returnCommodityList = val.getReturnSalesCommodityDTOList();
            if (CollectionUtil.isEmpty(returnCommodityList)) {
                throw new ApplicationException("退货商品列表不能为空！");
            }
            // 记录退货的数量
            List<Integer> numberFlag = new ArrayList<>();
            // 遍历一个订单下活动下的退货商品
            //2020年4月27日：因满赠bug，修改数据结构。
            returnCommodityList.forEach(commodity -> {
                log.info("商品信息->commodity：" + commodity.toString());
                Long upId = commodity.getUpId();
                String upIdStr = String.valueOf(upId);
                //商品-数量
                Integer preNumber = skuNumberMap.get(LoggerCommodityEnum.ORDER.getId()).get(upIdStr);
                //赠品-数量
                Integer giftNumber = null;
                if (skuNumberMap.get(LoggerCommodityEnum.GIFT.getId()) != null){
                    giftNumber = skuNumberMap.get(LoggerCommodityEnum.GIFT.getId()).get(upIdStr);
                }

                if (ObjectUtils.isEmpty(preNumber) && ObjectUtils.isEmpty(giftNumber)) {
                    log.error("无此商品或赠品的参加活动信息，现需要退货id:{}",  upIdStr);
                    throw new ApplicationException("无此商品或赠品的参加活动信息");
                }
                //退商品扣减商品购买数量
                if (!ObjectUtils.isEmpty(preNumber)){
                    int resultNumber = preNumber - commodity.getNumber();
                    numberFlag.add(commodity.getNumber());
                    skuNumberMap.get(LoggerCommodityEnum.ORDER.getId()).put(commodity.getUpId().toString(), resultNumber);
                }
                //退赠品-扣减赠品购买数量
                if (!ObjectUtils.isEmpty(giftNumber)){
                    int resultNumber = giftNumber - commodity.getNumber();
                    numberFlag.add(commodity.getNumber());
                    skuNumberMap.get(LoggerCommodityEnum.GIFT.getId()).put(commodity.getUpId().toString(), resultNumber);
                }
            });
            Integer reduce = numberFlag.stream().reduce(orderQty, (a, b) -> a - b);
            PromotionActivityLoggerDO updatedo = new PromotionActivityLoggerDO();
            updatedo.setId(promotionActivityLoggerDO.getId());
            // 2020-10-26 修改活动订单商品数量退货后不扣减展示
            //updatedo.setOrderQty(reduce);
            updatedo.setOrderDetail(JSON.toJSONString(skuNumberMap));
            if (reduce.equals(0)) {
                // 退货状态
                updatedo.setStatus(2);
            }
            promotionActivityLoggerMapper.updateById(updatedo);

        });

        return true;
    }

    @Override
    public PageBean<PromotionActivityLoggerListDTO> findPage(PromotionActivityLoggerQuery query) {

        IPage<PromotionActivityLoggerDO> pageParam = new Page<>(query.getPage(), query.getSize());
        IPage<PromotionActivityLoggerDO> poList = promotionActivityLoggerMapper.selectPage(pageParam, PromotionActivityLoggerConverter.queryWrapper(query));
        PageBean<PromotionActivityLoggerDO> res = new PageBean<>(poList);
        return ObjectCloneUtils.convertPageBean(res, PromotionActivityLoggerListDTO.class);

    }

    @Override
    public Boolean updateByOrder(List<PreSalesActivityLockPostRequest> preSalesList) {
        for (PreSalesActivityLockPostRequest dto : preSalesList) {
            Boolean temp = promotionActivityLoggerMapper.updateByOrderNumberAndAppIdAndTenantId(dto.clone(PromotionActivityLoggerDO.class)) > 0;
            if (!temp) {
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public StatisticsUserActivityCountVO statisticsUserActivityCount(StatisticsQuery query) {

        List<StatisticsUserActivityCountDetailDTO> result = promotionActivityLoggerMapper.statisticsUserActivityCount(query);
        StatisticsUserActivityCountVO responseDTO = new StatisticsUserActivityCountVO();

        responseDTO.setActivityCountResponseDTOList(result);
        responseDTO.getActivityCountResponseDTOList().forEach(user -> {
            user.setUserId(query.getUserId());
            user.setUserType(query.getUserType());
            user.setPaTemplateName(StrategyGroupEnum.getValueById(String.valueOf(user.getPaTemplateId())));
        });

        return responseDTO;
    }

    @Override
    public PreSaleTailMoneyVO retornPreSalesTailMoney(PreSaleTailMoneyQuery query) {
        // 获取活动日志
        List<PromotionActivityLoggerDO> promotionActivityLoggerDOList =
                promotionActivityLoggerMapper.
                        retornPreSalesTailMoney(query);
        // 获取活动详情
        List<Long> idList = CollectionsUtil.getPropertyList(promotionActivityLoggerDOList, PromotionActivityLoggerDO::getActivityId);
        List<PromotionActivityDO> acticityList = promotionActivityMapper.selectBatchIds(idList);
        // 判断是不是预售活动
        List<PreSaleTailMoneyVO.PreSaleTailDateDTO> resultList = new ArrayList<>();
        promotionActivityLoggerDOList.forEach(activityLogger -> {
            Long activityId = activityLogger.getActivityId();
            Boolean exitFlag = checkActivity(acticityList, activityId);
            if (!exitFlag) {
                return;
            }
            resultList.add(activityLogger.clone(PreSaleTailMoneyVO.PreSaleTailDateDTO.class));
        });
        // 返回结果
        PreSaleTailMoneyVO preSaleTailMoneyVO = new PreSaleTailMoneyVO();
        preSaleTailMoneyVO.setActivityIdList(resultList);
        return preSaleTailMoneyVO;
    }

    @Override
    public ActivityLogDTO getLogNumCount(PromotionActivityLogCountQuery query) {
        Map<Long, ActivityLogCountDTO> logNumCount = promotionActivityLoggerMapper.getLogNumCount(query);
        ActivityLogDTO activityLogDTO = new ActivityLogDTO();
        activityLogDTO.setActivityLogCountMap(logNumCount);
        return activityLogDTO;
    }

    private Boolean checkActivity(List<PromotionActivityDO> acticityList, Long activityId) {
        PromotionActivityDO oneInList = CollectionsUtil.findOneInList(acticityList, activity -> activity.getId().equals(activityId));
        if (Objects.nonNull(oneInList) && oneInList.getPaTemplateId().equals(Integer.valueOf(StrategyGroupEnum.YS_G.getId()))) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}





