package com.deepexi.dxp.marketing.service.specify.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cnhuafas.common.service.HttpClientService;
import com.deepexi.dxp.marketing.common.base.utils.CommonExceptionCode;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.config.HuafaConstantConfig;
import com.deepexi.dxp.marketing.constant.CommonConstant;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.MiniOrgRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.SysOptUserLogRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityGroupResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.OneCodeAuthDTO;
import com.deepexi.dxp.marketing.enums.activity.strategy.DeliveryChannelEnum;
import com.deepexi.dxp.marketing.enums.activity.strategy.StrategyGroupEnum;
import com.deepexi.dxp.marketing.enums.specify.ActivityPromotionTypeEnum;
import com.deepexi.dxp.marketing.enums.specify.MiniOrgTypeEnum;
import com.deepexi.dxp.marketing.enums.specify.WxCodeTypeEnum;
import com.deepexi.dxp.marketing.service.specify.MiniProgramService;
import com.deepexi.dxp.marketing.service.specify.UserProjectService;
import com.deepexi.dxp.marketing.utils.HTTPClientUtils;
import com.deepexi.dxp.marketing.utils.HuaFaHmacAuthUtil;
import com.deepexi.dxp.marketing.utils.RestTemplateUtil;
import com.deepexi.dxp.middle.promotion.dao.PromotionActivityDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityGroupDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityGroupRelatedDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityParticipationDAO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityFormFeedbackDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityGroupDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityParticipationDO;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.StringUtil;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pojo.ObjectCloneUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;

@Service
@Slf4j
public class MiniProgramServiceImpl implements MiniProgramService {

    @Autowired
    private HuafaConstantConfig huafaConstantConfig;

    @Autowired
    private PromotionActivityDAO promotionActivityDAO;

    @Autowired
    private UserProjectService userProjectService;

    @Autowired
    private HuaFaHmacAuthUtil huaFaHmacAuthUtil;

    @Resource
    private ActivityGroupDAO activityGroupDAO;

    @Resource
    private ActivityGroupRelatedDAO activityGroupRelatedDAO;

    @Resource
    private ActivityParticipationDAO activityParticipationDAO;
    @Resource
    private HttpClientService httpClientService;



    @Override
    public String createWxCode(String name,Integer type,String path, Map<String, Object> params){
        String postFormData = null;
        Map<String,Object> map = new HashMap<>(4);
        map.put("name",name);
        map.put("codeType",type);
        map.put("extend", JSON.toJSONString(params));
        if(WxCodeTypeEnum.MINI_PROGRAM_PC.getId().equals(type)){//appid
            map.put("appId", huafaConstantConfig.MINI_PROGRAM_APP_ID);
        }
//        if(WxCodeTypeEnum.WEBCHAT_WEBSITE.getId().equals(type)){
//            map.put("path", path);
//        }
        map.put("path", path);

        log.info("创建微信小程序码或者h5二维码请求参数:{}",map);
        String url = huafaConstantConfig.RS_CENTE_BASE_URL+CommonConstant.CREATE_WX_CODE_URL;
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        try{
            postFormData = RestTemplateUtil.orderMonitorPost(url, JSON.toJSONString(map),headers);
        }catch(Exception e){
            log.error("调用获取微信小程序码或者h5二维码异常",e);
            return  null;
            //throw new ApplicationException("获取微信小程序码失败");
        }

        log.info("创建微信小程序码或者h5二维码返回结果:{}",postFormData);

        if(StringUtil.isEmpty(postFormData)){
            //throw new ApplicationException("获取微信小程序码失败");
            log.error("获取微信小程序码或者h5二维码返回结果为空");
            return null;
        }
        JSONObject response = JSONUtil.parseObj(postFormData);
        String code = response.getStr("code");

        if(CommonExceptionCode.SUCCESS.equals(code)) {
            JSONObject data = JSONUtil.parseObj(response.getStr("data"));
            return data.getStr("codeUrl");
        }else{
            //throw new ApplicationException("获取微信小程序码失败");
            log.error("获取微信小程序码或者h5二维码返回失败code:{}",code);
            return null;
        }
    }

    @Override
    public Map<String, String> createOneCodeScene(String createBy, String name, Integer orgType, List<MiniOrgRequestDTO> orgList) {
        String postFormData = null;
        Map<String, String> result = Maps.newHashMap();
        Map<String, Object> map = new HashMap<>(5);
        map.put("name", name);
        map.put("orgType", orgType);
        map.put("createUser", createBy);
        if(CollectionUtil.isNotEmpty(orgList)){
            for(MiniOrgRequestDTO dto:orgList){
                dto.setCityId(dto.getRealCityId());
                dto.setCityName(dto.getRealCityName());
            }
            map.put("hfOrgList",orgList);
        }
        log.info("创建场景码请求参数:{}", map);
        String url = huafaConstantConfig.RS_CENTE_BASE_URL + CommonConstant.CREATE_ONE_CODE_SCENE_URL;
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        try {
            postFormData = RestTemplateUtil.orderMonitorPost(url, JSON.toJSONString(map), headers);
        } catch (Exception e) {
            log.error("获取场景码异常", e);
            return null;
        }
        log.info("获取场景码返回结果:{}", postFormData);

        if (StringUtil.isEmpty(postFormData)) {
            log.error("获取获取场景码返回结果为空");
            return null;
        }
        JSONObject response = JSONUtil.parseObj(postFormData);
        String code = response.getStr("code");
        result.put("code",code);
        if (CommonExceptionCode.SUCCESS.equals(code)) {
            result.put("sceneCode",JSONUtil.parseObj(response.getStr("data")).getStr("code"));
            result.put("id",JSONUtil.parseObj(response.getStr("data")).getStr("id"));
            return result;
        }else if(CommonExceptionCode.ERROR_CODE.equals(code)){
            return result;
        } else {
            log.error("获取获取场景码返回失败code:{}", code);
            return null;
        }
    }

    @Override
    public Map<String, String> updateOneCodeScene(Integer sceneCodeId, Integer orgType, List<MiniOrgRequestDTO> orgList) {

        String postFormData = null;
        Map<String, String> result = Maps.newHashMap();
        Map<String, Object> map = new HashMap<>(5);
        map.put("id",sceneCodeId);
        map.put("orgType", orgType);
        if(CollectionUtil.isNotEmpty(orgList)){
            for(MiniOrgRequestDTO dto:orgList){
                dto.setCityId(dto.getRealCityId());
                dto.setCityName(dto.getRealCityName());
            }
            map.put("hfOrgList",orgList);
        }
        log.info("创建场景码请求参数:{}", map);
        String url = huafaConstantConfig.RS_CENTE_BASE_URL + CommonConstant.UPDATE_ONE_CODE_SCENE_URL;
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        try {
            postFormData = RestTemplateUtil.orderMonitorPost(url, JSON.toJSONString(map), headers);
        } catch (Exception e) {
            log.error("获取场景码异常", e);
            return null;
        }
        log.info("获取场景码返回结果:{}", postFormData);

        if (StringUtil.isEmpty(postFormData)) {
            log.error("获取获取场景码返回结果为空");
            return null;
        }
        JSONObject response = JSONUtil.parseObj(postFormData);
        String code = response.getStr("code");
        result.put("code",code);
        if (CommonExceptionCode.SUCCESS.equals(code)) {
            result.put("sceneCode",JSONUtil.parseObj(response.getStr("data")).getStr("code"));
            result.put("id",JSONUtil.parseObj(response.getStr("data")).getStr("id"));
            return result;
        }else if(CommonExceptionCode.ERROR_CODE.equals(code)){
            return result;
        } else {
            log.error("获取获取场景码返回失败code:{}", code);
            return null;
        }
    }

    @Override
    public void aSynCreateOneCodeScene(Object  object, Integer orgType, List<MiniOrgRequestDTO> orgList,Integer promotionType) {
        CompletableFuture.runAsync(() -> {
            try{
                if(ActivityPromotionTypeEnum.ACTIVITY.getType().equals(promotionType)){//活动

                    PromotionActivityDO promotionActivityDO = (PromotionActivityDO)object;

//                    if(MiniOrgTypeEnum.PROJECT.getId().equals(orgType)){//组织机构为项目时只传项目
//                        orgList.forEach(item ->{
//                            item.setAreaId(null);
//                            item.setAreaName(null);
//                            item.setCityId(null);
//                            item.setCityName(null);
//                        });
//                    }
                    this.activityCreateOneCodeScene(promotionActivityDO,orgType,orgList);
                }else{ //专题活动
                    ActivityGroupDO byId = (ActivityGroupDO)object;
                    this.activityGroupCreateOneCodeScene(byId,orgType);
                }

            }catch (Exception e){
                log.error("生成场景码异常",e);
            }
        });
    }

    @Override
    public void aSynUpdateOneCodeScene(Object  object, Integer orgType, List<MiniOrgRequestDTO> orgList,Integer promotionType) {
        CompletableFuture.runAsync(() -> {
            try{
                if(ActivityPromotionTypeEnum.ACTIVITY.getType().equals(promotionType)){//活动

                    PromotionActivityDO promotionActivityDO = (PromotionActivityDO)object;

//                    if(MiniOrgTypeEnum.PROJECT.getId().equals(orgType)){//组织机构为项目时只传项目
//                        orgList.forEach(item ->{
////                            item.setAreaId(null);
////                            item.setAreaName(null);
//                            item.setCityId(null);
//                            item.setCityName(null);
//                        });
//                    }
                    this.activityUpdateOneCodeScene(promotionActivityDO,orgType,orgList);
                }else{ //专题活动
                    ActivityGroupResponseDTO byId = (ActivityGroupResponseDTO)object;
                    ActivityGroupDO groupResponse = byId.clone(ActivityGroupDO.class);
                    this.activityGroupUpdateOneCodeScene(groupResponse,orgType);
                }

            }catch (Exception e){
                log.error("生成场景码异常",e);
                e.printStackTrace();
            }
        });
    }

    @Override
    public JSONObject createOneCode(String name, List<MiniOrgRequestDTO> orgList, String typeCode, String sceneCode
            , Integer orgType, Integer type, String path, Map<String, Object> params, Integer paTemplateId, Integer appType
            , String remark, String createdBy, Date invalidTime) {
        String postFormData = null;
        Map<String,Object> map = new HashMap<>(4);
        map.put("name",name);
        map.put("extend", JSON.toJSONString(params));
        map.put("appType",appType);
        if(WxCodeTypeEnum.MINI_PROGRAM_PC.getId().equals(type)){//appid
            String appId = huafaConstantConfig.MINI_PROGRAM_APP_ID;
            //如果是报名活动，指定使用社群小程序
            if(paTemplateId != null && StrategyGroupEnum.HF_SIGN_UP_ACT.getId().equals(paTemplateId.toString())){
                appId = huafaConstantConfig.COMMUNITY_APPID;
            }
            map.put("appId", appId);
        }
        if(paTemplateId != null && (StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId().equals(paTemplateId.toString())
                || StrategyGroupEnum.HF_FISSION_REDUCEPRICE_ACT.getId().equals(paTemplateId.toString()))){
            map.put("appId", huafaConstantConfig.DY_APPID);
        }

        map.put("orgType",orgType);
        map.put("path", path);
        map.put("sceneCode",sceneCode);
        map.put("typeCode",typeCode);
        //说明信息
        map.put("codeInfo",remark);
        //创建用户名称
        map.put("createUserName", createdBy);
        //失效时间
        map.put("invalidTime", invalidTime);
        if(CollectionUtil.isNotEmpty(orgList)){

            for(MiniOrgRequestDTO dto:orgList){
                dto.setCityId(dto.getRealCityId());
                dto.setCityName(dto.getRealCityName());
            }

            map.put("hfOrgList",orgList);
        }

        log.info("创建一物一码请求参数:{}",map);
        String url = huafaConstantConfig.RS_CENTE_BASE_URL+CommonConstant.CREATE_ONE_CODE_URL;
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        try{
            postFormData = RestTemplateUtil.orderMonitorPost(url, JSON.toJSONString(map),headers);
        }catch(Exception e){
            log.error("调用一物一码异常",e);
            throw new ApplicationException(CommonExceptionCode.INVALIDATION,"生成推广码异常!");
        }

        log.info("创建一物一码返回结果:{}",postFormData);

        if(StringUtil.isEmpty(postFormData)){
            log.error("一物一码返回结果为空");
            throw new ApplicationException(CommonExceptionCode.INVALIDATION,"生成推广码异常!");
        }
        JSONObject response = JSONUtil.parseObj(postFormData);
        String code = response.getStr("code");

        if(CommonExceptionCode.SUCCESS.equals(code)) {
            JSONObject data = JSONUtil.parseObj(response.getStr("data"));
            return data;
        }else{
            log.error("一物一码返回失败code:{}",code);
            throw new ApplicationException(CommonExceptionCode.INVALIDATION,response.getStr("msg"));
        }
    }

    @Override
    public Boolean deleteByCodeId(Integer codeId) {
        String postFormData = null;
        String url = huafaConstantConfig.RS_CENTE_BASE_URL + CommonConstant.DELETE_ONE_CODE_URL + "/" + codeId;
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        try {
            postFormData = RestTemplateUtil.orderMonitorPost(url, null, headers);
        } catch (Exception e) {
            log.error("删除一物一码异常", e);
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "删除一物一码异常!");
        }
        log.info("删除一物一码返回结果:{}", postFormData);
        if (StringUtil.isEmpty(postFormData)) {
            log.error("删除一物一码返回结果为空");
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "删除一物一码异常!");
        }
        JSONObject response = JSONUtil.parseObj(postFormData);
        String code = response.getStr("code");

        if (CommonExceptionCode.SUCCESS.equals(code)) {
            return Boolean.TRUE;
        } else {
            log.error("删除一物一码返回失败code:{}", code);
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, response.getStr("msg"));
        }
    }

    @Override
    public JSONObject updateOneCode(String name, List<MiniOrgRequestDTO> orgList, String typeCode, String sceneCode,
                                    Integer orgType, Integer type, String path, Map<String, Object> params,
                                    Integer paTemplateId, Integer appType, Integer codeId) {
        String postFormData = null;
        Map<String,Object> map = new HashMap<>(4);
        map.put("id",codeId);
        map.put("name",name);
        map.put("extend", JSON.toJSONString(params));
        map.put("appType",appType);
        if(WxCodeTypeEnum.MINI_PROGRAM_PC.getId().equals(type)){//appid
            map.put("appId", huafaConstantConfig.MINI_PROGRAM_APP_ID);
        }
        if(paTemplateId != null && (StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId().equals(paTemplateId.toString())
                || StrategyGroupEnum.HF_FISSION_REDUCEPRICE_ACT.getId().equals(paTemplateId.toString()))){
            map.put("appId", huafaConstantConfig.DY_APPID);
        }
        map.put("orgType",orgType);
        map.put("path", path);
        map.put("sceneCode",sceneCode);
        map.put("typeCode",typeCode);

        if(CollectionUtil.isNotEmpty(orgList)){
            map.put("orgList",orgList);
        }

        log.info("更新一物一码接口请求参数:{}",map);
        String url = huafaConstantConfig.RS_CENTE_BASE_URL+CommonConstant.UPDATE_ONE_CODE_URL;
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        try{
            postFormData = RestTemplateUtil.orderMonitorPost(url, JSON.toJSONString(map),headers);
        }catch(Exception e){
            log.error("调用更新一物一码接口异常",e);
            throw new ApplicationException(CommonExceptionCode.INVALIDATION,"更新一物一码异常!");
        }

        log.info("更新一物一码接口返回结果:{}",postFormData);

        if(StringUtil.isEmpty(postFormData)){
            log.error("更新一物一码接口返回结果为空");
            throw new ApplicationException(CommonExceptionCode.INVALIDATION,"更新一物一码接口异常!");
        }
        JSONObject response = JSONUtil.parseObj(postFormData);
        String code = response.getStr("code");

        if(CommonExceptionCode.SUCCESS.equals(code)) {
            JSONObject data = JSONUtil.parseObj(response.getStr("data"));
            return data;
        }else{
            log.error("更新一物一码接口返回失败code:{}",code);
            throw new ApplicationException(CommonExceptionCode.INVALIDATION,response.getStr("msg"));
        }
    }

    /**
     * 用户信息登记，上报信息登记
     * @param activityFormFeedbackDO
     */
    @Async("threadExecutor")
    public void adoptUserLog(ActivityFormFeedbackDO activityFormFeedbackDO,String avatar,String ip){
        PromotionActivityDO promotionActivityDO = promotionActivityDAO.getById(activityFormFeedbackDO.getActivityId());
        //如果是客服小程序，则不上报潜客池
        Object deliveryChannel = promotionActivityDO.getExt().get("deliveryChannel");
        if (Objects.equals(deliveryChannel, DeliveryChannelEnum.KF.getId())) {
            log.info("用户信息登记，客服小程序不上报潜客池");
            return;
        }

        String postFormData = null;
//        String url = huafaConstantConfig.CUSTOMER_POOL_URL+CommonConstant.AD_OPT_USER_LOG_URL_VERSION3;
//        log.info("潜客池3.0信息推送，url={}",url);
//        HttpHeaders headers = new HttpHeaders();
//        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        try{
            SysOptUserLogRequestDTO userInfo = sysOptUserLogConverter(promotionActivityDO, activityFormFeedbackDO, avatar, ip);
            log.info("用户信息登记，上报信息接口请求参数:{}",userInfo);
            //postFormData = RestTemplateUtil.orderMonitorPost(url, JSON.toJSONString(userInfo),headers);
            postFormData = huaFaHmacAuthUtil.adoptUserLog(CommonConstant.AD_OPT_USER_LOG_URL_VERSION3,JSON.toJSONString(userInfo));
        }catch(Exception e){
            log.error("用户信息登记，上报信息接口异常",e);
        }
        log.info("用户信息登记，上报信息接口返回结果:{}",postFormData);
    }

    @Override
    public String activityGroupCreateOneCodeScene(ActivityGroupDO activityGroupDO, Integer orgType) {
        Map<String, Object> ext = MapUtils.isEmpty(activityGroupDO.getExt()) ? Maps.newHashMap() : activityGroupDO.getExt();

        List<ActivityParticipationDO> listByActivityList = activityParticipationDAO.getListByActivityList(activityGroupRelatedDAO.getActivityIdsByGroupId(activityGroupDO.getId()));
        List<MiniOrgRequestDTO> miniOrgRequestDTOS = ObjectCloneUtils.convertList(listByActivityList, MiniOrgRequestDTO.class);

        Map<String, String> resultMap = this.createOneCodeScene(activityGroupDO.getCreatedBy(), activityGroupDO.getName() + "_" + activityGroupDO.getId(), orgType, miniOrgRequestDTOS);
        if(CommonExceptionCode.SUCCESS.equals(MapUtils.getString(resultMap,"code"))){
            String sceneCode = MapUtils.getString(resultMap,"sceneCode");
            Integer sceneCodeId = MapUtils.getInteger(resultMap,"id");
            activityGroupDO.setSceneCode(sceneCode);
            ext.put("orgType",orgType);// add 2021-11-5
            ext.put("sceneCodeId",sceneCodeId);
            activityGroupDO.setExt(ext);
            activityGroupDAO.updateById(activityGroupDO);
            return sceneCode;
        }else if(CommonExceptionCode.ERROR_CODE.equals(MapUtils.getString(resultMap,"code"))){
            resultMap = this.createOneCodeScene(activityGroupDO.getCreatedBy(), activityGroupDO.getName() + "_" + activityGroupDO.getId(), MiniOrgTypeEnum.NATIONAL.getId(), null);
            if(CommonExceptionCode.SUCCESS.equals(MapUtils.getString(resultMap,"code"))){
                String sceneCode = MapUtils.getString(resultMap,"sceneCode");
                Integer sceneCodeId = MapUtils.getInteger(resultMap,"id");
                activityGroupDO.setSceneCode(sceneCode);
                ext.put("orgType",MiniOrgTypeEnum.NATIONAL.getId());// add 2021-11-5
                ext.put("sceneCodeId",sceneCodeId);
                activityGroupDO.setExt(ext);
                activityGroupDAO.updateById(activityGroupDO);
                return sceneCode;
            }
        }
        return null;
    }

    @Override
    public void activityGroupUpdateOneCodeScene(ActivityGroupDO activityGroupDO, Integer orgType) {
        Map<String, Object> ext = MapUtils.isEmpty(activityGroupDO.getExt()) ? Maps.newHashMap() : activityGroupDO.getExt();

        List<ActivityParticipationDO> listByActivityList = activityParticipationDAO.getListByActivityList(activityGroupRelatedDAO.getActivityIdsByGroupId(activityGroupDO.getId()));
        List<MiniOrgRequestDTO> miniOrgRequestDTOS = ObjectCloneUtils.convertList(listByActivityList, MiniOrgRequestDTO.class);

        if(Objects.isNull(ext.get("sceneCodeId"))){
            //旧数据是没有场景码id，如果是旧数据，则无法修改场景码
            return;
        }
        Integer sceneId = Integer.parseInt(ext.get("sceneCodeId").toString());

        Map<String, String> resultMap = this.updateOneCodeScene(sceneId, orgType, miniOrgRequestDTOS);
        if(CommonExceptionCode.SUCCESS.equals(MapUtils.getString(resultMap,"code"))){
//            String sceneCode = MapUtils.getString(resultMap,"sceneCode");
//            Integer sceneCodeId = MapUtils.getInteger(resultMap,"id");
//            activityGroupDO.setSceneCode(sceneCode);
//            ext.put("orgType",orgType);// add 2021-11-5
//            ext.put("sceneCodeId",sceneCodeId);
//            activityGroupDO.setExt(ext);
//            activityGroupDAO.updateById(activityGroupDO);
//            return sceneCode;
            return;
        }else if(CommonExceptionCode.ERROR_CODE.equals(MapUtils.getString(resultMap,"code"))){
            resultMap = this.updateOneCodeScene(sceneId, MiniOrgTypeEnum.NATIONAL.getId(), null);
            if(CommonExceptionCode.SUCCESS.equals(MapUtils.getString(resultMap,"code"))){
//                String sceneCode = MapUtils.getString(resultMap,"sceneCode");
//                Integer sceneCodeId = MapUtils.getInteger(resultMap,"id");
//                activityGroupDO.setSceneCode(sceneCode);
                ext.put("orgType",MiniOrgTypeEnum.NATIONAL.getId());// add 2021-11-5
                //ext.put("sceneCodeId",sceneCodeId);
                //activityGroupDO.setExt(ext);
                activityGroupDAO.updateById(activityGroupDO);
                //return sceneCode;
                return;
            }
        }
        return;
    }

    @Override
    public String activityCreateOneCodeScene(PromotionActivityDO promotionActivityDO, Integer orgType, List<MiniOrgRequestDTO> orgList) {
        //生成场景码
        Map<String, String> resultMap = this.createOneCodeScene(promotionActivityDO.getCreatedBy(),promotionActivityDO.getName() + promotionActivityDO.getId().toString(), orgType, orgList);
        Map<String, Object> ext = promotionActivityDO.getExt();

        if(CommonExceptionCode.SUCCESS.equals(MapUtils.getString(resultMap,"code"))){
            String sceneCode = MapUtils.getString(resultMap,"sceneCode");
            Integer sceneCodeId = MapUtils.getInteger(resultMap,"id");
            ext.put("sceneCode",sceneCode);
            ext.put("orgType",orgType);// add 2021-11-5
            ext.put("sceneCodeId",sceneCodeId);
            promotionActivityDO.setExt(ext);
            promotionActivityDAO.updateById(promotionActivityDO);
            return sceneCode;
        } else if(CommonExceptionCode.ERROR_CODE.equals(MapUtils.getString(resultMap,"code"))){//如果默认组织机构类型为项目生成的场景码返回500,则重新以全国去获取场景码
            resultMap = this.createOneCodeScene(promotionActivityDO.getCreatedBy(), promotionActivityDO.getName() + promotionActivityDO.getId().toString(), MiniOrgTypeEnum.NATIONAL.getId(), null);
            if(CommonExceptionCode.SUCCESS.equals(MapUtils.getString(resultMap,"code"))){
                String sceneCode = MapUtils.getString(resultMap,"sceneCode");
                Integer sceneCodeId = MapUtils.getInteger(resultMap,"id");
                ext.put("sceneCode",sceneCode);
                ext.put("orgType",MiniOrgTypeEnum.NATIONAL.getId());// add 2021-11-5
                ext.put("sceneCodeId",sceneCodeId);
                promotionActivityDO.setExt(ext);
                promotionActivityDAO.updateById(promotionActivityDO);
                return sceneCode;
            }
        }
        return null;
    }

    @Override
    public void activityUpdateOneCodeScene(PromotionActivityDO promotionActivityDO, Integer orgType, List<MiniOrgRequestDTO> orgList) {

        if(Objects.isNull(promotionActivityDO.getExt().get("sceneCodeId"))){
            //旧数据是没有场景码id，如果是旧数据，则无法修改场景码
            return;
        }
        Integer sceneId = Integer.parseInt(promotionActivityDO.getExt().get("sceneCodeId").toString());
        //修改场景码
        Map<String, String> resultMap = this.updateOneCodeScene(sceneId, orgType, orgList);
        Map<String, Object> ext = promotionActivityDO.getExt();

        if(CommonExceptionCode.SUCCESS.equals(MapUtils.getString(resultMap,"code"))){
//            String sceneCode = MapUtils.getString(resultMap,"sceneCode");
//            Integer sceneCodeId = MapUtils.getInteger(resultMap,"id");
//            ext.put("sceneCode",sceneCode);
//            ext.put("orgType",orgType);// add 2021-11-5
//            ext.put("sceneCodeId",sceneCodeId);
//            promotionActivityDO.setExt(ext);
//            promotionActivityDAO.updateById(promotionActivityDO);
//            return sceneCode;
            return;
        } else if(CommonExceptionCode.ERROR_CODE.equals(MapUtils.getString(resultMap,"code"))){//如果默认组织机构类型为项目生成的场景码返回500,则重新以全国去获取场景码
            resultMap = this.updateOneCodeScene(sceneId, MiniOrgTypeEnum.NATIONAL.getId(), null);
            if(CommonExceptionCode.SUCCESS.equals(MapUtils.getString(resultMap,"code"))){
//                String sceneCode = MapUtils.getString(resultMap,"sceneCode");
//                Integer sceneCodeId = MapUtils.getInteger(resultMap,"id");
//                ext.put("sceneCode",sceneCode);
                ext.put("orgType",MiniOrgTypeEnum.NATIONAL.getId());// add 2021-11-5
//                ext.put("sceneCodeId",sceneCodeId);
                promotionActivityDO.setExt(ext);
                promotionActivityDAO.updateById(promotionActivityDO);
//                return sceneCode;
                return;
            }
        }
        return;
    }

    /**
     *
     * @param projectId 此项目id是置业通的，不是华发的
     * @return
     */
    @Override
    public String getAccounts(String projectId) {
        log.info("根据公众号获取个人账号列表:{}",projectId);
        String url = huafaConstantConfig.RS_CENTE_BASE_URL+CommonConstant.SHORT_VIDEO_GETACCOUNTS_URL;

        Map<String, Object> params = new HashMap<>();
        params.put("projectId",projectId);
        try{
            return HTTPClientUtils.get(url,params);
        }catch(Exception e){
            log.error("根据公众号获取个人账号列表异常",e);
            throw new ApplicationException("根据公众号获取个人账号列表异常!");
        }
    }

    @Override
    public String getAreas() {
        String url = huafaConstantConfig.RS_CENTE_BASE_URL+CommonConstant.SHORT_VIDEO_GETAREAS_URL;
        try{
            return HTTPClientUtils.get(url);
        }catch(Exception e){
            log.error("获取区域和公众号列表异常",e);
            throw new ApplicationException("获取区域和公众号列表异常!");
        }
    }

    public String page(Map<String, Object> params) {
        log.info("分页获取个人视频列表列表:{}",params);
        String url = huafaConstantConfig.RS_CENTE_BASE_URL+CommonConstant.SHORT_VIDEO_PAGE_URL;
        try{
            return HTTPClientUtils.get(url,params);
        }catch(Exception e){
            log.error("分页获取个人视频列表列表异常",e);
            throw new ApplicationException("分页获取个人视频列表列表异常!");
        }
    }

    public Page<OneCodeAuthDTO> pageAuth(Integer codeId, Integer pageNo, Integer pageSize) {
        log.info("分页获取扫码列表，codeId={}",codeId);
        String url = huafaConstantConfig.RS_CENTE_BASE_URL+CommonConstant.ONE_CODE_AUTH_URL;
        JSONObject body = new JSONObject();
        body.set("codeId",codeId).set("pageNo",pageNo).set("pageSize",pageSize);
        try{
            Data<Page<OneCodeAuthDTO>> result = httpClientService.doPost(url, body.toString(), null, new TypeReference<Data<Page<OneCodeAuthDTO>>>() {
            });
            return result.getdata();
        }catch(Exception e){
            log.error("分页获取扫码列表异常",e);
        }
        return null;
    }

    private SysOptUserLogRequestDTO sysOptUserLogConverter(PromotionActivityDO promotionActivityDO,ActivityFormFeedbackDO activityFormFeedbackDO,String avatar,String ip){
        SysOptUserLogRequestDTO userInfo = new SysOptUserLogRequestDTO();
        userInfo.setActId(activityFormFeedbackDO.getActivityId().intValue());
        userInfo.setActName(promotionActivityDO.getName());
        /*userInfo.setCreateTime(DateUtils.format(activityFormFeedbackDO.getCreatedTime() != null ? activityFormFeedbackDO.getCreatedTime() : DateTime.now(),DateUtils.DEFAULT_DATE_TIME_FORMAT));*/
        userInfo.setHeadImgUrl(avatar);
        userInfo.setIp(ip);
        //userInfo.setIsValid(1);//有效
        userInfo.setNickname(activityFormFeedbackDO.getNickName());
        userInfo.setOpenId(activityFormFeedbackDO.getUserId());
        userInfo.setProjectId(activityFormFeedbackDO.getProjectId());
        //userInfo.setProjectName(activityFormFeedbackDO.getProjectName());
        //来源 0:h5 1:微信小程序 2 活动 3直播 21抖音小程序
        //0、微信小程序 1、H5  2、抖音 3、非微信外浏览器
        userInfo.setSource(2);//活动
        userInfo.setSourceType(8);//活动
        //userInfo.setUserId(activityFormFeedbackDO.getUserId());
        userInfo.setUnionId(activityFormFeedbackDO.getUnionId());

        if(StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId().equals(promotionActivityDO.getPaTemplateId())
                || StrategyGroupEnum.HF_FISSION_REDUCEPRICE_ACT.getId().equals(promotionActivityDO.getPaTemplateId())){
            userInfo.setAppId(huafaConstantConfig.MINI_PROGRAM_YSH_APP_ID);
        }else{
            userInfo.setAppId(huafaConstantConfig.MINI_PROGRAM_APP_ID);
        }
        Map<String, Object> limits = activityFormFeedbackDO.getLimits();
        Object cityInfoId = limits.get("cityInfoId");
        Object cityInfoValue = limits.get("cityInfoValue");
        Object userName = limits.get("userName");
        Object phone = limits.get("phone");
        if(cityInfoId != null){
            userInfo.setPageId(Integer.valueOf(cityInfoId.toString()));
            userInfo.setCityId(cityInfoId.toString().toString());//城市id
        }
        if(cityInfoValue != null){
            userInfo.setCityName(cityInfoValue.toString());//城市名称
        }
        if(userName != null){
            userInfo.setName(userName.toString());
        }
        if(phone != null){
            userInfo.setMobile(phone.toString());
        }
        //0、微信小程序 1、H5  2、抖音 3、非微信外浏览器
        //1-置业通小程序  2-百度小程序  3-头条小程序  4-抖音小程序  5-H5内网页 6-H5外网页
        userInfo.setAccountType((activityFormFeedbackDO.getType() == null || activityFormFeedbackDO.getType() == 0) ? 1://0->1
                (activityFormFeedbackDO.getType() == 2 ? 4://2 -> 4
                        (activityFormFeedbackDO.getType() == 1 ? 5:6))); // 1-> 5,3->6
        //userInfo.setDataType(1);
        return userInfo;
    }
}
