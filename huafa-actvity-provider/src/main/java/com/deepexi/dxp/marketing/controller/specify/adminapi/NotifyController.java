package com.deepexi.dxp.marketing.controller.specify.adminapi;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.WxPayCallBackRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.WxRefundCallBackRequestDTO;
import com.deepexi.dxp.marketing.service.specify.WxPayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 第三方回调controller
 * <AUTHOR>
 * @date  2021/05/19 10:54
 */
@RestController
@RequestMapping("/admin-api/v1/order/notify")
@Api(value = "支付回调",tags = {"支付回调"})
public class NotifyController {

    @Resource
    private WxPayService wxPayService;

    @PostMapping("/payCallBack")
    @ApiOperation(value="支付回调接口", notes = "支付回调接口")
    public Map<String,Object> payCallBack(@RequestBody WxPayCallBackRequestDTO dto){
        return wxPayService.payCallBack(dto);
    }

    @PostMapping("/refundCallBack")
    @ApiOperation(value="退款回调接口", notes = "退款回调接口")
    public Map<String,Object> refundCallBack(@RequestBody WxRefundCallBackRequestDTO dto){
        return wxPayService.refundCallBack(dto);
    }

}
