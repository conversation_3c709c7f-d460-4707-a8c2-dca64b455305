package com.deepexi.dxp.marketing.domain.marketing.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * DolphinScheduler分页DTO
 * <AUTHOR>
 * @Date 2020/3/27
 */
@Data
public class DolphinSchedulerPageDTO<T> implements Serializable {

    /**
     * 数据列表
     */
    private List<T> totalList;

    /**
     * 总数
     */
    private Long total;

    /**
     * 当前页数
     */
    private Long currentPage;

    /**
     * 总页数
     */
    private Long totalPage;

}
