package com.deepexi.dxp.marketing.service.promotion;

import com.deepexi.dxp.marketing.domain.promotion.dto.activity.*;
import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityCouponQuery;
import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityQuery;
import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionSeckillActivityQuery;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.ActivityCommoditValidateRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityCreatePostRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityUpdatePostRequest;
import com.deepexi.util.pageHelper.PageBean;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> xinjian.yao
 * @date 2019/11/21 11:52
 */
@Service
public interface PromotionActivityMiddleService {


    /**
     * 创建促销活动
     *
     * @param dto 入参dto 需要转换成po
     * @return 添加是否成功
     */
    Long create(PromotionActivityCreatePostRequest dto);

    /**
     * 根据id修改促销活动
     *
     * @param id  活动id
     * @param dto 活动dto
     * @return 是非修改成功
     */
    Boolean updateActivityById(Long id, PromotionActivityUpdatePostRequest dto);

    /**
     * 删除活动
     *
     * @param ids deletedList
     * @return 是非删除成功
     */
    Boolean delete(List<Long> ids);

    /**
     * 查询促销活动
     *
     * @param id 活动id
     * @return dto
     */
    PromotionActivityDetailPostResponseDTO detail(Long id);

    /**
     * 不分页查询活动
     *
     * @param dto 不分页查询活动dto
     * @return list 对象
     */
    List<PromotionActivityListPostVO> findAll(PromotionActivityQuery dto);

    /**
     * 分页查询
     *
     * @param dto dto
     * @return List
     */
    PageBean<PromotionActivityListPostVO> findPage(PromotionActivityQuery dto);


    /**
     * 批量查询ids
     *
     * @param ids ids
     * @return dto
     */
    List<PromotionActivityDetailPostResponseDTO> detail(List<Long> ids);

    /**
     * @param id 活动id
     * @return 改活动的具体信息
     */
    PromotionActivityListPostVO getActivityById(Long id);

    /**
     * 根据活动Id修改活动状态
     *
     * @param id
     * @param status
     * @return
     */
    boolean updateStatus(Long id, String status);

    /**
     * 修改活动状态
     *
     * @param dto 入参
     * @return 出错
     */
    Long updateActivityStatus(PromotionActivityStatusUpdateRequest dto);

    /**
     * 查询秒杀列表
     * @param dto 入参
     * @return 结果
     */
    PageBean<PromotionSeckillActivityVO> findSeckillPage(PromotionSeckillActivityQuery dto);

}
