package com.deepexi.dxp.marketing.manager.promotion;


import com.deepexi.dxp.marketing.domain.marketing.request.specify.FissionReceiveNowDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.PromotionActivityDetailDTO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityFormFeedbackDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityOrderDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityPartakeLogDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.PromotionHisResourceDO;

import java.util.List;

/**
 * 活动相关管理
 * <AUTHOR>
 * @date 2019/12/14 11:40
 */
public interface PromotionActivityManager {

     //缓存活动，包括基本信息、活动-资源列表、活动-每个资源的剩余数量
     PromotionActivityDetailDTO cacheActInfo(Long activityId);

     //强制更新：缓存活动，包括基本信息、活动-资源列表、活动-每个资源的剩余数量
     PromotionActivityDetailDTO forceCacheActInfo(Long activityId);
     void clearAllRunningListCache();

     //强制删除已过期的活动信息
     void forceDelCacheActInfo(Long activityId);

     //获取缓存中活动数据
     PromotionActivityDetailDTO getCacheActivityById(Long activityId);

     Boolean decrRedisQty(Long hisResourceId, Long activityId, int count);

     //扣减资源缓存实时库存
     Boolean decrRedisQty(Long hisResourceId, Long activityId);

     //扣减资源缓存实时库存,校验每天资源量
     Boolean decrRedisQtyCheckDay(Long hisResourceId, Long activityId);

     Boolean incrRedisQty(Long hisResourceId, Long activityId, int count);

     //增加资源缓存实时库存
     Boolean incrRedisQty(Long hisResourceId, Long activityId);

     //增加资源缓存实时库存（指定数量）
     Boolean incrRedisQtyByCount(Long hisResourceId, Long activityId, Long count);

     //扣减资源数据库实时剩余库存
     Boolean decrRemainQty(Long hisResourceId, Long count);

     //增加资源数据库实时剩余库存
     Boolean incrRemainQty(Long hisResourceId, Long count);

     //增加资源数据库最大资源库存，同时增加当前实时资源剩余库存
     Boolean incrIssuedQty(Long hisResourceId, Long count);

     //增加缓存用户参与活动次数
     void incrUserPartakeCount(String phone, Long activityId);

     //增加缓存用户领取资源次数
     void incrUserGetResourceCount(String phone, Long hisResourceId, Long activityId);

     //扣减缓存用户领取资源次数
     void decrUserGetResourceCount(String phone, Long hisResourceId, Long activityId);

     //增加缓存用户填写登记信息次数
     void incrUserFeedBackCount(String phone, Long activityId, Long hisResourceId);

     //缓存未支付订单信息
     void cacheUnpayOrderInfo(ActivityOrderDO clone);
     //检查未支付缓存是否存在
     boolean isExistsUnpayOrderInfo(String phone, Long activityId, Long resourceId);

     //清空缓存未支付订单信息
     void clearCacheUnpayOrderInfo(String phone, Long activityId, Long resourceId);

     //处理过期助力中活动
     void udpateAssistSuccessStatus();


     /**
      * 砍价核销
      * @param dto
      * @param promotionActivityDO
      * @param formFeedbackList
      * @param partakeLogById
      * @param byId
      * @return
      */
     Boolean bargainActivityVerify(FissionReceiveNowDTO dto, PromotionActivityDO promotionActivityDO, List<ActivityFormFeedbackDO> formFeedbackList, ActivityPartakeLogDO partakeLogById, PromotionHisResourceDO byId);

     /**
      * 更新项目信息
      */
     void updateUserProjectInof();
}
