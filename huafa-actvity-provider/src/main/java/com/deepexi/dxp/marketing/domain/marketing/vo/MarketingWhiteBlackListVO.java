package com.deepexi.dxp.marketing.domain.marketing.vo;

import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Class: MarketingWhiteBlackListVO
 * @Description:
 * @Author: zht
 * @Date: 2020/4/2
 */
@Data
@ApiModel
public class MarketingWhiteBlackListVO extends AbstractObject {
    /**
     * 会员id
     */
    @ApiModelProperty("会员id")
    private Long memberId;

    /**
     * 类型 0 白名单 1 黑名单
     */
    @ApiModelProperty("类型 0 白名单 1 黑名单")
    private Integer type;


    /**
     * 用户姓名
     */
    @ApiModelProperty("用户姓名")
    private String name;

    /**
     * 账号名字
     */
    @ApiModelProperty("账号名字")
    private String username;

    /**
     * 电话
     */
    @ApiModelProperty("电话")
    private String phone;

    /**
     * 邮箱
     */
    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("添加时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createdTime;
}
