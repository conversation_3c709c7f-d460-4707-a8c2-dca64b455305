package com.deepexi.dxp.marketing;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * swagger地址：http://localhost:38007/swagger-ui.html
 * <AUTHOR>
 * @date 2018/11/5
 * EnableDiscoveryClient 用于启动服务发现功能
 */
//@EnableDiscoveryClient
@EnableEurekaClient
@SpringBootApplication(scanBasePackages = {"com.deepexi.dxp"})
//@EnableFeignClients
@Configuration
@EnableScheduling //开启定时任务
@EnableAspectJAutoProxy(proxyTargetClass = true)
public class MarketingApplication {

    public static void main(String[] args) {
        SpringApplication.run(MarketingApplication.class, args);
    }
}