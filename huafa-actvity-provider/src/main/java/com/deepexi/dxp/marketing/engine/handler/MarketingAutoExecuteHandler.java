//package com.deepexi.dxp.marketing.engine.handler;
//
//import com.deepexi.common.extension.AppRuntimeEnv;
//import com.deepexi.dm.bury.point.api.BuryPointService;
//import com.deepexi.dxp.marketing.domain.marketing.dto.MarketingAutoDTO;
//import com.deepexi.dxp.marketing.domain.marketing.dto.MarketingResourceDataDTO;
//import com.deepexi.dxp.marketing.domain.marketing.vo.MemberListQuery;
//import com.deepexi.dxp.marketing.domain.merber.vo.MemberDetailVO;
//import com.deepexi.dxp.marketing.enums.contact.MessageTypeEnum;
//import com.deepexi.dxp.marketing.enums.marketing.MarketingTaskExecuteStatusEnum;
//import com.deepexi.dxp.marketing.enums.marketing.MarketingTaskTypeEnum;
//import com.deepexi.dxp.marketing.enums.marketing.TaskShortNameEnum;
//import com.deepexi.dxp.marketing.enums.redis.MarketingEnum;
//import com.deepexi.dxp.marketing.enums.redis.RedisPrefixEnum;
//import com.deepexi.dxp.marketing.manager.marketing.MarketingSendMsgManager;
////import com.deepexi.dxp.marketing.manager.marketing.MemberManager;
//import com.deepexi.dxp.marketing.service.marketing.MarketingWxTemplateService;
//import com.deepexi.dxp.marketing.service.marketing.SmsService;
//import com.deepexi.dxp.middle.marketing.dao.*;
//import com.deepexi.dxp.middle.marketing.domain.dto.MarketingTaskTouchDetailCreateDTO;
//import com.deepexi.dxp.middle.marketing.domain.dto.SmsSendDTO;
//import com.deepexi.dxp.middle.marketing.domain.dto.WxTemplateSendDTO;
//import com.deepexi.dxp.middle.marketing.domain.entity.*;
//import com.deepexi.dxp.middle.marketing.domain.query.MarketingActionTemplateResourceQuery;
//import com.deepexi.dxp.middle.marketing.domain.query.MarketingTaskTouchDetailFindPageQuery;
//import com.deepexi.dxp.middle.marketing.domain.vo.MarketingTaskTouchDetailVO;
//import com.deepexi.redis.service.RedisService;
//import com.deepexi.util.CollectionUtil;
//import com.deepexi.util.exception.ApplicationException;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.util.List;
//import java.util.Map;
//import java.util.Objects;
//import java.util.stream.Collectors;
//
///**
// * 自动任务执行(临时)
// *
// * <AUTHOR>
// * @version 1.0
// * @date 2020-04-02 15:50
// */
//@Service
//@Slf4j
//public class MarketingAutoExecuteHandler implements ExecuteHandle {
//
//    @Resource
//    private MarketingWxTemplateService marketingWxTemplateService;
//
//    @Resource
//    private SmsService smsService;
//
//    @Resource
//    private RedisService redisService;
//
//    @Resource
//    private BuryPointService buryPointService;
//
//    @Resource
//    private MarketingActionTemplateResourceDAO marketingActionTemplateResourceDao;
//
//    @Resource
//    private MarketingTaskSecurityFilterDAO marketingTaskSecurityFilterDAO;
//
//    @Resource
//    private MarketingTaskTouchDetailDAO marketingTaskTouchDetailDao;
//
//    @Resource
//    private MarketingActionTemplateTouchRuleDAO marketingActionTemplateTouchRuleDao;
//
//    @Resource
//    private MarketingActionTemplateDAO marketingActionTemplateDao;
//
//    @Resource
//    private MarketingSendMsgManager marketingSendMsgManager;
//
////    @Resource
////    private MemberManager memberManager;
//
//
//    @Override
//    public void execute(MarketingAutoDTO marketingAutoDTO, Long memberId, Long mappingId) {
//        log.info("自动任务执行数据{},{},{}", marketingAutoDTO, memberId, mappingId);
//        if (Objects.isNull(marketingAutoDTO) || Objects.isNull(memberId) || Objects.isNull(mappingId)) {
//            throw new ApplicationException("自动任务执行：传入数据为空,任务： {0}；mappingId：{1}；会员id：{2}"
//                    , marketingAutoDTO
//                    , mappingId
//                    , memberId
//            );
//        }
//        AppRuntimeEnv.setTenantId(marketingAutoDTO.getTenantId());
//        AppRuntimeEnv.setAppId(marketingAutoDTO.getAppId());
//        Long taskId = marketingAutoDTO.getId();
//        redisService.incr("TASK_CATCH_USER:" + MarketingTaskTypeEnum.AUTO.getValue() + ":" + taskId, 1L);
//        MarketingActionTemplateDO templateDetail = marketingActionTemplateDao.getById(marketingAutoDTO.getActionTemplateId());
//
//        if (null == templateDetail) {
//            throw new ApplicationException("自动任务执行：行动模板数据为空,任务id：{0}；mappingId：{1}" + mappingId
//                    , marketingAutoDTO.getId()
//                    , mappingId
//            );
//        }
//        String tenantId = marketingAutoDTO.getTenantId();
//        //资源
//        MarketingActionTemplateResourceQuery marketingActionTemplateResourceQuery = new MarketingActionTemplateResourceQuery();
//        marketingActionTemplateResourceQuery.setActionTemplateId(marketingAutoDTO.getActionTemplateId());
//        marketingActionTemplateResourceQuery.setTenantId(marketingAutoDTO.getTenantId());
//        marketingActionTemplateResourceQuery.setAppId(marketingAutoDTO.getAppId());
//        MarketingActionTemplateResourceDO marketingActionTemplateResource = null;
//        List<MarketingActionTemplateResourceDO> marketingActionTemplateResources = marketingActionTemplateResourceDao.findAll(marketingActionTemplateResourceQuery);
//        if (CollectionUtil.isNotEmpty(marketingActionTemplateResources)) {
//            marketingActionTemplateResource = marketingActionTemplateResources.get(0);
//        }
//        //获取安全过滤
//        MarketingTaskSecurityFilterDO marketingTaskSecurityFilterDO = new MarketingTaskSecurityFilterDO();
//        marketingTaskSecurityFilterDO.setTaskType(MarketingTaskTypeEnum.AUTO.getValue());
//        marketingTaskSecurityFilterDO.setTaskId(marketingAutoDTO.getId());
//        List<MarketingTaskSecurityFilterDO> marketingTaskSecurityFilterDtoList = marketingTaskSecurityFilterDAO.queryByMarketingTask(marketingTaskSecurityFilterDO);
//        if (CollectionUtil.isNotEmpty(marketingTaskSecurityFilterDtoList)) {
//            for (MarketingTaskSecurityFilterDO marketingTaskSecurityFilter : marketingTaskSecurityFilterDtoList) {
//                //安全策略
//                if (marketingTaskSecurityFilter.getSecurityType() == 1) {
//                    //白名单
//                    if ("1".equals(marketingTaskSecurityFilter.getCode())) {
//                        boolean white = redisService.sismember(  tenantId + ":" + RedisPrefixEnum.WHITE_LIST.getKey(), memberId.toString());
//                        if (!white) {
//                            throw new ApplicationException("自动任务执行：该会员不在白名单，停止触达,任务id：{0}；mappingId：{1}；会员id：{2}"
//                                    , marketingAutoDTO.getId()
//                                    , mappingId
//                                    , memberId
//                            );
//                        }
//                        //黑名单
//                    } else if ("2".equals(marketingTaskSecurityFilter.getCode())) {
//                        boolean black = redisService.sismember(  tenantId + ":" + RedisPrefixEnum.BLACK_LIST.getKey(), memberId.toString());
//                        if (black) {
//                            throw new ApplicationException("自动任务执行：该会员在黑名单，停止触达,任务id：{0}；mappingId：{1}；会员id：{2}"
//                                    , marketingAutoDTO.getId()
//                                    , mappingId
//                                    , memberId
//                            );
//                        }
//                    }
//                    //用户打扰过滤
//                } else if (marketingTaskSecurityFilter.getSecurityType() == 2) {
//                    //30天累计短信数超过10条
//                    if ("1".equals(marketingTaskSecurityFilter.getCode())) {
//                        boolean disableSend = redisService.sismember(MarketingEnum.getRedisFullKey(MarketingEnum.WHITE_LIST, tenantId), memberId.toString());
//                        if (disableSend) {
//                            throw new ApplicationException("自动任务执行：该会员30天累计短信数超过10条，停止触达,任务id：{0}；mappingId：{1}；会员id：{2}"
//                                    , marketingAutoDTO.getId()
//                                    , mappingId
//                                    , memberId
//                            );
//                        }
//                        //资源去重
//                    } else if ("2".equals(marketingTaskSecurityFilter.getCode())) {
//                        if (Objects.nonNull(marketingActionTemplateResource)) {
//                            MarketingTaskTouchDetailFindPageQuery marketingTaskTouchDetailFindPageQuery = new MarketingTaskTouchDetailFindPageQuery();
//                            marketingTaskTouchDetailFindPageQuery.setMappingId(mappingId);
//                            marketingTaskTouchDetailFindPageQuery.setStatus(1);
//                            marketingTaskTouchDetailFindPageQuery.setMemberId(memberId);
//                            marketingTaskTouchDetailFindPageQuery.setResourceId(marketingActionTemplateResource.getResourceId());
//                            List<MarketingTaskTouchDetailVO> marketingTaskTouchDetailVOList = marketingTaskTouchDetailDao.findAll(marketingTaskTouchDetailFindPageQuery);
//                            if (CollectionUtil.isNotEmpty(marketingTaskTouchDetailVOList)) {
//                                throw new ApplicationException("自动任务执行：该会员已触达相同资源，停止触达,任务id：{0}；mappingId：{1}；会员id：{2}"
//                                        , marketingAutoDTO.getId()
//                                        , mappingId
//                                        , memberId
//                                );
//                            }
//                        }
//                    }
//                }
//            }
//        }
//
//
//        //触达记录
//        MarketingTaskTouchDetailCreateDTO marketingTaskTouchDetailCreateDTO = new MarketingTaskTouchDetailCreateDTO();
//        marketingTaskTouchDetailCreateDTO.setTenantId(tenantId);
//        //任务数据
//        marketingTaskTouchDetailCreateDTO.setTaskId(marketingAutoDTO.getId());
//        marketingTaskTouchDetailCreateDTO.setMappingId(mappingId);
//        marketingTaskTouchDetailCreateDTO.setTaskType(MarketingTaskTypeEnum.AUTO.getValue());
//
////        //会员数据
////        MemberListQuery query = new MemberListQuery();
////        query.setId(memberId);
////        query.setAppId(marketingAutoDTO.getAppId());
////        query.setTenantId(tenantId);
////        MemberDetailVO memberDetail = memberManager.detail(query);
////        if (null == memberDetail) {
////            throw new ApplicationException("自动任务执行：查询会员数据为空,任务id：" + marketingAutoDTO.getId() + "；会员id：" + memberId);
////        }
//        //黑名单
//        boolean black = redisService.sismember(  tenantId + ":" + RedisPrefixEnum.BLACK_LIST.getKey(), memberId.toString());
//        if (black) {
//            throw new ApplicationException("自动任务执行：该会员在黑名单，停止触达,任务id：" + marketingAutoDTO.getId() + "；mappingId：" + mappingId + "；会员id：" + memberId);
//        }
//
//        marketingTaskTouchDetailCreateDTO.setMemberId(memberId);
////        marketingTaskTouchDetailCreateDTO.setMemberName(memberDetail.getName());
////        marketingTaskTouchDetailCreateDTO.setMemberPhone(memberDetail.getPhone());
//
//        //资源数据
//        MarketingResourceDataDTO marketingResourceDataDTO = null;
//        String url = Objects.nonNull(marketingActionTemplateResource) ? marketingActionTemplateResource.getResourceUrl() : null;
//        if (Objects.nonNull(marketingActionTemplateResource) && Objects.nonNull(marketingActionTemplateResource.getResourceId())) {
//            marketingTaskTouchDetailCreateDTO.setResourceName(marketingActionTemplateResource.getResourceName());
//            marketingTaskTouchDetailCreateDTO.setResourceId(marketingActionTemplateResource.getResourceId());
//            marketingTaskTouchDetailCreateDTO.setResourceCategoryCode(marketingActionTemplateResource.getResourceCategoryCode());
//            marketingTaskTouchDetailCreateDTO.setResourceChannel(marketingActionTemplateResource.getResourceChannel());
//            marketingResourceDataDTO = marketingActionTemplateResource.clone(MarketingResourceDataDTO.class);
//            marketingResourceDataDTO.setResourceId(marketingActionTemplateResource.getResourceId().toString());
//            marketingResourceDataDTO.setTaskId(marketingAutoDTO.getId());
//            marketingSendMsgManager.replaceResource(marketingResourceDataDTO);
//        }
//
//        //封装触达策略数据
//        MarketingActionTemplateTouchRuleDO marketingActionTemplateTouchRuleDO = new MarketingActionTemplateTouchRuleDO();
//        marketingActionTemplateTouchRuleDO.setActionTemplateId(marketingAutoDTO.getActionTemplateId());
//        marketingActionTemplateTouchRuleDO.setTenantId(marketingAutoDTO.getTenantId());
//        marketingActionTemplateTouchRuleDO.setAppId(marketingAutoDTO.getAppId());
//        List<MarketingActionTemplateTouchRuleDO> templateRuleList = marketingActionTemplateTouchRuleDao.findAll(marketingActionTemplateTouchRuleDO);
//        if (CollectionUtil.isEmpty(templateRuleList)) {
//            throw new ApplicationException("自动任务执行：触达规则为空,任务id：" + marketingAutoDTO.getId());
//        }
//        //key sort, value MarketingActionTemplateTouchRuleVO
//        Map<Integer, MarketingActionTemplateTouchRuleDO> templateRuleMap = templateRuleList.stream().collect(Collectors.toMap(MarketingActionTemplateTouchRuleDO::getSort, d -> d));
//        //主触达
//        MarketingActionTemplateTouchRuleDO mainTouchRule = templateRuleMap.get(1);
//        //验证是否存在消费记录
//        MarketingTaskTouchDetailVO marketingTaskTouchDetailDO = marketingTaskTouchDetailDao.selectByRecord(tenantId + "_" + MarketingTaskTypeEnum.AUTO.getValue() + "_" + marketingAutoDTO.getId() + "_" + memberId + "_" + mainTouchRule.getSendRuleChannel());
//        if (marketingTaskTouchDetailDO != null) {
//            throw new ApplicationException("自动任务执行：该会员在该规则下已存在mq消费记录，不再继续消费；memberId：" + memberId + "；任务id：" + marketingAutoDTO.getId() + "；mappingId：" + mappingId);
//        }
//        //任务简称
////        boolean mainExecute = execute(mainTouchRule, url, marketingAutoDTO.getId(), tenantId, marketingResourceDataDTO, memberDetail);
////        String taskShortName = TaskShortNameEnum.getValueById(mainTouchRule.getSendRuleChannel());
////        marketingTaskTouchDetailCreateDTO.setSendRuleChannel(mainTouchRule.getSendRuleChannel());
////        marketingTaskTouchDetailCreateDTO.setSendRuleType(mainTouchRule.getSendRuleType());
////        marketingTaskTouchDetailCreateDTO.setSendTemplateId(mainTouchRule.getSendTemplateId());
////        marketingTaskTouchDetailCreateDTO.setStatus(mainExecute ? 1 : 2);
////        marketingTaskTouchDetailCreateDTO.setAppId(marketingAutoDTO.getAppId());
////        marketingTaskTouchDetailCreateDTO.setTenantId(marketingAutoDTO.getTenantId());
//        //短信状态先给一个中间状态
////        if (MessageTypeEnum.SMS.getCode() == templateRuleMap.get(1).getSendRuleChannel()) {
////            marketingTaskTouchDetailCreateDTO.setStatus(MarketingTaskExecuteStatusEnum.WAIT.getValue());
////        } else {
////            marketingTaskTouchDetailCreateDTO.setStatus(mainExecute ? MarketingTaskExecuteStatusEnum.SUCCESS.getValue() : MarketingTaskExecuteStatusEnum.FAILURE.getValue());
////        }
////        marketingTaskTouchDetailCreateDTO.setRecord(tenantId + "_" + MarketingTaskTypeEnum.AUTO.getValue() + "_" + marketingAutoDTO.getId() + "_" + memberId + "_" + mainTouchRule.getSendRuleChannel());
////        marketingTaskTouchDetailDao.save(marketingTaskTouchDetailCreateDTO.clone(MarketingTaskTouchDetailDO.class));
////        //降级
////        //降级执行结果
////        boolean lastExecute = false;
////        if (templateRuleList.size() > 1 && !mainExecute) {
////            for (MarketingActionTemplateTouchRuleDO rule : templateRuleList) {
////                if (rule.getSort() != 1) {
////                    MarketingTaskTouchDetailVO marketingTaskTouchDetail = marketingTaskTouchDetailDao.selectByRecord(tenantId + "_" + MarketingTaskTypeEnum.AUTO.getValue() + "_" + marketingAutoDTO.getId() + "_" + memberId + "_" + rule.getSendRuleChannel());
////                    if (marketingTaskTouchDetail != null) {
////                        throw new ApplicationException("自动任务执行：该会员在该规则下已存在mq消费记录，不再继续消费；memberId：" + memberId + "；任务id：" + marketingAutoDTO.getId() + "；mappingId：" + mappingId);
////                    }
////                    lastExecute = execute(rule, url, marketingAutoDTO.getId(), tenantId, marketingResourceDataDTO, memberDetail);
////                    if (lastExecute) {
////                        taskShortName = TaskShortNameEnum.getValueById(rule.getSendRuleChannel());
////                    }
////                    marketingTaskTouchDetailCreateDTO.setSendRuleChannel(rule.getSendRuleChannel());
////                    marketingTaskTouchDetailCreateDTO.setSendRuleType(rule.getSendRuleType());
////                    marketingTaskTouchDetailCreateDTO.setSendTemplateId(rule.getSendTemplateId());
////                    marketingTaskTouchDetailCreateDTO.setAppId(marketingAutoDTO.getAppId());
////                    marketingTaskTouchDetailCreateDTO.setTenantId(marketingAutoDTO.getTenantId());
////                    //短信状态先给一个中间状态
////                    if (MessageTypeEnum.SMS.getCode() == templateRuleMap.get(2).getSendRuleChannel()) {
////                        marketingTaskTouchDetailCreateDTO.setStatus(MarketingTaskExecuteStatusEnum.WAIT.getValue());
////                    } else {
////                        marketingTaskTouchDetailCreateDTO.setStatus(lastExecute ? MarketingTaskExecuteStatusEnum.SUCCESS.getValue() : MarketingTaskExecuteStatusEnum.FAILURE.getValue());
////                    }
////                    marketingTaskTouchDetailCreateDTO.setRecord(tenantId + "_" + MarketingTaskTypeEnum.AUTO.getValue() + "_" + marketingAutoDTO.getId() + "_" + memberId + "_" + rule.getSendRuleChannel());
////                    marketingTaskTouchDetailDao.save(marketingTaskTouchDetailCreateDTO.clone(MarketingTaskTouchDetailDO.class));
////                }
////            }
////        }
////
////        //埋点
////        if (mainExecute || lastExecute) {
////            try {
////                marketingSendMsgManager.buryPoint(tenantId, memberId, taskShortName, mappingId);
////            } catch (Exception e) {
////                log.info("自动任务触达成功埋点失败：mapping：{}，member：{}，任务简称：{}", mappingId, memberId, taskShortName);
////            }
////        }
//    }
//
//
////    /**
////     * 任务执行器
////     *
////     * @param marketingActionTemplateTouchRule 行动模版规则
////     * @param url 链接
////     * @param tenantId 租户
////     * @param marketingResourceDataDTO 资源信息
////     * @param memberDetail 会员信息
////     * @return boolean
////     */
////    private boolean execute(MarketingActionTemplateTouchRuleDO marketingActionTemplateTouchRule, String url, Long taskId, String tenantId, MarketingResourceDataDTO marketingResourceDataDTO, MemberDetailVO memberDetail) {
////        try {
////            if (marketingActionTemplateTouchRule.getSendRuleChannel() == MessageTypeEnum.SMS.getCode()) {
////                SmsSendDTO smsSendDTO = marketingSendMsgManager.replaceSMS(marketingActionTemplateTouchRule.getSendTemplateId(), marketingResourceDataDTO, tenantId, taskId, memberDetail);
////                smsSendDTO.setTenantId(tenantId);
////                log.info("自动任务执行：发送短信参数：{}", smsSendDTO);
////                return smsService.sendSMSMessage(smsSendDTO);
////            } else if (marketingActionTemplateTouchRule.getSendRuleChannel() == MessageTypeEnum.WECHAT.getCode()) {
////                WxTemplateSendDTO wxTemplateSendDTO = marketingSendMsgManager.replaceWX(marketingActionTemplateTouchRule.getSendTemplateId(), url, marketingResourceDataDTO, tenantId, memberDetail);
////                wxTemplateSendDTO.setAppId(memberDetail.getAppId());
////                wxTemplateSendDTO.setTenantId(memberDetail.getTenantId());
////                log.info("自动任务执行：发送微信参数：{}", wxTemplateSendDTO);
////                return marketingWxTemplateService.sendWechatTemplate(wxTemplateSendDTO);
////            }
////        } catch (Exception e) {
////            return false;
////        }
////        return false;
////    }
//}
