package com.deepexi.dxp.marketing.service.specify;

import com.deepexi.dxp.marketing.domain.marketing.query.specify.CustomerFeedbackQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.CustomerFeedbackRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityFormFeedbackResponseVO;
import com.deepexi.dxp.marketing.domain.marketing.response.ExcelExportResponseDTO;
import com.deepexi.dxp.marketing.domain.specify.dto.ActivityFormFeedbackDTO;
import com.deepexi.util.pageHelper.PageBean;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface CustomerFeedbackService {
    /**
     * 用户反馈保存接口
     * @param requestDTO
     * @return
     */
    Boolean save(CustomerFeedbackRequestDTO requestDTO);

    /**
     * 用户反馈信息列表
     * @param query
     * @return
     */
    PageBean<ActivityFormFeedbackResponseVO> pageList(CustomerFeedbackQuery query);

    /**
     * 用户反馈信息列表
     * @param query
     * @return
     */
    PageBean<ActivityFormFeedbackResponseVO> pageHisList(CustomerFeedbackQuery query);

    /**
     * 用户反馈信息详情
     * @param id
     * @return
     */
    ActivityFormFeedbackResponseVO detail(Long id);

    /**
     * 表单反馈列表导出
     * @param response
     * @param query
     */
    ExcelExportResponseDTO exportFormFeedbackExcel(CustomerFeedbackQuery query);

    /**
     * 表单活动反馈信息查询
     * @param activityId
     * @param userId
     * @return
     */
    ActivityFormFeedbackResponseVO getByUserId(Long activityId,String phone);

    String getCity();

    ActivityFormFeedbackDTO getDetailByVerifyCode(String code);

    /**
     * 将表单数据转成界面可显示数据
     * @param limits
     * @param ext
     * @return
     */
    Map<String,Object> getFormLimits(Map<String,Object> limits, Map<String, Object> ext);
}
