package com.deepexi.dxp.marketing.service.specify;

import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityVerifyRejectLogQuery;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityVerifyRejectLogResponseDTO;
import com.deepexi.util.pageHelper.PageBean;

/**
 * 驳回
 */
public interface ActivityVerifyRejectLogService {

    PageBean<ActivityVerifyRejectLogResponseDTO> findPage(ActivityVerifyRejectLogQuery query);
}
