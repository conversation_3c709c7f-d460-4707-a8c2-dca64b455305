package com.deepexi.dxp.marketing.service.specify.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.deepexi.dxp.marketing.config.HuafaConstantConfig;
import com.deepexi.dxp.marketing.constant.RedisConstants;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityParticipationQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.UserProjectQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.MiniOrgRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityParticipationDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityParticipationGroupResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.PromotionActivityDetailDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.UserProjectResponseDTO;
import com.deepexi.dxp.marketing.enums.specify.ActivityPromotionTypeEnum;
import com.deepexi.dxp.marketing.enums.specify.ProjectIsJumpableEnum;
import com.deepexi.dxp.marketing.service.specify.ActivityParticipationService;
import com.deepexi.dxp.marketing.service.specify.UserProjectService;
import com.deepexi.dxp.middle.promotion.common.base.SuperEntity;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityGroupRelatedDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityParticipationDAO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityGroupRelatedDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityParticipationDO;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.StringUtil;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import com.deepexi.util.pojo.CloneDirection;
import com.deepexi.util.pojo.ObjectCloneUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ActivityParticipationServiceImpl implements ActivityParticipationService {

    @Autowired
    private ActivityParticipationDAO activityParticipationDAO;

    @Resource
    private UserProjectService userProjectService;

    @Autowired
    private HuafaConstantConfig huafaConstantConfig;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ActivityGroupRelatedDAO activityGroupRelatedDAO;


    @Override
    public List<ActivityParticipationGroupResponseDTO> projectGroupList(Long activityId) {
        if(activityId  == null){
            return Lists.newArrayList();
        }

        PromotionActivityDetailDTO result = JSON.parseObject(String.valueOf(redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO+activityId).get()),PromotionActivityDetailDTO.class);
        //活动校验-状态校验-时间校验
        if (Objects.nonNull(result)){
            return result.getFrontProjectList();
        }

        return this.getProjectGroupList(activityId);
    }

    @Override
    public List<ActivityParticipationGroupResponseDTO> getProjectGroupList(Long activityId) {
        List<ActivityParticipationGroupResponseDTO> responseDTOList = Lists.newArrayList();
        if(activityId  == null){
            return responseDTOList;
        }
        QueryWrapper<ActivityParticipationDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ActivityParticipationDO::getActivityId,activityId);
        List<ActivityParticipationDO> list = activityParticipationDAO.list(queryWrapper);//活动相关项目

        if(CollectionUtil.isEmpty(list)){
            return responseDTOList;
        }

        responseDTOList = this.sortProjectGroupList(list,responseDTOList);

        return responseDTOList;
    }

    @Override
    public List<ActivityParticipationGroupResponseDTO> projectGroupListByUserId(String userId){

        List<ActivityParticipationGroupResponseDTO> responseDTOList = Lists.newArrayList();
        if(StringUtil.isBlank(userId)){
            throw new ApplicationException("用户id不能为空");
        }

        UserProjectQuery query = new UserProjectQuery();
        query.setPageNumber(1);
        query.setPageSize(999999);
        query.setUserId(userId);
        PageBean<UserProjectResponseDTO> dtoList = userProjectService.getUserProjectNoLogin(query);

        List<ActivityParticipationDO> list = ObjectCloneUtils.convertList(dtoList.getContent(), ActivityParticipationDO.class, CloneDirection.OPPOSITE);

        if(CollectionUtil.isEmpty(list)){
            return responseDTOList;
        }

        responseDTOList = this.sortProjectGroupList(list,responseDTOList);

        return responseDTOList;
    }

    @Override
    public List<ActivityParticipationDO> listByActivityId(Long id) {
        return activityParticipationDAO.listByActivityId(id);
    }

    @Override
    public void updateBatchProjectIsJumpable(Long activityId) {
        List<ActivityParticipationDO> activityParticipationList = activityParticipationDAO.listByActivityId(activityId);
        if (CollectionUtil.isEmpty(activityParticipationList)){
            log.info("没有查询到{}", activityId);
            return;
        }
        List<ActivityParticipationDO> collect = activityParticipationList
                .parallelStream()
                .map(participation -> {
                    //true成功可跳转，false失败不可跳转
                    Boolean projectPageByHfProjectId = userProjectService.getProjectPageByHfProjectId(participation.getProjectId());
                    ActivityParticipationDO activityParticipationDO = new ActivityParticipationDO();
                    activityParticipationDO.setIsJumpable(projectPageByHfProjectId ? ProjectIsJumpableEnum.JUMPABLE.getId() : ProjectIsJumpableEnum.NO_JUMP.getId());
                    activityParticipationDO.setId(participation.getId());
                    return activityParticipationDO;
                })
                .filter(e-> Objects.equals(e.getIsJumpable(), ProjectIsJumpableEnum.JUMPABLE.getId()))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(collect)){
            log.info("---------没有需要更新跳转状态的项目数据--------------");
            return;
        }
        boolean result = activityParticipationDAO.updateBatchById(collect);
        log.info("执行项目是否跳转批量异步更新任务结果："+ result);
    }

    @Override
    public List<ActivityParticipationDTO> getSelectActivity(ActivityParticipationQuery activityParticipationQuery) {
        List<Long> activityIdList = Lists.newArrayList();
        if(ActivityPromotionTypeEnum.ACTIVITY.getType().equals(activityParticipationQuery.getPromotionType())){
            activityIdList.add(activityParticipationQuery.getActivityId());
            activityParticipationQuery.setActivityIdList(activityIdList);
        }else{
            QueryWrapper<ActivityGroupRelatedDO> queryWrapper = new QueryWrapper();
            queryWrapper.lambda().eq(ActivityGroupRelatedDO::getGroupId,activityParticipationQuery.getActivityId());//活动专题ID
            queryWrapper.lambda().eq(ActivityGroupRelatedDO::getDeleted, SuperEntity.DR_NORMAL);
            List<ActivityGroupRelatedDO> list = activityGroupRelatedDAO.list(queryWrapper);
            activityIdList = list.stream().map(ActivityGroupRelatedDO::getRelatedActivityId).distinct().collect(Collectors.toList());
            activityParticipationQuery.setActivityIdList(activityIdList);
        }

        return ObjectCloneUtils.convertList(activityParticipationDAO.getSelectActivity(activityParticipationQuery), ActivityParticipationDTO.class);
    }

    //以城市名称分组显示
    private List<ActivityParticipationGroupResponseDTO> sortProjectGroupList(List<ActivityParticipationDO> list, List<ActivityParticipationGroupResponseDTO> responseDTOList){
        //区域信息为空的项目list
        List<ActivityParticipationDO> emptyList = list.stream().filter(item -> StringUtil.isEmpty(item.getRealCityId())).collect(Collectors.toList());
        List<ActivityParticipationGroupResponseDTO> resultList = Lists.newArrayList();
        //区域信息不为空项目list
        List<ActivityParticipationDO> notEmptyList = list.stream().filter(item -> StringUtil.isNotEmpty(item.getRealCityId())).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(notEmptyList)){
            Map<String, List<ActivityParticipationDO>> map =
                    notEmptyList
                            .stream()
                            .collect(Collectors.groupingBy(ActivityParticipationDO::getRealCityName));
            for(Map.Entry<String, List<ActivityParticipationDO>> entry : map.entrySet()){
                String mapKey = entry.getKey();
                List<ActivityParticipationDO> values = entry.getValue();
                ActivityParticipationGroupResponseDTO activityParticipationGroupResponseDTO = new ActivityParticipationGroupResponseDTO();
                activityParticipationGroupResponseDTO.setCityId(values.get(0).getRealCityId());
                activityParticipationGroupResponseDTO.setCityName(mapKey);//区域名称
                List<ActivityParticipationGroupResponseDTO.ActivityParticipationItemResponseDTO> activityParticipationItemResponseDTOS = ObjectCloneUtils.convertList(values, ActivityParticipationGroupResponseDTO.ActivityParticipationItemResponseDTO.class);
                activityParticipationGroupResponseDTO.setProjectList(activityParticipationItemResponseDTOS);
                responseDTOList.add(activityParticipationGroupResponseDTO);
            }
            resultList =   citySort(responseDTOList);
        }

        if(CollectionUtil.isNotEmpty(emptyList)){//通用
            ActivityParticipationGroupResponseDTO activityParticipationGroupResponseDTO = new ActivityParticipationGroupResponseDTO();
            activityParticipationGroupResponseDTO.setCityName("通用");
            List<ActivityParticipationGroupResponseDTO.ActivityParticipationItemResponseDTO> activityParticipationItemResponseDTOS = ObjectCloneUtils.convertList(emptyList, ActivityParticipationGroupResponseDTO.ActivityParticipationItemResponseDTO.class);
            activityParticipationGroupResponseDTO.setProjectList(activityParticipationItemResponseDTOS);
            resultList.add(activityParticipationGroupResponseDTO);
        }

        return resultList;
    }

    /**
     * 对列表进行排序
     * @param responseDTOList
     */
    private List<ActivityParticipationGroupResponseDTO> citySort(List<ActivityParticipationGroupResponseDTO> responseDTOList){
        List<ActivityParticipationGroupResponseDTO> resultList = Lists.newArrayList();
        List<ActivityParticipationGroupResponseDTO> notContractList = Lists.newArrayList();
        if(huafaConstantConfig.HOT_CITY != null && CollectionUtil.isNotEmpty(huafaConstantConfig.HOT_CITY)){
            huafaConstantConfig.HOT_CITY.forEach(city ->{
                ActivityParticipationGroupResponseDTO activityParticipationGroupResponseDTO = responseDTOList.stream().filter(m -> m.getCityName().contains(city)).findAny().orElse(null);
                if(Objects.nonNull(activityParticipationGroupResponseDTO)){
                    resultList.add(activityParticipationGroupResponseDTO);
                }
            });
            notContractList = responseDTOList.stream().filter(item -> !huafaConstantConfig.HOT_CITY.stream().map(e -> e).collect(Collectors.toList()).contains(item.getCityName())).collect(Collectors.toList());
        }else{
            notContractList = responseDTOList;
        }
        if(CollectionUtil.isNotEmpty(notContractList)){
            //按首字母排序
            notContractList.sort((v1, v2) -> {
                char[] chars1 = v1.getCityName().toCharArray();
                char[] chars2 = v2.getCityName().toCharArray();
                String c1 = Character.toString(chars1[0]);
                String c2 = Character.toString(chars2[0]);
                if (Character.toString(chars1[0]).matches("[\\u4E00-\\u9FA5]+")) {
                    c1 = PinyinHelper.toHanyuPinyinStringArray(chars1[0])[0];
                }
                if (Character.toString(chars2[0]).matches("[\\u4E00-\\u9FA5]+")) {
                    c2 = PinyinHelper.toHanyuPinyinStringArray(chars2[0])[0];
                }
                c1 = c1.substring(0,1);
                c2 = c2.substring(0,1);
                if(v1.getCityName().contains("长沙") || v1.getCityName().contains("重庆")){
                    c1 = "c";
                }
                if(v2.getCityName().contains("长沙") || v2.getCityName().contains("重庆")){
                    c2 = "c";
                }
                return c1.compareTo(c2);
            });
            resultList.addAll(notContractList);
        }
        return resultList;
    }

}
