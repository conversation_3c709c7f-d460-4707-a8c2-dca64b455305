package com.deepexi.dxp.marketing.domain.marketing.request;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description：模板发送数据
 * @author：<PERSON><PERSON><PERSON><PERSON>
 * @version：1.0.0
 * @date：2021-03-31 2:15 下午
 */
@Data
public class WxTemplateSendDataRequest extends AbstractObject {

    /**
     * 参数名
     */
    @ApiModelProperty("参数名")
    private String name;

    /**
     * 参数值
     */
    @ApiModelProperty("参数值")
    private String value;

    /**
     * 参数显示颜色(可不填)
     */
    @ApiModelProperty("参数显示颜色")
    private String color;

}
