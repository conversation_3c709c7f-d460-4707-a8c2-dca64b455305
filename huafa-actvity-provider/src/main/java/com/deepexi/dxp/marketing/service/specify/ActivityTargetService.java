package com.deepexi.dxp.marketing.service.specify;

import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityTargetQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityTargetRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityTargetResponseDTO;
import com.deepexi.util.pageHelper.PageBean;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface ActivityTargetService {

	Boolean create(ActivityTargetRequestDTO dto);

	PageBean<ActivityTargetResponseDTO> getPage(ActivityTargetQuery query);

	ActivityTargetResponseDTO getById(Long id);

}
