package com.deepexi.dxp.marketing.domain.marketing.dto;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 新增流程定义DTO
 *
 * <AUTHOR>
 * @Date 2020/3/27
 */
@Data
@ApiModel
public class ProcessSaveDTO extends AbstractObject {

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID", required = true)
    private String tenantId;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "appId", required = true)
    private Long appId;

    /**
     * 关联项ID
     */
    @ApiModelProperty(value = "自动营销ID/模型ID/画布实例id", required = true)
    private String itemId;

    /**
     * 关联项名称
     */
    @ApiModelProperty(value = "自动营销名称/模型名称/画布名称")
    private String itemName;

}
