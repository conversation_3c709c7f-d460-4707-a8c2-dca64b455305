package com.deepexi.dxp.marketing.service.specify;

import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityAnalysisQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.EventsReportQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.SensorsBuriedPointDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.*;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityPromotionChannelDO;

import java.util.List;

/**
 * 神策请求接口
 * <AUTHOR>
 */
public interface SensorsService {

    /**
     * 活动次数查询
     */
    Long findActivityFrequencySqlQuery(EventsReportQuery query);
    /**
     * 活动人数查询
     */
    Long findActivityPeopleSqlQuery(EventsReportQuery query);

    /**
     * 活动ids 查询次数
     * @param eventsReportQuery
     * @return
     */
    List<ActivityPageViewVisitorsDTO> findActivityFrequencySqlInQuery(EventsReportQuery eventsReportQuery);


    /**
     * 活动ids 查询人数
     * @param eventsReportQuery
     * @return
     */
    List<ActivityPageViewVisitorsDTO> findActivityPeopleSqlInQuery(EventsReportQuery eventsReportQuery);

    /**
     * 查询活动今日以及昨日用户数
     * @param eventsReportQuery
     * @return
     */
    ActivityTodayYesterdayVisitorsDTO todayYesterdayPeopleNumber(EventsReportQuery eventsReportQuery);

    /**
     * 查询某个活动在指定时间范围内的指标统计
     * @param query
     * @return
     */
    List<ActivityTodayYesterdayVisitorsDTO> todayYesterdayPeopleNumberBatch(EventsReportQuery query);
    /**
     * 查询活动今日以及昨日浏览用次数
     * @param eventsReportQuery
     * @return
     */
    ActivityTodayYesterdayVisitorsDTO todayYesterdayNumber(EventsReportQuery eventsReportQuery);

    List<ActivityTodayYesterdayVisitorsDTO> todayYesterdayNumberGroupByEvent(EventsReportQuery eventsReportQuery);

    /**
     * 访问人数/分享人数势统计
     * @param eventsReportQuery
     * @return
     */
    List<ActivityTrendResponseDTO> findActivityTrendsPageView(EventsReportQuery eventsReportQuery);

    /**
     * 渠道分布统计访问人数，参与人数，分享人数，留资人数
     * @param eventsReportQuery
     * @return
     */
    List<ChannelDistributionResponseDTO> sensorsChannelDistribution(EventsReportQuery eventsReportQuery);

    /**
     * 城市分布统计
     * @param eventsReportQuery
     * @return
     */
    List<ActivityPageViewVisitorsDTO> findCityDistributionList(EventsReportQuery eventsReportQuery);

    /**
     * 查询全部转到访人数
     * @param eventsReportQuery
     * @return
     */
    Long findGoToViewPeopleNumber(EventsReportQuery eventsReportQuery);

    /**
     * 神策活动列表分析
     * @param activityId
     * @return
     */
    List<ActivityAnalysisResponseDTO> sensorsActivityAnalysisStatistics(String activityId);

    /**
     * 参与人数
     * @param activityId
     * @return
     */
    List<ActivityAnalysisResponseDTO> sensorsActivityAnalysisStatisticsJoinNumber(String activityId);

    /**
     * 参与用户统计
     * @param id
     * @return
     */
    IndexAnalysisDTO joinUserAnalysis(Long id);

    /**
     * 神策服务端埋点
     * @param sensorsBuriedPoint
     * @return
     */
    void sensorsBuriedPoint(SensorsBuriedPointDTO sensorsBuriedPoint);

    /**
     * 查询该活动，渠道是否存在
     * @param promotionChannelDO 自定义渠道名称
     * @return
     */
    Long channelCountByActivityId(ActivityPromotionChannelDO promotionChannelDO);

    /**
     * 海报分享统计
     * @param query
     * @return
     */
    List<ActivityAnalysisResponseDTO> posterChannelStatistics(ActivityAnalysisQuery query);

    Long posterChannelStatisticsCount(ActivityAnalysisQuery query);

}
