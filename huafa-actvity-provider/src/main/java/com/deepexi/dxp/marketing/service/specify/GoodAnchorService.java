package com.deepexi.dxp.marketing.service.specify;

import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPartakeRequest;
import com.deepexi.dxp.marketing.domain.marketing.response.*;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityLimitDO;

import java.util.List;

/**
 * @Description: 好主播活动接口
 * @Author: HT
 * @CreateTime: 2022/5/11
 */
public interface GoodAnchorService {

    GoodAnchorLikesRankResponseDTO queryLikesRanking(Integer id, Long activityId);

    List<GoodAnchorVideoResponseDTO> queryVideosByCityId(String cityId, Long activityId, String mobile);

    SaleTeamSaleOrgRelDTO getCityInfoByProjectId(String projectId);

    List<String> getProjectIdsByCityId(String cityId);

    GoodAnchorVideoExtResponseDTO queryVideosByAccountId(String accountId, Long activityId, String mobile);

    VideoResponseDTO queryVideoById(String videoId, String mobile, Long activityId);

    Boolean partakeAct(ActivityPartakeRequest requestVo, List<PromotionActivityLimitDO> promotionActivityLimitList);

    Boolean changeStauts(Long id, String id1);

    Boolean delete(Long id);

    String getUUID(String phone, Long activityId, Long id);
}
