package com.deepexi.dxp.marketing.service.specify.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepexi.dxp.marketing.common.base.utils.CommonExceptionCode;
import com.deepexi.dxp.marketing.constant.RedisConstants;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityUserRelatedDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.PromotionActivityDetailDTO;
import com.deepexi.dxp.marketing.enums.specify.ActivityTemplateNumberEnum;
import com.deepexi.dxp.marketing.manager.promotion.PromotionActivityManager;
import com.deepexi.dxp.marketing.service.specify.PromotionActivityService;
import com.deepexi.dxp.marketing.service.specify.PromotionMarathonService;
import com.deepexi.dxp.middle.promotion.dao.PromotionActivityDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityUserRelatedDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.CustomerFeedbackDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.PromotionMarathonDAO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityFormFeedbackDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityUserRelatedDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.PromotionMarathonDO;
import com.deepexi.util.exception.ApplicationException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2021/8/25
 */
@Slf4j
@Service
public class PromotionMarathonServiceImpl implements PromotionMarathonService {

    @Autowired
    private PromotionMarathonDAO promotionMarathonDAO;
    @Autowired
    private PromotionActivityService promotionActivityService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private PromotionActivityDAO promotionActivityDAO;
    @Autowired
    private PromotionActivityManager promotionActivityManager;
    @Autowired
    private CustomerFeedbackDAO customerFeedbackDAO;
    @Value("${hf.thejoyrun-url}")
    private String thejoyrunUrl;
    @Resource
    public ActivityUserRelatedDAO activityUserRelatedDAO;

    private final static String CODE_PREFIX = "HF-%s";

    @Override
    public void cleanData(Long activityId, String phone){
        RSet<String> phoneSet = redissonClient.getSet(String.format(RedisConstants.CACHE_PREV_KEY_ACT_MARATHON_PHONE, activityId));
        phoneSet.remove(phone);
        RBucket<PromotionMarathonDO> bucket = redissonClient.getBucket(String.format(RedisConstants.CACHE_PREV_KEY_ACT_MARATHON_CODE, phone));
        bucket.delete();
        promotionMarathonDAO.remove(Wrappers.lambdaQuery(PromotionMarathonDO.class).eq(PromotionMarathonDO::getPhone,phone));
        customerFeedbackDAO.remove(Wrappers.lambdaQuery(ActivityFormFeedbackDO.class)
                .eq(ActivityFormFeedbackDO::getActivityId,activityId)
                .eq(ActivityFormFeedbackDO::getPhone,phone));
        // 删除抽奖次数
        ActivityUserRelatedDTO activityUserRelatedDTO = new ActivityUserRelatedDTO();
        activityUserRelatedDTO.setActivityId(activityId);
        activityUserRelatedDTO.setPhone(phone);
        ActivityUserRelatedDTO activityUserRelated = activityUserRelatedDAO.getActivityUserRelated(activityUserRelatedDTO);
        if(activityUserRelated != null && activityUserRelated.getLimits()!= null){
            Map<String, Object> map = activityUserRelated.getLimits();
            map.put(ActivityTemplateNumberEnum.DRAW_NUMBER.getId(),1);
            map.put(ActivityTemplateNumberEnum.SHARE_NUMBER.getId(),10);
            ActivityUserRelatedDO entity = new ActivityUserRelatedDO();
            entity.setId(activityUserRelated.getId());
            entity.setLimits(map);
            activityUserRelatedDAO.updateById(entity);
        }
    }

    @Override
    public String allocationMarathonCode(Long activityId, String phone, Date endTime) {
        for (int i = 0; i < 3; i++) {
            // 已经存在直接返回参赛码
            RBucket<PromotionMarathonDO> bucket = redissonClient.getBucket(String.format(RedisConstants.CACHE_PREV_KEY_ACT_MARATHON_CODE, phone));
            if (bucket.get() != null) {
                return bucket.get().getCode();
            }
            // 生成参赛码
            String code = String.format(CODE_PREFIX, RandomUtil.randomString(8)).toUpperCase();
            PromotionMarathonDO promotionMarathonDO = new PromotionMarathonDO();
            // 添加到数据库
            promotionMarathonDO.setActivityId(activityId);
            promotionMarathonDO.setPhone(phone);
            promotionMarathonDO.setCode(code);
            promotionMarathonDO.setStatus(2);
            promotionMarathonDO.setCreatedTime(new Date());
            promotionMarathonDO.setUpdatedTime(new Date());
            try {
                promotionMarathonDAO.save(promotionMarathonDO);
                // 缓存参赛码
                long expireTime = endTime.getTime() - System.currentTimeMillis();
                expireTime = (expireTime / 1000) + RedisConstants.EXTRA_EXPIRE_SECOND;
                bucket.set(promotionMarathonDO, expireTime, TimeUnit.SECONDS);
                return promotionMarathonDO.getCode();
            } catch (DuplicateKeyException e) {
                log.error("生成参赛码重复:" + activityId + "+" + code + "+" + phone, e);
                promotionMarathonDO = promotionMarathonDAO.getOne(Wrappers.lambdaQuery(PromotionMarathonDO.class)
                        .eq(PromotionMarathonDO::getActivityId, activityId)
                        .eq(PromotionMarathonDO::getPhone, phone).last("limit 1"));
                if (promotionMarathonDO != null) {
                    log.warn("参赛码未缓存:{}", JSONUtil.toJsonStr(promotionMarathonDO));
                    return promotionMarathonDO.getCode();
                }
            }
        }
        throw new ApplicationException(CommonExceptionCode.INVALIDATION, "生成三次参赛码失败");
    }

    @Override
    public Integer checkStatus(String phone) {
        PromotionMarathonDO entity;
        RBucket<PromotionMarathonDO> bucket = redissonClient.getBucket(String.format(RedisConstants.CACHE_PREV_KEY_ACT_MARATHON_CODE, phone));
        entity = bucket.get();
        if (entity == null) {
            PromotionMarathonDO promotionMarathonDO = promotionMarathonDAO.getOne(Wrappers.lambdaQuery(PromotionMarathonDO.class)
                    .eq(PromotionMarathonDO::getPhone, phone).last("limit 1"));
            if (promotionMarathonDO != null) {
                entity = promotionMarathonDO;
            }
        }
        if (entity == null) {
            log.info("未报名,{}", phone);
            return 1;
        }
        if (Objects.equals(entity.getStatus(), 3)) {
            // 已完成比赛
            return 3;
        }
        // 未完赛则调悦跑圈接口查询最新完赛状态并更新
        boolean finishFlag = checkFinish(entity.getCode());
        if (finishFlag) {
            entity.setStatus(3);
            bucket.set(entity);
            promotionMarathonDAO.updateById(entity);
            return 3;
        } else {
            // 未完成比赛
            return 2;
        }
    }

    @Override
    public boolean checkFinish(String code) {
        String url = thejoyrunUrl + "?code=" + code;
        try {
            log.info("请求悦跑圈接口开始:{}", url);
            String mes = HttpUtil.get(url);
            log.info("请求悦跑圈接口结束:{}，返回结果:{}", url, mes);
            JSONObject jsonObject = JSONUtil.parseObj(mes);
            if (Objects.equals(jsonObject.getInt("code"), 0) && jsonObject.getJSONObject("data") != null
                    && Objects.equals(jsonObject.getJSONObject("data").getInt("is_finish"), 1)) {
                return true;
            }
        } catch (Exception e) {
            log.error("请求悦跑圈接口出错" + url, e);
        }
        return false;
    }

    @Override
    public PromotionActivityDetailDTO ready(Long activityId) {
        PromotionActivityDO entity = new PromotionActivityDO();
        entity.setId(activityId);
        entity.setRemark("marathon");
        promotionActivityDAO.updateById(entity);
        // 刷新缓存
        promotionActivityManager.forceCacheActInfo(activityId);

        refreshCache(activityId);

        return JSON.parseObject(String.valueOf(redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO + activityId).get()), PromotionActivityDetailDTO.class);
    }

    @Override
    public void refreshCache(Long activityId) {
        PromotionActivityDetailDTO dto = promotionActivityService.getActivityById(activityId);
        long expireTime = dto.getEndTime().getTime() - System.currentTimeMillis();
        expireTime = (expireTime / 1000) + RedisConstants.EXTRA_EXPIRE_SECOND;

        RSet<String> phoneSet = redissonClient.getSet(String.format(RedisConstants.CACHE_PREV_KEY_ACT_MARATHON_PHONE, activityId));
        long size = 1000L;
        long page = 1L;
        IPage<PromotionMarathonDO> pageResult;
        do {
            Page<PromotionMarathonDO> pageParam = new Page<>(page, size++);
            pageResult = promotionMarathonDAO.page(pageParam, Wrappers.lambdaQuery(PromotionMarathonDO.class).eq(PromotionMarathonDO::getActivityId, activityId).orderByAsc(PromotionMarathonDO::getId));
            if (CollectionUtil.isNotEmpty(pageResult.getRecords())) {
                for (PromotionMarathonDO entity : pageResult.getRecords()) {
                    redissonClient.getBucket(String.format(RedisConstants.CACHE_PREV_KEY_ACT_MARATHON_CODE, entity.getPhone())).set(entity, expireTime, TimeUnit.SECONDS);
                    phoneSet.add(entity.getPhone());
                }
            }
        } while (pageResult.getRecords().size() == size);
        phoneSet.expire(expireTime, TimeUnit.SECONDS);
    }

    @Override
    public JSONObject checkCode(String code) {
        PromotionMarathonDO promotionMarathonDO = promotionMarathonDAO.getOne(Wrappers.lambdaQuery(PromotionMarathonDO.class).eq(PromotionMarathonDO::getCode, code).last("limit 1"));
        if (promotionMarathonDO == null) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "参赛码不存在");
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.putOnce("code", promotionMarathonDO.getCode());
        jsonObject.putOnce("date", promotionMarathonDO.getUpdatedTime());
        return jsonObject;
    }

    @Override
    public Integer finish(String code,Integer status) {
        PromotionMarathonDO promotionMarathonDO = promotionMarathonDAO.getOne(Wrappers.lambdaQuery(PromotionMarathonDO.class).eq(PromotionMarathonDO::getCode, code).last("limit 1"));
        if (promotionMarathonDO == null) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "参赛码不存在");
        }
        RBucket<PromotionMarathonDO> bucket = redissonClient.getBucket(String.format(RedisConstants.CACHE_PREV_KEY_ACT_MARATHON_CODE, promotionMarathonDO.getPhone()));
        if(bucket.get() == null){
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "参赛码不存在");
        }
        promotionMarathonDO.setStatus(status);
        bucket.set(promotionMarathonDO);
        promotionMarathonDAO.updateById(promotionMarathonDO);
        return promotionMarathonDO.getStatus();
    }
}
