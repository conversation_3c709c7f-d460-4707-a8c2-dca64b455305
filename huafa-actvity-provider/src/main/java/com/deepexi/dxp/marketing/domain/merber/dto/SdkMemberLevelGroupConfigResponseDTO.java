package com.deepexi.dxp.marketing.domain.merber.dto;

import com.deepexi.util.domain.dto.BaseDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * SDK-会员等级组配置响应DTO
 *
 * <AUTHOR>
 * @since 2020年08月21日 14:08
 */
@Data
public class SdkMemberLevelGroupConfigResponseDTO extends BaseDTO {

    @ApiModelProperty("主键")
    private Long id;
    
    @ApiModelProperty("创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createdTime;

    @ApiModelProperty("创建人")
    private String updatedBy;
    
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updatedTime;

    @ApiModelProperty(value = "等级组编号")
    private String levelGroupCode;

    @ApiModelProperty(value = "等级组名称")
    private String levelGroupName;

    @ApiModelProperty(value = "状态 0 启用 1禁用")
    private Integer status;

    @ApiModelProperty(value = "等级组描述")
    private String description;

    @ApiModelProperty(value = "图片")
    private String iconUrl;

    @ApiModelProperty(value = "会员等级类型")
    private Integer levelType;

    @ApiModelProperty(value = "会员身份id")
    private Long identityId;

    @ApiModelProperty(value = "会员身份名称")
    private String identityName;

    @ApiModelProperty(value = "成长值类型id")
    private Long growthTypeId;

    @ApiModelProperty(value = "成长值类型名称")
    private String growthTypeName;
}

