package com.deepexi.dxp.marketing.domain.merber.dto;

import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @ClassName: SdkMemberLevelResponseDTO
 * @Description:
 * @Author: ZhouJian
 * @Date: 2020-06-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class SdkMemberLevelResponseDTO extends SuperDTO {

    @ApiModelProperty("会员ID")
    private Long memberId;
    @ApiModelProperty("expiredTime")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date expiredTime;
    @ApiModelProperty("等级组id")
    private Long levelGroupId;
    @ApiModelProperty("等级id")
    private Long levelId;
    @ApiModelProperty("levelType")
    private Integer levelType;
    @ApiModelProperty("等级名称")
    private String levelName;

}
