package com.deepexi.dxp.marketing.service.specify.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.CustomerFeedbackQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.SensorsBuriedPointDTO;
import com.deepexi.dxp.marketing.enums.specify.SensorsEventEnum;
import com.deepexi.dxp.marketing.manager.promotion.PromotionActivityManager;
import com.deepexi.dxp.marketing.service.specify.ActivityParticipationService;
import com.deepexi.dxp.marketing.service.specify.AsynchronousService;
import com.deepexi.dxp.marketing.service.specify.SensorsService;
import com.deepexi.dxp.middle.promotion.dao.PromotionActivityDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityPartakeLogDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityParticipationDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.CustomerFeedbackDAO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityFormFeedbackDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityOrderDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityPartakeLogDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityParticipationDO;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.StringUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AsynchronousServiceImpl implements AsynchronousService {

    @Resource
    public CustomerFeedbackDAO customerFeedbackDAO;

    @Resource
    public SensorsService sensorsService;
    @Resource
    private PromotionActivityDAO promotionActivityDAO;
    @Resource
    private ActivityParticipationDAO activityParticipationDAO;
    @Resource
    private ActivityParticipationService activityParticipationService;
    @Autowired
    private ActivityPartakeLogDAO activityPartakeLogDAO;

    @Lazy
    @Autowired
    private PromotionActivityManager promotionActivityManager;

    @Override
    @Async("threadExecutor")
    public void sensorsBuriedPoint(ActivityOrderDO activityOrderDO) {
        log.info("--------------------------开始异步执行神策退款订单埋点事件----------start---------------");
        CustomerFeedbackQuery query = new CustomerFeedbackQuery();
        query.setActivityId(activityOrderDO.getActivityId());
        //query.setUserId(activityPartakeLog.getUserId());
        query.setPhone(activityOrderDO.getPhone());
        if (activityOrderDO.getResourceId() != null && activityOrderDO.getResourceId() > 0) {
            query.setResourceId(activityOrderDO.getResourceId());
        }
        List<ActivityFormFeedbackDO> content = customerFeedbackDAO.pageList(query).getContent();

        PromotionActivityDO byId = promotionActivityDAO.getById(activityOrderDO.getActivityId());
        
        if (CollectionUtil.isNotEmpty(content) && byId != null) {
            ActivityFormFeedbackDO activityFormFeedbackDO = content.get(0);
            QueryWrapper<ActivityParticipationDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(ActivityParticipationDO::getActivityId,activityOrderDO.getActivityId());
            queryWrapper.lambda().eq(ActivityParticipationDO::getProjectId,activityFormFeedbackDO.getProjectId());
            List<ActivityParticipationDO> list = activityParticipationDAO.list(queryWrapper);
            ActivityParticipationDO activityParticipationDO = new ActivityParticipationDO();
            if (CollectionUtil.isNotEmpty(list)){
                activityParticipationDO = list.get(0);
            }
            Map<String, Object> superProperties = Maps.newHashMap();
            superProperties.put("activity_id", activityOrderDO.getActivityId());
            superProperties.put("activity_name", Optional.ofNullable(byId.getName()).orElse(""));
            superProperties.put("activity_type", byId.getPaTemplateId());
            superProperties.put("project_id", activityFormFeedbackDO.getProjectId());
            superProperties.put("project_name", Optional.ofNullable(activityFormFeedbackDO.getProjectName()).orElse(""));
            superProperties.put("project_city", Optional.ofNullable(activityParticipationDO.getCityName()).orElse(""));
//            superProperties.put("pay_money", activityOrderDO.getPayMoney());
            superProperties.put("order_no", activityOrderDO.getWxOrderNo());

            SensorsBuriedPointDTO sensorsBuriedPoint = new SensorsBuriedPointDTO();
            sensorsBuriedPoint.setEventName(SensorsEventEnum.REFUND_ORDER.getCode());
            sensorsBuriedPoint.setCookieId(activityOrderDO.getUnionId());
            sensorsBuriedPoint.setRegisterId(activityFormFeedbackDO.getPhone());
            sensorsBuriedPoint.setSuperProperties(superProperties);
            sensorsService.sensorsBuriedPoint(sensorsBuriedPoint);
            log.info("--------------------------开始异步执行神策退款订单埋点事件----------end---------------");
        } else {
            log.info("----------异步执行神策退款订单埋点事件用户登记信息不存在----------end---------------");
        }
    }

    @Override
    @Async("threadExecutor")
    public void sensorsBuriedPointFission(Long id, String eventName) {
        log.info("--------------------------开始异步执行神策裂变埋点事件{},{}----------start---------------", id, eventName);
        ActivityPartakeLogDO activityPartakeLog = activityPartakeLogDAO.getById(id);
        if (activityPartakeLog == null){
            log.error("--------------------------开始异步执行神策裂变埋点事件------发起人信息不存在----------");
            return ;
        }

        PromotionActivityDO byId = promotionActivityDAO.getById(activityPartakeLog.getActivityId());

        if (byId != null) {
            ActivityParticipationDO activityParticipationDO = new ActivityParticipationDO();
            if (StringUtil.isNotBlank(activityPartakeLog.getProjectId())) {
                QueryWrapper<ActivityParticipationDO> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(ActivityParticipationDO::getActivityId, activityPartakeLog.getActivityId());
                queryWrapper.lambda().eq(ActivityParticipationDO::getProjectId, activityPartakeLog.getProjectId());
                List<ActivityParticipationDO> list = activityParticipationDAO.list(queryWrapper);
                if (CollectionUtil.isNotEmpty(list)) {
                    activityParticipationDO = list.get(0);
                }
            }
            Map<String, Object> superProperties = Maps.newHashMap();
            superProperties.put("activity_id", activityPartakeLog.getActivityId());
            superProperties.put("activity_name", Optional.ofNullable(byId.getName()).orElse(""));
            superProperties.put("activity_type", byId.getPaTemplateId());
            superProperties.put("project_id", Optional.ofNullable(activityPartakeLog.getProjectId()).orElse(""));
            superProperties.put("project_name",  Optional.ofNullable(activityPartakeLog.getProjectName()).orElse(""));
            superProperties.put("project_city", Optional.ofNullable(activityParticipationDO.getCityName()).orElse(""));

            SensorsBuriedPointDTO sensorsBuriedPoint = new SensorsBuriedPointDTO();
            sensorsBuriedPoint.setEventName(eventName);
            sensorsBuriedPoint.setCookieId(activityPartakeLog.getUnionId());
            sensorsBuriedPoint.setRegisterId(activityPartakeLog.getPhone());
            sensorsBuriedPoint.setSuperProperties(superProperties);
            sensorsService.sensorsBuriedPoint(sensorsBuriedPoint);
            log.info("--------------------------开始异步执行神策裂变埋点事件{},{}----------end---------------", id, eventName);
        } else {
            log.info("----------异步执行神策退款订单埋点事件用户登记信息不存在{},{}----------end---------------", id, eventName);
        }
    }

    @Override
    @Async("threadExecutor")
    public void projectIsJumpable(Long id) {
        log.info("--------------------------开始异步执行项目是否跳转批量更新任务{}----------start---------------", id);
        activityParticipationService.updateBatchProjectIsJumpable(id);
        log.info("--------------------------开始异步执行项目是否跳转批量更新任务{}----------end---------------", id);
    }

    @Override
    @Async("threadExecutor")
    public void asyncCacheNewActInfo(Long activityId) {
        log.info("异步上传活动数据到redis，活动id={}",activityId);
        promotionActivityManager.forceCacheActInfo(activityId);
    }

}
