package com.deepexi.dxp.marketing.domain.marketing.vo;

import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 主动营销任务KPI配置项VO
 * @Author: HuangBo.
 * @Date: 2020/4/1 18:10
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Api(value = "KPI配置项VO")
public class MarketingActiveKpiItemVO extends SuperVO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 编码
     */
    @ApiModelProperty(value = "任务指标编码")
    private String code;


    /**
     * 任务目标类型(1:营收；2:商品；3:用户)
     */
    @ApiModelProperty(value = "任务目标类型(1:营收；2:商品；3:用户)")
    @NotNull(message = "指标任务类型不能为空.")
    private int type;

    /**
     * 任务目标名称
     */
    @ApiModelProperty(value = "任务指标名称")
    @NotNull(message = "指标名称不能为空.")
    private String name;

    /**
     * 父级指标
     */
    @ApiModelProperty(value = "父指标id")
    @NotNull(message = "父指标ID不能为空.")
    private Long pid;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
    @NotNull(message = "单位名称不能为空.")
    private String unitName;

    /**
     * 是否推荐(1是 0否)
     */
    @ApiModelProperty(value = "是否推荐(1是 0否)")
    @NotNull(message = "是否推荐不能为空.")
    private boolean recommend;

    /**
     * 同类型下的指标排序
     */
    @ApiModelProperty(value = "指标显示排序")
    @NotNull(message = "指标显示排序不能为空.")
    private int sort;
}
