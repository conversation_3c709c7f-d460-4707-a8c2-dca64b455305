//package com.deepexi.dxp.marketing.engine.processor;
//
//
//import com.deepexi.dxp.marketing.engine.MarketingTaskCallback;
//import com.deepexi.dxp.marketing.engine.MarketingTaskContainer;
//import com.deepexi.dxp.marketing.engine.event.MarketingTaskEvent;
//import com.deepexi.dxp.marketing.engine.listener.MarketingTaskListener;
//import com.deepexi.dxp.marketing.engine.task.MetaDataTask;
//import com.deepexi.dxp.marketing.enums.marketing.MarketingTaskActiveStatusEnum;
//import com.deepexi.dxp.marketing.service.marketing.MarketingActiveService;
//import com.deepexi.dxp.marketing.utils.SpringContextHolder;
//import com.deepexi.dxp.middle.marketing.domain.dto.MarketingActiveDTO;
//import lombok.extern.slf4j.Slf4j;
//
//import java.util.Date;
//
///**
// * 营销任务结束处理
// *
// * <AUTHOR>
// * @date 2020/4/8 10:16
// */
//@Slf4j
//public class MarketingTaskEndProcessor extends AbstractTaskProcessor {
//
//    public MarketingTaskEndProcessor(MetaDataTask metaDataTask) {
//        super(metaDataTask);
//    }
//
//    public MarketingTaskEndProcessor(MetaDataTask metaDataTask, MarketingTaskCallback callback) {
//        super(metaDataTask, callback);
//    }
//
//    @Override
//    protected void doProcess(MarketingTaskListener listener, MarketingTaskEvent event) {
//        try {
//            log.info("task-end-start-营销任务结束处理开始");
//            listener.end(event);
//        } finally {
//            event.semaphoreDown();
//            log.info("task-end-end-营销任务结束处理结束");
//        }
//    }
//
//    /**
//     * 结束时处理该任务相关事项：
//     * 1、将本任务对应的发送引擎各个处理器销毁
//     * 2、更新本任务的状态，变更为已完成
//     */
//    @Override
//    public void callBack() {
//        // 将本任务对应的发送引擎各个处理器销毁
//        SpringContextHolder.getBean(MarketingTaskContainer.class).destroyTask(this.getMetaDataTask().getKey());
//        // 更新本任务状态，改为已完成状态
//        MarketingActiveDTO activeDTO = (MarketingActiveDTO) this.getMetaDataTask().getMarketingTaskDTO();
//        // 设置完成状态
//        activeDTO.setStatus(MarketingTaskActiveStatusEnum.FINISHED.getValue());
//        // 设置完成时间
//        activeDTO.setTaskEndTime(new Date());
//        MarketingActiveService service = (MarketingActiveService) this.getMetaDataTask().getTaskService();
//        service.updateStatus(activeDTO);
//    }
//}
