package com.deepexi.dxp.marketing.controller.specify.middleapi;


import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.PromotionResourceResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.query.specify.PromotionResourceQuery;
import com.deepexi.dxp.marketing.domain.promotion.request.specify.PromotionResourceRequestDTO;
import com.deepexi.dxp.marketing.service.specify.PromotionResourceService;
import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@Slf4j
@RequestMapping("/middle-api/v1/promotion-resource")
@Api(value = "middle-资源管理", tags = "middle-资源管理")
public class MiddlePromotionResourceController {

    @Autowired
    private PromotionResourceService promotionResourceService;

    /**
     * 列表
     */
    @PostMapping("/list")
    public Data<PageBean<PromotionResourceResponseDTO>> noOverList(@RequestBody PromotionResourceQuery query) {
        return new Data<>(promotionResourceService.noOverList(query));
    }

    /**
     * 列表
     */
    @PostMapping("/allList")
    public Data<PageBean<PromotionResourceResponseDTO>> list(@RequestBody PromotionResourceQuery query) {
        return new Data<>(promotionResourceService.findPage(query));
    }

    @PostMapping("/createOrUpdate")
    @ApiOperation(value = "添加或更新资源信息")
    public Data<Boolean> createOrUpdate(@RequestBody @Valid  PromotionResourceRequestDTO dto) {
        return new Data<>(promotionResourceService.createOrUpdate(dto));
    }

    @DeleteMapping("/delete")
    @ApiOperation(value = "删除资源信息")
    public Data<Boolean> delete(@RequestBody Long id) {
        return new Data<>(promotionResourceService.deleteById(id));
    }

    @ApiOperation(value = "资源信息-详情")
    @GetMapping("/detail")
    public Data<PromotionResourceResponseDTO> detail(@RequestParam Long id) {
        return new Data<>(promotionResourceService.detail(id));
    }
}
