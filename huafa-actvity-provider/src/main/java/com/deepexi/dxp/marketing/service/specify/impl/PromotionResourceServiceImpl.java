package com.deepexi.dxp.marketing.service.specify.impl;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.deepexi.common.extension.AppRuntimeEnv;
import com.deepexi.dxp.marketing.common.base.utils.CommonExceptionCode;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.PromotionResourceResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.query.specify.PromotionResourceQuery;
import com.deepexi.dxp.marketing.domain.promotion.request.specify.PromotionResourceRequestDTO;
import com.deepexi.dxp.marketing.enums.resource.ResourceGrantWayEnum;
import com.deepexi.dxp.marketing.enums.specify.*;
import com.deepexi.dxp.marketing.service.specify.PromotionResourceService;
import com.deepexi.dxp.marketing.utils.CodeUtils;
import com.deepexi.dxp.marketing.utils.DateTimeUtils;
import com.deepexi.dxp.middle.marketing.common.base.SuperEntity;
import com.deepexi.dxp.middle.promotion.dao.specify.PromotionHisResourceDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.PromotionResourceDAO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.PromotionHisResourceDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.PromotionResourceDO;
import com.deepexi.util.StringUtil;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import com.deepexi.util.pojo.CloneDirection;
import com.deepexi.util.pojo.ObjectCloneUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

@Service
@Slf4j
public class PromotionResourceServiceImpl implements PromotionResourceService {

    @Autowired
    private PromotionResourceDAO promotionResourceDAO;

    @Autowired
    private PromotionHisResourceDAO promotionHisResourceDAO;

    @Override
    public PageBean<PromotionResourceResponseDTO> findPage(PromotionResourceQuery query) {
        PageBean<PromotionResourceDO> page = promotionResourceDAO.findPage(query);
        return ObjectCloneUtils.convertPageBean(page,PromotionResourceResponseDTO.class, CloneDirection.OPPOSITE);
    }

    @Override
    public PageBean<PromotionResourceResponseDTO> noOverList(PromotionResourceQuery query) {
        query.setWhetherAll(1);
        PageBean<PromotionResourceDO> page = promotionResourceDAO.findPage(query);
        return ObjectCloneUtils.convertPageBean(page,PromotionResourceResponseDTO.class, CloneDirection.OPPOSITE);
    }

    @Override
    public boolean createOrUpdate(PromotionResourceRequestDTO dto) {

        checkPromotionResourceRequestDTO(dto);

        PromotionResourceDO clone = dto.clone(PromotionResourceDO.class);

        if(PromotionResourceValidTimeTypeEnum.DESIGNATED_TIME.getId().equals(dto.getValidTimeType())){
            clone.setValidStartTime(DateTimeUtils.getStartOfDay(dto.getValidStartTime()));
            clone.setValidEndTime(DateTimeUtils.getEndOfDay(dto.getValidEndTime()));
        }

        if(clone.getId() != null){
            clone.setUpdatedTime(DateTime.now());
            clone.setUpdatedBy(StringUtil.isNotEmpty(dto.getUpdatedBy())?dto.getUpdatedBy():"admin");
            return promotionResourceDAO.updateById(clone);
        }
        clone.setTenantId(AppRuntimeEnv.getTenantId());
        clone.setAppId(AppRuntimeEnv.getAppId());
        clone.setCreatedTime(DateTime.now());
        clone.setCreatedBy(StringUtil.isNotEmpty(dto.getCreatedBy())?dto.getCreatedBy():"admin");
        clone.setUpdatedBy(clone.getCreatedBy());
        clone.setCode(CodeUtils.unRepeatSixCode());
        //普通券和房源券默认为线下核销
        if(PromotionResourceCouponCategoryEnum.ORDINARY_COUPON.getId().equals(clone.getCouponCategory())
                || PromotionResourceCouponCategoryEnum.REAL_ESTATE_COUPON.getId().equals(clone.getCouponCategory())){
            clone.setGrantWay(ResourceGrantWayEnum.OFFLINE_VERIFY.getId());
        }


        return promotionResourceDAO.save(clone);
    }

    private void checkPromotionResourceRequestDTO(PromotionResourceRequestDTO dto){
        if(Objects.isNull(dto)){
            throw new ApplicationException(CommonExceptionCode.INVALIDATION,"资源信息不能为空!");
        }

        //较验是否重名
        QueryWrapper<PromotionResourceDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PromotionResourceDO::getName,dto.getName());
        queryWrapper.lambda().eq(PromotionResourceDO::getIsDeleted, SuperEntity.DR_NORMAL);
        if(Objects.isNull(dto.getId())){
            int count = promotionResourceDAO.count(queryWrapper);
            if(count > 0)
                throw new ApplicationException(CommonExceptionCode.INVALIDATION,"已存在相同名称的资源!");
        }else{
            queryWrapper.lambda().ne(PromotionResourceDO::getId,dto.getId());
            int count = promotionResourceDAO.count(queryWrapper);
            if(count > 0)
                throw new ApplicationException(CommonExceptionCode.INVALIDATION,"已存在相同名称的资源!");
        }

        if(PromotionResourceTypeEnum.COUPON.getId().equals(dto.getType())){//卡券类型
            if(Objects.isNull(dto.getCouponCategory())){
                throw new ApplicationException(CommonExceptionCode.INVALIDATION,"请选择卡券细分类别!");
            }

            if(Objects.isNull(dto.getValidTimeType())){
                throw new ApplicationException(CommonExceptionCode.INVALIDATION,"请选择使用时间类型!");
            }

            if(PromotionResourceValidTimeTypeEnum.DESIGNATED_TIME.getId().equals(dto.getValidTimeType()) && Objects.isNull(dto.getValidStartTime()) && Objects.isNull(dto.getValidEndTime())){
                throw new ApplicationException(CommonExceptionCode.INVALIDATION,"指定时间不能为空!");
            }

            if(PromotionResourceValidTimeTypeEnum.EFFECTIVE_DAYS.getId().equals(dto.getValidTimeType()) && Objects.isNull(dto.getValidDay())){
                throw new ApplicationException(CommonExceptionCode.INVALIDATION,"有效天数不能为空!");
            }
            if(StringUtil.isEmpty(dto.getUseRule())){
                throw new ApplicationException(CommonExceptionCode.INVALIDATION,"使用规则不能为空!");
            }

            if(!PromotionResourceCouponCategoryEnum.COMMODITY_COUPON.getId().equals(dto.getCouponCategory())){//普通券、房源券
                if(PromotionResourceCouponCategoryEnum.ORDINARY_COUPON.getId().equals(dto.getCouponCategory())){//普通券
                    if(Objects.isNull(dto.getCouponType())){
                        throw new ApplicationException(CommonExceptionCode.INVALIDATION,"请选择卡券类型!");
                    }
                    if(PromotionResourceCouponTypeEnum.DISCOUNT_COUPON.getId().equals(dto.getCouponType()) && Objects.isNull(dto.getCouponValue())){
                        throw new ApplicationException(CommonExceptionCode.INVALIDATION,"券折扣不能为空!");
                    }
                    if(PromotionResourceCouponTypeEnum.DISCOUNT_COUPON.getId().equals(dto.getCouponType()) && dto.getCouponValue().compareTo(BigDecimal.ONE) < 0){
                        throw new ApplicationException(CommonExceptionCode.INVALIDATION,"折扣券面值必须大于等于1!");
                    }
                    if(PromotionResourceCouponTypeEnum.VOUCHER.getId().equals(dto.getCouponType()) && Objects.isNull(dto.getCouponValue())){
                        throw new ApplicationException(CommonExceptionCode.INVALIDATION,"卡券面值不能为空!");
                    }
                    if(PromotionResourceCouponTypeEnum.VOUCHER.getId().equals(dto.getCouponType()) && dto.getCouponValue().compareTo(BigDecimal.ZERO) <= 0){
                        throw new ApplicationException(CommonExceptionCode.INVALIDATION,"代金券面值必须大于0!");
                    }
                }else {//房源券->特价房
                    if(Objects.isNull(dto.getCouponValue())){
                        throw new ApplicationException(CommonExceptionCode.INVALIDATION,"券面值不能为空!");
                    }
                    if(dto.getCouponValue().compareTo(BigDecimal.ZERO) <= 0){
                        throw new ApplicationException(CommonExceptionCode.INVALIDATION,"券面值必须大于0!");
                    }
                }
            }
            if(PromotionResourceCouponCategoryEnum.COMMODITY_COUPON.getId().equals(dto.getCouponCategory()) && Objects.isNull(dto.getGrantWay())){
                throw new ApplicationException(CommonExceptionCode.INVALIDATION,"请选择礼品券发放方式!");
            }

            if(PromotionResourceCouponCategoryEnum.REAL_ESTATE_COUPON.getId().equals(dto.getCouponCategory())){//房源券
                if(StringUtil.isEmpty(dto.getHouseName())){
                    throw new ApplicationException(CommonExceptionCode.INVALIDATION,"房源名称不能为空!");
                }
                if(StringUtil.isEmpty(dto.getHouseVolume())){
                    throw new ApplicationException(CommonExceptionCode.INVALIDATION,"房源面积不能为空!");
                }
                if(StringUtil.isEmpty(dto.getHouseMessage())){
                    throw new ApplicationException(CommonExceptionCode.INVALIDATION,"房间信息不能为空!");
                }
                if(dto.getCostPriceType() == 0 && Objects.isNull(dto.getCostPrice())){
                    throw new ApplicationException(CommonExceptionCode.INVALIDATION,"原单价不能为空!");
                }
                if(dto.getDiscountPriceType() == 0 && Objects.isNull(dto.getDiscountPrice())){
                    throw new ApplicationException(CommonExceptionCode.INVALIDATION,"折后单价不能为空!");
                }
                if(dto.getCostPriceType() == 1 && Objects.isNull(dto.getCostPrice())){
                    throw new ApplicationException(CommonExceptionCode.INVALIDATION,"原总价不能为空!");
                }
                if(dto.getDiscountPriceType() == 1 && Objects.isNull(dto.getDiscountPrice())){
                    throw new ApplicationException(CommonExceptionCode.INVALIDATION,"折后总价不能为空!");
                }
            }

        }else if(PromotionResourceTypeEnum.THIRD_PARTY.getId().equals(dto.getType())){//第三方接入
            if(Objects.isNull(dto.getThirdCategory())){
                throw new ApplicationException(CommonExceptionCode.INVALIDATION,"请选择细分类别!");
            }
            if(Objects.isNull(dto.getCouponValue())){
                throw new ApplicationException(CommonExceptionCode.INVALIDATION,"面值不能为空!");
            }
            if(ThirdCategoryEnum.PHONE_FEE.getId().equals(dto.getThirdCategory()) && dto.getCouponValue().compareTo(BigDecimal.ZERO) <=0){
                throw new ApplicationException(CommonExceptionCode.INVALIDATION,"面值必须大于0!");
            }
            if(ThirdCategoryEnum.CASH_BAG.getId().equals(dto.getThirdCategory()) && dto.getCouponValue().compareTo(BigDecimal.ZERO) < 1){
                throw new ApplicationException(CommonExceptionCode.INVALIDATION,"面值必须大于等于1!");
            }
        }
    }

    @Override
    public boolean deleteById(Long id) {
        if(id == null){
            throw new ApplicationException(CommonExceptionCode.INVALIDATION,"资源id不能为空!");
        }
        //检查是否有关联活动了
        QueryWrapper<PromotionHisResourceDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PromotionHisResourceDO::getResourceId,id);
        int count = promotionHisResourceDAO.count(queryWrapper);
        if(count > 0){
            throw new ApplicationException(CommonExceptionCode.INVALIDATION,"已关联活动的资源不能删除!");
        }
        return promotionResourceDAO.removeById(id);
    }

    @Override
    public PromotionResourceResponseDTO detail(Long id) {
        PromotionResourceDO promotionResourceDO = promotionResourceDAO.getById(id);
        return Objects.nonNull(promotionResourceDO) ? promotionResourceDO.clone(PromotionResourceResponseDTO.class):null;
    }
}
