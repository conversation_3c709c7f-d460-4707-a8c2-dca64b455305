package com.deepexi.dxp.marketing.domain.marketing.dto;

import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import lombok.Data;

/**
 * 流程画布-条件节点—属性配置
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-07 16:03
 */
@Data
public class FlowCanvasConditionPropertyDTO extends SuperDTO {

    /**
     * 画布ID
     */
    private Long flowId;

    /**
     * 画布节点ID
     */
    private String nodeId;

    /**
     * 属性code
     */
    private String propertyCode;

    /**
     * 属性名称
     */
    private String propertyName;

    /**
     * 计算符号
     */
    private String symbols;

    /**
     * 比对值
     */
    private String value;

    /**
     * 比对值-结束
     */
    private String valueEnd;
}
