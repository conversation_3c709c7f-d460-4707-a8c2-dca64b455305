package com.deepexi.dxp.marketing.controller.specify.openapi;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityAnalysisQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityGroupQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityTopicRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.*;
import com.deepexi.dxp.marketing.enums.specify.ActivityGroupEnum;
import com.deepexi.dxp.marketing.service.specify.ActivityGroupService;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Objects;

/**
 * 活动专题模块controller
 *
 * <AUTHOR>
 * @date  2019/10/24 14:54
 * @see [相关类/方法]
 * @since 1.0
 */
@RestController
@RequestMapping("/open-api/v1/open/topic")
@Api(value = "移动端-活动专题",tags = {"移动端-活动专题"})
public class ActivityTopicOpenApiController {

    @Resource
    private ActivityGroupService activityGroupService;


    @PostMapping("/pageList")
    @ApiOperation(value = "活动专题列表", notes = "活动组列表")
    public Data<PageBean<ActivityGroupResponseDTO>> pageList(@RequestBody ActivityGroupQuery query) {
        query.setType(ActivityGroupEnum.TOPIC.getId());
        return new Data<>(activityGroupService.pageList(query));
    }


    @GetMapping("/detail")
    @ApiOperation(value = "活动专题详情", notes = "活动组详情")
    public Data<ActivityGroupResponseDTO> detail(@RequestParam Long id) {
        return new Data<>(activityGroupService.detail(id));
    }


    @PostMapping("/activityList")
    @ApiOperation(value = "活动专题详情-选择活动列表", notes = "选择活动列表")
    public Data<PageBean<ActivityGroupRelatedResponseDTO>> activityList(@RequestBody ActivityGroupQuery query) {
        return new Data<>(activityGroupService.activityList(query));
    }

}
