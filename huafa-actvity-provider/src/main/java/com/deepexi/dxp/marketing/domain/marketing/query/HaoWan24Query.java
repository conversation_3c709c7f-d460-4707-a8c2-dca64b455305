package com.deepexi.dxp.marketing.domain.marketing.query;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 24好玩Query
 * <AUTHOR>
 * @Date 2020/4/1
 */
@ApiModel
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HaoWan24Query {

    @ApiModelProperty("24好玩用户的appid")
    public String appid;

    @ApiModelProperty("页码")
    public Integer page;

    @ApiModelProperty("分页大小")
    public Integer page_size;

    @ApiModelProperty("签名")
    public String sign;

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

}
