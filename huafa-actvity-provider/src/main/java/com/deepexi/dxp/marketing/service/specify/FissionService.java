package com.deepexi.dxp.marketing.service.specify;

import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityFissionAssistResourceQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityFissionLogQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPartakeRequest;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.FissionReceiveNowDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.SwitchProjectDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityFissionAssistResourceResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityFissionLogResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.FissionHomeInfoResponseDTO;
import com.deepexi.util.pageHelper.PageBean;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface FissionService {
    /**
     * 裂变活动主页信息
     * @param query
     * @return
     */
    FissionHomeInfoResponseDTO homeInfo(ActivityPartakeRequest query);

    /**
     * 立即领取
     * @param dto
     * @return
     */
    Boolean receiveNow(FissionReceiveNowDTO dto);

    Boolean switchProject(SwitchProjectDTO dto);


    /**
     * 好友帮忙助力砍价记录
     * @param query
     * @return
     */
    PageBean<ActivityFissionLogResponseDTO> findFissionLogList(ActivityFissionLogQuery query);

    /**
     * 助力奖品轴，判断该用户是否已领取
     * @param activityId
     * @param partakeLogId
     * @return
     */
    List<ActivityFissionAssistResourceResponseDTO> getPrizeAxisStatus(Integer activityId,Integer partakeLogId);

    /**
     * 助力活动待领取奖品
     * @param query
     * @return
     */
    PageBean<ActivityFissionAssistResourceResponseDTO> findCertificatesList(ActivityFissionAssistResourceQuery query);
}
