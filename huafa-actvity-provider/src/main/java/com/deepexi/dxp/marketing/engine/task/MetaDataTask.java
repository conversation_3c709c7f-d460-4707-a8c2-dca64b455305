//package com.deepexi.dxp.marketing.engine.task;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.deepexi.common.extension.AppRuntimeEnv;
//import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
//import com.deepexi.dxp.marketing.domain.marketing.dto.HaoWanGameDTO;
//import com.deepexi.dxp.marketing.domain.marketing.dto.MarketingTaskUrl;
//import com.deepexi.dxp.marketing.domain.marketing.vo.PromotionActivityCouponVO;
//import com.deepexi.dxp.marketing.domain.promotion.dto.activity.ActivityRuleDTO;
//import com.deepexi.dxp.marketing.domain.marketing.query.PromotionActivityCouponQuery;
//import com.deepexi.dxp.marketing.domain.tags.dto.UserFromEsDTO;
//import com.deepexi.dxp.marketing.enums.activity.strategy.StrategyGroupEnum;
//import com.deepexi.dxp.marketing.enums.activity.strategy.condition.CouponConditionEnum;
//import com.deepexi.dxp.marketing.enums.contact.DMTemplateParamEnum;
//import com.deepexi.dxp.marketing.enums.contact.MessageTypeEnum;
//import com.deepexi.dxp.marketing.enums.marketing.*;
//import com.deepexi.dxp.marketing.manager.marketing.MetaDataParameters;
//import com.deepexi.dxp.middle.marketing.dao.MarketingActiveResourcesDAO;
//import com.deepexi.dxp.middle.marketing.domain.dto.*;
//import com.deepexi.dxp.middle.marketing.domain.entity.MarketingActiveResourcesDO;
//import com.deepexi.dxp.middle.marketing.domain.vo.MarketingWxTemplateVO;
//import com.deepexi.dxp.marketing.service.marketing.*;
//import com.deepexi.dxp.marketing.utils.SpringContextHolder;
//import com.deepexi.util.CollectionUtil;
//import com.deepexi.util.StringUtil;
//import com.deepexi.util.exception.ApplicationException;
//import com.deepexi.util.pojo.ObjectCloneUtils;
//import com.google.common.collect.Lists;
//import lombok.Getter;
//import lombok.extern.slf4j.Slf4j;
//
//import java.text.SimpleDateFormat;
//import java.util.*;
//
//@Slf4j
//public abstract class MetaDataTask {
//
//    /**
//     * 元数据封装对象
//     */
//    protected MetaDataParameters metaDataParameters;
//
//    /**
//     * 会员信息参数封装对象
//     */
//    private final MemberParameter memberParameter;
//    /**
//     * 由"tenantId_taskTypeId_taskId"格式组成的任务key，用于区分不同任务在MQ Topic里的tag
//     */
//    private String key;
//
//    /**
//     * 主动营销或自动营销DTO
//     */
//    protected Object marketingTaskDTO;
//
//    protected Long taskId;
//
//    private final MarketingTaskService taskService;
//
//    private MarketingActiveResourcesDAO marketingActiveResourcesDAO;
//
//    public MetaDataTask(MarketingTaskService taskService, MarketingActiveResourcesDAO marketingActiveResourcesDAO, Object marketingTaskDTO) {
//        setMarketingTaskDTO(marketingTaskDTO);
//        this.taskService = taskService;
//        this.marketingActiveResourcesDAO = marketingActiveResourcesDAO;
//        initMetaData();
//        initKey();
//        memberParameter = new MemberParameter();
//    }
//
//    public String getTenantId() {
//        if (StringUtil.isNotBlank(this.getKey())) {
//            return this.getKey().substring(0, this.getKey().indexOf("_"));
//        } else {
//            return ((SuperDTO) marketingTaskDTO).getTenantId();
//        }
//    }
//
//    /**
//     * 初始化MetaDataParameters对象，该对象里封装了营销任务资源、触达方式等信息
//     */
//    protected abstract void initMetaData();
//
//    /**
//     * 设置主动营销任务对象，用于后面从标签域查询该任务对应的所有会员信息
//     */
//    protected void setMarketingTaskDTO(Object marketingTaskDTO) {
//        this.marketingTaskDTO = marketingTaskDTO;
//    }
//
//    public Object getMarketingTaskDTO() {
//        return this.marketingTaskDTO;
//    }
//
//    private void initKey() {
//        if (metaDataParameters == null) {
//            throw new ApplicationException("请先初始化MetaDataParameters对象.");
//        }
//        this.key = metaDataParameters.getTenantId() + "_"
//                + metaDataParameters.getTaskType() + "_" + metaDataParameters.getTaskId();
//    }
//
//    public String getKey() {
//        return this.key;
//    }
//
//    /**
//     * 组装成可发送信息时的元数据对象
//     *
//     * @return
//     */
//    public List<TaskSendDataDTO> assembler(List<List<UserFromEsDTO>> members) {
//        AppRuntimeEnv.setTenantId(getTenantId());
//        List<TaskSendDataDTO> sendDataDTOList = Lists.newArrayList();
//        TaskSendDataDTO sendDataDto = null;
//        //找出触达规则的指定微信公众号appId
//        String wxAppId = null;
//        for (MetaDataParameters.TouchRuleContent touchRuleContent : metaDataParameters.getTouchRuleContentList()) {
//            if (MessageTypeEnum.WECHAT.getCode() == touchRuleContent.getSendRuleChannel()) {
//                MarketingWxTemplateService wxDmTemplateService = SpringContextHolder.getBean(MarketingWxTemplateService.class);
//                MarketingWxTemplateVO detail = wxDmTemplateService.detail(touchRuleContent.getSendTemplateId());
//                if (null != detail) {
//                    log.info("触达渠道指定微信：{}", detail);
//                    wxAppId = detail.getAuthorizerAppId();
//                }
//            }
//        }
//        // 循环组装会员信息
//        for (List<UserFromEsDTO> ms : members) {
//            for (UserFromEsDTO m : ms) {
//                sendDataDto = new TaskSendDataDTO();
//                sendDataDto.setTagName(getKey());
//                sendDataDto.setTenantId(getTenantId());
//                sendDataDto.setAppId(metaDataParameters.getAppId());
//                // 设置任务基本内容信息
//                sendDataDto.setTaskContent(createTaskContent(m));
//                // 设置会员基本信息
//                sendDataDto.setMemberContent(createMemberContent(m, wxAppId));
//                try {
//                    // 设置营销资源信息
//                    sendDataDto.setResourceContent(createResourceContent());
//                    // 设置触达方式信息
//                    sendDataDto.setTouchRuleContentList(setFirstTimeToucheRule());
//                    // 设置会员对应的模板、资源信息
//                    setSendTemplateContent(sendDataDto);
//                } catch (Exception e) {
//                    log.error("组装会员消息失败", e);
//                    continue;
//                }
//                sendDataDTOList.add(sendDataDto);
//            }
//        }
//        return sendDataDTOList;
//    }
//
//    /**
//     * 设置营销任务基本信息
//     */
//    private TaskSendDataDTO.TaskContent createTaskContent(UserFromEsDTO m) {
//        TaskSendDataDTO.TaskContent taskContent = new TaskSendDataDTO.TaskContent();
//
//
//        taskContent.setTaskId(metaDataParameters.getTaskId());
//        taskContent.setTaskType(metaDataParameters.getTaskType());
//        taskContent.setTaskMappingId(metaDataParameters.getTaskMappingId());
//        // 默认将主的触达策略做为第一次发送触达信息
//        Collections.sort(metaDataParameters.getTouchRuleContentList());
//        MetaDataParameters.TouchRuleContent touchRuleContent = metaDataParameters.getTouchRuleContentList().get(0);
//        int channel = touchRuleContent.getSendRuleChannel();
//        if (channel == TaskShortNameEnum.WX.getId()) {
//            taskContent.setTaskTypeShortName(TaskShortNameEnum.WX.getValue());
//        } else if (channel == TaskShortNameEnum.SMS.getId()) {
//            taskContent.setTaskTypeShortName(TaskShortNameEnum.SMS.getValue());
//        }
//        return taskContent;
//    }
//
//    /**
//     * 设置会员基本信息
//     */
//    private TaskSendDataDTO.MemberContent createMemberContent(UserFromEsDTO m, String appId) {
//        TaskSendDataDTO.MemberContent memberContent = new TaskSendDataDTO.MemberContent();
//        memberContent.setMemberId(m.getMemberId());
//        memberContent.setMemberName(m.getName());
//        memberContent.setPhone(m.getPhone());
//        if (CollectionUtil.isNotEmpty(m.getFanList()) && appId != null) {
//            List<UserFromEsDTO.Fan> fanList = m.getFanList();
//            for (UserFromEsDTO.Fan content : fanList) {
//                if (appId.equals(content.getMediaAppId())) {
//                    memberContent.setOpenId(content.getOpenId());
//                }
//            }
//        }
//        return memberContent;
//    }
//
//    /**
//     * 设置营销资源
//     */
//    private TaskSendDataDTO.ResourceContent createResourceContent() {
//        if (CollectionUtil.isEmpty(metaDataParameters.getResourceContent())) {
//            return null;
//        }
//        return metaDataParameters.getResourceContent().get(0).clone(TaskSendDataDTO.ResourceContent.class);
//    }
//
//    /**
//     * 设置触达规则，该方法暂时只适用于第一次启动任务时获取触达策略
//     *
//     * @return
//     */
//    private List<TaskSendDataDTO.TouchRuleContent> setFirstTimeToucheRule() {
//        List<TaskSendDataDTO.TouchRuleContent> ruleContentList = ObjectCloneUtils.convertList(
//                metaDataParameters.getTouchRuleContentList(), TaskSendDataDTO.TouchRuleContent.class);
//        Collections.sort(ruleContentList);
//        ruleContentList.get(0).setRuleExecuteStatus(true);
//        return ruleContentList;
//    }
//
//    /**
//     * 设置发送模板内容
//     *
//     * @param dataDto
//     */
//    public void setSendTemplateContent(TaskSendDataDTO dataDto) {
//        TaskSendDataDTO.SendTemplateContent sendTemplateContent = new TaskSendDataDTO.SendTemplateContent();
//        dataDto.setSendTemplateContent(sendTemplateContent);
//        Integer sendRuleChannel = null;
//        for (TaskSendDataDTO.TouchRuleContent e : dataDto.getTouchRuleContentList()) {
//            if (e.getRuleExecuteStatus()) {
//                sendRuleChannel = e.getSendRuleChannel();
//            }
//        }
//        dataDto.setRecord(getKey() + "_" + dataDto.getMemberContent().getMemberId() + "_" + sendRuleChannel);
//
//        if ("wx".equals(dataDto.getTaskContent().getTaskTypeShortName())) {
//            replaceWX(dataDto, memberParameter);
//        } else if ("sms".equals(dataDto.getTaskContent().getTaskTypeShortName())) {
//            replaceSMS(dataDto, memberParameter);
//        }
//    }
//
//
//    /**
//     * 封装短信发送参数
//     *
//     * @param dataDto
//     * @param memberParameter
//     * @return
//     */
//    TaskSendDataDTO replaceSMS(TaskSendDataDTO dataDto, MemberParameter memberParameter) {
//
//        //最终发送短信数据
//        SmsSendDTO smsSendMessageDTO = new SmsSendDTO();
//        smsSendMessageDTO.setTenantId(getTenantId());
//        smsSendMessageDTO.setOutId(SendVoucherNoEnum.SMS.getValue(), MarketingTaskTypeEnum.INITIATIVE.getValue(), dataDto.getTaskContent().getTaskId());
//        TaskSendDataDTO.SendTemplateContent.SmsData smsData = new TaskSendDataDTO.SendTemplateContent.SmsData();
//        dataDto.getSendTemplateContent().setSmsData(smsData);
//        smsData.setSmsSendDTO(smsSendMessageDTO);
//        smsSendMessageDTO.setAppId(memberParameter.getSmsAppId());
//        smsSendMessageDTO.setCaseId(memberParameter.getSmsAppId());
//        //组装参数
//        Map<String, String> resourceMap = setResourceMaps(dataDto);
//
//        MarketingSmsTemplateDTO dmTemplate = memberParameter.getMessageSMSDetail();
//        if (dmTemplate.getContentParam() != null) {
//            List<MarketingTemplateDataDTO> dmTemplateDataList = JSONArray.parseArray(
//                    dmTemplate.getContentParam(), MarketingTemplateDataDTO.class);
//
//            //最终发送短信map数据
//            Map<String, String> sendMap = new HashMap<>();
//
//            //设置对应值进短信模板内容中包含有的参数
//            for (MarketingTemplateDataDTO dmTemplateData : dmTemplateDataList) {
//                if (dmTemplateData.getType().equals(DMTemplateParamEnum.PARAM.getId()) && resourceMap.get(dmTemplateData.getValue()) != null) {
//                    sendMap.put(dmTemplateData.getName(), resourceMap.get(dmTemplateData.getValue()));
//                } else if (dmTemplateData.getType().equals(DMTemplateParamEnum.TEXT.getId())) {
//                    sendMap.put(dmTemplateData.getName(), dmTemplateData.getValue());
//                }
//            }
//
//            //设置发送参数
//            smsSendMessageDTO.setParamMap(sendMap);
//        }
//
//        //获取事件code 传入发送模板id
//        smsSendMessageDTO.setEventCode(memberParameter.getEventCode());
//        //生成一个不重复的数uuid
//        smsSendMessageDTO.setRequestNo(UUID.randomUUID().toString().replaceAll("-", ""));
//        //立即发送
//        smsSendMessageDTO.setPushType(1);
//
//        //设置发送手机号
//        List<SmsPhoneDTO> SmsPhoneDTOList = new ArrayList<>(16);
//        SmsPhoneDTO SMSPhoneDTO = new SmsPhoneDTO();
//        SMSPhoneDTO.setPhone(dataDto.getMemberContent().getPhone());
//        SmsPhoneDTOList.add(SMSPhoneDTO);
//        smsSendMessageDTO.setReceiverPhones(SmsPhoneDTOList);
//
//        return dataDto;
//    }
//
//    /**
//     * 封装微信发送参数
//     *
//     * @param dataDto
//     * @return
//     */
//    TaskSendDataDTO replaceWX(TaskSendDataDTO dataDto, MemberParameter memberParameter) {
//
//        WxTemplateSendDTO messageSendDTO = new WxTemplateSendDTO();
//        TaskSendDataDTO.SendTemplateContent.WxData wxData = new TaskSendDataDTO.SendTemplateContent.WxData();
//        dataDto.getSendTemplateContent().setWxData(wxData);
//        wxData.setWxSendDto(messageSendDTO);
//
//        WxTemplateSendDTO wxSendDto = dataDto.getSendTemplateContent().getWxData().getWxSendDto();
//        List<MarketingTemplateDataDTO> dmTemplateDataList = JSONArray.parseArray(
//                memberParameter.getWxDmDetail().getContentParam(), MarketingTemplateDataDTO.class);
//
//        //url
//        MarketingTaskUrl marketingTaskUrl = memberParameter.marketingTaskUrl;
//        if (memberParameter.getPromotionActivityCoupon() != null) {
//            Long resourceId = memberParameter.getPromotionActivityCoupon().getActivityId();
//            Integer promotionType = memberParameter.getPromotionActivityCoupon().getPaTemplateId();
//            if (Integer.valueOf(StrategyGroupEnum.LQHD_G.getId()).equals(memberParameter.getPromotionActivityCoupon().getPaTemplateId())) {
//                wxSendDto.setUrl(marketingTaskUrl.getUrl(), resourceId, promotionType);
//            } else {
//                wxSendDto.setUrl(marketingTaskUrl.getUrl(), resourceId, promotionType);
//            }
//        } else if (memberParameter.getHaoWanGameDTO() != null) {
//            HaoWanGameDTO haoWanGameDTO = memberParameter.getHaoWanGameDTO();
//            wxSendDto.setUrl(haoWanGameDTO.getGame_url());
//        } else {
//            wxSendDto.setUrl(marketingTaskUrl.getUrl());
//        }
//        //openid
//        wxSendDto.setTouser(dataDto.getMemberContent().getOpenId());
//        wxSendDto.setTemplate_id(memberParameter.getWxDmDetail().getTemplateId());
//        wxSendDto.setWxAppId(memberParameter.getWxDmDetail().getAuthorizerAppId());
//
//        List<WxTemplateSendDataDTO> templateDateList = new ArrayList<>();
//
//        Map<String, String> resourceMap = setResourceMaps(dataDto);
//
//        for (MarketingTemplateDataDTO dmTemplateData : dmTemplateDataList) {
//            WxTemplateSendDataDTO templateDate = new WxTemplateSendDataDTO();
//            templateDate.setName(dmTemplateData.getName());
//            if (dmTemplateData.getType().equals(DMTemplateParamEnum.PARAM.getId()) && resourceMap.get(dmTemplateData.getValue()) != null) {
//                templateDate.setValue(resourceMap.get(dmTemplateData.getValue()));
//            } else if (dmTemplateData.getType().equals(DMTemplateParamEnum.TEXT.getId())) {
//                templateDate.setValue(dmTemplateData.getValue());
//            }
//
//            templateDateList.add(templateDate);
//        }
//
//        wxSendDto.setData(templateDateList);
//        log.info("memberParameter:{},wxSendDto:{}", JSON.toJSONString(memberParameter), wxSendDto);
//        return dataDto;
//    }
//
//    private Map<String, String> setResourceMaps(TaskSendDataDTO dataDto) {
//        //封装进短信发送体
//        //来源数据
//        Map<String, String> resourceMap = new HashMap<>(16);
//
//        //会员名称
//        String userName = dataDto.getMemberContent().getMemberName() != null ? dataDto.getMemberContent().getMemberName() : dataDto.getMemberContent().getPhone();
//        resourceMap.put(MessageParamEnum.USER_NAME.getName(), userName);
//
//        if (memberParameter.getPromotionActivityCoupon() != null) {
//            PromotionActivityCouponVO promotionActivityCoupon = memberParameter.getPromotionActivityCoupon();
//            //活动名称
//            resourceMap.put(MessageParamEnum.ACTIVITY_NAME.getName(), promotionActivityCoupon.getActivityName());
//            //优惠券名称
//            resourceMap.put(MessageParamEnum.COUPON_NAME.getName(), promotionActivityCoupon.getCouponName());
//            //金额后缀
//            String amountSuffix = CouponTypeSuffixEnum.getValueById(promotionActivityCoupon.getCouponType());
//            //优惠券类型名称
//            String couponTypeName = CouponTypeEnum.getValueById(promotionActivityCoupon.getCouponType());
//
//            //优惠券类型名称
//            resourceMap.put(MessageParamEnum.COUPON_TYPE.getName(), couponTypeName);
//            //面值
//            resourceMap.put(MessageParamEnum.AMOUNT.getName(), promotionActivityCoupon.getCouponValue() + amountSuffix);
//            //领券条件
//            String couponCondition = promotionActivityCoupon.getActivityRuleDTOList().get(0).getSort();
//            resourceMap.put(MessageParamEnum.COUPON_CONDITION.getName(), CouponConditionEnum.getValueById(couponCondition));
//            //活动时间
//            String activityTime = null;
//            if (promotionActivityCoupon.getStartTime() != null && promotionActivityCoupon.getEndTime() != null) {
//                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                String startTime = formatter.format(promotionActivityCoupon.getStartTime());
//                String endTime = formatter.format(promotionActivityCoupon.getEndTime());
//                activityTime = startTime + "-" + endTime;
//            }
//            resourceMap.put(MessageParamEnum.ACTIVITY_TIME.getName(), activityTime);
//
//            //活动规则
//            List<ActivityRuleDTO> activityRuleDTOList = promotionActivityCoupon.getActivityRuleDTOList();
//            //满减活动
//            String activityRule = "";
//            if (promotionActivityCoupon.getPaTemplateId().equals(Integer.valueOf(StrategyGroupEnum.MJS_G.getId()))) {
//                for (ActivityRuleDTO activityRuleDTO : activityRuleDTOList) {
//                    if ("money".equals(activityRuleDTO.getStrategyType())) {
//                        activityRule = activityRule + "满" + activityRuleDTO.getCondition().get(0).getValue() + "减" + activityRuleDTO.getOperation().get(0).getValue() + "；";
//                    }
//                }
//            }
//            //打折活动
//            if (promotionActivityCoupon.getPaTemplateId().equals(Integer.valueOf(StrategyGroupEnum.DZCX_G.getId()))) {
//                for (ActivityRuleDTO activityRuleDTO : activityRuleDTOList) {
//                    if ("number".equals(activityRuleDTO.getStrategyType())) {
//                        activityRule = activityRule + "满" + activityRuleDTO.getCondition().get(0).getValue() + "件打" + activityRuleDTO.getOperation().get(0).getValue() + "折；";
//                    } else if ("money".equals(activityRuleDTO.getStrategyType())) {
//                        activityRule = activityRule + "满" + activityRuleDTO.getCondition().get(0).getValue() + "打" + activityRuleDTO.getOperation().get(0).getValue() + "折；";
//                    }
//                }
//            }
//            resourceMap.put(MessageParamEnum.ACTIVITY_RULE.getName(), activityRule);
//        } else if (memberParameter.getHaoWanGameDTO() != null) {
//            HaoWanGameDTO haoWanGameDTO = memberParameter.getHaoWanGameDTO();
//            //活动名称
//            resourceMap.put(MessageParamEnum.ACTIVITY_NAME.getName(), haoWanGameDTO.getName());
//            //活动时间
//            String activityTime = null;
//            if (haoWanGameDTO.getStart_time() != null && haoWanGameDTO.getEnd_time() != null) {
//                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                String startTime = formatter.format(haoWanGameDTO.getStart_time());
//                String endTime = formatter.format(haoWanGameDTO.getStart_time());
//                activityTime = startTime + "-" + endTime;
//            }
//            resourceMap.put(MessageParamEnum.ACTIVITY_TIME.getName(), activityTime);
//        }
//
//        return resourceMap;
//    }
//
//    public MarketingTaskService getTaskService() {
//        return taskService;
//    }
//
//    public MetaDataParameters getMetaDataParameters() {
//        return metaDataParameters;
//    }
//
//
//    /**
//     * 会员组装时与数据库操作频繁的相关参数
//     */
//    @Getter
//    protected class MemberParameter {
//
//        // 事件编码:短信发送时参数
//        private String eventCode;
//
//        //sms appid
//        private String smsAppId;
//
//        // 短信内容详情
//        private MarketingSmsTemplateDTO messageSMSDetail;
//
//        // 微信模板详情
//        private MarketingWxTemplateVO wxDmDetail;
//
//        // 领券活动资源
//        private PromotionActivityCouponVO promotionActivityCoupon;
//
//        //24好玩资源
//        private HaoWanGameDTO haoWanGameDTO;
//
//        //跳转地址
//        private MarketingTaskUrl marketingTaskUrl;
//
//        public MemberParameter() {
//            init();
//        }
//
//        private void init() {
//
//            MarketingPromotionActivityService marketingPromotionActivityService = SpringContextHolder
//                    .getBean(MarketingPromotionActivityService.class);
//            HaoWanService haoWanService = SpringContextHolder
//                    .getBean(HaoWanService.class);
//            MarketingWxTemplateService wxDmTemplateService = SpringContextHolder.getBean(
//                    MarketingWxTemplateService.class);
//            SmsService messageSmsService = SpringContextHolder.getBean(SmsService.class);
////            MarketingSmsTemplateService smsDmService = SpringContextHolder.getBean(MarketingSmsTemplateService.class);
//
//            this.marketingTaskUrl = SpringContextHolder.getBean(MarketingTaskUrl.class);
//            MarketingActiveResourcesDO marketingActiveResourcesDO = new MarketingActiveResourcesDO();
//            marketingActiveResourcesDO.setAppId(AppRuntimeEnv.getAppId());
//            marketingActiveResourcesDO.setActiveId(metaDataParameters.getTaskId());
//            AppRuntimeEnv.setTenantId(metaDataParameters.getTenantId());
//            List<MarketingActiveResourcesDO> marketingActiveResourcesDOS = marketingActiveResourcesDAO.queryByTaskActive(marketingActiveResourcesDO);
//            if (CollectionUtil.isNotEmpty(marketingActiveResourcesDOS)) {
//                MarketingActiveResourcesDO resourcesDO = marketingActiveResourcesDOS.get(0);
//                if (resourcesDO == null || StringUtil.isBlank(resourcesDO.getResourceDetailURL())) {
//                    return;
//                }
//                marketingTaskUrl.setUrl(resourcesDO.getResourceDetailURL());
//            } else {
//                marketingTaskUrl.setUrl("");
//            }
//
//            smsAppId = SpringContextHolder.getEnvironmentProperty("deepexi.spaas.sdk.smsAppId");
//
//            for (MetaDataParameters.TouchRuleContent touchRuleContent : metaDataParameters.getTouchRuleContentList()) {
//                long templateId = Long.valueOf(String.valueOf(touchRuleContent.getSendTemplateId()));
//                int channel = touchRuleContent.getSendRuleChannel();
//
//                if (MessageTypeEnum.WECHAT.getCode() == channel) {
//                    // 获取微信模板详情
//                    this.wxDmDetail = wxDmTemplateService.detail(templateId);
//                    if (this.wxDmDetail == null) {
//                        throw new ApplicationException("营销任务【任务ID：{0}】发送引擎引用的微信模板不存在【模板ID:{1}】"
//                                , metaDataParameters.getTaskType() + "_" + metaDataParameters.getTaskId()
//                                , templateId);
//                    }
//                    //marketingTaskUrl.setWxAppId(this.wxDmDetail.getAuthorizerAppId());
//                } else if (MessageTypeEnum.SMS.getCode() == channel) {
//
//                    String tenantId = getTenantId();
//                    // 获取短信模板详情
//                    log.info("营销任务【任务ID:{}】发送,tenantId:{},templateId:{}"
//                            , metaDataParameters.getTaskId()
//                            , tenantId
//                            , templateId);
//                    this.eventCode = messageSmsService.getEventCode(templateId, tenantId);
//                    if (StringUtil.isEmpty(this.eventCode)) {
//                        throw new ApplicationException("营销任务【任务ID：{0}】发送引擎引用的短信模板EventCode不存在【模板ID:{1}】"
//                                , metaDataParameters.getTaskType() + "_" + metaDataParameters.getTaskId()
//                                , templateId);
//                    }
//
//                    Long smsIdByEventId = messageSmsService.getSMSIdByEventId(templateId, tenantId);
//                    if (null == smsIdByEventId) {
//                        throw new ApplicationException("营销任务【任务ID：{0}】发送引擎引用的短信模板主id不存在【模板ID:{1}】"
//                                , metaDataParameters.getTaskId()
//                                , templateId);
//                    }
////                    this.messageSMSDetail = smsDmService.findDMContentById((long) templateId);
//                    if (this.messageSMSDetail == null) {
//                        throw new ApplicationException("营销任务【任务ID：{0}】发送引擎引用的短信模板不存在【模板ID:{1}】"
//                                , metaDataParameters.getTaskType() + "_" + metaDataParameters.getTaskId()
//                                , templateId);
//                    }
//                }
//            }
//
//            // 获取消息所发送的资源
//            if (CollectionUtil.isNotEmpty(metaDataParameters.getResourceContent())) {
//                MetaDataParameters.ResourceContent resourceContent = metaDataParameters.getResourceContent().get(0);
//                Long resourceId = resourceContent.getResourceId();
//                //DR活动
//                if (ResourceTypeEnum.DR1.getId().equals(resourceContent.getResourceTypeCode())) {
//                    PromotionActivityCouponQuery query = new PromotionActivityCouponQuery();
//                    query.setActivityId(resourceId);//此处传资源id(resourceId)
//                    //获取领券活动和优惠券信息
//                    this.promotionActivityCoupon = marketingPromotionActivityService.getPromotionActivityCoupon(query);
//                    if (this.promotionActivityCoupon == null) {
//                        String rid = String.valueOf(resourceId);
//                        String aid = metaDataParameters.getTaskType() + "_" + metaDataParameters.getTaskId();
//                        throw new ApplicationException("营销任务【任务ID：{0}】发送引擎配置了资源,但是资源对应的活动或者券不存在【资源ID:{1}】"
//                                , aid, rid);
//                    }
//                }
//                //24好玩
//                if (ResourceTypeEnum.HAO_WAN.getId().equals(resourceContent.getResourceTypeCode())) {
//                    String tenantId = getTenantId();
//                    AppRuntimeEnv.setTenantId(tenantId);
//                    AppRuntimeEnv.setAppId(metaDataParameters.getAppId());
//                    String haoWanResourceId = resourceContent.getResourceId().toString();
//                    this.haoWanGameDTO = haoWanService.getByGameId(haoWanResourceId);
//                    if (Objects.isNull(haoWanGameDTO)) {
//                        String aid = metaDataParameters.getTaskType() + "_" + metaDataParameters.getTaskId();
//                        throw new ApplicationException("营销任务【任务ID：{0}】发送引擎配置了资源,但是资源对应的24好玩活动不存在【资源ID:{1}】"
//                                , aid, haoWanResourceId);
//                    }
//                }
//            }
//        }
//
//
//    }
//}
