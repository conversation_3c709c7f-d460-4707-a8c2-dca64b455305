package com.deepexi.dxp.marketing.domain.marketing.request;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author：liuyang
 * @version：1.0.0
 * @date：2021-03-31 17:21
 */
@Data
@ApiModel
public class MarketingKpiGroupQueryKpiByTypeRequest extends AbstractObject implements Serializable {

    @ApiModelProperty(value = "活动目标类型(1:营收,2:商品,3:用户)", required = false)
    private Integer type;

    @ApiModelProperty(value = "指标组状态(1:启用,2:禁用)", required = false)
    private Integer status;
}
