package com.deepexi.dxp.marketing.domain.marketing.request;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 *
 *
 * @ClassName: TenantInitRequest
 * @Description:
 * @Author: z<PERSON>jian
 * @Date: 2020/12/15
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class TenantInitRequest extends AbstractObject implements Serializable {

    @ApiModelProperty(value = "租户id", required = true)
    @NotBlank(message = "租户信息不能为空")
    private String tenantId;

    /**
     * 接入方id
     */
    @ApiModelProperty("接入方id")
    @NotNull(message = "接入方id不能为空")
    private Long appId;

}
