package com.deepexi.dxp.marketing.converter;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSON;
import com.deepexi.common.extension.AppRuntimeEnv;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.*;
import com.deepexi.dxp.marketing.enums.resource.ActivityStatusEnum;
import com.deepexi.dxp.marketing.enums.resource.BottomBtnTypeEnum;
import com.deepexi.dxp.marketing.enums.resource.ShowRanklistTypeEnum;
import com.deepexi.dxp.marketing.enums.specify.*;
import com.deepexi.dxp.marketing.utils.DateTimeUtils;
import com.deepexi.dxp.marketing.utils.MapBeanUtil;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityLimitDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.*;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.DateUtils;
import com.deepexi.util.StringUtil;
import com.deepexi.util.exception.ApplicationException;
import com.google.common.collect.Lists;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 优惠券信息转换
 */
public class CouponConverter {

    /**
     * 保存活动基本信息转换
     * @param requestDTO
     * @return
     */
    public  static PromotionActivityDO converter(CouponRequestDTO requestDTO){
        PromotionActivityDO activityDO = new PromotionActivityDO();
        activityDO.setStatus(Integer.parseInt(ActivityStatusEnum.UN_START.getId()));
        activityDO.setName(requestDTO.getName());
        activityDO.setAppId(AppRuntimeEnv.getAppId());
        activityDO.setCreatedBy(requestDTO.getCreatedBy());
        activityDO.setTenantId(AppRuntimeEnv.getTenantId());
        activityDO.setCreatedPerson(requestDTO.getCreatedBy());
        activityDO.setPaTemplateId(requestDTO.getPaTemplateId());
        activityDO.setExt(MapBeanUtil.object2Map(requestDTO.getActivityExtVO()));
        Date endTime = DateUtils.getDate(requestDTO.getEndTime());
        activityDO.setEndTime(DateTimeUtils.getEndOfDay(endTime));
        Date startTime = DateUtils.getDate(requestDTO.getStartTime());
        activityDO.setStartTime(DateTimeUtils.getStartOfDay(startTime));
        return activityDO;
    }

    /**
     * 项目参与活动中间表
     * @param requestDTO
     * @return
     */
    public  static List<ActivityParticipationDO> participationConverter(CouponRequestDTO requestDTO, Long activityId) {
        List<ActivityParticipationDO> activityParticipationList = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(requestDTO.getProjectIds())){
            requestDTO.getProjectIds().forEach(project->{
                ActivityParticipationDO activityParticipationDO = new ActivityParticipationDO();
                activityParticipationDO.setAppId(AppRuntimeEnv.getAppId());
                activityParticipationDO.setTenantId(AppRuntimeEnv.getTenantId());
                activityParticipationDO.setCreatedBy(requestDTO.getCreatedBy());
                activityParticipationDO.setUpdatedBy(requestDTO.getUpdatedBy());
                activityParticipationDO.setActivityId(activityId);
                activityParticipationDO.setProjectId(project.getProjectId());
                activityParticipationDO.setAreaId(project.getAreaId());
                activityParticipationDO.setAreaName(project.getAreaName());
                activityParticipationDO.setCityId(project.getCityId());
                activityParticipationDO.setCityName(project.getCityName());
                activityParticipationDO.setOrgId(project.getOrgId());
                activityParticipationDO.setOrgName(project.getOrgName());
                activityParticipationDO.setPorjectPeriodList(JSON.toJSONString(project.getPorjectPeriodList()));
                activityParticipationDO.setPositionId(project.getPositionId());
                activityParticipationDO.setProjectName(project.getProjectName());
                activityParticipationDO.setProjectNumber(project.getProjectNumber());
                activityParticipationDO.setSaleOrgId(project.getSaleOrgId());
                activityParticipationDO.setSaleOrgName(project.getSaleOrgName());
                activityParticipationDO.setUserAlias(project.getUserAlias());
                activityParticipationDO.setUserName(project.getUserName());
                activityParticipationDO.setRealCityId(project.getRealCityId());
                activityParticipationDO.setRealCityName(project.getRealCityName());
                activityParticipationDO.setRealAreaId(project.getRealAreaId());
                activityParticipationDO.setRealAreaName(project.getRealAreaName());
                activityParticipationList.add(activityParticipationDO);
            });
        }
        return activityParticipationList;
    }

    /**
     * 活动规则信息转换
     * @param requestDTO
     * @return
     */
    public  static PromotionActivityLimitDO limitConverter(CouponRequestDTO requestDTO) {
        PromotionActivityLimitDO promotionActivityLimitDO = new PromotionActivityLimitDO();
        promotionActivityLimitDO.setAppId(AppRuntimeEnv.getAppId());
        promotionActivityLimitDO.setTenantId(AppRuntimeEnv.getTenantId());
        promotionActivityLimitDO.setCreatedBy(requestDTO.getCreatedBy());
        promotionActivityLimitDO.setExt(MapBeanUtil.object2Map(requestDTO.getRuleConfigVO()));
        return promotionActivityLimitDO;
    }

    /**
     * 活动页信息转换
     * @param requestDTO
     * @return
     */
    public  static ActivityPageDO activityPageConverter(CouponRequestDTO requestDTO) {
        ActivityPageDO activityPageDO = new ActivityPageDO();
        activityPageDO.setAppId(AppRuntimeEnv.getAppId());
        activityPageDO.setTenantId(AppRuntimeEnv.getTenantId());
        activityPageDO.setCreatedBy(requestDTO.getCreatedBy());
        activityPageDO.setUpdatedBy(requestDTO.getUpdatedBy());
        ActivityPageVO activityPageVO = requestDTO.getActivityPageVO();
        if (Objects.nonNull(activityPageVO)){
            activityPageDO.setActivityIconUrl(activityPageVO.getActivityIconUrl());
            activityPageDO.setActivityDiagram(activityPageVO.getActivityDiagram());
            activityPageDO.setActivityRulesUrl(activityPageVO.getActivityRulesUrl());
            List<Integer> bottomBtnTypes = activityPageVO.getBottomBtnTypes();
            String collect = bottomBtnTypes.stream().map(String::valueOf).collect(Collectors.joining(","));
            activityPageDO.setBottomBtnType(collect);
            if (collect.contains(BottomBtnTypeEnum.CONTACT_NUMBER.getId().toString()) && StringUtil.isBlank(activityPageVO.getPhone())){
                throw new ApplicationException("底部按钮联系电话不能为空");
            }
            if (collect.contains(BottomBtnTypeEnum.CONTACT_ONLINE.getId().toString()) && StringUtil.isBlank((String)activityPageVO.getExt().get("btn6Url"))){
                throw new ApplicationException("底部按钮在线咨询图片不能为空");
            }
            activityPageDO.setPhone(activityPageVO.getPhone());
            activityPageDO.setType(1);
            activityPageDO.setIsShowRanklist(ShowRanklistTypeEnum.SHUT_DOWN.getId());
        }
        return activityPageDO;
    }

    /**
     * 分享也信息转换
     * @param requestDTO
     * @return
     */
    public static ActivityPageShareDO activityShareConverter(CouponRequestDTO requestDTO) {
        ActivityPageShareDO activityPageShareDO = new ActivityPageShareDO();
        activityPageShareDO.setAppId(AppRuntimeEnv.getAppId());
        activityPageShareDO.setTenantId(AppRuntimeEnv.getTenantId());
        activityPageShareDO.setCreatedBy(requestDTO.getCreatedBy());
        activityPageShareDO.setUpdatedBy(requestDTO.getUpdatedBy());
        ActivityPageShareVO activityPageShareVO = requestDTO.getActivityPageShareVO();
        if (Objects.nonNull(activityPageShareVO)){
            activityPageShareDO.setShareTitle(activityPageShareVO.getShareTitle());
            activityPageShareDO.setShareContent(activityPageShareVO.getShareContent());
            activityPageShareDO.setShareIconUrl(activityPageShareVO.getShareIconUrl());
            activityPageShareDO.setPosterTitle(activityPageShareVO.getPosterTitle());
            activityPageShareDO.setSharePosterUrl(activityPageShareVO.getSharePosterUrl());
            activityPageShareDO.setType(1);
        }
        return activityPageShareDO;
    }

    public static ActivityPartakeLogDTO partakeLogConverter(String resourceCode,ReceiveCouponRequestDTO dto, PromotionHisResourceDO promotionHisResourceDO) {
        ActivityPartakeLogDTO activityPartakeLog = new ActivityPartakeLogDTO();
        activityPartakeLog.setAppId(AppRuntimeEnv.getAppId());
        activityPartakeLog.setTenantId(AppRuntimeEnv.getTenantId());
        activityPartakeLog.setActivityId(promotionHisResourceDO.getActivityId());
        activityPartakeLog.setUserId(dto.getUserId());
        activityPartakeLog.setUserName(dto.getUserName());
        activityPartakeLog.setPhone(dto.getPhone());
        activityPartakeLog.setNickName(dto.getNickName());
        activityPartakeLog.setPrizeResult(promotionHisResourceDO.getName());
        activityPartakeLog.setResourceId(promotionHisResourceDO.getId());
        activityPartakeLog.setType(dto.getType());
        activityPartakeLog.setCreatedTime(DateTime.now());
        activityPartakeLog.setCreatedBy(dto.getUserName());
        activityPartakeLog.setUpdatedBy(dto.getUserName());
        activityPartakeLog.setUnionId(dto.getUnionId());
        activityPartakeLog.setGetTime(DateUtils.format(DateTime.now(),DateUtils.DEFAULT_DATE_TIME_FORMAT));
        activityPartakeLog.setCode(resourceCode);
        return activityPartakeLog;
    }

    public static ActivityVerifyDO verifyConverter(String resourceCode,ReceiveCouponRequestDTO dto, PromotionHisResourceDO promotionHisResourceDO,Integer verifyType,Integer isPay) {
        ActivityVerifyDO activityVerifyDO = new ActivityVerifyDO();
        activityVerifyDO.setAppId(AppRuntimeEnv.getAppId());
        activityVerifyDO.setTenantId(AppRuntimeEnv.getTenantId());
        activityVerifyDO.setResourceId(promotionHisResourceDO.getId());
        activityVerifyDO.setVerifyType(verifyType);
        activityVerifyDO.setUserId(dto.getUserId());
        activityVerifyDO.setNickName(dto.getNickName());
        activityVerifyDO.setUserName(dto.getUserName());
        activityVerifyDO.setPhone(dto.getPhone());
        activityVerifyDO.setName(promotionHisResourceDO.getName());
        activityVerifyDO.setProjectId(dto.getProjectId());
        activityVerifyDO.setVerifyStatus(VerifyStatusEnum.NO_VERIFY.getId());
        activityVerifyDO.setUnionId(dto.getUnionId());
        activityVerifyDO.setType(dto.getType());
        activityVerifyDO.setCreatedTime(new Date());
        activityVerifyDO.setCreatedBy(dto.getUserName());//领取人
        activityVerifyDO.setUpdatedBy(dto.getUserName());
        activityVerifyDO.setIsPay(isPay);
        activityVerifyDO.setValidTimeType(promotionHisResourceDO.getValidTimeType());
        activityVerifyDO.setValidStartTime(promotionHisResourceDO.getValidStartTime());
        activityVerifyDO.setValidEndTime(promotionHisResourceDO.getValidEndTime());
        activityVerifyDO.setPayTime(new Date());
        activityVerifyDO.setCode(resourceCode);
        activityVerifyDO.setRefundStatus(VerifyRefundStatusEnum.NO_REFUND.getId());
        activityVerifyDO.setOrderId(0L);
        activityVerifyDO.setActivityId(dto.getActivityId());
        activityVerifyDO.setProjectName(dto.getProjectName());

        if(PromotionResourceTypeEnum.COUPON.getId().equals(promotionHisResourceDO.getType()) && Objects.nonNull(promotionHisResourceDO.getCouponCategory())){
            if(
                    PromotionResourceCouponCategoryEnum.REAL_ESTATE_COUPON.getId().equals(promotionHisResourceDO.getCouponCategory()) ||
                            PromotionResourceCouponCategoryEnum.ORDINARY_COUPON.getId().equals(promotionHisResourceDO.getCouponCategory())
            ){
                activityVerifyDO.setVerifyType(VerifyTypeEnum.COUPON.getId());

            } else if(PromotionResourceCouponCategoryEnum.COMMODITY_COUPON.getId().equals(promotionHisResourceDO.getCouponCategory())){
                activityVerifyDO.setVerifyType(VerifyTypeEnum.GIFT.getId());
            }
        } else if(PromotionResourceTypeEnum.THIRD_PARTY.getId().equals(promotionHisResourceDO.getType())){
            activityVerifyDO.setVerifyType(VerifyTypeEnum.THIRD.getId());
        }
        return activityVerifyDO;
    }
}
