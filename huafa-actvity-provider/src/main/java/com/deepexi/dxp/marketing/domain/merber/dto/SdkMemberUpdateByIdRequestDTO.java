package com.deepexi.dxp.marketing.domain.merber.dto;

import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @Class: SdkMemberUpdateByIdRequestDTO
 * @Description:
 * @Author: lizhongbao
 * @Date: 2020/7/28
 **/
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class SdkMemberUpdateByIdRequestDTO extends SuperDTO {
    @ApiModelProperty("businessId")
    private Long businessId;
    @ApiModelProperty("code")
    private String code;
    @ApiModelProperty("email")
    private String email;
    @ApiModelProperty("inviterId")
    private Long inviterId;
    @ApiModelProperty("origin")
    private String origin;
    @ApiModelProperty("phone")
    private String phone;
    @ApiModelProperty("registerAddress")
    private String registerAddress;
    @ApiModelProperty("registerAt")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date registerAt;
    @ApiModelProperty("registerStore")
    private String registerStore;
    @ApiModelProperty("status")
    private Integer status;
    @ApiModelProperty("storeId")
    private Long storeId;
    @ApiModelProperty("type")
    private Integer type;
    @ApiModelProperty("username")
    private String username;
}
