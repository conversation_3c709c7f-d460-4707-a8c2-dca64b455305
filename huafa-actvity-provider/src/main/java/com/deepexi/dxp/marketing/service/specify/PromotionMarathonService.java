package com.deepexi.dxp.marketing.service.specify;

import cn.hutool.json.JSONObject;
import com.deepexi.dxp.marketing.domain.marketing.response.PromotionActivityDetailDTO;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/8/25
 */
public interface PromotionMarathonService {

    void cleanData(Long activityId, String phone);

    /**
     * 分配参赛码
     *
     * @param activityId 活动id
     * @param phone      用户手机号
     * @return 参赛码
     */
    String allocationMarathonCode(Long activityId, String phone, Date endTime);

    Integer checkStatus(String phone);

    boolean checkFinish(String code);

    PromotionActivityDetailDTO ready(Long activityId);

    void refreshCache(Long activityId);

    JSONObject checkCode(String code);

    Integer finish(String code,Integer status);
}
