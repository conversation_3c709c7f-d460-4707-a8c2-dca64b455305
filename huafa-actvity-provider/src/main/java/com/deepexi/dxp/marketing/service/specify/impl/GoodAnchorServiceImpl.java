package com.deepexi.dxp.marketing.service.specify.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.deepexi.common.extension.AppRuntimeEnv;
import com.deepexi.dxp.marketing.common.base.utils.CommonExceptionCode;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.constant.RedisConstants;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPartakeRequest;
import com.deepexi.dxp.marketing.domain.marketing.response.*;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.enums.activity.limit.PATemplateBaseEnum;
import com.deepexi.dxp.marketing.enums.specify.ActivityTemplateNumberEnum;
import com.deepexi.dxp.marketing.enums.specify.FissonStatusEnum;
import com.deepexi.dxp.marketing.enums.specify.FissonTypeEnum;
import com.deepexi.dxp.marketing.enums.specify.UserJoinTypeEnum;
import com.deepexi.dxp.marketing.service.specify.GoodAnchorService;
import com.deepexi.dxp.marketing.service.specify.MiniProgramService;
import com.deepexi.dxp.marketing.service.specify.PromotionActivityService;
import com.deepexi.dxp.marketing.utils.HuaFaHmacAuthUtil;
import com.deepexi.dxp.marketing.utils.RandomUtils;
import com.deepexi.dxp.middle.promotion.converter.specify.BargainingConverter;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityFissionLogDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityPartakeLogDAO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityLimitDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityFissionLogDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityPartakeLogDO;
import com.deepexi.dxp.middle.promotion.mapper.specify.ActivityFissionLogMapper;
import com.deepexi.dxp.middle.promotion.mapper.specify.ActivityPartakeLogMapper;
import com.deepexi.util.exception.ApplicationException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 好主播活动接口实现
 * @Author: HT
 * @CreateTime: 2022/5/11
 */
@Service
@Slf4j
public class GoodAnchorServiceImpl implements GoodAnchorService {

    @Resource
    private ActivityPartakeLogMapper activityPartakeLogMapper;

    @Resource
    private ActivityFissionLogMapper activityFissionLogMapper;

    @Resource
    private ActivityFissionLogDAO activityFissionLogDAO;

    @Resource
    private HuaFaHmacAuthUtil huaFaHmacAuthUtil;

    @Resource
    private ActivityPartakeLogDAO activityPartakeLogDAO;

    @Resource
    private MiniProgramService miniProgramService;

    @Resource
    private RedissonClient redissonClient;

    @Autowired
    private PromotionActivityService promotionActivityService;

    /**
     * UUID有效期
     */
    public static final int UUID_DURATION = 30;

    /**
     * 项目id -> DTO
     */
    public static Map<String, SaleTeamSaleOrgRelDTO> projectIdToSaleTeamSaleOrgRelDTOMap = null;

    /**
     * cityId -> projectIds
     */
    public static Map<String, List<String>> cityIdToProjectIdListMap = null;

    @Override
    public GoodAnchorLikesRankResponseDTO queryLikesRanking(Integer id, Long activityId) {

        List<ActivityPartakeLogDO> activityPartakeLogDOS = activityPartakeLogMapper.queryLikesRanking(activityId);
        if (CollectionUtils.isEmpty(activityPartakeLogDOS)) {
            return new GoodAnchorLikesRankResponseDTO();
        }

        List<ProjectRankDTO> projectRankDTOS = assembleProjectRankDTOS(activityPartakeLogDOS);

        // 项目维度
        GoodAnchorLikesRankResponseDTO result = new GoodAnchorLikesRankResponseDTO();
        if (id == 2) {
            result.setProjectRankDTOList(projectRankDTOS);
        }

        // 城市维度
        if (id == 1) {
            Map<String, List<ProjectRankDTO>> cityIdToProjectRankDTOMap = projectRankDTOS.stream()
                    .collect(Collectors.groupingBy(ProjectRankDTO::getCityId));
            List<CityRankDTO> collectCityResult = cityIdToProjectRankDTOMap.keySet().stream()
                    .map(cityId -> {
                        List<ProjectRankDTO> projectRankList = cityIdToProjectRankDTOMap.get(cityId);
                        CityRankDTO cityRankDTO = new CityRankDTO();
                        cityRankDTO.setCityId(cityId);
                        cityRankDTO.setCityName(projectRankList.get(0).getCityName());
                        Integer sumLikes = projectRankList.stream()
                                .map(ProjectRankDTO::getLikesNum)
                                .reduce(0, Integer::sum);
                        cityRankDTO.setLikesNum(sumLikes);
                        return cityRankDTO;
                    })
                    .sorted(Comparator.comparing(CityRankDTO::getLikesNum).reversed())
                    .collect(Collectors.toList());
            result.setCityRankDTOList(collectCityResult);
        }

        return result;
    }

    @Override
    public List<GoodAnchorVideoResponseDTO> queryVideosByCityId(String cityId, Long activityId, String mobile) {

        // 查询出所有的项目
        List<String> projectIds = getProjectIdsByCityId(cityId);

        // 根据项目查出活动的参赛作品
        List<ActivityPartakeLogDO> activityPartakeLogDOS = activityPartakeLogMapper.queryVideoByProjectIdList(projectIds, activityId);
        List<GoodAnchorVideoResponseDTO> result = activityPartakeLogDOS.stream()
                .map(logDO -> {
                    GoodAnchorVideoResponseDTO item = new GoodAnchorVideoResponseDTO();
                    item.setId(logDO.getId());
                    item.setVideoId(logDO.getVideoId());
                    item.setVideoTitle(logDO.getVideoTitle());
                    item.setVideoUrl(logDO.getVideoUrl());
                    item.setVideoCoverUrl(logDO.getCoverUrl());
                    item.setAccountId(logDO.getUserId());
                    item.setAccountName(logDO.getNickName());
                    item.setAvatar(logDO.getAvatar());
                    item.setLikesNum(logDO.getCurrentFissonPrice().intValue());
                    return item;
                }).collect(Collectors.toList());

        // 查看是否点赞
        List<String> partakeLogIdList = getLikesPartakeLogByMobileFromCache(mobile, activityId);
        result.forEach(logDO -> logDO.setLikesFlag(partakeLogIdList.contains(String.valueOf(logDO.getId())) ? 1 : 0));

        return result;
    }

    @Override
    public SaleTeamSaleOrgRelDTO getCityInfoByProjectId(String projectId) {

        if (projectIdToSaleTeamSaleOrgRelDTOMap == null) {
            List<SaleTeamSaleOrgRelDTO> projectInfos = huaFaHmacAuthUtil.getProjectInfos();
            projectIdToSaleTeamSaleOrgRelDTOMap = projectInfos.stream()
                    .collect(Collectors.toMap(SaleTeamSaleOrgRelDTO::getProjectId, item -> item,(key1,key2) -> key1));
        }

        return projectIdToSaleTeamSaleOrgRelDTOMap.get(projectId);
    }

    @Override
    public List<String> getProjectIdsByCityId(String cityId) {
        if (cityIdToProjectIdListMap == null) {
            cityIdToProjectIdListMap = huaFaHmacAuthUtil.getProjectInfos().stream()
                    .collect(Collectors.groupingBy(SaleTeamSaleOrgRelDTO::getRealCityId,
                            Collectors.mapping(SaleTeamSaleOrgRelDTO::getProjectId, Collectors.toList())));
        }
        return cityIdToProjectIdListMap.get(cityId) == null ? Lists.newArrayList() : cityIdToProjectIdListMap.get(cityId);
    }

    @Override
    public GoodAnchorVideoExtResponseDTO queryVideosByAccountId(String accountId, Long activityId, String mobile) {

        // 查出主播下的所有的作品
        List<ActivityPartakeLogDO> activityPartakeLogDOS = activityPartakeLogMapper.queryVideoByAccountId(accountId, activityId);
        if (CollectionUtils.isEmpty(activityPartakeLogDOS)) {
            return null;
        }

        // 作品集合
        List<VideoResponseDTO> videoResponseDTOS = assembleVideoResponseDTOS(activityPartakeLogDOS, mobile, activityId);

        // 个人信息
        GoodAnchorInfoVO goodAnchorInfoVO = assembleAnchorInfo(activityPartakeLogDOS);

        // 活动信息
        PromotionActivityDetailDTO actDTO = getActivityDetailDTO(activityId);

        return GoodAnchorVideoExtResponseDTO.builder()
                .videoResponseDTOList(videoResponseDTOS)
                .goodAnchorInfoVO(goodAnchorInfoVO)
                .actDTO(actDTO)
                .build();
    }

    @Override
    public VideoResponseDTO queryVideoById(String videoId, String mobile, Long activityId) {

        ActivityPartakeLogDO logDO = activityPartakeLogMapper.getById(videoId,activityId);
        if (logDO == null) {
            return null;
        }

        Map<Long, VideoFromZYTVO> collectMap = getVideoFromZYTVOMap(logDO.getUserId());
        VideoResponseDTO item = new VideoResponseDTO();
        item.setId(logDO.getId());
        item.setVideoId(logDO.getVideoId());
        item.setVideoTitle(logDO.getVideoTitle());
        item.setVideoUrl(logDO.getVideoUrl());
        item.setVideoCoverUrl(logDO.getCoverUrl());
        item.setLikesNum(logDO.getCurrentFissonPrice().intValue());
        if (collectMap != null && collectMap.containsKey(logDO.getVideoId())) {
            VideoFromZYTVO videoFromZYTVO = collectMap.get(logDO.getVideoId());
            item.setShareCount(videoFromZYTVO.getShareCount());
            item.setPlayCount(videoFromZYTVO.getPlayCount());
        }

        // 查出这些作品被点赞的情况
        List<String> partakeLogIdList = getLikesPartakeLogByMobileFromCache(mobile, logDO.getActivityId());
        item.setLikesFlag(partakeLogIdList.contains(String.valueOf(logDO.getId())) ? 1 : 0);

        GoodAnchorInfoVO goodAnchorInfoVO = new GoodAnchorInfoVO();
        goodAnchorInfoVO.setAccountId(logDO.getUserId());
        goodAnchorInfoVO.setAccountName(logDO.getNickName());
        goodAnchorInfoVO.setUserName(logDO.getUserName());
        goodAnchorInfoVO.setAvatar(logDO.getAvatar());
        goodAnchorInfoVO.setMobile(logDO.getPhone());
        goodAnchorInfoVO.setUserKey(logDO.getUserKey());
        goodAnchorInfoVO.setAccountInfo(logDO.getAccountInfo());
        goodAnchorInfoVO.setProjectId(logDO.getProjectId());
        SaleTeamSaleOrgRelDTO cityInfoByProjectId = getCityInfoByProjectId(logDO.getProjectId());
        goodAnchorInfoVO.setProjectName(cityInfoByProjectId.getProjectName());
        goodAnchorInfoVO.setRealCityId(cityInfoByProjectId.getRealCityId());
        goodAnchorInfoVO.setRealCityName(cityInfoByProjectId.getRealCityName());
        goodAnchorInfoVO.setSaleTeamId(cityInfoByProjectId.getSaleTeamId());
        goodAnchorInfoVO.setSaleTeamName(cityInfoByProjectId.getSaleTeamName());
        item.setGoodAnchorInfoVO(goodAnchorInfoVO);

        item.setActDTO(getActivityDetailDTO(logDO.getActivityId()));
        return item;
    }

    /**
     * 好主播活动参与
     *
     * @param requestVo
     * @param promotionActivityLimitList
     * @return
     */
    @Override
    @Transactional
    public Boolean partakeAct(ActivityPartakeRequest requestVo, List<PromotionActivityLimitDO> promotionActivityLimitList) {
        if (UserJoinTypeEnum.JOIN_TYPE_1.getId().equals(requestVo.getUserJoinType())) {
            return this.takePartIn(requestVo);
        } else if (UserJoinTypeEnum.JOIN_TYPE_2.getId().equals(requestVo.getUserJoinType())) {
            return this.likeAction(requestVo, promotionActivityLimitList);
        }

        throw new ApplicationException("无效的活动参与类型userJoinType=" + requestVo.getUserJoinType());
    }

    @Override
    public Boolean changeStauts(Long id, String status) {
        ActivityPartakeLogDO log = new ActivityPartakeLogDO();
        log.setId(id);
        log.setFissonStatus(Convert.toInt(status));
        return activityPartakeLogDAO.updateById(log);
    }

    @Override
    public Boolean delete(Long id) {
        return activityPartakeLogDAO.removeById(id);
    }

    @Override
    public String getUUID(String phone, Long activityId, Long id) {
        RBucket<Object> bucket = getRepeatBucket(phone, activityId, id);
        // 一个用户在2s内不能对一个作品重复点赞
        if (bucket.remainTimeToLive() / 1000 > (UUID_DURATION - 2)) {
            log.error("good anchor getUUID repeat likes, phone:{}, activityId: {}, id: {}", phone, activityId, id);
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "请勿对一个作品频繁点赞~");
        }
        String uuId = RandomUtils.getRandomStr();
        bucket.set(uuId, UUID_DURATION, TimeUnit.SECONDS);
        log.info("good anchor getUUID, phone:{}, activityId: {}, id: {}, uuid: {}", phone, activityId, id, uuId);
        return uuId;
    }

    private RBucket<Object> getRepeatBucket(String phone, Long activityId, Long id) {
        String redisKey = String.format(RedisConstants.CACHE_PREV_KEY_ACT_SUBMIT_REPEAT, activityId, phone, id);
        return redissonClient.getBucket(redisKey);
    }

    private List<ProjectRankDTO> assembleProjectRankDTOS(List<ActivityPartakeLogDO> activityPartakeLogDOS) {
        return activityPartakeLogDOS.stream()
                .map(apLogDO -> {
                    String projectId = apLogDO.getProjectId();
                    SaleTeamSaleOrgRelDTO cityInfoByProjectId = getCityInfoByProjectId(projectId);
                    if (cityInfoByProjectId == null) {
                        log.error("project without city info, projectId: {}", projectId);
                        return null;
                    }
                    ProjectRankDTO projectRankDTO = new ProjectRankDTO();
                    projectRankDTO.setProjectId(projectId);
                    projectRankDTO.setCityId(cityInfoByProjectId.getRealCityId());
                    projectRankDTO.setCityName(cityInfoByProjectId.getRealCityName());
                    projectRankDTO.setLikesNum(apLogDO.getCurrentFissonPrice().intValue());
                    projectRankDTO.setProjectName(apLogDO.getProjectName());
                    return projectRankDTO;
                })
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(ProjectRankDTO::getLikesNum).reversed())
                .collect(Collectors.toList());
    }

    /**
     * 点赞活动
     *
     * @param requestVo
     * @param promotionActivityLimitList
     * @return
     */
    private Boolean likeAction(ActivityPartakeRequest requestVo, List<PromotionActivityLimitDO> promotionActivityLimitList) {

        log.info("good anchor partakeAct, requestVo: {}", JSON.toJSONString(requestVo));

        // 校验uuid
        checkUuId(requestVo);

        // 获取活动规则
        List<BaseActivityDTO> numberList = BargainingConverter.getPromotionActivityLimit(promotionActivityLimitList, PATemplateBaseEnum.NUMBER.getId());

        // 用户点赞作品数限制
        Integer helpNumber = BargainingConverter.elLimitConverter(numberList, ActivityTemplateNumberEnum.HELPER_NUMBER.getId());

        // 用户每个作品点赞次数限制
        Integer helpTimes = BargainingConverter.elLimitConverter(numberList, ActivityTemplateNumberEnum.HELP_TIMES.getId());

        // 查缓存是否超过限制
        RMap<Object, Object> rMap = checkLimit(requestVo, helpNumber, helpTimes);

        // 业务逻辑
        saveOrUpdateLikes(requestVo);

        // 设置缓存
        // 作品id
        setLimitCache(requestVo, rMap);

        return Boolean.TRUE;
    }

    private void setLimitCache(ActivityPartakeRequest requestVo, RMap<Object, Object> rMap) {
        String id = String.valueOf(requestVo.getSponsorId());
        Object redisMapValue = rMap.get(requestVo.getPhone());
        Gson gson = new Gson();
        if (redisMapValue == null) {
            // 此号码第一次进来
            Map<String, Integer> map = Maps.newHashMap();
            map.put(id, 1);
            rMap.put(requestVo.getPhone(), gson.toJson(map));
            rMap.expire(getNowToNextDaySeconds(), TimeUnit.SECONDS);
        } else {
            // 此号码此此作品第一次点赞
            Map<String, Double> idToLikesNumMap = gson.fromJson(String.valueOf(redisMapValue), Map.class);
            if (!idToLikesNumMap.containsKey(id)) {
                idToLikesNumMap.put(id, 1d);
            } else {
                double likesNum = idToLikesNumMap.get(id) + 1d;
                idToLikesNumMap.put(id, likesNum);
            }
            rMap.put(requestVo.getPhone(), gson.toJson(idToLikesNumMap));
        }
    }

    private void checkUuId(ActivityPartakeRequest requestVo) {
        RBucket<Object> bucket = getRepeatBucket(requestVo.getPhone(), requestVo.getActivityId(), requestVo.getSponsorId());
        if (StringUtils.isBlank(requestVo.getUuId()) || bucket.get() == null || !String.valueOf(bucket.get()).equals(requestVo.getUuId())) {
            log.error("点赞无效, requestVo:{}", JSON.toJSONString(requestVo));
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "点赞无效，请联系管理员~");
        }
        bucket.delete();
    }

    private void saveOrUpdateLikes(ActivityPartakeRequest requestVo) {
        // 作品id
        Long id = requestVo.getSponsorId();
        // 更新作品表
        // 更新或新增点赞表
        activityPartakeLogMapper.likesVideoById(id);
        int updateResult = activityFissionLogMapper.likesVideo(requestVo.getPhone(), id);
        if (updateResult == 0) {
            ActivityPartakeLogDO partakeLogDO = new ActivityPartakeLogDO();
            partakeLogDO.setId(id);
            ActivityFissionLogDO activityFissionLogDO = BargainingConverter.activityFissionLogConverter(requestVo,1,partakeLogDO);
            activityFissionLogDAO.save(activityFissionLogDO);
        }
    }

    private RMap<Object, Object> checkLimit(ActivityPartakeRequest requestVo, Integer helpNumber, Integer helpTimes) {
        // 手机号 -> 作品id:点赞数
        String redisKey = String.format(RedisConstants.CACHE_PREV_KEY_ACT_GOOD_ANCHOR_LIKES_LIMIT, requestVo.getActivityId());
        RMap<Object, Object> rMap = redissonClient.getMap(redisKey);
        Object redisValue = rMap.get(requestVo.getPhone());
        if (redisValue != null) {
            String id = String.valueOf(requestVo.getSponsorId());
            Map<String, Double> idToLikesNumMap = new Gson().fromJson(String.valueOf(redisValue), Map.class);
            if (!idToLikesNumMap.containsKey(id) && idToLikesNumMap.keySet().size() >= helpNumber) {
                throw new ApplicationException(CommonExceptionCode.INVALIDATION, "每日点赞的作品数量已达上限~");
            }
            if (idToLikesNumMap.containsKey(id)) {
                if (idToLikesNumMap.get(id) >= helpTimes) {
                    throw new ApplicationException(CommonExceptionCode.INVALIDATION, "每日点赞此作品的数量已达上限~");
                }
            }
        }
        return rMap;
    }

    /**
     * 参与活动
     *
     * @param requestVo
     * @return
     */
    private Boolean takePartIn(ActivityPartakeRequest requestVo) {
        List dynamicForm = (List) requestVo.getDynamicForm();
        for (Object o : dynamicForm) {
            ActivityPartakeLogDO partakeLogDO = requestVo.clone(ActivityPartakeLogDO.class);
            //从参数复制数据
            BeanUtil.copyProperties(o, partakeLogDO);
            //需判断视频是否已经参赛过
            int count = activityPartakeLogDAO.getByVideoId(partakeLogDO.getActivityId(), partakeLogDO.getVideoId());
            if (count > 0) {
                throw new ApplicationException("该视频已参与过活动，请勿重复参与：" + partakeLogDO.getVideoTitle());
            }
            partakeLogDO.setCurrentFissonPrice(BigDecimal.ZERO);
            partakeLogDO.setTenantId(AppRuntimeEnv.getTenantId());
            partakeLogDO.setAppId(AppRuntimeEnv.getAppId());
            partakeLogDO.setFissonStatus(FissonStatusEnum.PROCESSING.getId());
            partakeLogDO.setFissonType(FissonTypeEnum.GOOD_ANCHOR.getId());
            activityPartakeLogDAO.save(partakeLogDO);
        }
        return Boolean.TRUE;
    }

    private List<VideoResponseDTO> assembleVideoResponseDTOS(List<ActivityPartakeLogDO> activityPartakeLogDOS, String mobile, Long activityId) {

        // 查出这些作品被点赞的情况
        List<String> partakeLogIdList = getLikesPartakeLogByMobileFromCache(mobile, activityId);

        // 根据accountId查询视频信息列表
        Map<Long, VideoFromZYTVO> collectMap = getVideoFromZYTVOMap(activityPartakeLogDOS.get(0).getUserId());

        return activityPartakeLogDOS.stream()
                .map(logDO -> {
                    VideoResponseDTO item = new VideoResponseDTO();
                    item.setId(logDO.getId());
                    item.setVideoId(logDO.getVideoId());
                    item.setVideoTitle(logDO.getVideoTitle());
                    item.setVideoUrl(logDO.getVideoUrl());
                    item.setVideoCoverUrl(logDO.getCoverUrl());
                    item.setLikesNum(logDO.getCurrentFissonPrice().intValue());
                    item.setLikesFlag(partakeLogIdList.contains(String.valueOf(logDO.getId())) ? 1 : 0);
                    if (collectMap != null && collectMap.containsKey(logDO.getVideoId())) {
                        VideoFromZYTVO videoFromZYTVO = collectMap.get(logDO.getVideoId());
                        item.setShareCount(videoFromZYTVO.getShareCount());
                        item.setPlayCount(videoFromZYTVO.getPlayCount());
                    }
                    return item;
                }).collect(Collectors.toList());
    }

    private Map<Long, VideoFromZYTVO> getVideoFromZYTVOMap(String accountId) {
        Map<Long, VideoFromZYTVO> collectMap = null;
        VideoFromZYTResponseDTO videoFromZYTResponseDTO = fetchVideoInfo(accountId);
        if (videoFromZYTResponseDTO != null && CollectionUtils.isNotEmpty(videoFromZYTResponseDTO.getRecords())) {
            collectMap = videoFromZYTResponseDTO.getRecords().stream().collect(Collectors.toMap(VideoFromZYTVO::getId, Function.identity()));
        }
        return collectMap;
    }

    private VideoFromZYTResponseDTO fetchVideoInfo(String account) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("accountId", account);
        params.put("pageNo", 1);
        params.put("pageSize", 1000);
        String pageResult = miniProgramService.page(params);
        Gson gson = new Gson();
        Data<VideoFromZYTResponseDTO> result = gson.fromJson(pageResult, new TypeToken<Data<VideoFromZYTResponseDTO>>() {
        }.getType());
        return result.getdata();
    }

    private Map<Long, List<String>> getPartakeLogIdToPhoneListMap(String mobile, List<ActivityPartakeLogDO> activityPartakeLogDOS) {
        Map<Long, List<String>> partakeLogIdToPhoneListMap = null;
        if (StringUtils.isNotBlank(mobile)) {
            List<Long> partakeLogIdList = activityPartakeLogDOS.stream()
                    .map(ActivityPartakeLogDO::getId)
                    .collect(Collectors.toList());
            List<ActivityFissionLogDO> activityFissionLogDOS = activityFissionLogMapper.queryByPartakeLogIdList(partakeLogIdList);
            partakeLogIdToPhoneListMap = activityFissionLogDOS.stream()
                    .collect(Collectors.groupingBy(ActivityFissionLogDO::getPartakeLogId,
                            Collectors.mapping(ActivityFissionLogDO::getPhone, Collectors.toList())));
        }
        return partakeLogIdToPhoneListMap;
    }

    private List<String> getLikesPartakeLogByMobileFromCache(String mobile, Long activityId) {

        if (StringUtils.isBlank(mobile)) {
            return Lists.newArrayList();
        }

        String redisKey = String.format(RedisConstants.CACHE_PREV_KEY_ACT_GOOD_ANCHOR_LIKES_LIMIT, activityId);
        RMap<Object, Object> rMap = redissonClient.getMap(redisKey);
        Object redisValue = rMap.get(mobile);
        if (redisValue == null) {
            return Lists.newArrayList();
        }

        Map<String, Double> idToLikesNumMap = new Gson().fromJson(String.valueOf(redisValue), Map.class);
        return Lists.newArrayList(idToLikesNumMap.keySet());
    }

    private GoodAnchorInfoVO assembleAnchorInfo(List<ActivityPartakeLogDO> activityPartakeLogDOS) {
        ActivityPartakeLogDO apLogDO = activityPartakeLogDOS.get(0);
        GoodAnchorInfoVO goodAnchorInfoVO = new GoodAnchorInfoVO();
        goodAnchorInfoVO.setAccountId(apLogDO.getUserId());
        goodAnchorInfoVO.setAccountName(apLogDO.getNickName());
        goodAnchorInfoVO.setUserName(apLogDO.getUserName());
        goodAnchorInfoVO.setAvatar(apLogDO.getAvatar());
        goodAnchorInfoVO.setMobile(apLogDO.getPhone());
        goodAnchorInfoVO.setUserKey(apLogDO.getUserKey());
        goodAnchorInfoVO.setAccountInfo(apLogDO.getAccountInfo());
        goodAnchorInfoVO.setProjectId(apLogDO.getProjectId());
        goodAnchorInfoVO.setShareIcon(apLogDO.getShareIcon());
        SaleTeamSaleOrgRelDTO cityInfoByProjectId = getCityInfoByProjectId(apLogDO.getProjectId());
        goodAnchorInfoVO.setProjectName(cityInfoByProjectId.getProjectName());
        goodAnchorInfoVO.setRealCityId(cityInfoByProjectId.getRealCityId());
        goodAnchorInfoVO.setRealCityName(cityInfoByProjectId.getRealCityName());
        goodAnchorInfoVO.setSaleTeamId(cityInfoByProjectId.getSaleTeamId());
        goodAnchorInfoVO.setSaleTeamName(cityInfoByProjectId.getSaleTeamName());
        // 点赞排名
        /*List<ActivityPartakeLogDO> apDOs = activityPartakeLogMapper.queryLikesTotalGroupByUserId(activityId);
        int i = 1;
        for (ActivityPartakeLogDO apDO : apDOs) {
            if (apDO.getUserId().equals(goodAnchorInfoVO.getAccountId())) {
                goodAnchorInfoVO.setRank(i);
                break;
            } else {
                i++;
            }
        }*/
        return goodAnchorInfoVO;
    }

    public static long getNowToNextDaySeconds() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return (calendar.getTimeInMillis() - System.currentTimeMillis()) / 1000;
    }

    private PromotionActivityDetailDTO getActivityDetailDTO(Long activityId) {
        PromotionActivityDetailDTO promotionActivityDetailDTO = JSON.parseObject(String.valueOf(redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO + activityId).get()),
                PromotionActivityDetailDTO.class);
        if (promotionActivityDetailDTO == null) {
            promotionActivityDetailDTO = promotionActivityService.getActivityById(activityId);
        }
        return promotionActivityDetailDTO;
    }

}
