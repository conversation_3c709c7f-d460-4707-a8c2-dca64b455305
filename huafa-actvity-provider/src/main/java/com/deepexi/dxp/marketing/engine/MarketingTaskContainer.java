//package com.deepexi.dxp.marketing.engine;
//
//import com.deepexi.dxp.marketing.engine.listener.MarketingTaskListenerFactory;
//import org.springframework.stereotype.Component;
//
///**
// * 管理营销任务各个组件生命周期容器
// */
//
//@Component
//public class MarketingTaskContainer {
//
//    /**
//     * 初始化主动或自动营销发送引擎
//     *
//     * @param taskKey 由"tenantId_taskTypeId_taskId"格式组成的任务唯一标识key
//     */
//    public void initTask(String taskKey) {
//        MarketingTaskListenerFactory.initListenerForTask(taskKey);
//    }
//
//    public void destroyTask(String taskKey) {
//        MarketingTaskListenerFactory.removeListenerForTask(taskKey);
//    }
//
//}
