package com.deepexi.dxp.marketing.domain.promotion.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel(description = "取消活动请求对象")
public class CancelActivityRequest {

    @ApiModelProperty(value = "参与记录ID", required = true)
    @NotNull(message = "参与记录ID不能为空")
    private Long partakeLogId;

    @ApiModelProperty(value = "手机号", required = true)
    @NotBlank(message = "手机号不能为空")
    private String phone;

    @ApiModelProperty(value = "取消人员的下标，从0开始", required = true)
    private List<Integer> items;
    @ApiModelProperty(value = "是否管理员操作")
    private boolean isAdmin = false;
}
