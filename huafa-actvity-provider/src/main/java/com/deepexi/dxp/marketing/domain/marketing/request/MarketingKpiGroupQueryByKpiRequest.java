package com.deepexi.dxp.marketing.domain.marketing.request;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author：liuyang
 * @version：1.0.0
 * @date：2021-03-31 17:24
 */
@Data
@ApiModel
public class MarketingKpiGroupQueryByKpiRequest extends AbstractObject implements Serializable {

    @ApiModelProperty(value = "活动目标类型(1:营收,2:商品,3:用户)", required = true)
    private Integer type;

    @ApiModelProperty(value = "核心指标ID", required = true)
    private Long coreKpiItemsId;
}
