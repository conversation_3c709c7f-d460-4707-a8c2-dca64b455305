package com.deepexi.dxp.marketing.domain.promotion.dto;

import com.deepexi.dxp.marketing.domain.marketing.response.AccountQueryVo;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityFissionLogDO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Map;

@Data
@NoArgsConstructor
public class OneCodeAuthDTO {
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;
    @ApiModelProperty(value = "小程序码id")
    private Long codeId;
    @ApiModelProperty(value = "小程序appId")
    private String appId;
    @ApiModelProperty(value = "小程序用户id")
    private String openId;
    @ApiModelProperty(value = "电话")
    private String phone;
    @ApiModelProperty(value = "用户名")
    private String name;
    @ApiModelProperty(value = "头像")
    private String avatar;
    @ApiModelProperty(value = "是否报名:0-未报名，1-已报名")
    private Integer isSignUp;
    @ApiModelProperty("报名数量")
    private Integer total;
    private Map<String, Object> ext;
    public OneCodeAuthDTO(ActivityFissionLogDO fissionLogDO, AccountQueryVo accountVo,Map<String, Object> ext) {
        this.createTime = fissionLogDO.getCreatedTime();
        this.openId = fissionLogDO.getUserId();
        this.phone = fissionLogDO.getPhone();
        this.name = fissionLogDO.getNickName();
        if (accountVo != null) {
            this.avatar = accountVo.getProfilePicture();
        }
        this.isSignUp = 1;
        this.ext = ext;
        this.total = fissionLogDO.getMoney().intValue();
    }
}