package com.deepexi.dxp.marketing.controller.specify.adminapi;

import cn.hutool.json.JSONObject;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.response.CommonOrgResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.CommonUserInfoResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.MenuPermissionDTO;
import com.deepexi.dxp.marketing.service.specify.CommonSystemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/admin-api/v1/common")
@Api(value = "通用营销系统接口", description = "通用营销系统接口", tags = {"通用营销系统接口"})
public class CommonSystemController{

    @Autowired
    private CommonSystemService commonSystemService;

    @GetMapping("/getOrg")
    @ApiOperation(value="用户组织查询", notes = "用户组织查询")
    public Data<List<CommonOrgResponseDTO>> getOrg(@RequestParam String userId) {
        return new Data<>(commonSystemService.getOrg(userId));
    }

    @GetMapping("/orgIds")
    @ApiOperation(value="组织权限树列表", notes = "组织权限树列表")
    public JSONObject getOrgTree(@RequestParam String userId) {
        return commonSystemService.getOrgTreeByUserAlias(userId);
    }

    @GetMapping("/getUserInfo")
    @ApiOperation(value = "获取登录用户信息", notes = "获取登录用户信息", nickname = "getUserInfo")
    public Data<CommonUserInfoResponseDTO.CommonUserInfoDTO> getUserInfo(@RequestParam String userId) {
        return new Data<>(commonSystemService.getUserInfo(userId));
    }
    @GetMapping("/getUserInfoByUserId")
    @ApiOperation(value = "免登获取登录用户信息", notes = "免登获取登录用户信息", nickname = "getUserInfo")
    public Data<CommonUserInfoResponseDTO.CommonUserInfoDTO> getUserInfoByUserId(@RequestParam String userId) {
        return new Data<>(commonSystemService.getUserInfoByUserId(userId));
    }

    @GetMapping("/getUserAllMenuPermission")
    @ApiOperation(value="用户所有菜单权限", notes = "用户所有菜单权限")
    public Data<List<MenuPermissionDTO>> getUserAllMenuPermission(@RequestParam String userId,@RequestParam String clientCode,@RequestParam String menuId) {
        return new Data<>(commonSystemService.getUserAllMenuPermission(userId,clientCode,menuId));
    }
}
