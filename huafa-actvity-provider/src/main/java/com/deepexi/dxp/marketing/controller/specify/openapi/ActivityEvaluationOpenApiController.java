package com.deepexi.dxp.marketing.controller.specify.openapi;

import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.ActivityEvaluationQueryDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.SubmitEvaluationDTO;
import com.deepexi.dxp.marketing.service.specify.ActivityEvaluationService;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityEvaluationDO;
import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 活动评价微信小程序端控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/open-api/v1/open/activity-evaluation")
@Api(value = "活动评价小程序端", tags = "活动评价小程序端")
@Slf4j
public class ActivityEvaluationOpenApiController {

    @Resource
    private ActivityEvaluationService activityEvaluationService;

    @PostMapping("/submit")
    @ApiOperation(value = "提交活动评价")
    public Data<Boolean> submitEvaluation(@Valid @RequestBody SubmitEvaluationDTO evaluationDTO) {
        Boolean result = activityEvaluationService.submitEvaluation(evaluationDTO);
        return new Data<>(result);
    }

    @GetMapping("/detail")
    @ApiOperation(value = "获取用户评价详情")
    public Data<ActivityEvaluationDO> getUserEvaluation(
            @ApiParam(value = "活动ID", required = true) @RequestParam Long partakeLogId,
            @ApiParam(value = "用户电话", required = true) @RequestParam String phone) {
        ActivityEvaluationDO result = activityEvaluationService.getUserEvaluation(partakeLogId, phone);
        return new Data<>(result);
    }

    @PostMapping("/list")
    @ApiOperation(value = "分页查询活动评价列表")
    public Data<PageBean<ActivityEvaluationDO>> getEvaluationList(@RequestBody ActivityEvaluationQueryDTO query) {
        // 调用专门的小程序端查询方法
        PageBean<ActivityEvaluationDO> result = activityEvaluationService.getEvaluationListForMiniProgram(query);
        return new Data<>(result);
    }


}