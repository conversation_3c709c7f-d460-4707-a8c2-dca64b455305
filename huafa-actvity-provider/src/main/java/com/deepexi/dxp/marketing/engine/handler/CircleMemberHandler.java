//package com.deepexi.dxp.marketing.engine.handler;
//
//import com.alibaba.fastjson.JSONObject;
//import com.deepexi.dxp.marketing.domain.tags.dto.UserFromEsDTO;
//import com.deepexi.dxp.marketing.engine.event.MarketingTaskEvent;
//import com.deepexi.dxp.marketing.engine.listener.MarketingTaskListener;
//import com.deepexi.dxp.marketing.engine.processor.AbstractTaskProcessor;
//import com.deepexi.dxp.marketing.engine.processor.MarketingTaskEndProcessor;
//import com.deepexi.dxp.middle.marketing.domain.dto.MarketingActiveDTO;
//import com.deepexi.dxp.marketing.mq.rocket.SendSelectedUserForMetaDataProducer;
//import com.deepexi.dxp.marketing.service.marketing.MarketingActiveService;
//import com.deepexi.dxp.marketing.utils.SpringContextHolder;
//import com.deepexi.dxp.marketing.utils.TagIterator;
//import com.deepexi.dxp.marketing.utils.ThreadPoolUtils;
//import com.deepexi.redis.service.RedisService;
//import com.deepexi.util.CollectionUtil;
//import com.deepexi.util.JsonUtil;
//import com.deepexi.util.exception.ApplicationException;
//import com.google.common.collect.Lists;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.math.NumberUtils;
//import java.util.Collection;
//import java.util.List;
//import java.util.Objects;
//import java.util.concurrent.CountDownLatch;
//import java.util.concurrent.atomic.AtomicInteger;
//import java.util.stream.Collectors;
//
///**
// * 圈人，标签域数据处理，主要用户营销任务启动时实时获取标签、客群对应的会员消息，
// * 再将获取会员信息塞入到MQ，以供Assembler进行消息发送时的元数据组装;
// *
// * <AUTHOR>
// * @date 2020/3/23 18:10
// */
//@Slf4j
//public class CircleMemberHandler extends MarketingTaskListener<MarketingTaskEvent> {
//
//    /**
//     * 是否退出异常监听线程
//     */
//    private static volatile boolean exitExceptionThread = false;
//
//    /**
//     * 记录任务发送会员人数
//     */
//    private final AtomicInteger memberNums = new AtomicInteger(0);
//
//    /**
//     * 增加可发送的会员数量
//     *
//     * @param num
//     */
//    private void addMemberNums(int num) {
//        memberNums.addAndGet(num);
//    }
//
//
//    private CountDownLatch getCountDownLatch(int fetchSize, long total) {
//        if (total > 0 && total < fetchSize) {
//            return new CountDownLatch(1);
//        } else if (total > 0 && total > fetchSize) {
//            return new CountDownLatch((int) Math.ceil((double) total / fetchSize));
//        }
//        return null;
//    }
//
//    private JSONObject getExperimentJsonObject(MarketingActiveDTO taskActiveDTO) {
//
//        RedisService redisService = SpringContextHolder.getBean("redisService");
//        String text = (String) redisService.get(RedisHandler.getExperimentTaskOffsetKey(
//                taskActiveDTO.getGroupCode(), taskActiveDTO.getId()));
//        JSONObject jsonObject = JSONObject.parseObject(text);
//
//        int start = (Integer) jsonObject.get("start");
//        int end = (Integer) jsonObject.get("end");
//        int total = end - start + 1;
//        jsonObject.put("total", total);
//        return jsonObject;
//    }
//
//    /**
//     * 获取普通营销任务发送时全取的会员总数
//     */
//    private long getGeneralTotal(AbstractTaskProcessor processor) {
//        try {
//            return processor.getMetaDataTask().getTaskService().getMemberTotal(processor.getMetaDataTask());
//        } catch (Exception e) {
//            log.error("从标签域里获取营销任务会员总数失败.", e);
//            throw new ApplicationException("从标签域里获取营销任务会员总数失败");
//        }
//    }
//
//    /**
//     * 将会员JSON集合转化为MemberEsDTO集合
//     *
//     * @param dataList
//     * @return
//     */
//    private List<UserFromEsDTO> getMemberEsDTOList(Collection<String> dataList) {
//        return dataList.stream().map(json -> JsonUtil.json2Bean(json, UserFromEsDTO.class)).collect(Collectors.toList());
//    }
//
//    /**
//     * 获取实验任务会员
//     */
//    private void experimentTask(MarketingTaskEvent event, SendSelectedUserForMetaDataProducer producer
//            , CountDownLatch countDownLatch, int fetchSize, JSONObject jsonObject) {
//
//        AbstractTaskProcessor processor = (AbstractTaskProcessor) event.getSource();
//        MarketingActiveDTO taskActiveDTO = (MarketingActiveDTO) processor.getMetaDataTask().getMarketingTaskDTO();
//
//        RedisService redisService = SpringContextHolder.getBean(RedisService.class);
//        int start = (Integer) jsonObject.get("start");
//        int end = (Integer) jsonObject.get("end");
//        int size = fetchSize < end ? fetchSize : end;
//        String dataKey = RedisHandler.getExperimentTaskDataKey(taskActiveDTO.getGroupCode());
//
//        if (start == end) {
//            List<UserFromEsDTO> members = getMemberEsDTOList(redisService.lrange(dataKey, start, end));
//            if (CollectionUtil.isNotEmpty(members)) {
//                ThreadPoolUtils.executor(new Worker(countDownLatch, event, members, producer));
//            } else {
//                countDownLatch.countDown();
//            }
//        } else {
//            for (int i = start; i < end; i += size) {
//                int offset = (size == end ? size : (i + size - 1));
//                if (offset > end) {
//                    offset = end;
//                }
//                List<UserFromEsDTO> members = getMemberEsDTOList(redisService.lrange(dataKey, i, offset));
//                if (CollectionUtil.isNotEmpty(members)) {
//                    ThreadPoolUtils.executor(new Worker(countDownLatch, event, members, producer));
//                } else {
//                    countDownLatch.countDown();
//                }
//            }
//        }
//    }
//
//    private void generalTask(MarketingTaskEvent event, SendSelectedUserForMetaDataProducer producer
//            , CountDownLatch countDownLatch, int fetchSize, long total) {
//
//        if (total == 0) {
//            throw new ApplicationException("本次营销任务没有圈到会员.");
//        }
//
//        TagIterator tagIterator = null;
//        AbstractTaskProcessor processor = (AbstractTaskProcessor) event.getSource();
//        try {
//            tagIterator = processor.getMetaDataTask().getTaskService()
//                    .fetchMembers(processor.getMetaDataTask(), fetchSize);
//        } catch (Exception e) {
//            log.error("从标签域里获取营销任务会员信息失败.", e);
//            throw new ApplicationException("从标签域里获取营销任务会员信息失败");
//        }
//        while (tagIterator.hasNext()) {
//            List<UserFromEsDTO> members = getMemberEsDTOList(tagIterator.next().getJsonMembers());
//            if (CollectionUtil.isNotEmpty(members)) {
//                ThreadPoolUtils.executor(new Worker(countDownLatch, event, members, producer));
//            } else {
//                countDownLatch.countDown();
//            }
//        }
//    }
//
//    @Override
//    public void start(MarketingTaskEvent event) {
//        // 每次抓取人数
//        int fetchSize = 5000;
//        // ES里总人数
//        long total = 0L;
//        CountDownLatch countDownLatch = null;
//
//        AbstractTaskProcessor processor = (AbstractTaskProcessor) event.getSource();
//        MarketingActiveDTO taskActiveDTO = (MarketingActiveDTO) processor.getMetaDataTask().getMarketingTaskDTO();
//        SendSelectedUserForMetaDataProducer producer = SpringContextHolder.getBean(SendSelectedUserForMetaDataProducer.class);
//        // 如果是实验任务
//        if (taskActiveDTO.isExperiment()) {
//            JSONObject jsonObject = getExperimentJsonObject(taskActiveDTO);
//            total = NumberUtils.createLong(jsonObject.get("total").toString());
//            countDownLatch = getCountDownLatch(fetchSize, total);
//            experimentTask(event, producer, countDownLatch, fetchSize, jsonObject);
//        } else {
//            total = getGeneralTotal(processor);
//            countDownLatch = getCountDownLatch(fetchSize, total);
//            generalTask(event, producer, countDownLatch, fetchSize, total);
//        }
//
//        if (countDownLatch != null) {
//            // 等待所有的线程处理完所有的数据
//            try {
//                countDownLatch.await();
//            } catch (InterruptedException e) {
//                log.error("EsHandler 等待所有线程处理完所有的数据时发生错误.", e);
//                Thread.currentThread().interrupt();
//            }
//        }
//
//        if (memberNums.get() == 0) {
//            throw new ApplicationException("营销任务圈选" + total + "人，但被安全过滤全部过滤掉.");
//        }
//
//        // 將任务圈的总人数放入redis，供任务是否完成监听器使用
//        RedisService redisService = SpringContextHolder.getBean("redisService");
//        redisService.set(event.getRedisProduceMemberNums(), String.valueOf(memberNums.get()));
//
//        // 更新受众目标人数
//        try {
//            processor.getMetaDataTask().getTaskService().updateEstimateMemberNums(
//                    processor.getMetaDataTask().getMetaDataParameters().getTaskId()
//                    , memberNums.get(), processor.getMetaDataTask().getMetaDataParameters().getTenantId());
//        } catch (Exception e) {
//            log.error("启动任务获取ES会员，更新受众目标人数时发生错误.", e);
//        }
//
//        // 开启监听任务发送是否完成功能
//        Thread taskEndListener = new Thread(new TaskFinishedListener(event));
//        taskEndListener.start();
//
//        //开启任务异常监听器（异常丢失信息时）
//        Thread taskFinishedStatusListener = new Thread(new TaskFinishedStatusListener(event));
//        taskFinishedStatusListener.start();
//
//        // 更新标签+客群+会员对应的人数
//        try {
//            processor.getMetaDataTask().getTaskService().updateTagsMemberNums(processor.getMetaDataTask());
//        } catch (Exception e) {
//            log.error("启动任务获取ES会员，更新获取标签+客群+会员对应的人数时发生错误.", e);
//        }
//    }
//
//    /**
//     * 任务结束时操作处理
//     *
//     * @param event
//     */
//    @Override
//    public void end(MarketingTaskEvent event) {
//
//    }
//
//    /**
//     * 从标签域里取出会员数据后通过多线程进行会员发送元数据组装，
//     * 组装完后将元数据发送至MQ
//     */
//    public class Worker implements Runnable {
//
//        private final List<UserFromEsDTO> members;
//        private final SendSelectedUserForMetaDataProducer producer;
//        private final int partitionSize = 70;
//        private final MarketingTaskEvent event;
//        private final CountDownLatch countDownLatch;
//
//        public Worker(CountDownLatch countDownLatch, MarketingTaskEvent event
//                , List<UserFromEsDTO> members, SendSelectedUserForMetaDataProducer producer) {
//            this.countDownLatch = countDownLatch;
//            this.event = event;
//            this.members = members;
//            this.producer = producer;
//        }
//
//        @Override
//        public void run() {
//            try {
//                // 过滤掉在安全配置里配置的会员
//                List<UserFromEsDTO> availableMembers = members.stream().filter(
//                        memberEsDTO -> !this.event.isFilter(memberEsDTO.getMemberId(), this.event.getTenantId())
//                ).collect(Collectors.toList());
//
//                if (CollectionUtil.isEmpty(availableMembers)) {
//                    return;
//                }
//                List<List<UserFromEsDTO>> parts = Lists.partition(availableMembers, partitionSize);
//                parts.stream().forEach(ms -> {
//                    producer.sendToMetaDataHandler(event, ms);
//                    // 会员人数累加，这里需要考虑当上面一行发送失败时，需要减去ms size
//                    addMemberNums(ms.size());
//                });
//            } finally {
//                countDownLatch.countDown();
//            }
//        }
//
//    }
//
//    /**
//     * 任务完成状态改变监听处理
//     */
//    public class TaskFinishedListener implements Runnable {
//        // 开始任务时事件对象
//        private final MarketingTaskEvent startTaskEvent;
//
//        public TaskFinishedListener(MarketingTaskEvent startTaskEvent) {
//            this.startTaskEvent = startTaskEvent;
//        }
//
//        @Override
//        public void run() {
//            RedisService redisService = SpringContextHolder.getBean("redisService");
//
//            MarketingActiveService service = (MarketingActiveService) startTaskEvent
//                    .getMetaDataTask().getTaskService();
//            log.info("主动营销任务监听任务【key:" + startTaskEvent.getMetaDataTask().getKey()
//                    + "】发送情况【开始监听】");
//
//            while (true) {
//                String produceNumsObj = (String) redisService.get(startTaskEvent.getRedisProduceMemberNums());
//                String consumeNumsObj = (String) redisService.get(startTaskEvent.getRedisConsumeMemberNums());
//
//                log.info("主动营销任务监听任务【key:" + startTaskEvent.getMetaDataTask().getKey()
//                        + "】发送情况consumeNums：" + consumeNumsObj);
//
//                if (Objects.nonNull(produceNumsObj) && Objects.nonNull(consumeNumsObj)) {
//                    int produceNums = Integer.valueOf(produceNumsObj);
//                    int consumeNums = Integer.valueOf(consumeNumsObj);
//                    if (produceNums == consumeNums) {
//                        //当发送人数和已发送人数相同时通知异常处理线程退出
//                        exitExceptionThread = true;
//                        break;
//                    }
//                }
//                try {
//                    Thread.sleep(30000L);
//                } catch (Exception e) {
//
//                }
//            }
//            log.info("主动营销任务监听任务【key:" + startTaskEvent.getMetaDataTask().getKey()
//                    + "】发送情况线程【结束监听】");
//
//            // 任务执行完成，处理完成后的事项
//            AbstractTaskProcessor processor = new MarketingTaskEndProcessor(startTaskEvent.getMetaDataTask());
//            processor.doProcess();
//
//            log.info("主动营销任务【key:" + startTaskEvent.getMetaDataTask().getKey() + "】执行完成后" +
//                    "，更新任务状态、销毁各发生引擎各处理器【处理完成】");
//        }
//    }
//
//    public class TaskFinishedStatusListener implements Runnable {
//        // 开始任务时事件对象
//        private final MarketingTaskEvent startTaskEvent;
//
//        public TaskFinishedStatusListener(MarketingTaskEvent startTaskEvent) {
//            this.startTaskEvent = startTaskEvent;
//        }
//
//        private Integer priorConsumeNums = -1;
//
//        @Override
//        public void run() {
//            RedisService redisService = SpringContextHolder.getBean("redisService");
//
//            while (!exitExceptionThread) {
//                Object consumeNumsObj = redisService.get(startTaskEvent.getRedisConsumeMemberNums());
//
//                log.info("主动营销任务异常监听器：监听任务【key:" + startTaskEvent.getMetaDataTask().getKey()
//                        + "】发送情况consumeNums：" + consumeNumsObj);
//                int consumeNums;
//                if (consumeNumsObj == null) {
//                    consumeNums = 0;
//                } else {
//                    consumeNums = Integer.valueOf(String.valueOf(consumeNumsObj));
//                }
//                if (priorConsumeNums == consumeNums) {
//                    log.info("主动营销任务异常监听器【key:" + startTaskEvent.getMetaDataTask().getKey()
//                            + "】发送情况线程【结束监听】");
//                    redisService.set(startTaskEvent.getRedisProduceMemberNums(), String.valueOf(consumeNums));
//                    break;
//                }
//                priorConsumeNums = consumeNums;
//
//                try {
//                    Thread.sleep(300000L);
//                } catch (Exception e) {
//
//                }
//            }
//            log.info("主动营销任务异常监听器【key:" + startTaskEvent.getMetaDataTask().getKey()
//                    + "】发送情况线程【结束监听】");
//        }
//    }
//
//}
