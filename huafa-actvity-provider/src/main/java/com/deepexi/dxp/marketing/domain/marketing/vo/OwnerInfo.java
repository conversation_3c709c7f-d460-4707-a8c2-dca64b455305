package com.deepexi.dxp.marketing.domain.marketing.vo;

import lombok.Data;

import java.util.Date;

@Data
public class OwnerInfo {
    /**
     * 成为业主时间
     */
    private Date createTime;
    
    /**
     * 业主类型（10业主、20同住人、30租客）
     */
    private Integer customerType;
    
    /**
     * 房间id
     */
    private String houseId;
    
    /**
     * 房间名
     */
    private String houseNo;
    
    /**
     * 是否一手业主 0：否，1：是
     */
    private Integer isFirst;
    
    /**
     * 房间业态ID
     */
    private String productId;
    
    /**
     * 房间业态名称
     */
    private String productName;
    
    /**
     * 项目id
     */
    private String projectId;
}