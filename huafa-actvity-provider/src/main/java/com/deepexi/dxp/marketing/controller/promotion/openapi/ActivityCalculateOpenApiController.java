//package com.deepexi.dxp.marketing.controller.promotion.openapi;
//
//
//import com.deepexi.dxp.marketing.domain.promotion.dto.activity.CommodityActivityVO;
//import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.*;
//import com.deepexi.dxp.marketing.domain.promotion.dto.coupon.PromotionCouponLoggerListPostResponseDTO;
//import com.deepexi.dxp.marketing.domain.promotion.request.activity.CommodityActivityRequest;
//import com.deepexi.dxp.marketing.domain.promotion.request.calculate.ActivityOrderParamsRequest;
//import com.deepexi.dxp.marketing.domain.promotion.request.calculate.ActivityParamsRequest;
//import com.deepexi.dxp.marketing.domain.promotion.request.calculate.CommodityListActivityRequest;
//import com.deepexi.dxp.marketing.service.promotion.ActivityCalculateMiddleService;
//import com.deepexi.dxp.marketing.service.promotion.PromotionCouponLoggerMiddleService;
//import com.deepexi.dxp.marketing.service.promotion.PromotionCouponMiddleService;
//import com.deepexi.util.config.Payload;
//import com.deepexi.util.pageHelper.PageBean;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//import org.springframework.web.bind.annotation.*;
//
//import javax.annotation.Resource;
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * <AUTHOR> xinjian.yao
// * @date 2019/12/7 14:47
// */
//@RestController
//@Slf4j
//@RequestMapping("/open-api/v1/promotion-activity/calculate")
//@Api(description = "促销活动计算", value = "促销活动计算", tags = "PromotionActivityCalculateSdk")
//public class ActivityCalculateOpenApiController {
//
//
//    @Autowired
//    private ActivityCalculateMiddleService activityCalculateMiddleService;
//
//    @Autowired
//    private PromotionCouponMiddleService promotionCouponMiddleService;
//
//    @Autowired
//    private PromotionCouponLoggerMiddleService promotionCouponLoggerMiddleService;
//
//    @Resource
//    private ThreadPoolTaskExecutor threadExecutor;
//
//    /**
//     * 活动下的商品信息
//     *
//     * @param activityIds 活动的id
//     * @return 返回活动的配置信息 里面包含商品信息，然后去应用层调用商品域的信息 就可以获取到详情的商品数据了
//     */
//    @PostMapping("/activity-commodity")
//    @ApiOperation(value = "活动下的商品信息", nickname = "activityCalculateGetCommodityByActivityId")
//    public Payload<List<ActivityConfigDTO>> getCommodityByActivityId(@RequestBody List<Long> activityIds) {
//        List<ActivityConfigDTO> resDTOList = activityCalculateMiddleService.getCommodityByActivityId(activityIds);
//        return new Payload<>(resDTOList);
//    }
//
//    /**
//     * 商品可参与活动
//     *
//     * @param vo 用户商品的信息
//     * @return 返回商品是否可以参与活动
//     */
//    @PostMapping("/commodity-activity")
//    @ApiOperation(value = "商品可参与活动", nickname = "activityCalculateCommodityActivity")
//    public Payload<CommodityActivityVO> commodityActivity(@RequestBody CommodityActivityRequest vo) {
//        CommodityActivityVO commodityActivityVO = activityCalculateMiddleService.commodityActivity(vo);
//
//        return new Payload<>(commodityActivityVO);
//    }
//
//    /**
//     * 多个商品可参与活动
//     *
//     * @param vo 用户和多个商品的数据信息
//     * @return 多个商品的活动信息
//     */
//    @PostMapping("/commodity-listActivity")
//    @ApiOperation(value = "多个商品可参与活动", nickname = "activityCalculateCommodityListActivity")
//    public Payload<List<CommodityActivityVO>> commodityListActivity(@RequestBody CommodityListActivityRequest vo) {
//        List<CommodityActivityVO> commodityActivityVOList = activityCalculateMiddleService.commodityListActivity(vo);
//
//        return new Payload<>(commodityActivityVOList);
//    }
//
//    /**
//     * 购物车计算
//     *
//     * @param vo 用户商品信息
//     * @return 计算返回结果
//     */
//    @PostMapping("/shopping-cart")
//    @ApiOperation(value = "购物车", nickname = "activityCalculateShoppingCart")
//    public Payload<ShoppingCartVO> shoppingCart(@RequestBody ActivityParamsRequest vo) {
//        ShoppingCartVO shoppingCartVO = activityCalculateMiddleService.calculate(vo);
//
//        return new Payload<>(shoppingCartVO);
//    }
//
//    /**
//     * 活动计算：订单计算
//     *
//     * @param activityRequestParams 订单入参
//     * @return 订单出参
//     */
//    @PostMapping("/order-calculate")
//    @ApiOperation(value = "订单计算", nickname = "activityCalculateOrderCalculate")
//    public Payload<OrderEditResponseDTO> orderCalculate(@RequestBody ActivityOrderParamsRequest activityRequestParams) {
//        OrderEditResponseDTO orderEditResponseDTO = activityCalculateMiddleService.orderCalculate(activityRequestParams);
//        if (null == orderEditResponseDTO) {
//            return null;
//        }
//        return new Payload<>(orderEditResponseDTO);
//    }
//
//    /**
//     * 积分兑换活动
//     *
//     * @param vo   活动参数信息
//     * @param page 页数
//     * @param size 大小
//     * @return
//     */
//    @PostMapping("/show-all")
//    @ApiOperation(value = "积分兑换活动", nickname = "activityCalculateShowJfdh")
//    public Payload<PageBean<ActivityResponseParamsDTO>> showJfdh(@RequestBody ActivityParamsRequest vo,
//                                                                 @RequestParam(value = "page", defaultValue = "0") Integer page,
//                                                                 @RequestParam(value = "size", defaultValue = "10") Integer size) {
//        PageBean<ActivityResponseParamsDTO> list = activityCalculateMiddleService.showJFDH(vo, page, size);
//        return new Payload<>(list);
//    }
//
//    /**
//     * 进行积分兑换活动
//     *
//     * @param vo 获取请求信息
//     * @return 积分兑换返回结果
//     */
//    @PostMapping("/exchange")
//    @ApiOperation(value = "进行积分兑换商品", nickname = "activityCalculateExchange")
//    public Payload<ActivityResponseParamsDTO> exchange(@RequestBody ActivityParamsRequest vo) {
//        ActivityResponseParamsDTO activityResponseParamsDTO = activityCalculateMiddleService.calculateJFDH(vo);
//        if (null == activityResponseParamsDTO) {
//            return null;
//        }
//        return new Payload<>(activityResponseParamsDTO);
//    }
//
//    /**
//     * 购物券的列表信息
//     *
//     * @param vo   参数信息
//     * @param page 页数
//     * @param size 参数
//     * @return
//     */
//    @PostMapping("/coupon-list")
//    @ApiOperation(value = "购物券list", nickname = "activityCalculateCouponList")
//    public Payload<PageBean<CouponResponseDTO>> couponList(@RequestBody ActivityParamsRequest vo,
//                                                           @RequestParam(value = "page") Integer page,
//                                                           @RequestParam(value = "size") Integer size) {
//        PageBean<CouponResponseDTO> list = activityCalculateMiddleService.couponList(vo, page, size);
//        return new Payload<>(list);
//    }
//
//    /**
//     * 订单页面用户可用优惠券列表
//     *
//     * @param vo 入参
//     * @return 出 参数
//     */
//    @PostMapping("/usable-coupon-list")
//    @ApiOperation(value = "订单页面用户可用优惠券列表", nickname = "activityCalculateUsableCouponList")
//    public Payload<PageBean<PromotionCouponLoggerListPostResponseDTO>> usableCouponList(@RequestBody ActivityParamsRequest vo) {
//        // 这里不能使用clone工具，由于brandId 两个字段类型不匹配，一个是String 一个是Long
//        PageBean<PromotionCouponLoggerListPostResponseDTO> list =
//                activityCalculateMiddleService.usableCouponList(vo);
//        if (null == list) {
//            return new Payload<>(new PageBean<>(new ArrayList<>()));
//        }
//        return new Payload<>(list);
//    }
//
//    /**
//     * 用户积分兑换次数
//     *
//     * @param appId    应用id
//     * @param tenantId 渠道id
//     * @param userId   用户id
//     * @param userType 用户类型
//     * @return 积分兑换的次数
//     */
//    @GetMapping("/get-jfdh-times")
//    @ApiOperation(value = "积分兑换次数", nickname = "activityCalculateGetJFDHTimes")
//    public Payload<Integer> getJFDHTimes(Long appId, String tenantId, Long userId, String userType) {
//        return new Payload<>(activityCalculateMiddleService.getJFDHTimes(appId, tenantId, userId, userType));
//    }
//}
