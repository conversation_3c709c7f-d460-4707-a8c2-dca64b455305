package com.deepexi.dxp.marketing.service.specify.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityTargetQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityTargetRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityTargetResponseDTO;
import com.deepexi.dxp.marketing.service.specify.ActivityTargetService;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityTargetDAO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityTargetDO;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import com.deepexi.util.pojo.CloneDirection;
import com.deepexi.util.pojo.ObjectCloneUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@Slf4j
public class ActivityTargetServiceImpl implements ActivityTargetService {

	@Autowired
	private ActivityTargetDAO activityTargetDAO;

	@Override
	public Boolean create(ActivityTargetRequestDTO dto) {

		ActivityTargetDO targetDO = dto.clone(ActivityTargetDO.class);

		LambdaQueryWrapper<ActivityTargetDO> queryWrapper = new LambdaQueryWrapper<>();


		if (Objects.nonNull(targetDO.getId())) {
			LambdaQueryWrapper<ActivityTargetDO> wrapper = queryWrapper.eq(ActivityTargetDO::getId, dto.getId());
			ActivityTargetDO one = activityTargetDAO.getOne(wrapper);
			if (dto.getTargetName().equals(one.getTargetName())) {
				return activityTargetDAO.updateById(targetDO);
			} else {
				LambdaQueryWrapper<ActivityTargetDO> query = new LambdaQueryWrapper<>();
				query.eq(ActivityTargetDO::getTargetName, dto.getTargetName());
				ActivityTargetDO activityTargetDO = activityTargetDAO.getOne(query);
				if (Objects.isNull(activityTargetDO)) {
					return activityTargetDAO.updateById(targetDO);
				} else {
					throw new ApplicationException("名称不能重复");
				}

			}
		} else {
			if (Objects.nonNull(dto.getTargetName())) {
				queryWrapper.eq(ActivityTargetDO::getTargetName, dto.getTargetName());
				ActivityTargetDO one = activityTargetDAO.getOne(queryWrapper);
				if (Objects.nonNull(one)) {
					throw new ApplicationException("名称不能重复");
				}
			}
			return activityTargetDAO.save(targetDO);
		}
	}

	@Override
	public PageBean<ActivityTargetResponseDTO> getPage(ActivityTargetQuery query) {
		PageBean<ActivityTargetDO> page = activityTargetDAO.getPage(query);
		if (Objects.nonNull(page)) {
			PageBean<ActivityTargetResponseDTO> activityTargetResponseDTOList = ObjectCloneUtils.convertPageBean(page, ActivityTargetResponseDTO.class);
			return activityTargetResponseDTOList;
		}
		return new PageBean<ActivityTargetResponseDTO>();
	}

	@Override
	public ActivityTargetResponseDTO getById(Long id) {
		ActivityTargetDO activityTargetDO =  activityTargetDAO.getById(id);
		if(Objects.isNull(activityTargetDO)){
			throw new ApplicationException("数据不存在");
		}
		return activityTargetDO.clone(ActivityTargetResponseDTO.class, CloneDirection.OPPOSITE);
	}
}
