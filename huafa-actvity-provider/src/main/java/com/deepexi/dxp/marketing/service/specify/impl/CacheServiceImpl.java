package com.deepexi.dxp.marketing.service.specify.impl;

import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityQuery;
import com.deepexi.dxp.marketing.enums.resource.ActivityStatusEnum;
import com.deepexi.dxp.marketing.manager.promotion.PromotionActivityManager;
import com.deepexi.dxp.marketing.service.specify.CacheService;
import com.deepexi.dxp.middle.promotion.dao.PromotionActivityDAO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class CacheServiceImpl implements CacheService {

    @Autowired
    private PromotionActivityDAO promotionActivityDAO;

    @Autowired
    private PromotionActivityManager promotionActivityManager;

    @Override
    public Boolean updateCacheActInfo(Long id){

        PromotionActivityQuery query = new PromotionActivityQuery();
        query.setPage(1);
        query.setSize(9999999);
        query.setId(id);
        List<PromotionActivityDO> actList = promotionActivityDAO.findAllPublishActivityList(query).getContent();
        for(PromotionActivityDO pdo:actList){

            if (
                pdo.getStatus().equals(Integer.parseInt(ActivityStatusEnum.UN_START.getId())) ||
                pdo.getStatus().equals(Integer.parseInt(ActivityStatusEnum.IN_PROGRESS.getId()))
            ){
                promotionActivityManager.forceCacheActInfo(pdo.getId());
            }else{
                promotionActivityManager.forceDelCacheActInfo(pdo.getId());
            }
        }
        return Boolean.TRUE;
    }


}
