package com.deepexi.dxp.marketing.service.specify;

import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityFissionLogQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.PartakeLogQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.*;
import com.deepexi.dxp.marketing.domain.marketing.response.*;
import com.deepexi.dxp.marketing.domain.promotion.dto.CommunityActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.CreateSignCodeDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.OneCodeAuthDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.*;
import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityPageQuery;
import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityQuery;
import com.deepexi.dxp.marketing.domain.promotion.request.ActivityAuditRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.CancelActivityRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.PromotionActivityCreateRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.PromotionActivityUpdateRequest;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityLimitDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityPartakeLogDO;
import com.deepexi.util.pageHelper.PageBean;

import java.util.List;

public interface PromotionActivityService {

    /**
     * 活动列表
     * @param query
     * @return
     */
    PageBean<PromotionActivityVO> findPage(PromotionActivityQuery query);

    /**
     * 活动列表
     * @param query
     * @return
     */
    List<PromotionActivityVO> list(PromotionActivityQuery query);

    /**
     * 分页查询全部活动列表
     * @param query
     * @return
     */
    PageBean<PromotionActivityDTO> findWholePageList(PromotionActivityPageQuery query);

    /**
     * 创建促销活动
     *
     * @param dto 入参dto 需要转换成po
     * @return 添加是否成功
     */
    Long create(PromotionActivityCreateRequest dto);

    /**
     * 删除活动
     * @param ids
     * @return
     */
    Boolean delete(List<Long> ids);

    /**
     * 根据id修改促销活动
     *
     * @param dto 活动dto
     * @return 是非修改成功
     */
    Boolean updateActivityById(PromotionActivityUpdateRequest dto);

    /**
     * 获取已到达启动时间的活动
     */
    List<PromotionActivityDO> getListByShouldStart();

    /**
     * 修改已到达启动时间的活动
     */
    Boolean updateStatusByStartTime();

    /**
     * 获取已到达结束时间的活动
     */
    List<PromotionActivityDO> getListByShouldFinish();

    /**
     * 完成已到达结束时间的活动
     */
    Boolean updateStatusByEndTime();

    /**
     * 终止活动
     * @param id
     * @param status
     * @return
     */
    boolean updateStatus(Long id, String status);

    /**
     * 更改活动状态
     * @param requestVo
     * @return
     */
    boolean seckillUpdateStatus(PromotionActivityUpdateStatusRequest requestVo);

    /**
     * @param id 活动id
     * @return 改活动的具体信息
     */
    PromotionActivityDetailDTO getActivityById(Long id);

    /**
     * 用户参与活动前的校验数据
     * @param dto
     * @return
     */
    Boolean partakeActCheck(ReceiveCouponRequestDTO dto);

    /**
     * 用户参与活动
     * @param dto
     * @return
     */
    Data<Object> partakeAct(ActivityPartakeRequest dto);

    /**
     * 活动参与记录
     * @param query
     * @return
     */
    PageBean<ActivityPartakeLogResponseDTO> actPartakeLogList(PartakeLogQuery query);

    /**
     * 个人活动列表
     * @param query
     * @return
     */
    PageBean<PromotionActivityMiniVO> findMyActivityList(PromotionActivityQuery query);

    /**
     * 获取所有进行中的活动列表
     * @param query
     * @return
     */
    PageBean<PromotionActivityVO> getAllRunningActivityList(PromotionActivityQuery query);

    ActivityUserRelatedDTO getActivityUserRelated(ActivityPartakeRequest query, List<PromotionActivityLimitDO> promotionActivityLimitList,String paTemplateId);


    /**
     * 排行榜
     * @param activityId
     * @return
     */
    List<FissionRankResponseDTO> rankList(Long activityId);


    /**
     * 裂变好友列表
     * @param query
     * @return
     */
    PageBean<ActivityFissionLogResponseDTO> fissionFriendsList(ActivityFissionLogQuery query);

    String getUUID(String key);

    /**
     * 有效期结束，检验活动是否成功
     * @param partakeLog
     * @return
     */
    Integer checkFissionEndTime(ActivityPartakeLogDTO partakeLog,String name);

    //助力发放奖品
    void grantResource(PromotionActivityDO promotionActivityDO);

    /**
     * 获取参与明细导出数据
     * @param query
     * @return
     */
    ExcelExportResponseDTO getExportPartakeLogExcelData(PartakeLogQuery query);

    /**
     * 获取活动列表导出数据
     * @param query
     * @return
     */
    ExcelExportResponseDTO getExportActivityListExcelData(PromotionActivityPageQuery query);

    /**
     * 校验用户帮助砍价
     * @param partakeLogId
     * @param phone
     * @param activityId
     * @param helpTimes
     * @return
     */
    Boolean helpTimesCheck(Long partakeLogId,String phone,Long activityId,Integer helpTimes);


    /**
     * 抖音小程序端活动列表
     * 抖音小程序端，活动直接发布到抖音的活动列表页，跟置业通是一样的，但是已经结束的活动列表不展示
     * @param query
     * @return
     */
    PageBean<PromotionActivityVO> getTiktokActivityList(PromotionActivityQuery query);

    /**
     * 活动结束,结束正在砍价中的砍价活动
     * @param promotionActivityDO
     */
    void checkBargainEndTime(PromotionActivityDO promotionActivityDO);

    /**
     * 短信模板消息发送
     * @param mobile
     * @param userName
     * @param coupon
     * @param code
     * @param templateCode
     */
    void templateSend(String mobile, String userName, String coupon, String code, String templateCode);

    void minSubscribeNews(String templateParam, String userOpenId, String userName,String templateId);

    /**
     * 小程序订阅消息
     * @param promotionActivityDO
     * @param partakeLogById
     */
    void miniTemplateNews(PromotionActivityDO promotionActivityDO, ActivityPartakeLogDO partakeLogById);

    /**
     * 通过用户ID获取活动ID
     * @param userId
     * @return
     */
    List<Long> getActivityIdsByUserId(String userId);
    List<Long> getActivityIdsByUserIdV2();

    /**
     * 更改上下架状态
     * @param id
     * @param status
     * @return
     */
    boolean updateOnOffStatus(Long id, String status,String deliveryChannel);

    Boolean receiveNow(FissionReceiveNowDTO dto);

    ActivityHomeInfoResponseDTO homeInfo(ActivityHomeInfoRequest query);

    Boolean switchProject(SwitchProjectDTO dto);

    Boolean whiteList(WhiteListRequestDto dto);

    Object share(ShareLuckyDrawRequestDTO dto);

    CreateSignCodeDTO createSignCode(Long id);

    List<OneCodeAuthDTO> scanList(Long id);

    List<CommunityActivityDTO> getByCommunity(List<Long> ids, boolean fetchDetail);

    List<String> tagList(String deliveryChannel, String cityId);

    Boolean audit(ActivityAuditRequest dto);

    Boolean signIn(String scene, String userId);

    List<PromotionActivityDO> getSignUpActivityCheckList();

    Boolean cancelAct(CancelActivityRequest dto);

    Boolean toggleTopStatus(Long id, Integer status);

    Boolean cancelActByAdmin(CancelActivityRequest dto);

    void fillActivityCommonInfo(List dtoList);
}
