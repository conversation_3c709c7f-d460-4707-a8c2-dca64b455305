package com.deepexi.dxp.marketing.manager.promotion.impl.limit;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityCommodityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityConfigDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.TemplateLimitDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityParamsDTO;
import com.deepexi.dxp.marketing.enums.activity.limit.PATemplateNumberEnum;
import com.deepexi.dxp.marketing.manager.promotion.impl.BaseLimit;
import com.deepexi.dxp.middle.promotion.domain.dto.CommodityNumLimitDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>.cai
 * @date created in 14:54 2019/11/26
 */
@Slf4j
public class CalculateNumberLimit extends BaseLimit {

    private List<NumberLimitEnumsCalculate> calculateHelper = new ArrayList<>();

    public CalculateNumberLimit(TemplateLimitDTO templateLimitDTO, ActivityConfigDTO activity, ActivityParamsDTO params) {
        super(templateLimitDTO, activity, params);
    }

    @Override
    public Boolean calculate() {
        // 活动存储的值
        List<BaseActivityDTO> activityNumberLimit = super.getActivity().getNumberLimit();
        // 需求比较的值
        ActivityParamsDTO params = super.getParams();
        // 初始化计算信息
        init();
        return calculateNumberLimit(activityNumberLimit, params);
    }


    private interface NumberLimitEnumsCalculate {
        /**
         * 计算接口
         *
         * @param baseActivityDTO 设计活动
         * @param params          传过来的参数
         * @return 不同类型是非成功
         */
        Boolean calculate(BaseActivityDTO baseActivityDTO, ActivityParamsDTO params);
    }

    /**
     * 单日次数限制
     *
     * @return 枚举类里面单日次数限制 类型的处理方法
     */
    private NumberLimitEnumsCalculate dayNumEnum() {
        return (baseActivity, params) -> {
            // 活动进来的id 和枚举类的id不一样时 返回null
            if (!PATemplateNumberEnum.DRCS.getId().equals(baseActivity.getId())) {
                return null;
            }
            int dayNum = Optional.ofNullable(params.getDayNum())
                    .orElse(0);
            int param = Integer.parseInt(baseActivity.getValue());
            return param > dayNum;
        };
    }

    /**
     * 活动总次数限制
     *
     * @return 枚举类里面总次数限制 类型的处理方法
     */
    private NumberLimitEnumsCalculate totalNumEnum() {
        return (baseActivity, params) -> {
            // 活动进来的id 和枚举类的id不一样时 返回null
            if (Objects.nonNull(baseActivity)
                    && !PATemplateNumberEnum.ZCS.getId().equals(baseActivity.getId())) {
                return null;
            }
            int totalNum = Optional.ofNullable(params.getTotalNum()).orElse(0);
            int param = Integer.parseInt(baseActivity.getValue());
            return param > totalNum;
        };
    }

    /**
     * 活动商品的购买次数限制
     *
     * @return 枚举类里面总次数限制 类型的处理方法
     */
    private NumberLimitEnumsCalculate commodityTotalNumEnum() {
        //例子json: [{"id": "commodityNum", "flag": "<=", "value": [{"upId": "313", "limit": "2"}, {"upId": "233", "limit": "5"}, {"upId": "212", "limit": "2"}, {"upId": "454", "limit": "3"}]}]
        return (baseActivity, params) -> {
            // 活动进来的id 和枚举类的id不一样时 返回null
            if (Objects.nonNull(baseActivity)
                    && !PATemplateNumberEnum.COMMODITY_NUM.getId().equals(baseActivity.getId())) {
                return null;
            }
            ActivityCommodityDTO commoditie = params.getCommoditie();
            String commodityListJson = baseActivity.getValue();
            if (StringUtils.isEmpty(commodityListJson)) {
                log.info("商品每人限购为空");
                return null;
            }
            List<CommodityNumLimitDTO> commodityList = JSON.parseArray(commodityListJson, CommodityNumLimitDTO.class);
            //遍历找到该商品的每人次数限制
            CommodityNumLimitDTO commodityNumLimitDTO = commodityList.stream().filter(item -> StringUtils.isNotEmpty(item.getUpId()) && NumberUtil.parseLong(item.getUpId()) == commoditie.getUpId().longValue())
                    .findFirst()
                    .orElse(null);
            if (commodityNumLimitDTO == null || NumberUtil.parseInt(commodityNumLimitDTO.getLimit()) <= 0) {
                log.info("该商品{}不限购", commoditie.getUpId());
                return true;
            }

            //已购买的次数
            Integer commonditieBuyNums = params.getCommoditieBuyNum();
            Integer limitBuyCount = Integer.parseInt(commodityNumLimitDTO.getLimit());
            if ((commonditieBuyNums + commoditie.getSkuAmount()) > limitBuyCount) {
                log.error("userId: {},upId:{} 已购买数{} + 本次数量{} 大于限制次数 , limitBuyCount:{}", params.getUserId(), commoditie.getUpId(), commonditieBuyNums, commoditie.getSkuAmount(), limitBuyCount);
                return false;
            }
            return true;
        };
    }

    /**
     * 枚举类每添加一种类型，都需要再这里初始化这种类型的处理结果，不然活动选择那种类型 会报错
     */
    private void init() {
        calculateHelper.add(totalNumEnum());
        calculateHelper.add(dayNumEnum());
        calculateHelper.add(commodityTotalNumEnum());
    }


    private Boolean calculateNumberLimit(List<BaseActivityDTO> activityNumberLimit, ActivityParamsDTO params) {
        for (BaseActivityDTO baseActivityDTO : activityNumberLimit) {
            Boolean calculateResult = numberCalculate(baseActivityDTO, params);
            if (Boolean.FALSE.equals(calculateResult)) {
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    /**
     * @param baseActivityDTO 活动次数限制类型的活动信息
     * @param params          当前活动获得的值
     * @return 计算的入口
     */
    private Boolean numberCalculate(BaseActivityDTO baseActivityDTO, ActivityParamsDTO params) {
        // 循环执行枚举下的活动逻辑
        for (NumberLimitEnumsCalculate numberLimitEnumsCalculate : calculateHelper) {
            Boolean result = numberLimitEnumsCalculate.calculate(baseActivityDTO, params);
            boolean existFlag = Optional.ofNullable(result)
                    .isPresent();
            if (existFlag) {
                return result;
            }
        }
        return Boolean.FALSE;
    }

}
