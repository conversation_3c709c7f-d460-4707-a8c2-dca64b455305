package com.deepexi.dxp.marketing.service.specify;

import com.deepexi.dxp.marketing.domain.marketing.response.ExcelExportResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.ResourceHvImportLogResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.query.specify.ResourceHvImportLogQuery;
import com.deepexi.dxp.marketing.domain.promotion.request.specify.PromotionResourceImportRequestDTO;
import com.deepexi.util.pageHelper.PageBean;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public interface ResourceHvImportLogService {

    /**
     * 列表
     * @param query
     * @return
     */
    PageBean<ResourceHvImportLogResponseDTO> findPage(ResourceHvImportLogQuery query);

    /**
     * 导入
     * @param promotionResourceImportRequestDTO
     * @return
     */
    Boolean importResource(PromotionResourceImportRequestDTO promotionResourceImportRequestDTO);

    /**
     * 失败记录导出
     * @param response
     * @param id
     */
    void exportExcel(HttpServletResponse response,Long id);

    /**
     * 下载导入模板
     */
    void exportTemplate(Integer type,
                        HttpServletRequest request, HttpServletResponse response);

    /**
     * 获取房源券导入失败记录数据
     * @param id
     * @return
     */
    ExcelExportResponseDTO getExportExcelData(Long id);
}
