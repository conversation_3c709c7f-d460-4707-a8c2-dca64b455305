package com.deepexi.dxp.marketing.controller.specify.adminapi;
import com.deepexi.dxp.marketing.common.base.utils.CommonExceptionCode;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.utils.hfoss.HuafaAmazonOssUtil;
import com.deepexi.util.exception.ApplicationException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/admin-api/v1/oss")
@Api(value = "oss服务", tags = "oss服务")
@Slf4j
public class OssController {

    @Autowired
    private HuafaAmazonOssUtil huafaAmazonOssUtil;

    @PostMapping("/fileUpload")
    @ApiOperation("上传")
    public Data<String> fileUpload(@RequestParam(value = "file") MultipartFile multipartFile, String fileDir) {
        try {
            return new Data(huafaAmazonOssUtil.upload(multipartFile));
        } catch (Exception e) {
            e.printStackTrace();
            log.error("{}文件上传失败！", multipartFile.getOriginalFilename());
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "文件上传失败!");
        }
    }
}
