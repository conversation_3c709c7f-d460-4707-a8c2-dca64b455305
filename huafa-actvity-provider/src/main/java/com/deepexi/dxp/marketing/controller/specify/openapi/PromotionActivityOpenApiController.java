package com.deepexi.dxp.marketing.controller.specify.openapi;

import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.cnhuafas.common.annotation.DistributedLock;
import com.deepexi.dxp.marketing.common.base.utils.CommonExceptionCode;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.constant.RedisConstants;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityFissionLogQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.*;
import com.deepexi.dxp.marketing.domain.marketing.response.*;
import com.deepexi.dxp.marketing.domain.promotion.dto.CommunityActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.CreateSignCodeDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.OneCodeAuthDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.PromotionActivityMiniVO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.PromotionActivityVO;
import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityQuery;
import com.deepexi.dxp.marketing.domain.promotion.request.CancelActivityRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.PromotionActivityCreateRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.PromotionActivityUpdateRequest;
import com.deepexi.dxp.marketing.enums.activity.strategy.StrategyGroupEnum;
import com.deepexi.dxp.marketing.enums.specify.PlatformTypeEnum;
import com.deepexi.dxp.marketing.geetest.GeetestLib;
import com.deepexi.dxp.marketing.geetest.entity.GeetestLibResult;
import com.deepexi.dxp.marketing.service.specify.ActivityParticipationService;
import com.deepexi.dxp.marketing.service.specify.PromotionActivityService;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.StringUtil;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.deepexi.dxp.marketing.constant.RedisConstants.CACHE_PREV_KEY_ACT_PARTAKE_LOCK;

/**
 * <AUTHOR>
 * @date  2021/05/24 17:54
 */
@RestController
@RequestMapping("/open-api/v1/open/promotion-activity/")
@Api(value = "参与活动接口",tags = {"参与活动接口"})
@Slf4j
public class PromotionActivityOpenApiController {

    @Resource
    private PromotionActivityService promotionActivityService;

    @Autowired
    private ActivityParticipationService activityParticipationService;

    @Resource
    private RedissonClient redissonClient;

    @Value("${geetest.id}")
    private String gtId;
    @Value("${geetest.key}")
    private String gtKey;

    @PostMapping("/partakeActCheck")
    @ApiOperation(value = "活动参与资源领取较验", notes = "活动参与资源领取较验")
    public Data<Boolean> partakeActCheck(@RequestBody ReceiveCouponRequestDTO dto) {
        return new Data<>(promotionActivityService.partakeActCheck(dto));
    }

    @PostMapping("/firstRegister")
    @ApiOperation(value = "获取滑块验证参数")
    public Data<Object> firstRegister(@RequestParam String userId) {
        GeetestLib gtLib = new GeetestLib(gtId,gtKey);
        GeetestLibResult result = gtLib.firstRegister(userId);
        return new Data<>(JSONUtil.parseObj(result.getData()));
    }

    @PostMapping("/partakeAct")
    @ApiOperation(value = "参与活动", notes = "参与活动")
    public Data<Object> partakeAct(@RequestBody @Valid ActivityPartakeRequest dto) {
        //先检验是否合法,优生活上的活动不检查（助力，砍价，集卡活动）
        PromotionActivityDetailDTO actDTO = JSON.parseObject(String.valueOf(redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO + dto.getActivityId()).get()), PromotionActivityDetailDTO.class);
        Assert.notNull(actDTO,"活动已下架或不存在");
        //表单活动可以不作校验
        if (!Objects.equals(StrategyGroupEnum.HF_FORM_ACT.getId(),actDTO.getPaTemplateId().toString()) &&
                !Objects.equals(StrategyGroupEnum.HF_SIGN_UP_ACT.getId(),actDTO.getPaTemplateId().toString())) {
            GeetestLib gtLib = new GeetestLib(gtId,gtKey);
            GeetestLibResult result = gtLib.successValidate(dto.getValidateObj(),dto.getUserId());
            Assert.isTrue(result.getStatus() == 1,"用户校验不通过");
        }

        //加锁
        RLock lock = redissonClient.getLock(CACHE_PREV_KEY_ACT_PARTAKE_LOCK + dto.getActivityId() + dto.getPhone());
        try {
            if (lock.tryLock(100, 10 * 1000, TimeUnit.MILLISECONDS)) {
                return promotionActivityService.partakeAct(dto);
            }
        } catch (IllegalArgumentException e) {
            throw e;
        } catch (ApplicationException e) {
            throw e;
        }  catch (Exception e) {
            log.error("参与活动失败", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        throw new ApplicationException(CommonExceptionCode.PARTICIPATED, "活动太繁忙，请稍后重试");
    }

    @ApiOperation(value = "取消参与活动")
    @PostMapping("/cancelAct")
    @DistributedLock(suffix = "#partakeLogId")
    public Data<Boolean> cancelAct(@Valid @RequestBody CancelActivityRequest dto) {
        dto.setAdmin(false);
        return new Data<>(promotionActivityService.cancelAct(dto));
    }

    @ApiOperation(value = "获取指定用户的活动列表", notes = "获取指定用户的活动列表")
    @PostMapping("/findMyActivityList")
    public Data<PageBean<PromotionActivityMiniVO>> findMyActivityList(@Valid @RequestBody PromotionActivityQuery query) {
        if ((query.getStatus() == null && CollectionUtil.isEmpty(query.getStatusList())) || StringUtil.isEmpty(query.getPhone())) {//活动状态、用户id必须
            return new Data<>(new PageBean<>());
        }
        query.setUserId(null);//以手机号进行查询
        return new Data<>(promotionActivityService.findMyActivityList(query));
    }

    @ApiOperation(value = "获取正在进行中的活动列表", notes = "获取正在进行中的活动列表")
    @PostMapping("/getAllRunningActivityList")
    public Data<PageBean<PromotionActivityVO>> getAllRunningActivityList(@RequestBody PromotionActivityQuery query) {
        return new Data<>(promotionActivityService.getAllRunningActivityList(query));
    }

    /**
     * 根据id查询活动
     *
     * @param id
     * @return
     */
    @GetMapping("/getById")
    @ApiOperation(value = "根据id查询活动")
    public Data<PromotionActivityDetailDTO> getActivityById(@RequestParam Long id) throws Exception {
        return new Data<>(promotionActivityService.getActivityById(id));
    }

    @GetMapping("/getUUID")
    @ApiOperation(value = "获取uuid")
    public Data<String> getUUID(@RequestParam String phone, @RequestParam Long activityId, @RequestParam Long hisResourceId) {
        return new Data<>(promotionActivityService.getUUID(String.format(RedisConstants.CACHE_PREV_KEY_ACT_RESOURCE_INFO_REPEAT, phone, activityId, hisResourceId == null ? 0L : hisResourceId)));
    }

    @GetMapping("/projectListByActivityId")
    @ApiOperation(value = "获取指定活动的项目列表", notes = "获取指定活动的项目列表")
    public Data<List<ActivityParticipationGroupResponseDTO>> projectListByActivityId(@RequestParam Long id) {
        return new Data<>(activityParticipationService.projectGroupList(id));
    }

    @GetMapping("/projectListByUserId")
    @ApiOperation(value = "获取指定用户的项目列表", notes = "获取指定用户的项目列表")
    public Data<List<ActivityParticipationGroupResponseDTO>> projectListByUserId(@RequestParam String userId) {
        return new Data<>(activityParticipationService.projectGroupListByUserId(userId));
    }

    @PostMapping("/rankList")
    @ApiOperation(value = "排行榜", notes = "排行榜")
    public Data<List<FissionRankResponseDTO>> rankList(@RequestParam Long activityId) {
        return new Data<>(promotionActivityService.rankList(activityId));
    }

    @ApiOperation(value = "抖音小程序端活动列表", notes = "抖音小程序端活动列表")
    @PostMapping("/getTiktokActivityList")
    public Data<PageBean<PromotionActivityVO>> getTiktokActivityList(@RequestBody PromotionActivityQuery query) {
        if(query.getDeliveryChannel() == null || !PlatformTypeEnum.DY_MINIPROGRAM.getId().equals(query.getDeliveryChannel())){
            throw new ApplicationException("参数错误");
        }
        return new Data<>(promotionActivityService.getTiktokActivityList(query));
    }

    @PostMapping("/fissionFriendsListPost")
    @ApiOperation(value="裂变好友列表", notes = "裂变好友列表")
    public Data<PageBean<ActivityFissionLogResponseDTO>> fissionFriendsListPost(@RequestBody ActivityFissionLogQuery query) {
        return new Data<>(promotionActivityService.fissionFriendsList(query));
    }

    @PostMapping("/receiveNow")
    @ApiOperation(value="立即领取")
    public Data<Boolean> receiveNow(@RequestBody @Valid FissionReceiveNowDTO dto) {
        //加锁
        RLock lock = redissonClient.getLock(CACHE_PREV_KEY_ACT_PARTAKE_LOCK + dto.getActivityId() + dto.getPhone());
        try {
            if (lock.tryLock(100, 10 * 1000, TimeUnit.MILLISECONDS)) {
                return new Data<>(promotionActivityService.receiveNow(dto));
            }
        } catch (IllegalArgumentException e) {
            throw e;
        } catch (ApplicationException e) {
            throw e;
        } catch (Exception e) {
            log.error("领取奖品失败", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        throw new ApplicationException(CommonExceptionCode.PARTICIPATED, "活动太繁忙，请稍后重试");
    }

    @PostMapping("/homeInfo")
    @ApiOperation(value="移动端-首页信息")
    public Data<ActivityHomeInfoResponseDTO> homeInfo(@Valid @RequestBody ActivityHomeInfoRequest query) {
        return new Data<>(promotionActivityService.homeInfo(query));
    }

    @PostMapping("/whiteList")
    @ApiOperation(value="参与活动白名单")
    public Data<Boolean> whiteList(@Valid @RequestBody WhiteListRequestDto dto) {
        return new Data<>(promotionActivityService.whiteList(dto));
    }

    @PostMapping("/switchProject")
    @ApiOperation(value="切换项目")
    public Data<Boolean> switchProject(@RequestBody @Valid SwitchProjectDTO dto) {
        return new Data<>(promotionActivityService.switchProject(dto));
    }

    @PostMapping("/share")
    @ApiOperation(value="分享活动")
    public Data<Object> share(@RequestBody @Valid ShareLuckyDrawRequestDTO dto) {
        return new Data<>(promotionActivityService.share(dto));
    }

    @PostMapping("/create")
    @ApiOperation(value = "创建活动")
    public Data<Long> create(@Valid @RequestBody PromotionActivityCreateRequest dto) {
        dto.setFromMini(true);
        return new Data<>(promotionActivityService.create(dto));
    }
    @PostMapping("/update")
    @ApiOperation(value = "更新活动")
    public Data<Boolean> update(@RequestBody PromotionActivityUpdateRequest dto) {
        dto.setFromMini(true);
        return new Data<>(promotionActivityService.updateActivityById(dto));
    }

    @PostMapping("/createSignCode")
    @ApiOperation(value = "生成活动签到码")
    @DistributedLock(suffix = "{#id}")
    public Data<CreateSignCodeDTO> createSignCode(@RequestParam Long id) {
        return new Data<>(promotionActivityService.createSignCode(id));
    }

    @PostMapping("/signIn")
    @ApiOperation(value = "活动签到")
    public Data<Boolean> signIn(@RequestParam String scene, @RequestParam String phone) {
        return new Data<>(promotionActivityService.signIn(scene, phone));
    }

    @GetMapping("/scanList")
    @ApiOperation(value = "扫码签到记录")
    public Data<List<OneCodeAuthDTO>> scanList(@RequestParam Long id) {
        return new Data<>(promotionActivityService.scanList(id));
    }

    @GetMapping("/getByCommunity")
    @ApiOperation(value = "根据社群Id列表返回活动数量")
    public Data<List<CommunityActivityDTO>> getByCommunity(@RequestParam List<Long> ids, boolean fetchDetail) {
        return new Data<>(promotionActivityService.getByCommunity(ids, fetchDetail));
    }

    @GetMapping("/tagList")
    @ApiOperation(value = "根据发布渠道获取活动标签")
    public Data<List<String>> tagList(@RequestParam String deliveryChannel,String cityId) {
        return new Data<>(promotionActivityService.tagList(deliveryChannel,cityId));
    }
}