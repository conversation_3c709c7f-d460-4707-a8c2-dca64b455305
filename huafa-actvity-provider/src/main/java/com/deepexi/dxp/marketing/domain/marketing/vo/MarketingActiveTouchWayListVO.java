package com.deepexi.dxp.marketing.domain.marketing.vo;


import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 主动营销触达方式
 * @Author: HuangBo.
 * @Date: 2020/3/16 11:42
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MarketingActiveTouchWayListVO extends SuperVO {

  /**
   * 主动营销任务
   */
  @ApiModelProperty(value = "主动营销任务ID")
  @NotNull(message = "主动营销任务ID不能为空")
  private Long activeId;

  @ApiModelProperty(value = "主动营销触达方式")
  @NotNull(message = "主动营销触达方式不能为空")
  private List<MarketingActiveTouchWayVO> touchWayVOList;
}
