package com.deepexi.dxp.marketing.domain.marketing.request;
import com.deepexi.dxp.marketing.common.base.request.SuperRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/11
 */
@Data
@ApiModel
public class IdRequest extends SuperRequest {
    /**
     * 画布主键id
     */
    @ApiModelProperty(value = "主键id", required = true)
    @NotNull(message = "id不能为空")
    private Long id;
}
