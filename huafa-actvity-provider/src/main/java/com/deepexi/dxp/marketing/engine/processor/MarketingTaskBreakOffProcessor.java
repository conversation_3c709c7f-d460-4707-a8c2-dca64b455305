//package com.deepexi.dxp.marketing.engine.processor;
//
//
//import com.deepexi.dxp.marketing.engine.MarketingTaskCallback;
//import com.deepexi.dxp.marketing.engine.event.MarketingTaskEvent;
//import com.deepexi.dxp.marketing.engine.listener.MarketingTaskListener;
//import com.deepexi.dxp.marketing.engine.task.MetaDataTask;
//import lombok.extern.slf4j.Slf4j;
//
///**
// * 营销任务暂停处理事件源
// *
// * <AUTHOR>
// * @date 2020/3/18 18:54
// */
//@Slf4j
//public class MarketingTaskBreakOffProcessor extends AbstractTaskProcessor {
//
//    public MarketingTaskBreakOffProcessor(MetaDataTask metaDataTask) {
//        super(metaDataTask);
//    }
//
//    public MarketingTaskBreakOffProcessor(MetaDataTask metaDataTask, MarketingTaskCallback callback) {
//        super(metaDataTask, callback);
//    }
//
//    @Override
//    protected void doProcess(MarketingTaskListener listener, MarketingTaskEvent event) {
//        try {
//            log.info("breakOff-start-营销任务暂停开始");
//            listener.breakOff(event);
//        } finally {
//            event.semaphoreDown();
//            log.info("breakOff-end-营销任务暂停结束");
//        }
//    }
//
//    @Override
//    public void callBack() {
//
//    }
//}
