package com.deepexi.dxp.marketing.service.promotion;

import com.deepexi.dxp.marketing.domain.promotion.dto.activity.PromotionActivityTemplateDetailPostDTO;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityTemplateCreatePostRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityTemplateDeletedPostRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityTemplateUpdatePostRequest;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityTemplateDO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> xinjian.yao
 * @date 2019/11/20 16:54
 */
@Service
public interface PromotionActivityTemplateMiddleService {


    /**
     * 活动模板批量更新接口
     * @param list
     * @return
     */
    int updateBatch(List<PromotionActivityTemplateDO> list);

    /**
     * @param dto 活动模板dto
     * @return 添加是非成功
     */
    Boolean create(PromotionActivityTemplateCreatePostRequest dto);

    /**
     * @param id  活动模板id
     * @param dto 活动模板dto
     * @return 修改是非成功
     */
    Boolean update(Long id, PromotionActivityTemplateUpdatePostRequest dto);

    /**
     * 根据id查看活动模板详情
     *
     * @param id id
     * @return dto
     */
    PromotionActivityTemplateDetailPostDTO detail(Long id);

    /**
     * 根据dto 删除数据
     *
     * @param dtoList dtoList
     * @return 输出成功
     */
    Boolean deleted(List<PromotionActivityTemplateDeletedPostRequest> dtoList);
}



