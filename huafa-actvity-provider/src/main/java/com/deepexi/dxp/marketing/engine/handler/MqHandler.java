//package com.deepexi.dxp.marketing.engine.handler;
//import com.deepexi.dxp.marketing.engine.event.MarketingTaskEvent;
//import com.deepexi.dxp.marketing.engine.listener.MarketingTaskListener;
//
///**
// * 当营销任务发送任务被暂停、终止、启动时，营销任务MQ处理监听器
// *
// * <AUTHOR>
// * @date 2020/3/18 17:50
// */
//public class Mq<PERSON><PERSON><PERSON> extends MarketingTaskListener {
//
//    @Override
//    public void goOn(MarketingTaskEvent event) {
//
//    }
//
//    @Override
//    public void breakOff(MarketingTaskEvent event) {
//
//    }
//
//    @Override
//    public void start(MarketingTaskEvent event) {
//
//    }
//
//    @Override
//    public void suspend(MarketingTaskEvent event) {
//        super.suspend(event);
//
//    }
//
//    @Override
//    public void end(MarketingTaskEvent event) {
//
//    }
//}
