package com.deepexi.dxp.marketing.domain.marketing.vo;


import com.deepexi.dxp.marketing.common.base.vo.SuperExtVO;
import com.deepexi.dxp.marketing.domain.marketing.dto.MarketingAutoConditionDTO;
import com.deepexi.dxp.middle.marketing.domain.vo.MarketingActionTemplateResourceRuleVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @desc AutoMarketingTaskVO  自动营销任务
 * @Date: Mon Mar 09 15:26:44 CST 2020
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MarketingAutoVO extends SuperExtVO {
    /**
     * 条件触发后时
     */
    @ApiModelProperty("条件触发后时")
    private Long executeAfter;


    /**
     * 执行时间单位 0 秒 1 分 2时 3日 4月 5年
     */
    @ApiModelProperty("执行时间单位 0 秒 1 分 2时 3日 4月 5年")
    private Integer executeUnit;

    /**
     * 自动任务名称
     */
    @ApiModelProperty("自动任务名称")
    private String name;
    /**
     * 任务编码(暂未定生成规则)
     */
    @ApiModelProperty("任务编码(暂未定生成规则)")
    private String code;
    /**
     * 任务状态(1:草稿，2:未开始，3:运行中，4:暂停，5:结束)
     */
    @ApiModelProperty("任务状态(1:草稿，2:未开始，3:运行中，4:暂停，5:结束)")
    private Integer status;
    /**
     * 执行时机
     */
    @ApiModelProperty("执行时机")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date executeTime;

    /**
     * 行动模版ID
     */
    @ApiModelProperty("行动模版ID")
    private Long actionTemplateId;
    /**
     * 描述
     */
    @ApiModelProperty("描述")
    private String description;

    /**
     * 0 立即执行 1 延后执行
     */
    @ApiModelProperty("0 立即执行 1 延后执行")
    private Integer executeNow;

    @ApiModelProperty("触发时机")
    private List<MarketingAutoConditionDTO> marketingAutoConditions;


    @ApiModelProperty("模版")
    private MarketingActionTemplateResourceRuleVO marketingActionTemplateResourceRuleVO;



    @ApiModelProperty("安全设置")
    private List<MarketingTaskSecurityFilterVO> marketingTaskSecurityFilters;
}

