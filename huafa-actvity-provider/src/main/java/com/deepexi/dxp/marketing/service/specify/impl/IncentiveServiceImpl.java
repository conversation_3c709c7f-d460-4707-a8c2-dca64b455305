package com.deepexi.dxp.marketing.service.specify.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.deepexi.common.extension.AppRuntimeEnv;
import com.deepexi.dxp.marketing.common.base.utils.CommonExceptionCode;
import com.deepexi.dxp.marketing.config.HuafaConstantConfig;
import com.deepexi.dxp.marketing.constant.InteractionConstant;
import com.deepexi.dxp.marketing.converter.ActivityInfoConverter;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityOrderQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.*;
import com.deepexi.dxp.marketing.domain.marketing.response.IncentiveResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.RechargeMobileResponseDTO;
import com.deepexi.dxp.marketing.domain.specify.dto.ActivityFormFeedbackDTO;
import com.deepexi.dxp.marketing.enums.coupon.WhetherEnum;
import com.deepexi.dxp.marketing.enums.dolphinscheduler.InstanceExecuteStatusEnum;
import com.deepexi.dxp.marketing.enums.specify.*;
import com.deepexi.dxp.marketing.service.specify.IncentiveService;
import com.deepexi.dxp.marketing.utils.GenerateIdUtil;
import com.deepexi.dxp.marketing.utils.HuaFaHmacAuthUtil;
import com.deepexi.dxp.marketing.utils.RestTemplateUtil;
import com.deepexi.dxp.middle.promotion.converter.specify.LuckyDrawConverter;
import com.deepexi.dxp.middle.promotion.dao.specify.*;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityOrderDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityVerifyDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ExternalServiceLogDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.PromotionHisResourceDO;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.DateUtils;
import com.deepexi.util.JsonUtil;
import com.deepexi.util.StringUtil;
import com.deepexi.util.exception.ApplicationException;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * 发送激励
 * <AUTHOR> 交互
 */
@Service
@Slf4j
public class IncentiveServiceImpl implements IncentiveService {

    @Resource
    private HuafaConstantConfig huafaConstantConfig;
    @Resource
    private HuaFaHmacAuthUtil huaFaHmacAuthUtil;

    @Resource
    private ActivityOrderDAO activityOrderDAO;

    @Resource
    private GenerateIdUtil generateIdUtil;

    @Resource
    public ActivityVerifyDAO activityVerifyDAO;

    @Resource
    private PromotionHisResourceDAO promotionHisResourceDAO;
    @Resource
    private CustomerFeedbackDAO customerFeedbackDAO;

    @Resource
    private ExternalServiceLogDAO externalServiceLogDAO;

    @Value("${deepexi.marketing.specify.pay-call-back-base-url}")
    private String callBackBaseUrl;

    @Override
    public IncentiveResponseDTO sendIncentive(SendIncentiveRequestDTO requestDTO) {
        try{
            String postFormData = this.sendTransfer(requestDTO);

            //创建订单信息
            IncentiveResponseDTO incentiveResponseDTO = JSONUtil.toBean(postFormData, IncentiveResponseDTO.class);

            Integer orderStatus = OrderStatusEnum.PAYED.getId();
            Integer verifyStatus = VerifyStatusEnum.VERIFY_ED.getId();
            if (StringUtil.isEmpty(postFormData) || !incentiveResponseDTO.getCode().equals(CommonExceptionCode.SUCCESS)
                    || (incentiveResponseDTO.getData() != null && !InstanceExecuteStatusEnum.SUCCESS.getValue().equals(incentiveResponseDTO.getData().getStatus()))) {
                orderStatus = OrderStatusEnum.PAY_FAIL.getId();
                verifyStatus = VerifyStatusEnum.NO_VERIFY.getId();
            }

            //保存订单信息
            ActivityOrderDO activityOrderDO = this.saveActivityOrder(requestDTO, incentiveResponseDTO,orderStatus);

            //公众号保存核销信息
            this.saveActivityVerify(requestDTO, verifyStatus, activityOrderDO);
            return incentiveResponseDTO;
        }catch (Exception e){
            log.info("发送激励异常请求参数:{}", JsonUtil.bean2JsonString(requestDTO));
            log.error("发送激励异常:",e);
            IncentiveResponseDTO incentiveResponseDTO = new IncentiveResponseDTO();
            incentiveResponseDTO.setCode("500");
            incentiveResponseDTO.setMsg(e.getMessage());
            incentiveResponseDTO.setData(null);
            //保存订单信息
            ActivityOrderDO activityOrderDO = this.saveActivityOrder(requestDTO, incentiveResponseDTO,OrderStatusEnum.PAY_FAIL.getId());

            //公众号保存核销信息
            this.saveActivityVerify(requestDTO, VerifyStatusEnum.NO_VERIFY.getId(), activityOrderDO);
            return incentiveResponseDTO;
        }
    }

    private void saveActivityVerify(SendIncentiveRequestDTO requestDTO, Integer verifyStatus, ActivityOrderDO activityOrderDO) {
        if (/*requestDTO.getType().equals(ChannelTypeEnum.WECHAT_WEBSITE.getId()) && */activityOrderDO != null){
            ActivityFormFeedbackDTO activityFormFeedbackDTO = new ActivityFormFeedbackDTO();
            activityFormFeedbackDTO.setNickName(requestDTO.getNickName());
            activityFormFeedbackDTO.setUserName(requestDTO.getUserName());
            activityFormFeedbackDTO.setPhone(requestDTO.getPhone());
            activityFormFeedbackDTO.setUnionId(requestDTO.getUnionId());
            PromotionHisResourceDO byId = promotionHisResourceDAO.getById(requestDTO.getHisResourceId());
            ActivityVerifyDO activityVerifyDO = ActivityInfoConverter.verifyConverter(activityOrderDO,activityFormFeedbackDTO, byId, requestDTO.getResourceCode(), verifyStatus, WhetherEnum.NO.getId());
            activityVerifyDAO.save(activityVerifyDO);
        }
    }

    @Override
    public String sendTransfer(SendIncentiveRequestDTO requestDTO) throws Exception {
        this.sendCheck(requestDTO);

        String url = huafaConstantConfig.PAY_BASE_URL+InteractionConstant.TRANSFER_URL;
        String appId = huafaConstantConfig.MINI_PROGRAM_APP_ID;
        if (ChannelTypeEnum.WECHAT_WEBSITE.getId().equals(requestDTO.getType())){
            appId = huafaConstantConfig.WECHAT_APP_ID;
        }else if (SendTemplateNewsRequestDTO.MP_FROM_USH.equals(requestDTO.getMpFrom())){
            appId = huafaConstantConfig.MINI_PROGRAM_YSH_APP_ID;
        }

        SendIncentiveRequest clone = requestDTO.clone(SendIncentiveRequest.class);
        //测试环境默认取配置，生产环境如不需要则将配置文件配置内容改成空的
        clone.setProjectId(requestDTO.getProjectId());
        clone.setAppId(appId);
        clone.setBizOrderNo(generateIdUtil.getOrderNo(GenerateTypeEnum.PAY_TYPE));
        requestDTO.setBizOrderNo(clone.getBizOrderNo());
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        log.info("=====================发送激励开始=====================");
        String postFormData = RestTemplateUtil.orderMonitorPost(url, JSON.toJSONString(clone),headers);
        log.info("发送激励返回：{}",postFormData);
        this.recordServiceLog(JSON.toJSONString(clone),postFormData,requestDTO.getActId(),requestDTO.getHisResourceId(),0,url);
        return postFormData;
    }

    /**
     * 生成接口日志
     * @param requestJson
     * @param responseJson
     * @param responseJson
     * @param resourceId
     * @param type
     */
    private void recordServiceLog(String requestJson,String responseJson,Long activityId,Long resourceId,Integer type,String url){
        CompletableFuture.runAsync(() -> {
            try{
                ExternalServiceLogDO externalServiceLogDO = new ExternalServiceLogDO();
                externalServiceLogDO.setRequestJson(requestJson);
                externalServiceLogDO.setResponseJson(responseJson);
                externalServiceLogDO.setActivityId(activityId);
                externalServiceLogDO.setResourceId(resourceId);
                externalServiceLogDO.setType(type);//0红包1话费
                externalServiceLogDO.setUrl(url);
                externalServiceLogDAO.save(externalServiceLogDO);
            }catch(Exception e){
                log.error("日志记录异常",e);
            }
        });
    }

    @Override
    @Async("threadExecutor")
    public void sendIncentiveAsync(SendIncentiveRequestDTO requestDTO) {
        requestDTO.setBizOrderNo(StringUtil.isNotEmpty(requestDTO.getBizOrderNo())?requestDTO.getBizOrderNo():generateIdUtil.getOrderNo(GenerateTypeEnum.PAY_TYPE));
        sendIncentive(requestDTO);
    }

    @Override
    public RechargeMobileResponseDTO rechargeMobile(RechargeMobileRequestDTO requestDTO) {
        //订单编码
        String orderNo = generateIdUtil.getOrderNo(GenerateTypeEnum.PAY_TYPE);
        try{
            this.rechargeCheck(requestDTO);
            int retry = 0;
            int RETRY = 3;
            RechargeMobileResponseDTO responseDTO = new RechargeMobileResponseDTO();
            while (retry < RETRY) {

                //回调地址
                RechargeMobileRequest rechargeMobileRequest = requestDTO.getRechargeMobileRequest();
                rechargeMobileRequest.setReqNo(orderNo);
                rechargeMobileRequest.setState(InteractionConstant.state);
                rechargeMobileRequest.setCallBackUrl(callBackBaseUrl + InteractionConstant.MONEY_CALLBACK_URL);


                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
                log.info("=====================话费充值开始=====================次数：{}",retry);
                log.info("============话费充值实际参数======：{}",JSON.toJSONString(rechargeMobileRequest));

                String payBaseurl = huafaConstantConfig.PAY_BASE_URL.replace("/v1", "");
                String postFormData = RestTemplateUtil.orderMonitorPost(payBaseurl + InteractionConstant.RECHARGE_MOBILE_URL, JSON.toJSONString(rechargeMobileRequest), headers);
                log.info("话费充值返回：{}", postFormData);
                this.recordServiceLog(JSON.toJSONString(rechargeMobileRequest),postFormData,Long.valueOf(requestDTO.getRechargeMobileRequest().getActId()),requestDTO.getHisResourceId(),1,payBaseurl + InteractionConstant.RECHARGE_MOBILE_URL);
                if (StringUtil.isNotEmpty(postFormData)) {
                    //创建订单信息
                    responseDTO = JSONUtil.toBean(postFormData, RechargeMobileResponseDTO.class);
                    if (responseDTO.getCode().equals(CommonExceptionCode.SUCCESS) && responseDTO.getData() != null) {
                        RechargeMobileResponseDTO.RechargeMobileResponseResult data = responseDTO.getData();
                        if ("1".equals(data.getResult())) {
                            this.saveRechargeMobileActivityOrder(requestDTO, data,orderNo,OrderStatusEnum.PAYED.getId(),VerifyStatusEnum.VERIFY_ED.getId());
                            return responseDTO;
                        }else if("0".equals(data.getResult())){
                            this.saveRechargeMobileActivityOrder(requestDTO, data,orderNo,OrderStatusEnum.PENDING.getId(),VerifyStatusEnum.NO_VERIFY.getId());
                            return responseDTO;
                        }
                    }
                }
                retry ++;
            }
            responseDTO.setCode("500");
            responseDTO.setMsg("充值失败");
            responseDTO.setData(null);
            return responseDTO;
        }catch (Exception e){
            log.info("话费充值异常请求参数:{}", JsonUtil.bean2JsonString(requestDTO));
            log.error("话费充值异常:",e);
            RechargeMobileResponseDTO incentiveResponseDTO = new RechargeMobileResponseDTO();
            incentiveResponseDTO.setCode("500");
            incentiveResponseDTO.setMsg(e.getMessage());
            incentiveResponseDTO.setData(null);
            this.saveRechargeMobileActivityOrder(requestDTO, null,orderNo,OrderStatusEnum.PAY_FAIL.getId(),VerifyStatusEnum.NO_VERIFY.getId());
            return incentiveResponseDTO;
        }
    }

    @Override
    @Async("threadExecutor")
    public void rechargeMobileAsync(RechargeMobileRequestDTO requestDTO) {
        rechargeMobile(requestDTO);
    }

    @Override
    public Boolean sendIncentiveCallBack(SendIncentiveCallBackRequestDTO requestDTO) {
        ActivityOrderQuery query = new ActivityOrderQuery();
        query.setWxOrderNo(requestDTO.getOrderNo());
        List<ActivityOrderDO> list = activityOrderDAO.findList(query);
        if (CollectionUtil.isEmpty(list)){
            throw new ApplicationException("订单号不存在");
        }
        ActivityOrderDO activityOrderDO = list.get(0);

        ActivityFormFeedbackDTO activityFormFeedbackDTO = customerFeedbackDAO.getActivityFormFeedUserId(requestDTO.getActivityId(), requestDTO.getPhone(),requestDTO.getHisResourceId());

        PromotionHisResourceDO byId = promotionHisResourceDAO.getById(requestDTO.getHisResourceId());
        String resourceCode = generateIdUtil.getResourceCode();
        ActivityVerifyDO activityVerifyDO = ActivityInfoConverter.verifyConverter(activityOrderDO,activityFormFeedbackDTO, byId, resourceCode, VerifyStatusEnum.VERIFY_ED.getId(), WhetherEnum.YES.getId());
        return activityVerifyDAO.save(activityVerifyDO);
    }

    @Override
    public Boolean moneyCallBack(String reqNo, String result, String state) {
        if (StringUtil.isBlank(reqNo)){
            throw new ApplicationException("reqNo不能为空");
        }
        if (!state.equals(InteractionConstant.state)){
            throw new ApplicationException("请求不属于本系统");
        }
        ActivityOrderQuery query = new ActivityOrderQuery();
        query.setCode(reqNo);
        List<ActivityOrderDO> list = activityOrderDAO.findList(query);
        if (CollectionUtil.isEmpty(list)){
            throw new ApplicationException("订单不存在");
        }
        Integer status = OrderStatusEnum.PAY_FAIL.getId();
        if ("1".equals(result)){
            status = OrderStatusEnum.PAYED.getId();
        }
        ActivityOrderDO activityOrderDO = new ActivityOrderDO();
        activityOrderDO.setId(list.get(0).getId());
        activityOrderDO.setStatus(status);
        activityOrderDAO.updateById(activityOrderDO);

        ActivityVerifyDO activityVerifyDO = new ActivityVerifyDO();
        activityVerifyDO.setVerifyStatus(VerifyStatusEnum.VERIFY_ED.getId());

        UpdateWrapper<ActivityVerifyDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(ActivityVerifyDO::getOrderId,list.get(0).getId());
        activityVerifyDAO.update(activityVerifyDO,updateWrapper);
        return true;
    }

    private void saveRechargeMobileActivityOrder(RechargeMobileRequestDTO requestDTO, RechargeMobileResponseDTO.RechargeMobileResponseResult data,
                                                 String orderNo,Integer status,Integer verifyStatus) {
        if (Objects.isNull(requestDTO)) {
            return;
        }
        RechargeMobileRequest rechargeMobileRequest = requestDTO.getRechargeMobileRequest();

        ActivityOrderDO dto = new ActivityOrderDO();
        dto.setActivityId(Long.parseLong(rechargeMobileRequest.getActId()));
        dto.setResourceId(requestDTO.getHisResourceId());
        dto.setUserId(requestDTO.getOpenid());
        dto.setCode(orderNo);
        dto.setPayMoney(BigDecimal.valueOf(rechargeMobileRequest.getAmount()).divide(BigDecimal.valueOf(100),2, BigDecimal.ROUND_DOWN));
        dto.setAppId(AppRuntimeEnv.getAppId() == null?1001L:AppRuntimeEnv.getAppId());
        dto.setTenantId(StringUtil.isNotEmpty(AppRuntimeEnv.getTenantId())?AppRuntimeEnv.getTenantId():"hfb981fd0e654f7c90470bc865d83690");
        dto.setStatus(status);
        dto.setPayTime(DateUtils.now());
        dto.setOrderType(OrderTypeEnum.RECHARGE.getId());
        dto.setUnionId(requestDTO.getUnionId());
        dto.setType(requestDTO.getType());
        dto.setCreatedBy(requestDTO.getUserName());
        dto.setUpdatedBy(requestDTO.getUserName());
        dto.setPhone(rechargeMobileRequest.getTelNo());
        Map<String,Object> map = Maps.newHashMap();
        map.put("projectId",requestDTO.getProjectId());
        map.put("projectName",requestDTO.getProjectName());
        dto.setExt(map);
        if (data != null){
            dto.setExt(object2Map(data));
            dto.setWxOrderNo(data.getOrderNo());
        }
        boolean save = activityOrderDAO.save(dto);
        log.info("发送激励订单响应结果：" + save);

        ActivityFormFeedbackDTO activityFormFeedbackDTO = new ActivityFormFeedbackDTO();
        activityFormFeedbackDTO.setNickName(requestDTO.getNickName());
        activityFormFeedbackDTO.setUserName(requestDTO.getUserName());
        activityFormFeedbackDTO.setPhone(rechargeMobileRequest.getTelNo());
        activityFormFeedbackDTO.setUnionId(requestDTO.getUnionId());
        PromotionHisResourceDO byId = promotionHisResourceDAO.getById(requestDTO.getHisResourceId());

        ActivityVerifyDO activityVerifyDO = ActivityInfoConverter.verifyConverter(dto,activityFormFeedbackDTO, byId, requestDTO.getCode(), verifyStatus, WhetherEnum.NO.getId());
        boolean saveVerify = activityVerifyDAO.save(activityVerifyDO);
        log.info("发送激励核销记录响应结果：" + saveVerify);
    }

    private void rechargeCheck(RechargeMobileRequestDTO requestDTO) {
        Assert.notNull(requestDTO, "入参对象不能为空");
        Assert.notNull(requestDTO.getRechargeMobileRequest(), "入参对象不能为空");
        Assert.notNull(requestDTO.getRechargeMobileRequest(), "入参对象不能为空");
        Assert.notNull(requestDTO.getRechargeMobileRequest().getTelNo(), "手机号不能为空");
        Assert.notNull(requestDTO.getRechargeMobileRequest().getActId(), "活动id不能为空");
        Assert.notNull(requestDTO.getRechargeMobileRequest().getProjectId(), "项目id不能为空");
    }

    private void sendCheck(SendIncentiveRequestDTO requestDTO) {
        Assert.notNull(requestDTO, "入参对象不能为空");
        Assert.notNull(requestDTO.getActId(), "活动id不能为空");
        Assert.notNull(requestDTO.getHisResourceId(), "资源id不能为空");
        Assert.notNull(requestDTO.getType(), "来源不能为空");
        Assert.notNull(requestDTO.getOpenid(), "openid不能为空");
        Assert.notNull(requestDTO.getProjectId(), "项目id不能为空");
        Assert.notNull(requestDTO.getDescription(), "订单描述不能为空");
        Assert.notNull(requestDTO.getAmount(), "激励金额不能为空");
        Assert.notNull(requestDTO.getSendName(), "红包发送者名称不能为空");
        Assert.notNull(requestDTO.getWishing(), "红包祝福语不能为空");
        Assert.notNull(requestDTO.getActName(), "活动名称不能为空");
    }

    private ActivityOrderDO saveActivityOrder(SendIncentiveRequestDTO requestDTO,IncentiveResponseDTO incentiveResponseDTO,Integer status) {
        if (Objects.isNull(incentiveResponseDTO) || Objects.isNull(requestDTO)) {
            return null;
        }

        IncentiveResponseDTO.IncentiveResponseResult data = incentiveResponseDTO.getData();

        ActivityOrderDO dto = new ActivityOrderDO();
        dto.setActivityId(requestDTO.getActId());
        dto.setResourceId(requestDTO.getHisResourceId());
        dto.setUserId(requestDTO.getOpenid());
        dto.setCode(requestDTO.getBizOrderNo());
        dto.setPayMoney(new BigDecimal(requestDTO.getAmount()/100));
        dto.setAppId(AppRuntimeEnv.getAppId() == null?1001L:AppRuntimeEnv.getAppId());
        dto.setTenantId(StringUtil.isNotEmpty(AppRuntimeEnv.getTenantId())?AppRuntimeEnv.getTenantId():"hfb981fd0e654f7c90470bc865d83690");
        dto.setStatus(status);
        dto.setPayTime(DateUtils.now());
        dto.setOrderType(OrderTypeEnum.RED_ENVELOPE.getId());
        dto.setPhone(requestDTO.getPhone());

        dto.setUnionId(requestDTO.getUnionId());
        dto.setType(requestDTO.getType());
        dto.setCreatedBy(requestDTO.getUserName());
        dto.setUpdatedBy("");
        if (data != null){
            dto.setExt(object2Map(data));
            dto.setWxOrderNo(data.getOrderNo());
        }
        Map<String, Object> projectInfo = new HashMap<>();
        if(StringUtil.isNotEmpty(requestDTO.getProjectId()) && StringUtil.isNotEmpty(requestDTO.getProjectName())){
            projectInfo.put("projectId",requestDTO.getProjectId());
            projectInfo.put("projectName",requestDTO.getProjectName());
        }
        if (dto.getExt() != null && !dto.getExt().isEmpty()){
            dto.getExt().putAll(projectInfo);
        }else{
            dto.setExt(projectInfo);
        }

        boolean save = activityOrderDAO.save(dto);
        log.info("发送激励订单响应结果：" + save);
        return dto;
    }

    private Map<String, Object> object2Map(Object data) {
        Map<String, Object> map = Maps.newHashMap();
        if (Objects.isNull(data)) {
            return map;
        }
        Class clazz = data.getClass();
        Field[] fields = clazz.getDeclaredFields();
        try {
            for (Field field : fields) {
                field.setAccessible(true);
                map.put(field.getName(), field.get(data));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return map;
    }

    @Async("threadExecutor")
    public void thirdVerify(ActivityPartakeRequest activityPartakeLogDO, PromotionHisResourceDO promotionHisResourceDO,
                             ActivityFormFeedbackDTO activityFormFeedbackDTO, String activityName, String resourceCode, String mpFrom) {
        ReceiveNowDTO dto = new ReceiveNowDTO();
        dto.setUserId(activityPartakeLogDO.getUserId());
        dto.setNickName(activityFormFeedbackDTO.getNickName());
        dto.setUserName(activityFormFeedbackDTO.getUserName());
        dto.setPhone(activityFormFeedbackDTO.getPhone());
        dto.setProjectId(activityPartakeLogDO.getProjectId());
        dto.setProjectName(activityPartakeLogDO.getProjectName());
        dto.setType(activityPartakeLogDO.getType());
        dto.setActivityId(promotionHisResourceDO.getActivityId());
        dto.setUnionId(activityPartakeLogDO.getUnionId());
        if (ThirdCategoryEnum.PHONE_FEE.getId().equals(promotionHisResourceDO.getThirdCategory())) {
            //话费充值
            rechargeMobileAsync(LuckyDrawConverter.rechargeMobileConverter(dto, activityName, promotionHisResourceDO, resourceCode));
        } else if (ThirdCategoryEnum.CASH_BAG.getId().equals(promotionHisResourceDO.getThirdCategory())) {
            //发送红包激励
            sendIncentiveAsync(LuckyDrawConverter.sendIncentiveConverter(dto, activityName, promotionHisResourceDO, resourceCode, mpFrom));
        }
    }
}
