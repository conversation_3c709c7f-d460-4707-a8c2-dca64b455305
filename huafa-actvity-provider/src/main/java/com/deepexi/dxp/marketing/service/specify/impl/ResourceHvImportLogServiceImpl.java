package com.deepexi.dxp.marketing.service.specify.impl;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.deepexi.common.extension.AppRuntimeEnv;
import com.deepexi.dxp.marketing.common.base.utils.CommonExceptionCode;
import com.deepexi.dxp.marketing.domain.marketing.response.ExcelExportResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.PromotionResourceExportResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.ResourceHvImportLogResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.query.specify.ResourceHvImportLogQuery;
import com.deepexi.dxp.marketing.domain.promotion.request.specify.PromotionResourceImportRequestDTO;
import com.deepexi.dxp.marketing.enums.resource.ResourceGrantWayEnum;
import com.deepexi.dxp.marketing.enums.specify.PromotionResourceCouponCategoryEnum;
import com.deepexi.dxp.marketing.enums.specify.PromotionResourceCouponTypeEnum;
import com.deepexi.dxp.marketing.enums.specify.PromotionResourceTypeEnum;
import com.deepexi.dxp.marketing.enums.specify.PromotionResourceValidTimeTypeEnum;
import com.deepexi.dxp.marketing.service.specify.ResourceHvImportLogService;
import com.deepexi.dxp.marketing.utils.CodeUtils;
import com.deepexi.dxp.marketing.utils.DateTimeUtils;
import com.deepexi.dxp.marketing.utils.ExcelExportUtil;
import com.deepexi.dxp.middle.marketing.common.base.SuperEntity;
import com.deepexi.dxp.middle.promotion.dao.specify.PromotionResourceDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.ResourceHvImportLogDAO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.PromotionResourceDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ResourceHvImportLogDO;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.DateUtils;
import com.deepexi.util.StringUtil;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import com.deepexi.util.pojo.CloneDirection;
import com.deepexi.util.pojo.ObjectCloneUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ResourceHvImportLogServiceImpl implements ResourceHvImportLogService {


    @Autowired
    private ResourceHvImportLogDAO resourceHvImportLogDAO;

    @Autowired
    private PromotionResourceDAO promotionResourceDAO;

    @Value("${deepexi.marketing.specify.resource-img-url}")
    private String resourceDefaultImgUrl;

    @Override
    public PageBean<ResourceHvImportLogResponseDTO> findPage(ResourceHvImportLogQuery query) {
        return ObjectCloneUtils.convertPageBean(resourceHvImportLogDAO.findPage(query),ResourceHvImportLogResponseDTO.class, CloneDirection.OPPOSITE);
    }

    @Override
    public Boolean importResource(PromotionResourceImportRequestDTO promotionResourceImportRequestDTO) {
        List<PromotionResourceImportRequestDTO.PromotionResourceImportDetailRequestDTO> importRequestDTOList = promotionResourceImportRequestDTO.getImportRequestDTOList();
        Integer type = promotionResourceImportRequestDTO.getType();
        if(CollectionUtil.isEmpty(importRequestDTOList)){
            throw new ApplicationException(CommonExceptionCode.INVALIDATION,"没有可导入数据!");
        }

        //保存日志
        ResourceHvImportLogDO resourceHvImportLogDO = new ResourceHvImportLogDO();
        resourceHvImportLogDO.setImportNum(importRequestDTOList.size());
        resourceHvImportLogDO.setSuccessNum(0);
        resourceHvImportLogDO.setFailNum(0);
        resourceHvImportLogDO.setImportStatus(0);
        resourceHvImportLogDO.setTenantId(AppRuntimeEnv.getTenantId());
        resourceHvImportLogDO.setAppId(AppRuntimeEnv.getAppId());
        resourceHvImportLogDO.setCreatedTime(DateTime.now());
        resourceHvImportLogDO.setUpdatedTime(DateTime.now());
        resourceHvImportLogDO.setDeleted(0);
        resourceHvImportLogDO.setCreatedBy(StringUtil.isNotEmpty(promotionResourceImportRequestDTO.getCreatedBy())?promotionResourceImportRequestDTO.getCreatedBy():"admin");
        resourceHvImportLogDO.setUpdatedBy(resourceHvImportLogDO.getCreatedBy());
        resourceHvImportLogDO.setCouponType(StringUtil.isNotEmpty(importRequestDTOList.get(0).getCouponValue()) && importRequestDTOList.get(0).getCouponValue().contains("折") ? 1 : 0);
        resourceHvImportLogDAO.save(resourceHvImportLogDO);
        Long id = resourceHvImportLogDO.getId();
        CompletableFuture.runAsync(() -> {
            insertData(type,id,importRequestDTOList);
        });
        return true;
    }

    private void insertData(Integer type,Long id,List<PromotionResourceImportRequestDTO.PromotionResourceImportDetailRequestDTO> importRequestDTOList){
        List<PromotionResourceDO> trueList = Lists.newArrayList();//正确数据
        List<PromotionResourceExportResponseDTO> failList = Lists.newArrayList();//失败数据
        int len = importRequestDTOList.size();
        List<String> names = getNames();
        ResourceHvImportLogDO resourceHvImportLogDO = resourceHvImportLogDAO.getById(id);
        for(int i=0;i<len;i++){
            PromotionResourceImportRequestDTO.PromotionResourceImportDetailRequestDTO importRequestDTO = importRequestDTOList.get(i);
            String checkResult  =  checkData(importRequestDTO,type,names);
            if(StringUtil.isEmpty(checkResult)){

                if(CollectionUtil.isNotEmpty(trueList)){//较验重名，重名不进行导入
                    List<PromotionResourceDO> collect = trueList.stream().filter(trueItem -> trueItem.getName().equals(importRequestDTO.getName())).collect(Collectors.toList());
                    if(CollectionUtil.isNotEmpty(collect)){
                        PromotionResourceExportResponseDTO promotionResourceExportResponseDTO = importRequestDTO.clone(PromotionResourceExportResponseDTO.class);
                        promotionResourceExportResponseDTO.setFailReason("已存在相同名字房源券");
                        failList.add(promotionResourceExportResponseDTO);
                        continue;
                    }
                }

                PromotionResourceDO pd = new PromotionResourceDO();
                pd.setAppId(AppRuntimeEnv.getAppId() == null?1001L:AppRuntimeEnv.getAppId());
                pd.setTenantId(StringUtil.isNotEmpty(AppRuntimeEnv.getTenantId())?AppRuntimeEnv.getTenantId():"hfb981fd0e654f7c90470bc865d83690");
                pd.setType(PromotionResourceTypeEnum.COUPON.getId());
                pd.setCouponCategory(PromotionResourceCouponCategoryEnum.REAL_ESTATE_COUPON.getId());
                pd.setName(importRequestDTO.getName());

                pd.setGrantWay(ResourceGrantWayEnum.OFFLINE_VERIFY.getId());

                //不区分代金券与折扣券
                pd.setCouponType(PromotionResourceCouponTypeEnum.VOUCHER.getId());
                pd.setCouponValue(new BigDecimal(importRequestDTO.getCouponValue()));

                pd.setUrl(resourceDefaultImgUrl);//默认图片地址

                if(importRequestDTO.getValidTime().contains("至")){
                    String[] validTimeArr = importRequestDTO.getValidTime().split("至");
                    Date startTime = DateUtils.getDate(validTimeArr[0]);
                    Date endTime = DateUtils.getDate(validTimeArr[1]);
                    pd.setValidTimeType(PromotionResourceValidTimeTypeEnum.DESIGNATED_TIME.getId());
                    pd.setValidStartTime(DateTimeUtils.getStartOfDay(startTime));
                    pd.setValidEndTime(DateTimeUtils.getEndOfDay(endTime));
                }else if(importRequestDTO.getValidTime().contains("不限制")){
                    pd.setValidTimeType(PromotionResourceValidTimeTypeEnum.NO_RESTRICTIONS.getId());
                }else{
                    pd.setValidTimeType(PromotionResourceValidTimeTypeEnum.EFFECTIVE_DAYS.getId());
                    pd.setValidDay(Integer.valueOf(timeNum(importRequestDTO.getValidTime())));
                }

                if(StringUtil.isNotEmpty(importRequestDTO.getCostPrice())) {
                    pd.setCostPrice(importRequestDTO.getCostPrice());
                    pd.setCostPriceType(1);
                }
                if(StringUtil.isNotEmpty(importRequestDTO.getBeforeCostPrice())) {
                    pd.setCostPrice(importRequestDTO.getBeforeCostPrice());
                    pd.setCostPriceType(0);
                }

                if(StringUtil.isNotEmpty(importRequestDTO.getDiscountPrice())){
                    pd.setDiscountPrice(importRequestDTO.getDiscountPrice());
                    pd.setDiscountPriceType(1);
                }
                if(StringUtil.isNotEmpty(importRequestDTO.getAfterDiscountPrice())){
                    pd.setDiscountPrice(importRequestDTO.getAfterDiscountPrice());
                    pd.setDiscountPriceType(0);
                }

                pd.setHouseName(importRequestDTO.getHouseName());
                pd.setHouseMessage(importRequestDTO.getHouseMessage());
                pd.setHouseVolume(importRequestDTO.getHouseVolume());
                pd.setIsDeleted(0);
                pd.setCreatedTime(DateTime.now());
                pd.setCreatedBy(resourceHvImportLogDO.getCreatedBy());
                pd.setUpdatedBy(resourceHvImportLogDO.getCreatedBy());
                pd.setCode(CodeUtils.unRepeatSixCode());
                pd.setUseRule(importRequestDTO.getUseRule());

                trueList.add(pd);
            }else{
                PromotionResourceExportResponseDTO promotionResourceExportResponseDTO =  importRequestDTO.clone(PromotionResourceExportResponseDTO.class);
                promotionResourceExportResponseDTO.setFailReason(checkResult);
                failList.add(promotionResourceExportResponseDTO);
            }
        }
        //保存正常数据
        if(CollectionUtil.isNotEmpty(trueList)){
            insertBatch(trueList);
        }
        resourceHvImportLogDO.setSuccessNum(trueList.size());
        resourceHvImportLogDO.setFailNum(failList.size());
        resourceHvImportLogDO.setImportStatus(1);
        if(CollectionUtil.isNotEmpty(failList)){
            String failJson = JSONArray.toJSONString(failList);
            resourceHvImportLogDO.setImportFailJson(failJson);
        }
        resourceHvImportLogDAO.updateById(resourceHvImportLogDO);
    }

    private void insertBatch(List<PromotionResourceDO> list){
        try{
            int insertLength = list.size();
            int i = 0;
            while (insertLength > 600) {
                promotionResourceDAO.batchInsert(list.subList(i, i + 600));
                i = i + 600;
                insertLength = insertLength - 600;
            }
            if (insertLength > 0) {
                promotionResourceDAO.batchInsert(list.subList(i, i + insertLength));
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Override
    public void exportExcel(HttpServletResponse response, Long id) {
        ResourceHvImportLogDO resourceHvImportLogDO = resourceHvImportLogDAO.getById(id);
        if(Objects.nonNull(resourceHvImportLogDO) && StringUtil.isNotEmpty(resourceHvImportLogDO.getImportFailJson())){
            List<PromotionResourceExportResponseDTO> list = JSONObject.parseArray(resourceHvImportLogDO.getImportFailJson(),PromotionResourceExportResponseDTO.class);
            ExcelExportUtil util = new ExcelExportUtil();
            util.setTitle("房源券失败记录");
            String[] heardList = new String[]{"序号","*资源名称","卡券面值(元)","*使用时间","*使用规则(最多5000个字符)","*项目名称","*房源面积(㎡)","*楼栋单元房号信息","原总价(万元)","原单价(万元)","折后总价(万元)","折后单价(万元)","失败原因"};
            String[] headerKey = new String[]{"","name","couponValue","validTime","useRule","houseName","houseVolume","houseMessage","costPrice","beforeCostPrice","discountPrice","afterDiscountPrice","failReason"};
            List<Map<String, String>> dataList = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                Map<String, String> data = Maps.newHashMap();
                PromotionResourceExportResponseDTO dto = list.get(i);
                data.put(headerKey[0], (i+1) + "");
                data.put(headerKey[1], dto.getName());
                data.put(headerKey[2], dto.getCouponValue());
                data.put(headerKey[3], dto.getValidTime());
                data.put(headerKey[4], dto.getUseRule());
                data.put(headerKey[5], dto.getHouseName());
                data.put(headerKey[6], dto.getHouseVolume());
                data.put(headerKey[7], dto.getHouseMessage());
                data.put(headerKey[8], dto.getCostPrice());
                data.put(headerKey[9], dto.getBeforeCostPrice());
                data.put(headerKey[10], dto.getDiscountPrice());
                data.put(headerKey[11], dto.getAfterDiscountPrice());
                data.put(headerKey[12], dto.getFailReason());
                dataList.add(data);
            }
            util.setHeardKey(headerKey);
            util.setHeardList(heardList);
            util.setData(dataList);
            util.setFileName("房源券导入失败列表");
            util.setSheetName("房源券导入失败列表");
            try {
                util.exportExport(response);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    @Override
    public void exportTemplate(Integer type, HttpServletRequest request, HttpServletResponse response) {
        ExcelExportUtil util = new ExcelExportUtil();
        String titleName = 1 == type ? "*卡券折扣（折）":"*卡券面值（元）";
        String fileName = 1 == type ? "房源导入模板-折扣券" : "房源导入模板-代金券";
        util.setTitle(fileName);
        String[] heardList = new String[]{"*资源名称",titleName,"*使用时间","*使用规则","*房源名称","*房源面积","*房间信息","*原价","*折后价"};
        String[] headerKey = new String[]{"name","couponValue","validTime","useRule","houseName","houseVolume","houseMessage","costPrice","discountPrice"};
        List<Map<String, String>> dataList = new ArrayList<>();
        Map<String, String> data = Maps.newHashMap();
        if(type == 0){
            data.put(headerKey[0], "华发世纪城10000元认购金");
            data.put(headerKey[1], "100");
            data.put(headerKey[2], "2021-04-05至2021-05-05");
            data.put(headerKey[3], "仅限线下核销使用");
            data.put(headerKey[4], "华发世纪城");
            data.put(headerKey[5], "200㎡");
            data.put(headerKey[6], "9-1003");
            data.put(headerKey[7], "200万");
            data.put(headerKey[8], "180万");
            dataList.add(data);

            data = Maps.newHashMap();
            data.put(headerKey[0], "华发世纪城10000元认购金");
            data.put(headerKey[1], "900");
            data.put(headerKey[2], "2022-10-05至2022-10-25");
            data.put(headerKey[3], "仅限线下核销使用");
            data.put(headerKey[4], "华发世纪城");
            data.put(headerKey[5], "200㎡");
            data.put(headerKey[6], "9-1003");
            data.put(headerKey[7], "200万");
            data.put(headerKey[8], "180万");
            dataList.add(data);

            data = Maps.newHashMap();
            data.put(headerKey[0], "华发世纪城10000元认购金");
            data.put(headerKey[1], "200");
            data.put(headerKey[2], "自获得后7天有效");
            data.put(headerKey[3], "仅限线下核销使用");
            data.put(headerKey[4], "华发世纪城");
            data.put(headerKey[5], "200㎡");
            data.put(headerKey[6], "9-1003");
            data.put(headerKey[7], "200万");
            data.put(headerKey[8], "180万");
            dataList.add(data);

            data = Maps.newHashMap();
            data.put(headerKey[0], "华发世纪城10000元认购金");
            data.put(headerKey[1], "200");
            data.put(headerKey[2], "自获得后36天有效");
            data.put(headerKey[3], "仅限线下核销使用");
            data.put(headerKey[4], "华发世纪城");
            data.put(headerKey[5], "200㎡");
            data.put(headerKey[6], "9-1003");
            data.put(headerKey[7], "200万");
            data.put(headerKey[8], "180万");
            dataList.add(data);


            data = Maps.newHashMap();
            data.put(headerKey[0], "华发世纪城10000元认购金");
            data.put(headerKey[1], "300");
            data.put(headerKey[2], "不限制");
            data.put(headerKey[3], "仅限线下核销使用");
            data.put(headerKey[4], "华发世纪城");
            data.put(headerKey[5], "200㎡");
            data.put(headerKey[6], "9-1003");
            data.put(headerKey[7], "200万");
            data.put(headerKey[8], "180万");
            dataList.add(data);
        }else{
            data.put(headerKey[0], "华发世纪城10000元认购金");
            data.put(headerKey[1], "97折");
            data.put(headerKey[2], "2021-04-05至2021-05-05");
            data.put(headerKey[3], "仅限线下核销使用");
            data.put(headerKey[4], "华发世纪城");
            data.put(headerKey[5], "200㎡");
            data.put(headerKey[6], "9-1003");
            data.put(headerKey[7], "200万");
            data.put(headerKey[8], "180万");
            dataList.add(data);

            data = Maps.newHashMap();
            data.put(headerKey[1], "55折");
            data.put(headerKey[2], "2021-12-05至2021-12-25");
            data.put(headerKey[0], "华发世纪城10000元认购金");
            data.put(headerKey[3], "仅限线下核销使用");
            data.put(headerKey[4], "华发世纪城");
            data.put(headerKey[5], "200㎡");
            data.put(headerKey[6], "9-1003");
            data.put(headerKey[7], "200万");
            data.put(headerKey[8], "180万");
            dataList.add(data);

            data = Maps.newHashMap();
            data.put(headerKey[1], "88折");
            data.put(headerKey[2], "自获得后7天有效");
            data.put(headerKey[0], "华发世纪城10000元认购金");
            data.put(headerKey[3], "仅限线下核销使用");
            data.put(headerKey[4], "华发世纪城");
            data.put(headerKey[5], "200㎡");
            data.put(headerKey[6], "9-1003");
            data.put(headerKey[7], "200万");
            data.put(headerKey[8], "180万");
            dataList.add(data);

            data = Maps.newHashMap();
            data.put(headerKey[1], "66折");
            data.put(headerKey[2], "自获得后25天有效");
            data.put(headerKey[0], "华发世纪城10000元认购金");
            data.put(headerKey[3], "仅限线下核销使用");
            data.put(headerKey[4], "华发世纪城");
            data.put(headerKey[5], "200㎡");
            data.put(headerKey[6], "9-1003");
            data.put(headerKey[7], "200万");
            data.put(headerKey[8], "180万");
            dataList.add(data);

            data = Maps.newHashMap();
            data.put(headerKey[1], "55折");
            data.put(headerKey[2], "不限制");
            data.put(headerKey[0], "华发世纪城10000元认购金");
            data.put(headerKey[3], "仅限线下核销使用");
            data.put(headerKey[4], "华发世纪城");
            data.put(headerKey[5], "200㎡");
            data.put(headerKey[6], "9-1003");
            data.put(headerKey[7], "200万");
            data.put(headerKey[8], "180万");
            dataList.add(data);

            data = Maps.newHashMap();
            data.put(headerKey[1], "58折");
            data.put(headerKey[2], "不限制");
            data.put(headerKey[0], "华发世纪城10000元认购金");
            data.put(headerKey[3], "仅限线下核销使用");
            data.put(headerKey[4], "华发世纪城");
            data.put(headerKey[5], "200㎡");
            data.put(headerKey[6], "9-1003");
            data.put(headerKey[7], "200万");
            data.put(headerKey[8], "180万");
            dataList.add(data);
        }

        util.setHeardKey(headerKey);
        util.setHeardList(heardList);
        util.setData(dataList);
        util.setFileName(fileName);
        util.setSheetName(fileName);
        try {
            util.resourceExportTemplate(response);
        } catch (Exception ex) {
            ex.printStackTrace();
        }

    }

    @Override
    public ExcelExportResponseDTO getExportExcelData(Long id) {
        ResourceHvImportLogDO resourceHvImportLogDO = resourceHvImportLogDAO.getById(id);
        if(Objects.nonNull(resourceHvImportLogDO) && StringUtil.isNotEmpty(resourceHvImportLogDO.getImportFailJson())){
            List<PromotionResourceExportResponseDTO> list = JSONObject.parseArray(resourceHvImportLogDO.getImportFailJson(),PromotionResourceExportResponseDTO.class);

            String[] heardList = new String[]{"序号","*资源名称","卡券面值(元)","*使用时间","*使用规则(最多5000个字符)","*项目名称","*房源面积(㎡)","*楼栋单元房号信息","原总价(万元)","原单价(万元)","折后总价(万元)","折后单价(万元)","失败原因"};
            String[] headerKey = new String[]{"","name","couponValue","validTime","useRule","houseName","houseVolume","houseMessage","costPrice","beforeCostPrice","discountPrice","afterDiscountPrice","failReason"};
            List<Map<String, String>> dataList = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                Map<String, String> data = Maps.newHashMap();
                PromotionResourceExportResponseDTO dto = list.get(i);
                data.put(headerKey[0], (i+1) + "");
                data.put(headerKey[1], dto.getName());
                data.put(headerKey[2], dto.getCouponValue());
                data.put(headerKey[3], dto.getValidTime());
                data.put(headerKey[4], dto.getUseRule());
                data.put(headerKey[5], dto.getHouseName());
                data.put(headerKey[6], dto.getHouseVolume());
                data.put(headerKey[7], dto.getHouseMessage());
                data.put(headerKey[8], dto.getCostPrice());
                data.put(headerKey[9], dto.getBeforeCostPrice());
                data.put(headerKey[10], dto.getDiscountPrice());
                data.put(headerKey[11], dto.getAfterDiscountPrice());
                data.put(headerKey[12], dto.getFailReason());
                dataList.add(data);
            }

            return ExcelExportResponseDTO.builder()
                    .title("房源券失败记录")
                    .fileName("房源券导入失败列表")
                    .headerKey(headerKey).headList(heardList)
                    .dataList(dataList)
                    .sheetName("房源券导入失败列表")
                    .build();
        }
        return null;
    }


    private String checkData(PromotionResourceImportRequestDTO.PromotionResourceImportDetailRequestDTO importRequestDTO,Integer type1,List<String> names){
        StringBuffer failReason = new StringBuffer();//失败原因
        Pattern p = Pattern.compile(".*\\d+.*");

        if(StringUtil.isEmpty(importRequestDTO.getName()) || importRequestDTO.getName().length() > 20){
            return "资源名称不能为空且不能超过20字符";
        }

        /*if((StringUtil.isEmpty(importRequestDTO.getDiscountPrice()) || StringUtil.isEmpty(importRequestDTO.getCostPrice()))
                && (StringUtil.isEmpty(importRequestDTO.getAfterDiscountPrice()) || StringUtil.isEmpty(importRequestDTO.getBeforeCostPrice()))){
            return "原总价和折后总价或原单价和折后单价，两个组合必须填写一个!";
        }*/
        if(StringUtil.isNotEmpty(importRequestDTO.getCostPrice()) && StringUtil.isNotEmpty(importRequestDTO.getBeforeCostPrice())){
            return "原总价和原单价只能填写一个!";
        }

        if(StringUtil.isNotEmpty(importRequestDTO.getDiscountPrice()) && StringUtil.isNotEmpty(importRequestDTO.getAfterDiscountPrice())){
            return "折后总价和折后单价只能填写一个!";
        }

        if(StringUtil.isEmpty(importRequestDTO.getCostPrice()) && StringUtil.isEmpty(importRequestDTO.getBeforeCostPrice())){
            return "原总价或原单价必须填写一个!";
        }

        if(StringUtil.isEmpty(importRequestDTO.getDiscountPrice()) && StringUtil.isEmpty(importRequestDTO.getAfterDiscountPrice())){
            return "折后总价或折后单价必须填写一个!";
        }


        //较验是否重名
        /*QueryWrapper<PromotionResourceDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PromotionResourceDO::getName,importRequestDTO.getName());
        queryWrapper.lambda().eq(PromotionResourceDO::getIsDeleted, SuperEntity.DR_NORMAL);
        int count = promotionResourceDAO.count(queryWrapper);*/
        if(names.stream().filter(name -> name.equals(importRequestDTO.getName())).findAny().isPresent()){ return "资源名称已存在";}

        if(StringUtil.isEmpty(importRequestDTO.getCouponValue())){
            return "面值不能为空!";
        }
        if(!StringUtils.isNumeric(importRequestDTO.getCouponValue())){
            return "面值数据不对!";
        }


        if(StringUtil.isEmpty(importRequestDTO.getValidTime())){
            return "使用时间不能为空";
        }

        if(importRequestDTO.getValidTime().contains("至")){//起止时间
            String[] validTimeArr = importRequestDTO.getValidTime().split("至");
            if(validTimeArr.length != 2){
                return "使用时间格式不对";
            }
            Date startTime = null;
            Date endTime = null;
            if(!DateUtils.isDate(validTimeArr[0]) || !DateTimeUtils.isLegalDate(validTimeArr[0].length(),validTimeArr[0], DateUtils.DATE_FORMAT)){
                return "使用时间格式不对";
            }else{
                startTime = DateUtils.getDate(validTimeArr[0]);

            }
            if(!DateUtils.isDate(validTimeArr[1]) ||  !DateTimeUtils.isLegalDate(validTimeArr[1].length(),validTimeArr[1], DateUtils.DATE_FORMAT)){
                return "使用时间格式不对";
            }else{
                endTime = DateUtils.getDate(validTimeArr[1]);
            }

            if(Objects.nonNull(startTime) && Objects.nonNull(endTime)  && startTime.after(endTime)){
                return "开始时间不能大于结束时间";
            }
            if(startTime.before(DateTime.now())){
                return "开始时间不能小于当前时间";
            }
        }else if(importRequestDTO.getValidTime().contains("自获得后")){
            Matcher m = p.matcher(importRequestDTO.getValidTime());
            if (!m.matches()) {//没有时间内容
                return "使用时间格式不对";
            }
            String timeNumber = timeNum(importRequestDTO.getValidTime());
            if(StringUtil.isEmpty(timeNumber)){
                return "使用时间数字不能为空";
            }

            m = p.matcher(importRequestDTO.getValidTime().replace("自获得后","").replace("天有效",""));

            if (!m.matches()) {
                return "使用时间格式不对";
            }

            if(importRequestDTO.getValidTime().replace("自获得后","").replace("天有效","").contains("-")){
                return "使用时间数字不能为负数";
            }
            if(Integer.valueOf(timeNumber) < 1 || importRequestDTO.getValidTime().replace("自获得后","").replace("天有效","").contains(".")){
                return "使用时间数字必须大于等于1的整数";
            }
        }else if(importRequestDTO.getValidTime().contains("不限制")){
            //
        }else{
            return "使用时间格式不对.";
        }

        if(StringUtil.isEmpty(importRequestDTO.getUseRule()) || importRequestDTO.getUseRule().length() > 5000){
            return "使用规则不能为空且最大长度不能超过5000字符";
        }

        if(StringUtil.isEmpty(importRequestDTO.getHouseName()) || importRequestDTO.getHouseName().length() > 20){
            return "项目名称不能为空且不能超过20字符";
        }

        if(StringUtil.isEmpty(importRequestDTO.getHouseVolume()) || importRequestDTO.getHouseVolume().length() > 20){
            return "房源面积不能为空且不能超过20字符";
        }

        if(StringUtil.isEmpty(importRequestDTO.getHouseMessage()) || importRequestDTO.getHouseMessage().length() > 20){
            return "楼栋单元房号信息不能为空且不能超过20字符";
        }

        if(StringUtil.isNotEmpty(importRequestDTO.getCostPrice()) && StringUtil.isNotEmpty(importRequestDTO.getDiscountPrice())){
            if(importRequestDTO.getCostPrice().length() > 10){
                return "原总价不能超过10字符";
            }
            Matcher m = p.matcher(importRequestDTO.getCostPrice());

            if (!m.matches()) {
                return "原总价格式不对";
            }

            if(importRequestDTO.getDiscountPrice().length() > 10){
                return "折后总价不能为空且不能超过10字符";
            }
            m = p.matcher(importRequestDTO.getDiscountPrice());

            if (!m.matches()) {
                return "折后总价格式不对";
            }
        }


        if(StringUtil.isNotEmpty(importRequestDTO.getBeforeCostPrice()) && StringUtil.isNotEmpty(importRequestDTO.getAfterDiscountPrice())){
            if(importRequestDTO.getBeforeCostPrice().length() > 10){
                return "原单价不能超过10字符";
            }
            Matcher m = p.matcher(importRequestDTO.getBeforeCostPrice());

            if (!m.matches()) {
                return "原单价格式不对";
            }

            if(importRequestDTO.getAfterDiscountPrice().length() > 10){
                return "折后单价不能为空且不能超过10字符";
            }
            m = p.matcher(importRequestDTO.getAfterDiscountPrice());

            if (!m.matches()) {
                return "折后单价格式不对";
            }
        }


        return "";
    }

    /**
     * 获取字符串中的时间数字
     * @param str
     * @return
     */
    private String timeNum(String str){
        String regEx="[^0-9]+";
        Pattern pattern = Pattern.compile(regEx);
        //用定义好的正则表达式拆分字符串，把字符串中的数字留出来
        String[] cs = pattern.split(str);
        if(cs != null && cs.length > 0){
            List<String> collect = Lists.newArrayList(cs).stream().filter(a -> StringUtil.isNotEmpty(a)).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(collect)){
                return collect.get(0);
            }
        }
        return null;
    }

    private List<String> getNames(){
        QueryWrapper<PromotionResourceDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PromotionResourceDO::getIsDeleted, SuperEntity.DR_NORMAL);
        queryWrapper.lambda().select(PromotionResourceDO::getName);
        List<PromotionResourceDO> list = promotionResourceDAO.list(queryWrapper);
        if(CollectionUtil.isNotEmpty(list)){
            return list.stream().map(PromotionResourceDO::getName).distinct().collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }
}
