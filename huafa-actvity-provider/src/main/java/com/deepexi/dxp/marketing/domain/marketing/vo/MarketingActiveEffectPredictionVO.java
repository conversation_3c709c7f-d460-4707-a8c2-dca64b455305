package com.deepexi.dxp.marketing.domain.marketing.vo;


import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@Api(value = "效果预测VO")
public class MarketingActiveEffectPredictionVO extends SuperVO {

    @ApiModelProperty("目标受众预估")
    private Long targetPersonCount;


    @ApiModelProperty("预估订单提交量")
    private Long targetSubmitOrderCount1;

    @ApiModelProperty("预估订单提交量")
    private Long targetSubmitOrderCount2;


    @ApiModelProperty("预估订单成交量")
    private Long targetCompleteOrderCount1;

    @ApiModelProperty("预估订单成交量")
    private Long targetCompleteOrderCount2;

    @ApiModelProperty("预估成交总额")
    private BigDecimal targetCompleteOrderPayMoneyCount1;

    @ApiModelProperty("预估成交总额")
    private BigDecimal targetCompleteOrderPayMoneyCount2;

    @ApiModelProperty("预估客单价")
    private BigDecimal targetCustomSingleMoneyCount1;

    @ApiModelProperty("预估客单价")
    private BigDecimal targetCustomSingleMoneyCount2;


    @ApiModelProperty("预估投放资源成本")
    private BigDecimal targetInvestmentCount1;

    @ApiModelProperty("预估投放资源成本")
    private BigDecimal targetInvestmentCount12;


    @ApiModelProperty("预估核销率")
    private BigDecimal verificationPercent1;

    @ApiModelProperty("预估核销率")
    private BigDecimal verificationPercent2;


    private TemplateVO templateVO;
    private ResouceVO resouceVO;
    private List<HistoryOrderVO> historyOrderVOList;

    @Data
    @EqualsAndHashCode(callSuper = true)
    @Api(value = "XX行动模板流量漏斗")
    public static class TemplateVO extends SuperVO {
        @ApiModelProperty("目标人数")
        private Long targetPersonCount;
        @ApiModelProperty("目标人数占比")
        private BigDecimal targetPersonCountPer;

        @ApiModelProperty("触达人数")
        private Long touchCount;
        @ApiModelProperty("触达人数占比")
        private BigDecimal touchCountPer;

        @ApiModelProperty("访问人数")
        private Long visitCout;
        @ApiModelProperty("访问人数占比")
        private BigDecimal visitCoutPer;

        @ApiModelProperty("领取人数")
        private Long gotCount;
        @ApiModelProperty("领取人数占比")
        private BigDecimal gotCountPer;

        @ApiModelProperty("核销人数")
        private Long verificationCount;
        @ApiModelProperty("核销人数占比")
        private BigDecimal verificationCountPer;

    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @Api(value = "资源转化漏斗")
    public static class ResouceVO extends SuperVO {
        @ApiModelProperty("触达人数")
        private Long touchCount;
        @ApiModelProperty("触达人数占比")
        private BigDecimal touchCountPer;

        @ApiModelProperty("访问人数")
        private Long visitCout;
        @ApiModelProperty("访问人数占比")
        private BigDecimal visitCoutPer;

        @ApiModelProperty("参与人数")
        private Long takeInCout;
        @ApiModelProperty("参与人数占比")
        private BigDecimal takeInCoutPer;

        @ApiModelProperty("核销人数")
        private Long verificationCount;
        @ApiModelProperty("核销人数占比")
        private BigDecimal verificationCountPer;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @Api(value = "计划触达人群历史订单数据分析")
    public static class HistoryOrderVO extends SuperVO {

        @ApiModelProperty("月份")
        private Integer mouth;

        @ApiModelProperty("订单")
        private BigDecimal orderCount;
        @ApiModelProperty("销售")
        private BigDecimal saleCount;

    }

}
