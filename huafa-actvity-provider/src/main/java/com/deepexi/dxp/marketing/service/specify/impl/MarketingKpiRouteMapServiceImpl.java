package com.deepexi.dxp.marketing.service.specify.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.deepexi.common.extension.AppRuntimeEnv;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.MarketingKpiRouteMapQuery;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.PromotionActivityResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityQuery;
import com.deepexi.dxp.marketing.service.specify.MarketingKpiRouteMapService;
import com.deepexi.dxp.middle.marketing.common.base.SuperEntity;
import com.deepexi.dxp.middle.marketing.dao.specify.MarketingKpiRouteMapDAO;
import com.deepexi.dxp.middle.marketing.dao.specify.MarketingKpiRouteMapNodeDAO;
import com.deepexi.dxp.middle.marketing.dao.specify.MarketingKpiRouteMapNodeItemsDAO;
import com.deepexi.dxp.middle.marketing.domain.dto.MarketingKpiRouteMapDTO;
import com.deepexi.dxp.middle.marketing.domain.dto.MarketingKpiRouteMapNodeDTO;
import com.deepexi.dxp.middle.marketing.domain.dto.MarketingKpiRouteMapNodeItemsDTO;
import com.deepexi.dxp.middle.marketing.domain.entity.MarketingKpiRouteMapDO;
import com.deepexi.dxp.middle.marketing.domain.entity.MarketingKpiRouteMapNodeDO;
import com.deepexi.dxp.middle.marketing.domain.entity.MarketingKpiRouteMapNodeItemsDO;
import com.deepexi.dxp.middle.promotion.dao.PromotionActivityDAO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.StringUtil;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import com.deepexi.util.pojo.ObjectCloneUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 指标组Service
 *
 * @Author: HuangBo.
 * @Date: 2020/6/18 15:37
 */

@Service
@Slf4j
@Transactional(readOnly = true)
public class MarketingKpiRouteMapServiceImpl implements MarketingKpiRouteMapService {

    @Autowired
    private MarketingKpiRouteMapNodeDAO marketingKpiRouteMapNodeDao;


    @Autowired
    private MarketingKpiRouteMapNodeItemsDAO marketingKpiRouteMapNodeItemsDao;

    @Autowired
    private MarketingKpiRouteMapDAO marketingKpiRouteMapDao;

    @Resource
    public PromotionActivityDAO promotionActivityDAO;

    @Override
    @Transactional(rollbackFor = ApplicationException.class, readOnly = false)
    public Boolean save(MarketingKpiRouteMapDTO dtoEntity) {

        //编辑界面也需限制，不允许修改已关联活动的指标路径为已禁用
        extractedStatusCheck(dtoEntity.getId(), dtoEntity.getStatus());

        List<MarketingKpiRouteMapNodeDTO> routeMapNodeDTOList = dtoEntity.getMapNodeList();
        boolean res = marketingKpiRouteMapDao.save(dtoEntity);
        if (res && CollectionUtil.isNotEmpty(routeMapNodeDTOList)) {
            // 先删除指标图点集合
            marketingKpiRouteMapNodeDao.deleteByRouteMap(dtoEntity.getId(), AppRuntimeEnv.getTenantId());
            // 再删除节点绑定的指标
            marketingKpiRouteMapNodeItemsDao.deleteByRouteMap(dtoEntity.getId());

            routeMapNodeDTOList.forEach(e -> {
                e.setRouteMapId(dtoEntity.getId());
            });
            // 批量保存指标路径图节点集合信息
            List<MarketingKpiRouteMapNodeDO> routeMapNodeDOList = ObjectCloneUtils.convertList(
                    routeMapNodeDTOList, MarketingKpiRouteMapNodeDO.class);
            res = marketingKpiRouteMapNodeDao.saveBatch(routeMapNodeDOList);
            assembleKpiRouteMapDTO(dtoEntity);

            // 批量保存路径图节点绑定的指标集合信息
            routeMapNodeDTOList.forEach(e -> {
                // 通过名称来匹配节点，已经约束同一个路径图里节点名称不能相同
                MarketingKpiRouteMapNodeDTO nodeDTO = dtoEntity.getMapNodeList()
                        .stream().filter(n -> e.getName().equals(n.getName()))
                        .collect(Collectors.toList()).get(0);
                batchSaveNodeItems(e.getNodeItemsList(), nodeDTO);
                assembleKpiRouteMapNodeDTO(nodeDTO);
            });
        }
        return res;
    }

    @Override
    public MarketingKpiRouteMapDTO queryById(Long id) {

        MarketingKpiRouteMapDTO routeMapDTO = marketingKpiRouteMapDao.queryById(id);
        if(Objects.isNull(routeMapDTO)){
            return null;
        }
        // 图路径所有的节点信息
        routeMapDTO.setMapNodeList(marketingKpiRouteMapNodeDao.queryByRouteMap(routeMapDTO.getId(), AppRuntimeEnv.getTenantId()));
        // 图路径所有节点绑定的所有指标信息
        List<MarketingKpiRouteMapNodeItemsDTO> allNodeItemsDTOList = marketingKpiRouteMapNodeItemsDao.queryByRouteMap(routeMapDTO.getId());
        // 将指标信息归类到自己绑定的节点下面
        if (CollectionUtil.isNotEmpty(allNodeItemsDTOList)) {
            routeMapDTO.getMapNodeList().forEach(e -> {
                List<MarketingKpiRouteMapNodeItemsDTO> nodeItemsDTOList = allNodeItemsDTOList.stream()
                        .filter(n -> e.getId().equals(n.getRouteMapNodeId())).collect(Collectors.toList());
                e.setNodeItemsList(nodeItemsDTOList);
            });
        }
        return routeMapDTO;
    }

    /**
     * 根据查询条件获取集合
     */
    @Override
    public List<MarketingKpiRouteMapDTO> queryList(MarketingKpiRouteMapQuery query) {
        return marketingKpiRouteMapDao.queryList(query);
    }

    /**
     * 分页查询列表
     */
    @Override
    public PageBean<MarketingKpiRouteMapDTO> pageList(MarketingKpiRouteMapQuery query) {
        PageBean<MarketingKpiRouteMapDTO> marketingKpiRouteMapPage = marketingKpiRouteMapDao.pageList(query);
        List<MarketingKpiRouteMapDTO> content = marketingKpiRouteMapPage.getContent();
        if (CollectionUtil.isNotEmpty(content)) {

            List<Long> routeMapIds = content.stream().map(MarketingKpiRouteMapDTO::getId).collect(Collectors.toList());
            List<MarketingKpiRouteMapNodeDTO> marketingKpiRouteMapNodeList = marketingKpiRouteMapNodeDao.queryByRouteMap(routeMapIds);
            Map<Long, List<MarketingKpiRouteMapNodeDTO>> marketingKpiRouteMapNodeMap = Maps.newHashMap();
            if (CollectionUtil.isNotEmpty(marketingKpiRouteMapNodeList)){
                marketingKpiRouteMapNodeMap = marketingKpiRouteMapNodeList.stream().collect(Collectors.groupingBy(MarketingKpiRouteMapNodeDTO::getRouteMapId));
            }

            Map<Long, List<MarketingKpiRouteMapNodeDTO>> finalMarketingKpiRouteMapNodeMap = marketingKpiRouteMapNodeMap;
            content.forEach(kpi->{
                kpi.setMapNodeList(finalMarketingKpiRouteMapNodeMap.get(kpi.getId()));
            });
        }
        return marketingKpiRouteMapPage;
    }

    /**
     * 启用/禁用
     *
     * @return
     */
    @Override
    @Transactional(rollbackFor = ApplicationException.class, readOnly = false)
    public Boolean processStatus(Long id, Integer status) {
        extractedStatusCheck(id, status);
        return marketingKpiRouteMapDao.processStatus(id, status);
    }

    private void extractedStatusCheck(Long id, Integer status) {
        if (id != null && status.equals(SuperEntity.STATUS_DISABLE)){
            //【指标路径】已经关联活动的指标路径不能禁用
            PromotionActivityQuery promotionActivityQuery = new PromotionActivityQuery();
            promotionActivityQuery.setKpiPath(id);
            promotionActivityQuery.setPage(1);
            promotionActivityQuery.setSize(1);
            IPage<PromotionActivityResponseDTO> promotionActivityResponsePage = promotionActivityDAO.pageListOrderByCreateTime(promotionActivityQuery);
            if (CollectionUtil.isNotEmpty(promotionActivityResponsePage.getRecords())){
                throw new ApplicationException("已经关联活动的指标路径不能禁用!");
            }
        }
    }

    @Override
    public Boolean existByName(String tenantId, String name, String oldName) {
//        if (StringUtil.isBlank(tenantId)) {
//            tenantId = AppRuntimeEnv.getTenantId();
//        }
        if (StringUtil.isBlank(StringUtils.trimWhitespace(name))) {
            throw new ApplicationException("名称不能为空.");
        }
        if (name.equals(oldName)) {
            return false;
        }
        return CollectionUtil.isNotEmpty(marketingKpiRouteMapDao.queryByName(tenantId, name));
    }

    @Override
    @Transactional(readOnly = false)
    public Boolean deleteByRoute(Long id) {
        MarketingKpiRouteMapDO routeMapDO = marketingKpiRouteMapDao.getById(id);
        if (Objects.isNull(routeMapDO)) {
            throw new ApplicationException("指标路径不存在");
        }

        marketingKpiRouteMapDao.deleteByRouteId(id);

        List<MarketingKpiRouteMapNodeDTO> marketingKpiRouteMapNodeDTOList = marketingKpiRouteMapNodeDao.queryByRouteMap(id, null);
        if (Objects.nonNull(marketingKpiRouteMapNodeDTOList)){
            marketingKpiRouteMapNodeDao.deleteByRouteMap(id,null);
        }

        List<MarketingKpiRouteMapNodeItemsDTO> marketingKpiRouteMapNodeItemsDTOList = marketingKpiRouteMapNodeItemsDao.queryByRouteMap(id);

        if (Objects.nonNull(marketingKpiRouteMapNodeItemsDTOList)){
            marketingKpiRouteMapNodeItemsDao.deleteByRouteMap(id);
        }
        return true;
    }

    private MarketingKpiRouteMapDTO assembleKpiRouteMapDTO(MarketingKpiRouteMapDTO routeMapDTO) {
        List<MarketingKpiRouteMapNodeDTO> routeMapNodeDTOList = marketingKpiRouteMapNodeDao.queryByRouteMap(routeMapDTO.getId(), AppRuntimeEnv.getTenantId());
        routeMapDTO.setMapNodeList(routeMapNodeDTOList);
        return routeMapDTO;
    }

    private MarketingKpiRouteMapNodeDTO assembleKpiRouteMapNodeDTO(MarketingKpiRouteMapNodeDTO routeMapNodeDTO) {
        List<MarketingKpiRouteMapNodeItemsDTO> nodeItemsDTOList = marketingKpiRouteMapNodeItemsDao.queryByRouteMapNode(routeMapNodeDTO.getId());
        routeMapNodeDTO.setNodeItemsList(nodeItemsDTOList);
        return routeMapNodeDTO;
    }

    /**
     * 批量保存路径图节点绑定的指标信息
     */
    private void batchSaveNodeItems(List<MarketingKpiRouteMapNodeItemsDTO> nodeItemsDTOList, MarketingKpiRouteMapNodeDTO nodeDTO) {
        if (CollectionUtil.isNotEmpty(nodeItemsDTOList)) {
            nodeItemsDTOList.forEach(e -> {
                e.setRouteMapId(nodeDTO.getRouteMapId());
                e.setRouteMapNodeId(nodeDTO.getId());
            });
            // 批量保存路径图节点绑定的指标信息
            List<MarketingKpiRouteMapNodeItemsDO> nodeItemsDOList = ObjectCloneUtils.convertList(
                    nodeItemsDTOList, MarketingKpiRouteMapNodeItemsDO.class);
            marketingKpiRouteMapNodeItemsDao.saveBatch(nodeItemsDOList);
        }
    }


}
