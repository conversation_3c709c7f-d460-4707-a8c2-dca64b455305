package com.deepexi.dxp.marketing.domain.marketing.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * DolphinScheduler流程定义列表DTO
 *
 * <AUTHOR>
 * @Date 2020/3/27
 */
@Data
@ApiModel
public class DolphinSchedulerProcessDTO implements Serializable {

    @ApiModelProperty(value = "主键")
    private String id;

}
