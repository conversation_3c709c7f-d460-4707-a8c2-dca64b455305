package com.deepexi.dxp.marketing.controller.specify.adminapi;

import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.ActivityEvaluationDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.ActivityEvaluationQueryDTO;
import com.deepexi.dxp.marketing.service.specify.ActivityEvaluationService;
import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 活动评价PC管理端控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin-api/v1/activity-evaluation")
@Api(value = "活动评价管理端", tags = "活动评价管理端")
@Slf4j
public class ActivityEvaluationController {

    @Resource
    private ActivityEvaluationService activityEvaluationService;

    @PostMapping("/list")
    @ApiOperation(value = "分页查询活动评价列表")
    public Data<PageBean<ActivityEvaluationDTO>> getEvaluationList(@Valid @RequestBody ActivityEvaluationQueryDTO query) {
        log.info("查询活动评价列表，查询条件：{}", query);
        PageBean<ActivityEvaluationDTO> result = activityEvaluationService.getEvaluationList(query);
        return new Data<>(result);
    }

}