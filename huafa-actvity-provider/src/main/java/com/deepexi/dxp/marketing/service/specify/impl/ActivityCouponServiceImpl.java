package com.deepexi.dxp.marketing.service.specify.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.deepexi.dxp.marketing.common.base.utils.CommonExceptionCode;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.config.HuafaConstantConfig;
import com.deepexi.dxp.marketing.constant.PayConstant;
import com.deepexi.dxp.marketing.constant.RedisConstants;
import com.deepexi.dxp.marketing.converter.CouponConverter;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.CouponPartakeRecordQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.CustomerFeedbackQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.*;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityCouponPartakeLogResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.CouponActivityResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.OrderPayResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.ResourceHisDetailResponseDTO;
import com.deepexi.dxp.marketing.enums.coupon.VerifyTypeEnum;
import com.deepexi.dxp.marketing.enums.coupon.WhetherEnum;
import com.deepexi.dxp.marketing.enums.resource.ActivityStatusEnum;
import com.deepexi.dxp.marketing.enums.resource.ResourceGrantWayEnum;
import com.deepexi.dxp.marketing.enums.specify.*;
import com.deepexi.dxp.marketing.manager.promotion.PromotionActivityManager;
import com.deepexi.dxp.marketing.service.specify.*;
import com.deepexi.dxp.marketing.utils.ExcelExportUtil;
import com.deepexi.dxp.marketing.utils.GenerateIdUtil;
import com.deepexi.dxp.marketing.utils.MapBeanUtil;
import com.deepexi.dxp.marketing.utils.PhoneEncryUtils;
import com.deepexi.dxp.middle.marketing.common.base.SuperEntity;
import com.deepexi.dxp.middle.marketing.domain.dto.MarketingKpiRouteMapDTO;
import com.deepexi.dxp.middle.promotion.dao.PromotionActivityDAO;
import com.deepexi.dxp.middle.promotion.dao.PromotionActivityLimitDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.*;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityLimitDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.*;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.DateUtils;
import com.deepexi.util.JsonUtil;
import com.deepexi.util.StringUtil;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import com.deepexi.util.pojo.ObjectCloneUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.compress.utils.Lists;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ActivityCouponServiceImpl implements ActivityCouponService {

    @Autowired
    private PromotionActivityDAO promotionActivityDAO;

    @Autowired
    private ActivityPageDAO activityPageDAO;

    @Autowired
    private ActivityPageShareDAO activityPageShareDAO;

    @Autowired
    private ActivityParticipationDAO activityParticipationDAO;

    @Autowired
    private PromotionResourceDAO promotionResourceDAO;

    @Autowired
    private PromotionHisResourceDAO promotionHisResourceDAO;

    @Autowired
    private PromotionActivityLimitDAO promotionActivityLimitDAO;

    @Autowired
    private ActivityPartakeLogDAO activityPartakeLogDAO;

    @Resource
    public CustomerFeedbackDAO customerFeedbackDAO;

    @Resource
    private ActivityOrderService activityOrderService;

    @Resource
    private WxPayService wxPayService;

    @Value("${deepexi.marketing.specify.pay-call-back-base-url}")
    private String callBackBaseUrl;

    @Autowired
    private ActivityTargetDAO activityTargetDAO;

    @Autowired
    private MarketingKpiRouteMapService routeMapService;

    @Autowired
    private ActivityPromotionChannelService activityPromotionChannelService;

    @Autowired
    private GenerateIdUtil generateIdUtil;

    @Resource
    public ActivityVerifyDAO activityVerifyDAO;

    @Autowired
    private PromotionActivityManager promotionActivityManager;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private HuafaConstantConfig huafaConstantConfig;


    @Override
    @Transactional(rollbackFor = ApplicationException.class)
    public boolean save(CouponRequestDTO requestDTO) {
        //添加校验
        this.addCheck(requestDTO);
        if(StringUtil.isNotEmpty(requestDTO.getActivityPageVO().getPhone()) && !PhoneEncryUtils.isPhoneLegal(requestDTO.getActivityPageVO().getPhone())){
            throw new ApplicationException("联系电话格式不正确!");
        }

        requestDTO.setCreatedBy(StringUtil.isEmpty(requestDTO.getCreatedBy()) ? "admin":requestDTO.getCreatedBy());
        requestDTO.setUpdatedBy(StringUtil.isEmpty(requestDTO.getCreatedBy()) ? "admin":requestDTO.getCreatedBy());

        //保存活动基本信息
        PromotionActivityDO promotionActivityDO = CouponConverter.converter(requestDTO);
        promotionActivityDAO.save(promotionActivityDO);

        //保存适用项目
        List<ActivityParticipationDO> activityParticipationList = CouponConverter.participationConverter(requestDTO,promotionActivityDO.getId());
        activityParticipationDAO.saveBatch(activityParticipationList);

        //保存活动规则配置
        PromotionActivityLimitDO promotionActivityLimitDO = CouponConverter.limitConverter(requestDTO);
        promotionActivityLimitDO.setActivityId(promotionActivityDO.getId());
        promotionActivityLimitDAO.save(promotionActivityLimitDO);

        //保存领券/购券规则配置
        List<HisResourceJsonVO> hisResourceJsonList = ObjectCloneUtils.convertList(requestDTO.getPrizeList(),HisResourceJsonVO.class);
//        promotionActivityDAO.saveHisResource(hisResourceJsonList,promotionActivityDO.getId());

        //保存界面活动页配置
        ActivityPageDO activityPageDO = CouponConverter.activityPageConverter(requestDTO);
        activityPageDO.setActivityId(promotionActivityDO.getId());
        activityPageDAO.save(activityPageDO);

        //保存界面分享页配置
        ActivityPageShareDO activityPageShareDO = CouponConverter.activityShareConverter(requestDTO);
        activityPageShareDO.setActivityId(promotionActivityDO.getId());
        activityPageShareDAO.save(activityPageShareDO);

        activityPromotionChannelService.createDefaults(promotionActivityDO.getId(),ActivityPromotionTypeEnum.ACTIVITY.getType(), promotionActivityDO.getPaTemplateId());
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateActivityById(CouponRequestDTO requestDTO) {

        requestDTO.setCreatedBy(StringUtil.isEmpty(requestDTO.getCreatedBy()) ? "admin":requestDTO.getCreatedBy());
        requestDTO.setUpdatedBy(StringUtil.isEmpty(requestDTO.getCreatedBy()) ? "admin":requestDTO.getCreatedBy());

        Long id = requestDTO.getId();
        //未开始的状态下支持编辑全部信息
        if (requestDTO.getStatus().equals(Integer.valueOf(ActivityStatusEnum.UN_START.getId())) || requestDTO.getStatus().equals(Integer.valueOf(ActivityStatusEnum.DRAFT.getId()))) {
            //添加校验
            requestDTO.setId(id);
            this.updateCheck(requestDTO);
            //编辑活动基本信息
            PromotionActivityDO promotionActivityDO = CouponConverter.converter(requestDTO);
            promotionActivityDO.setId(id);
            promotionActivityDAO.updateById(promotionActivityDO);

            //保存适用项目---->删除旧数据重新添加
            UpdateWrapper<ActivityParticipationDO> participationUpdateWrapper = new UpdateWrapper<>();
            participationUpdateWrapper.lambda().eq(ActivityParticipationDO::getActivityId, id);
            participationUpdateWrapper.lambda().eq(ActivityParticipationDO::getDeleted, 0);
            activityParticipationDAO.remove(participationUpdateWrapper);
            List<ActivityParticipationDO> activityParticipationList = CouponConverter.participationConverter(requestDTO, promotionActivityDO.getId());
            activityParticipationDAO.saveBatch(activityParticipationList);

            //编辑活动规则配置
            PromotionActivityLimitDO promotionActivityLimitDO = CouponConverter.limitConverter(requestDTO);
            UpdateWrapper<PromotionActivityLimitDO> wrapperLimit = new UpdateWrapper<>();
            wrapperLimit.lambda().eq(PromotionActivityLimitDO::getActivityId, id);
            promotionActivityLimitDAO.update(promotionActivityLimitDO, wrapperLimit);

            //保存活动配置----》删除旧数据重新添加
            UpdateWrapper<PromotionHisResourceDO> hisResourceUpdateWrapper = new UpdateWrapper<>();
            hisResourceUpdateWrapper.lambda().eq(PromotionHisResourceDO::getActivityId, id);
            hisResourceUpdateWrapper.lambda().eq(PromotionHisResourceDO::getDeleted, SuperEntity.DR_NORMAL);
            promotionHisResourceDAO.remove(hisResourceUpdateWrapper);
            List<HisResourceJsonVO> hisResourceJsonList = ObjectCloneUtils.convertList(requestDTO.getPrizeList(), HisResourceJsonVO.class);
//            promotionHisResourceDAO.saveHisResource(hisResourceJsonList, promotionActivityDO.getId());

            //编辑界面活动页配置
            ActivityPageDO activityPageDO = CouponConverter.activityPageConverter(requestDTO);
            UpdateWrapper<ActivityPageDO> wrapperPage = new UpdateWrapper<>();
            wrapperPage.lambda().eq(ActivityPageDO::getActivityId, id);
            activityPageDAO.update(activityPageDO, wrapperPage);

            //编辑界面分享页配置
            ActivityPageShareDO activityPageShareDO = CouponConverter.activityShareConverter(requestDTO);
            UpdateWrapper<ActivityPageShareDO> wrapperShare = new UpdateWrapper<>();
            wrapperShare.lambda().eq(ActivityPageShareDO::getActivityId, id);
            activityPageShareDAO.update(activityPageShareDO, wrapperShare);
        }else if (requestDTO.getStatus().equals(Integer.valueOf(ActivityStatusEnum.IN_PROGRESS.getId()))){
            //进行中的状态下仅支持增加奖品数量，其它信息不可编辑
            promotionHisResourceDAO.updateResourceIssuedNumber(ObjectCloneUtils.convertList(requestDTO.getPrizeList(), HisResourceJsonVO.class));
        }
        return Boolean.TRUE;
    }

    /**
     * 优惠券活动修改较验
     * @param requestDTO
     */
    private void updateCheck(CouponRequestDTO requestDTO) {
        PromotionActivityDO byId = promotionActivityDAO.getById(requestDTO.getId());
        if (byId == null){
            throw new ApplicationException("活动不存在");
        }
        this.check(requestDTO);
    }

    /**
     * 优惠券活动添加-活动较验
     * @param requestDTO
     */
    private void addCheck(CouponRequestDTO requestDTO) {
        QueryWrapper<PromotionActivityDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PromotionActivityDO::getName,requestDTO.getName());
        if(requestDTO.getId() != null){
            queryWrapper.lambda().ne(PromotionActivityDO::getId,requestDTO.getId());
        }
        PromotionActivityDO one = promotionActivityDAO.getOne(queryWrapper);
        if (Objects.nonNull(one)){
            throw new ApplicationException("活动名称重复!");
        }
        this.check(requestDTO);
    }

    /**
     * 优惠券活动添加-参数较验
     * @param requestDTO
     */
    private void check(CouponRequestDTO requestDTO) {

        List<PrizeConfigVO> prizeList = requestDTO.getPrizeList();
        if(CollectionUtil.isEmpty(prizeList)){
            throw new ApplicationException("领券/购券规则配置不能为空");
        }



        Date startDate = DateUtils.getDate(requestDTO.getStartTime(), DateUtils.DEFAULT_DATE_TIME_FORMAT);
        Date endDate = DateUtils.getDate(requestDTO.getEndTime(), DateUtils.DEFAULT_DATE_TIME_FORMAT);
        Date now = DateUtils.now();
        if (startDate.after(endDate)){
            throw new ApplicationException("活动结束时间不能小于活动开始时间");
        }
        //活动状态设置
        if (requestDTO.getStatus() != null) {
            if (endDate.before(now)) {
                //结束时间在当前时间之前  ---》 活动已结束
                requestDTO.setStatus(Integer.valueOf(ActivityStatusEnum.FINISH.getId()));
            } else if (startDate.after(now)) {
                //开始时间在当前时间之后 ---》 活动未开始
                requestDTO.setStatus(Integer.valueOf(ActivityStatusEnum.UN_START.getId()));
            } else {
                requestDTO.setStatus(Integer.valueOf(ActivityStatusEnum.IN_PROGRESS.getId()));
            }
        }
    }


    @Override
    public CouponActivityResponseDTO detail(Long id) {
        if (Objects.isNull(id)){
            throw new ApplicationException("活动id不能为空");
        }

        //查询活动基础信息
        PromotionActivityDO detail = promotionActivityDAO.getById(id);
        if (Objects.isNull(detail)){
            throw new ApplicationException("活动不存在");
        }
        CouponActivityResponseDTO couponActivityResponseDTO = detail.clone(CouponActivityResponseDTO.class);
        couponActivityResponseDTO.setExt((ActivityExtVO)MapBeanUtil.map2Object(detail.getExt(),ActivityExtVO.class));
        Integer activityGoal = detail.getExt() == null ? null : Optional.ofNullable(detail.getExt().get("activityGoal")).map(c->Integer.parseInt(c.toString())).orElse(null);
        if (Objects.nonNull(activityGoal)){
            ActivityTargetDO activityTargetDO = activityTargetDAO.getById(activityGoal);
            //活动目标名称
            couponActivityResponseDTO.setActivityTargetName(Objects.nonNull(activityTargetDO)?activityTargetDO.getTargetName():"");
        }

        //指标路径名称
        Integer kpiPath = detail.getExt() == null ? null : Optional.ofNullable(detail.getExt().get("kpiPath")).map(c->Integer.parseInt(c.toString())).orElse(null);
        if (Objects.nonNull(kpiPath)){
            MarketingKpiRouteMapDTO marketingKpiRouteMapDTO = routeMapService.queryById(Long.valueOf(kpiPath));
            couponActivityResponseDTO.setMarketingKpiRouteMapName(Objects.nonNull(marketingKpiRouteMapDTO)?marketingKpiRouteMapDTO.getName():"");
        }


        //获取项目列表
        List<ActivityParticipationDO> activityParticipationList = activityParticipationDAO.listByActivityId(id);
        if (CollectionUtil.isNotEmpty(activityParticipationList)){
            List<ActivityParticipationVO> activityParticipationVOList = ObjectCloneUtils.convertList(activityParticipationList, ActivityParticipationVO.class);
            couponActivityResponseDTO.setProjectInfoList(activityParticipationVOList);
        }

        //活动规则信息
        couponActivityResponseDTO.setRuleConfigVO(this.getActivityLimit(id));

        //活动信息
        couponActivityResponseDTO.setPrizeList(this.getPrizeList(id));

        //活动页
        ActivityPageDO activityPageDO = activityPageDAO.getByActivity(id, ActivityTypeEnum.ACTIVITY.getId());
        ActivityPageVO activityPageVO = Optional.ofNullable(activityPageDO).orElseGet(ActivityPageDO::new).clone(ActivityPageVO.class);
        String bottomBtnType = activityPageDO.getBottomBtnType();
        if(StringUtil.isNotEmpty(bottomBtnType)){
            String[] split = bottomBtnType.split(",");
            Integer[]  types = (Integer[])ConvertUtils.convert(split,Integer.class);
            activityPageVO.setBottomBtnTypes(Arrays.asList(types));
        }

        couponActivityResponseDTO.setActivityPageVO(activityPageVO);

        //活动分享页
        ActivityPageShareDO activityPageShareDO = activityPageShareDAO.getByActivity(id, ActivityTypeEnum.ACTIVITY.getId());
        couponActivityResponseDTO.setActivityPageShareVO(Optional.ofNullable(activityPageShareDO).orElseGet(ActivityPageShareDO::new).clone(ActivityPageShareVO.class));
        return couponActivityResponseDTO;
    }

    /**
     * 查询活动规则
     * @param id 活动id
     * @return
     */
    private RuleConfigVO getActivityLimit(Long id) {

        //查询活动规则信息
        List<PromotionActivityLimitDO> promotionActivityLimit = promotionActivityLimitDAO.selectByActivityId(id);
        if (CollectionUtil.isEmpty(promotionActivityLimit)){
            return new RuleConfigVO();
        }
        return (RuleConfigVO)MapBeanUtil.map2Object(promotionActivityLimit.get(0).getExt(), RuleConfigVO.class);
    }

    /**
     * 领券/购券规则信息
     * @param activityId
     * @return
     */
    private List<ResourceHisDetailResponseDTO> getPrizeList(Long activityId) {
        QueryWrapper<PromotionHisResourceDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PromotionHisResourceDO::getActivityId,activityId);
        queryWrapper.lambda().eq(PromotionHisResourceDO::getDeleted,SuperEntity.DR_NORMAL);
        List<PromotionHisResourceDO> list = promotionHisResourceDAO.list(queryWrapper);
        List<ResourceHisDetailResponseDTO> resourceHisDetailResponseDTOS = ObjectCloneUtils.convertList(list, ResourceHisDetailResponseDTO.class);
        return Optional.ofNullable(resourceHisDetailResponseDTOS).orElse(Lists.newArrayList());
    }

    @Override
    public PageBean<ActivityCouponPartakeLogResponseDTO> partakeLogList(CouponPartakeRecordQuery query) {
        PageBean<ActivityCouponPartakeLogResponseDTO> page = activityPartakeLogDAO.couponFindPage(query);

        this.getCustomerFeedbackList(page.getContent(),query.getActivityId());
        return page;
    }

    /**
     * 拼装用户登记明细和 礼品券发放方式
     * @param content
     * @param activityId
     */
    private void getCustomerFeedbackList(List<ActivityCouponPartakeLogResponseDTO> content,Long activityId) {
        if (CollectionUtil.isEmpty(content)){
            return;
        }
        //提取用户id
       // Set<String> userIds = content.stream().map(ActivityCouponPartakeLogResponseDTO::getUserId).collect(Collectors.toSet());
        Set<String> phoneList = content.stream().map(ActivityCouponPartakeLogResponseDTO::getPhone).collect(Collectors.toSet());

        //提取资源id
        Set<Long> resourceIds = content.stream().map(ActivityCouponPartakeLogResponseDTO::getResourceId).collect(Collectors.toSet());

        QueryWrapper<ActivityFormFeedbackDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ActivityFormFeedbackDO::getActivityId,activityId);
        queryWrapper.lambda().in(ActivityFormFeedbackDO::getPhone,phoneList);
        queryWrapper.lambda().in(ActivityFormFeedbackDO::getDeleted,0);
        List<ActivityFormFeedbackDO> list = customerFeedbackDAO.list(queryWrapper);

        Map<String, ActivityFormFeedbackDO> formFeedbackMap = Maps.newHashMap();
        Map<Long, PromotionHisResourceDO> resourceMap = Maps.newHashMap();

        if (CollectionUtil.isNotEmpty(list)){
            formFeedbackMap = list.stream().collect(Collectors.toMap(ActivityFormFeedbackDO::getPhone, formFeedbackDO -> formFeedbackDO));
        }

        QueryWrapper<PromotionHisResourceDO> queryWrapperResource = new QueryWrapper<>();
        queryWrapperResource.lambda().in(PromotionHisResourceDO::getId,resourceIds);
        //queryWrapperResource.lambda().eq(PromotionHisResourceDO::getActivityId,activityId);
        List<PromotionHisResourceDO> resourceList = promotionHisResourceDAO.list(queryWrapperResource);
        if (CollectionUtil.isNotEmpty(resourceList)){
            resourceMap = resourceList.stream().collect(Collectors.toMap(PromotionHisResourceDO::getId, Function.identity()));
        }

        Map<Long, PromotionHisResourceDO> finalResourceMap = resourceMap;
        Map<String, ActivityFormFeedbackDO> finalFormFeedbackMap = formFeedbackMap;
        try{
            content.forEach(log->{
                ActivityFormFeedbackDO activityFormFeedbackDO = finalFormFeedbackMap == null ? null : finalFormFeedbackMap.get(log.getPhone());
                PromotionHisResourceDO promotionHisResourceDO = finalResourceMap.get(log.getResourceId());
                if (activityFormFeedbackDO != null){

                    FeedbackInfoVO feedbackVO = JSON.parseObject(JSON.toJSONString(activityFormFeedbackDO.getLimits()),FeedbackInfoVO.class);
                    log.setFeedback(feedbackVO);
                }
                //资源信息
                log.setResourceHisDetailResponseDTO(promotionHisResourceDO.clone(ResourceHisDetailResponseDTO.class));
                //免费领取时间前端展示
                if(StringUtil.isNotEmpty(log.getGetTime())){
                    log.setPayTime(DateUtils.getDate(log.getGetTime(),DateUtils.DEFAULT_DATE_TIME_FORMAT));
                }
            });
        }catch(Exception e){
            log.error("获取资源信息异常!",e);
        }
    }

    @Override
    public void exportPartakeLogExcel(HttpServletResponse response, CouponPartakeRecordQuery query) {
        List<ActivityCouponPartakeLogResponseDTO> content = activityPartakeLogDAO.couponFindList(query);
        ExcelExportUtil util = new ExcelExportUtil();
        util.setTitle("优惠券活动参与明细");
        String[] heardList = new String[]{"序号","活动订单编号","昵称","手机号","资源类型","资源名称","资源明细","领取方式","领取/支付时间","支付金额","发放方式"};
        String[] headerKey = new String[]{"","orderId","nickName","phone","type","name","resourceDetail","receiveMode","payTime","payMoney","grantWay"};
        List<Map<String, String>> dataList = new ArrayList<>();
        Long activityId = query.getActivityId();
        if(CollectionUtil.isNotEmpty(content)){
            //提取用户id
            Set<String> userIds = content.stream().map(ActivityCouponPartakeLogResponseDTO::getUserId).collect(Collectors.toSet());

            //提取资源id
            Set<Long> resourceIds = content.stream().map(ActivityCouponPartakeLogResponseDTO::getResourceId).collect(Collectors.toSet());

            QueryWrapper<ActivityFormFeedbackDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(ActivityFormFeedbackDO::getActivityId,activityId);
            queryWrapper.lambda().in(ActivityFormFeedbackDO::getUserId,userIds);
            queryWrapper.lambda().in(ActivityFormFeedbackDO::getDeleted,0);
            List<ActivityFormFeedbackDO> list = customerFeedbackDAO.list(queryWrapper);

            Map<String, ActivityFormFeedbackDO> formFeedbackMap = Maps.newHashMap();
            Map<Long, PromotionHisResourceDO> resourceMap = Maps.newHashMap();

            if (CollectionUtil.isNotEmpty(list)){
                formFeedbackMap = list.stream().collect(Collectors.toMap(ActivityFormFeedbackDO::getUserId, formFeedbackDO -> formFeedbackDO));
            }

            QueryWrapper<PromotionHisResourceDO> queryWrapperResource = new QueryWrapper<>();
            queryWrapperResource.lambda().in(PromotionHisResourceDO::getResourceId,resourceIds);
            queryWrapperResource.lambda().eq(PromotionHisResourceDO::getActivityId,activityId);
            List<PromotionHisResourceDO> resourceList = promotionHisResourceDAO.list(queryWrapperResource);
            if (CollectionUtil.isNotEmpty(resourceList)){
                resourceMap = resourceList.stream().collect(Collectors.toMap(PromotionHisResourceDO::getResourceId, Function.identity()));
            }

            Map<Long, PromotionHisResourceDO> finalResourceMap = resourceMap;
            Map<String, ActivityFormFeedbackDO> finalFormFeedbackMap = formFeedbackMap;
            for (int i = 0; i < content.size(); i++) {
                Map<String, String> data = Maps.newHashMap();
                ActivityCouponPartakeLogResponseDTO dto = content.get(i);
                PromotionHisResourceDO promotionHisResourceDO = finalResourceMap.get(dto.getResourceId());
                data.put(headerKey[0], (i+1) + "");
                data.put(headerKey[1], dto.getOrderNo());
                data.put(headerKey[2], dto.getNickName());
                data.put(headerKey[3], dto.getPhone());
                data.put(headerKey[4], PromotionResourceTypeEnum.getValueById(promotionHisResourceDO.getType()));
                data.put(headerKey[5], promotionHisResourceDO.getName());
                StringBuffer sb = new StringBuffer();
                sb.append("细分类别:").append(PromotionResourceCouponCategoryEnum.getValueById(promotionHisResourceDO.getCouponCategory())).append("\t\n");
                sb.append("卡券类型:").append(PromotionResourceCouponTypeEnum.getValueById(promotionHisResourceDO.getCouponType())).append("\t\n");
                if(promotionHisResourceDO.getCouponType().equals(PromotionResourceCouponTypeEnum.DISCOUNT_COUPON)){
                    sb.append("卡券面值:").append(promotionHisResourceDO.getCouponValue()).append("折\t\n");
                }else if(promotionHisResourceDO.getCouponType().equals(PromotionResourceCouponTypeEnum.VOUCHER)){
                    sb.append("卡券面值:").append(promotionHisResourceDO.getCouponValue()).append("元\t\n");
                }
                sb.append("房源名称:").append(promotionHisResourceDO.getHouseName());
                data.put(headerKey[6], sb.toString());
                data.put(headerKey[7], ReceiveModeEnum.getValueById(promotionHisResourceDO.getReceiveMode()));
                data.put(headerKey[8], dto.getPayTime() != null ?DateUtils.format(dto.getPayTime(),DateUtils.DEFAULT_DATE_TIME_FORMAT):"");
                data.put(headerKey[9], dto.getPayMoney() != null ? String.valueOf(dto.getPayMoney()) :"");
                data.put(headerKey[10], ResourceGrantWayEnum.getValueById(promotionHisResourceDO.getGrantWay()));
                dataList.add(data);
            }
        }
        util.setHeardKey(headerKey);
        util.setHeardList(heardList);
        util.setData(dataList);
        util.setFileName("优惠券活动参与明细");
        util.setSheetName("优惠券活动参与明细");
        try {
            util.exportExport(response);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean receiveCoupons(ReceiveCouponRequestDTO dto) {

        PromotionHisResourceDO promotionHisResourceDO = promotionHisResourceDAO.getById(dto.getHisResourceId());

        if(!ReceiveModeEnum.FREE.getId().equals(promotionHisResourceDO.getReceiveMode())){
            throw new ApplicationException("该优惠券领取方式不是免费领取!");
        }

        //较验
        check(dto,promotionHisResourceDO);

        //获取项目信息
        if(dto.getProjectId() != null){
            QueryWrapper<ActivityParticipationDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(ActivityParticipationDO::getActivityId,dto.getActivityId());
            queryWrapper.lambda().eq(ActivityParticipationDO::getProjectId,dto.getProjectId());
            ActivityParticipationDO activityParticipationDO = activityParticipationDAO.getOne(queryWrapper);
            dto.setProjectName(activityParticipationDO.getProjectName());
        }

        try{
            boolean decrFlag = promotionActivityManager.decrRedisQty(promotionHisResourceDO.getId(), promotionHisResourceDO.getActivityId());
            if(!decrFlag){
                throw new ApplicationException("当前资源不足，无法领取");
            }
            //领券记录
            String resourceCode = generateIdUtil.getResourceCode();
            ActivityPartakeLogDTO activityPartakeLogDTO = CouponConverter.partakeLogConverter(resourceCode,dto, promotionHisResourceDO);
            activityPartakeLogDAO.save(activityPartakeLogDTO);

            //更新剩余资源数量--更新条件 主键及资源剩余数量必须大于0,更新内容为资源剩余数量-1及更新时间
            UpdateWrapper<PromotionHisResourceDO> wrapper = new UpdateWrapper<>();
            wrapper.lambda().eq(PromotionHisResourceDO::getId,promotionHisResourceDO.getId()).gt(PromotionHisResourceDO::getRemainingQuantity,0).set(PromotionHisResourceDO::getRemainingQuantity,promotionHisResourceDO.getRemainingQuantity()-1).set(PromotionHisResourceDO::getUpdatedTime,new Date());

            //添加核销记录
            activityVerifyDAO.save(CouponConverter.verifyConverter(resourceCode,dto,promotionHisResourceDO,VerifyTypeEnum.COUPON.getId(), WhetherEnum.NO.getId()));
            return promotionHisResourceDAO.update(null, wrapper);

        }catch (Exception e){
            promotionActivityManager.incrRedisQty(promotionHisResourceDO.getId(),dto.getActivityId());
            throw e;
        }

    }

    @Override
    public Boolean receiveCheck(ReceiveCouponRequestDTO dto) {
        PromotionHisResourceDO promotionHisResourceDO = promotionHisResourceDAO.getById(dto.getHisResourceId());

        //活动较验
        PromotionActivityDO activity = promotionActivityDAO.getById(dto.getActivityId());
        if (Objects.isNull(activity)){
            throw new ApplicationException("活动不存在");
        }
        if (activity.getStatus().equals(Integer.parseInt(ActivityStatusEnum.UN_START.getId()))){
            throw new ApplicationException("活动尚未开始，请耐心等待！");
        }else if (!activity.getStatus().equals(Integer.parseInt(ActivityStatusEnum.IN_PROGRESS.getId()))){
            throw new ApplicationException("来晚啦，活动已结束！");
        }
        //库存较验

        if(Objects.isNull(promotionHisResourceDO)){
            throw new ApplicationException("参数错误，优惠券不存在！");
        }

        //取redis库存较验
        long currentCount = redissonClient.getAtomicLong(RedisConstants.CACHE_PREV_KEY_ACT_RESOURCE_INFO_COUNT+promotionHisResourceDO.getId()).get();
        if (currentCount <= 0){
            log.info("库存[{}]已经为0", promotionHisResourceDO.getId());
            throw new ApplicationException("奖品已经被抢光啦~下次再来吧~");
        }

        //查询相同活动相同优惠券相同用户领取次数
        if(promotionHisResourceDO.getLimitTimes() <= activityPartakeLogDAO.getCountByActivity(dto.getActivityId(),dto.getHisResourceId(),dto.getUserId())){
            throw new ApplicationException(CommonExceptionCode.INVALIDATION,promotionHisResourceDO.getReceiveMode() == 0 ? "领取次数到达上限":"购买次数到达上限");
        }
        return Boolean.TRUE;
    }

    private Boolean check(ReceiveCouponRequestDTO dto,PromotionHisResourceDO promotionHisResourceDO) {
        //信息登记较验
        CustomerFeedbackQuery query = new CustomerFeedbackQuery();
        query.setActivityId(dto.getActivityId());
        query.setUserId(dto.getOpenId());//userid即为openid
        List<ActivityFormFeedbackDO> formFeedbackList = customerFeedbackDAO.pageList(query).getContent();
        if (CollectionUtil.isEmpty(formFeedbackList)){
            log.info("未填写信息登记，req={}",JsonUtil.bean2JsonString(query));
            throw new ApplicationException("请填写信息登记");
        }

        //活动较验
        PromotionActivityDO activity = promotionActivityDAO.getById(dto.getActivityId());
        if (Objects.isNull(activity)){
            throw new ApplicationException("活动不存在");
        }
        if (activity.getStatus().equals(Integer.parseInt(ActivityStatusEnum.UN_START.getId()))){
            throw new ApplicationException("活动尚未开始，请耐心等待！");
        }else if (!activity.getStatus().equals(Integer.parseInt(ActivityStatusEnum.IN_PROGRESS.getId()))){
            throw new ApplicationException("来晚啦，活动已结束！");
        }
        //库存较验

        if(Objects.isNull(promotionHisResourceDO)){
            throw new ApplicationException("参数错误，优惠券不存在！");
        }

        if(promotionHisResourceDO.getRemainingQuantity() == 0){
            throw new ApplicationException("优惠券可领取数量不足！");
        }

        //查询相同活动相同优惠券相同用户领取次数
        if(promotionHisResourceDO.getLimitTimes() <= activityPartakeLogDAO.getCountByActivity(dto.getActivityId(),dto.getHisResourceId(),dto.getUserId())){
            throw new ApplicationException("优惠券可领取数量:{},已达到上限！",promotionHisResourceDO.getLimitTimes());
        }
        return Boolean.TRUE;
    }

    @Override
    public Data<OrderPayResponseDTO> payPurchase(ReceiveCouponRequestDTO dto) {
        PromotionHisResourceDO promotionHisResourceDO = promotionHisResourceDAO.getById(dto.getHisResourceId());

        if(!ReceiveModeEnum.PAY.getId().equals(promotionHisResourceDO.getReceiveMode())){
            throw new ApplicationException("该优惠券领取方式不是付费领取!");
        }

        //较验
        check(dto,promotionHisResourceDO);

        try {
            boolean decrFlag = promotionActivityManager.decrRedisQty(promotionHisResourceDO.getId(), promotionHisResourceDO.getActivityId());
            if (!decrFlag) {
                throw new ApplicationException("当前资源不足，无法领取");
            }
            //生成订单
            ActivityOrderRequestDTO activityOrderRequestDTO = dto.clone(ActivityOrderRequestDTO.class);
            activityOrderRequestDTO.setUserId(dto.getOpenId());
            activityOrderRequestDTO.setResourceId(dto.getHisResourceId());//存的是历史资源主键
            activityOrderRequestDTO.setPayMoney(promotionHisResourceDO.getPurchasePrice());
            activityOrderRequestDTO.setCode(generateIdUtil.getOrderNo(GenerateTypeEnum.PAY_TYPE));


            //调用支付接口
            OrderPayRequestDTO requestDTO = new OrderPayRequestDTO();
            requestDTO.setAmount(activityOrderRequestDTO.getPayMoney().multiply(new BigDecimal(100)).intValue());//单位为分
            String url = "";
            if(PlatformTypeEnum.WX_MINIPROGRAM.getId().equals(dto.getType())){
                requestDTO.setAppId(huafaConstantConfig.MINI_PROGRAM_APP_ID);
                url = huafaConstantConfig.PAY_BASE_URL+PayConstant.ORDER_PAY_URL;
            }else if(PlatformTypeEnum.WX_H5.getId().equals(dto.getType())){//微信内H5网页
                requestDTO.setAppId(huafaConstantConfig.WECHAT_APP_ID);
                url = huafaConstantConfig.PAY_BASE_URL+PayConstant.ORDER_PAY_URL;
            }else if(PlatformTypeEnum.H5.getId().equals(dto.getType())){//h5
                requestDTO.setAppId(huafaConstantConfig.WECHAT_APP_ID);
                url = huafaConstantConfig.PAY_BASE_URL+PayConstant.H5_PAY_URL;
            }
            requestDTO.setBizNotifyUrl(callBackBaseUrl+PayConstant.COUPON_PAY_CALL_BACK_URL);//回调地址
            requestDTO.setBizOrderNo(activityOrderRequestDTO.getCode());
            requestDTO.setDescription("优惠券下单");
            requestDTO.setExpireMinutes(PayConstant.EXPIREMINUTES);
            requestDTO.setOpenid(dto.getOpenId());
            requestDTO.setProjectId(String.valueOf(dto.getProjectId()));
            Data<OrderPayResponseDTO> orderPayResponseDTOData = null;

            if(CommonExceptionCode.SUCCESS.equals(orderPayResponseDTOData.getCode())){
                activityOrderRequestDTO.setWxOrderNo(orderPayResponseDTOData.getdata().getOrderNo());
                activityOrderRequestDTO.setStatus(OrderStatusEnum.UNPAY.getId());
                activityOrderRequestDTO.setOrderPayResponseDTO(orderPayResponseDTOData.getdata());
                activityOrderService.save(activityOrderRequestDTO);
            }
            return orderPayResponseDTOData;
        }catch (Exception e){
            promotionActivityManager.incrRedisQty(promotionHisResourceDO.getId(),dto.getActivityId());
            throw e;
        }
    }

    @Override
    public Map<String,Object> payCallBack(CouponPayCallBackRequestDTO dto) {
        Map<String,Object> returnMap = new HashMap<>();
        log.info("优惠券下单回调:{}",JsonUtil.bean2JsonString(dto));
        if(Objects.isNull(dto)){
            return null;
        }
        //查询订单
        ActivityOrderDO activityOrderDO = activityOrderService.getByCode(dto.getBizOrderNo());

        if(Objects.isNull(activityOrderDO)){
            log.info("优惠券下单回调返回订单不存在!");
            return null;
        }
        orderHandle(dto,activityOrderDO);
        returnMap.put("code","SUCCESS");
        returnMap.put("message","成功");
        return returnMap;
    }

    /**
     * 订单处理
     * @param dto
     */
    private void orderHandle(CouponPayCallBackRequestDTO dto,ActivityOrderDO activityOrderDO){
        if("SUCCESS".equals(dto.getStatus())){
            //领券
            String resourceCode = generateIdUtil.getResourceCode();
            PromotionHisResourceDO promotionHisResourceDO = promotionHisResourceDAO.getById(activityOrderDO.getResourceId());

            ReceiveCouponRequestDTO receiveCouponRequestDTO = (ReceiveCouponRequestDTO)MapBeanUtil.mapToObject2(activityOrderDO.getExt(),ReceiveCouponRequestDTO.class);
            if(activityOrderDO.getExt() != null){
                receiveCouponRequestDTO.setActivityId(Long.valueOf((Integer) activityOrderDO.getExt().get("activityId")));
                receiveCouponRequestDTO.setHisResourceId(Long.valueOf((Integer) activityOrderDO.getExt().get("hisResourceId")));
                receiveCouponRequestDTO.setType((Integer) activityOrderDO.getExt().get("type"));
                receiveCouponRequestDTO.setUnionId(String.valueOf(activityOrderDO.getExt().get("unionId")));
                receiveCouponRequestDTO.setProjectId(String.valueOf(activityOrderDO.getExt().get("projectId")));
                receiveCouponRequestDTO.setOpenId(String.valueOf(activityOrderDO.getExt().get("openId")));
            }

            ActivityPartakeLogDTO activityPartakeLogDTO = CouponConverter.partakeLogConverter(resourceCode,receiveCouponRequestDTO, promotionHisResourceDO);
            activityPartakeLogDTO.setOrderId(activityOrderDO.getId());
            activityPartakeLogDTO.setOrderNo(activityOrderDO.getCode());
            activityPartakeLogDAO.save(activityPartakeLogDTO);

            //更新剩余资源数量--更新条件 主键及资源剩余数量必须大于0,更新内容为资源剩余数量-1及更新时间
            UpdateWrapper<PromotionHisResourceDO> wrapper = new UpdateWrapper<>();
            wrapper.lambda().eq(PromotionHisResourceDO::getId,promotionHisResourceDO.getId()).gt(PromotionHisResourceDO::getRemainingQuantity,0).set(PromotionHisResourceDO::getRemainingQuantity,promotionHisResourceDO.getRemainingQuantity()-1).set(PromotionHisResourceDO::getUpdatedTime,new Date());
            promotionHisResourceDAO.update(null, wrapper);

            ActivityVerifyDO activityVerifyDO = CouponConverter.verifyConverter(resourceCode,receiveCouponRequestDTO, promotionHisResourceDO, VerifyTypeEnum.COUPON.getId(), WhetherEnum.YES.getId());
            //设置项目名称
            if(activityVerifyDO.getProjectId() != null){
                QueryWrapper<ActivityParticipationDO> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(ActivityParticipationDO::getActivityId,activityVerifyDO.getActivityId());
                queryWrapper.lambda().eq(ActivityParticipationDO::getProjectId,activityVerifyDO.getProjectId());
                ActivityParticipationDO activityParticipationDO = activityParticipationDAO.getOne(queryWrapper);
                activityVerifyDO.setProjectName(activityParticipationDO.getProjectName());
            }
            //添加核销记录
            activityVerifyDO.setOrderId(activityOrderDO.getId());
            activityVerifyDAO.save(activityVerifyDO);
            //领券记录
            activityOrderDO.setStatus(OrderStatusEnum.PAYED.getId());
        }else if("PAYERROR".equals(dto.getStatus())){
            activityOrderDO.setStatus(OrderStatusEnum.PAY_FAIL.getId());
        }else if("CLOSED".equals(dto.getStatus())){
            //交易关闭
            activityOrderDO.setStatus(OrderStatusEnum.CLOSED.getId());
        }
        activityOrderService.updateById(activityOrderDO);
    }
}
