package com.deepexi.dxp.marketing.controller.specify.middleapi;

import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.UserLotteryRecordQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.VerifyInfoQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.LuckyDrawRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityPartakeLogResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.LuckyDrawResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.VerifyInfoResponseDTO;
import com.deepexi.dxp.marketing.service.specify.LuckyDrawService;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 抽奖活动
 * <AUTHOR>
 */
@RestController
@RequestMapping("/middle-api/v1/luckyDraw")
@Api(value = "middle-抽奖活动",tags = {"middle-抽奖活动接口"})
public class MiddleLuckyDrawController {

    @Resource
    private LuckyDrawService luckyDrawService;

    @PostMapping("/prizeResultList")
    @ApiOperation(value="活动详情-中奖结果列表", notes = "活动详情-中奖结果列表")
    public Data<Set<String>> prizeResultList(@RequestParam Long id) {
        return new Data<>(luckyDrawService.prizeResultList(id));
    }

    @PostMapping("/verifyInfoList")
    @ApiOperation(value="活动详情-奖品领取/核销明细", notes = "活动详情-奖品领取/核销明细")
    public Data<List<VerifyInfoResponseDTO>> verifyInfoList(@RequestBody @Valid VerifyInfoQuery query) {
        return new Data<>(luckyDrawService.verifyInfoList(query));
    }
}
