package com.deepexi.dxp.marketing.domain.marketing.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @Class: DolphinSchedulerProcessReleaseDTO
 * @Description: DolphinScheduler 流程定义修改发布状态DTO
 * @Author: lizhongbao
 * @Date: 2020/3/28
 **/
@Data
public class DolphinSchedulerProcessInstanceStartDTO implements Serializable {

    /**
     * 失败策略,可用值:END,CONTINUE
     */
    private String failureStrategy;

    /**
     * 流程定义ID
     */
    private Integer processDefinitionId;

    /**
     * 流程实例优先级,可用值:HIGHEST,HIGH,MEDIUM,LOW,LOWEST
     */
    private String processInstancePriority;

    /**
     * 定时时间
     */
    private String scheduleTime;

    /**
     * 发送组ID
     */
    private Integer warningGroupId;

    /**
     * 发送策略,可用值:NONE,SUCCESS,FAILURE,ALL
     */
    private String warningType;

    /**
     * 指令类型,可用值:
     * START_PROCESS,
     * START_CURRENT_TASK_PROCESS,
     * RECOVER_TOLERANCE_FAULT_PROCESS,
     * RECOVER_SUSPENDED_PROCESS,
     * START_FAILURE_TASK_PROCESS,
     * COMPLEMENT_DATA,
     * SCHEDULER,
     * REPEAT_RUNNING,
     * PAUSE,
     * STOP,
     * RECOVER_WAITTING_THREAD
     */
    private String execType;

    /**
     * 收件人
     */
    private String receivers;

    /**
     * 收件人(抄送)
     */
    private String receiversCc;

    /**
     * 运行模式,可用值:RUN_MODE_SERIAL,RUN_MODE_PARALLEL
     */
    private String runMode;

    /**
     * 开始节点列表(节点name)
     */
    private String startNodeList;

    /**
     * 任务依赖类型,可用值:TASK_ONLY,TASK_PRE,TASK_POST
     */
    private String taskDependType;

    /**
     * 超时时间
     */
    private Integer timeout;

    /**
     * Worker Server分组ID
     */
    private Integer workerGroupId;

    public DolphinSchedulerProcessInstanceStartDTO (Integer processDefinitionId) {
        this.processDefinitionId = processDefinitionId;
        this.failureStrategy = "CONTINUE";
        this.processInstancePriority = "MEDIUM";
        this.warningGroupId = -1;
        this.warningType = "NONE";
        this.runMode = "RUN_MODE_SERIAL";
        this.taskDependType = "TASK_POST";
    }

}
