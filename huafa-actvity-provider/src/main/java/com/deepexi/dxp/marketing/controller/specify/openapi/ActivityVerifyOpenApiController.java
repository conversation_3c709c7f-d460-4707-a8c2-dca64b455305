package com.deepexi.dxp.marketing.controller.specify.openapi;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityVerifyRejectLogQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.MyVerifyQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.VerifyPageQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityVerifyRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.SendIncentiveCallBackRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.SendIncentiveRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityVerifyInResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityVerifyRejectLogResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.IncentiveResponseDTO;
import com.deepexi.dxp.marketing.service.specify.ActivityVerifyRejectLogService;
import com.deepexi.dxp.marketing.service.specify.ActivityVerifyService;
import com.deepexi.dxp.marketing.service.specify.IncentiveService;
import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
@RequestMapping("/open-api/v1/open/verify")
@Api(value = "核销管理openApi-",tags = {"核销管理模块openApi"})
public class ActivityVerifyOpenApiController {

    @Autowired
    private ActivityVerifyService activityVerifyService;

    @Autowired
    private ActivityVerifyRejectLogService activityVerifyRejectLogService;

    @GetMapping("/list")
    @ApiOperation(value="核销列表", notes = "核销列表")
    public Data<PageBean<ActivityVerifyInResponseDTO>> list(@Valid VerifyPageQuery query) {
        return new Data<>(activityVerifyService.findPage(query));
    }

    @PostMapping("/listPost")
    @ApiOperation(value="核销列表", notes = "核销列表")
    public Data<PageBean<ActivityVerifyInResponseDTO>> listPost(@RequestBody @Valid VerifyPageQuery query) {
        return new Data<>(activityVerifyService.findPage(query));
    }

    @GetMapping("/detail")
    @ApiOperation(value="核销详情", notes = "核销详情")
    public Data<ActivityVerifyInResponseDTO> detail(@RequestParam Long id) {
        return new Data<>(activityVerifyService.getDetail(id));
    }

    @GetMapping("/rejectLogList")
    @ApiOperation(value="驳回列表", notes = "驳回列表")
    public Data<PageBean<ActivityVerifyRejectLogResponseDTO>> rejectLogList(@Valid ActivityVerifyRejectLogQuery query) {
        return new Data<>(activityVerifyRejectLogService.findPage(query));
    }

    @PostMapping("/rejectLogListPost")
    @ApiOperation(value="驳回列表", notes = "驳回列表")
    public Data<PageBean<ActivityVerifyRejectLogResponseDTO>> rejectLogListPost(@RequestBody @Valid ActivityVerifyRejectLogQuery query) {
        return new Data<>(activityVerifyRejectLogService.findPage(query));
    }

    @ApiOperation(value = "核销")
    @PutMapping("/wiped")
    public Data<Boolean> wiped(@RequestBody @Valid ActivityVerifyRequestDTO requestDTO) {
        return new Data<>(activityVerifyService.wiped(requestDTO));
    }


    @ApiOperation(value = "驳回")
    @PutMapping("/reject")
    public Data<Boolean> reject(@RequestBody @Valid ActivityVerifyRequestDTO requestDTO) {
        return new Data<>(activityVerifyService.reject(requestDTO));
    }

    @ApiOperation(value = "退款")
    @PutMapping("/refunds")
    public Data<Boolean> refunds(@RequestBody @Valid ActivityVerifyRequestDTO requestDTO) {//reason 退款原因
        return new Data<>(activityVerifyService.refunds(requestDTO));
    }

    @GetMapping("/myList")
    @ApiOperation(value="个人核销列表", notes = "个人核销列表")
    public Data<PageBean<ActivityVerifyInResponseDTO>> myList(@Valid MyVerifyQuery query) {
        return new Data<>(activityVerifyService.myList(query));
    }

    @PostMapping("/myListPost")
    @ApiOperation(value="个人核销列表", notes = "个人核销列表")
    public Data<PageBean<ActivityVerifyInResponseDTO>> myListPost(@RequestBody @Valid MyVerifyQuery query) {
        return new Data<>(activityVerifyService.myList(query));
    }

    @GetMapping("/getQrCode")
    @ApiOperation(value="核销二维码", notes = "核销二维码")
    public Data<String> getQrCode(@RequestParam String code) {
        return new Data<>(activityVerifyService.getQrCode(code));
    }
}
