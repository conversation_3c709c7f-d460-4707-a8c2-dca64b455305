package com.deepexi.dxp.marketing.domain.marketing.vo;


import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import com.deepexi.dxp.middle.marketing.domain.dto.MemberListDTO;
import com.deepexi.dxp.middle.marketing.domain.dto.MemberSocialInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;


/**
 * 营销任务投放会员
 * @Author: HuangBo.
 * @Date: 2020/3/12 19:18
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MarketingActiveTargetUsersVO extends SuperVO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 主动营销任务投放标签表ID
     */
    @ApiModelProperty(value = "主动营销任务投放标签ID")
    private Long activeTagId;

    /**
     * 营销任务投放客群表ID
     */
    @ApiModelProperty(value = "营销任务投放客群ID")
    private Long memberGroupId;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 用户类型
     */
    @ApiModelProperty(value = "用户类型")
    private Integer userType;

    /**
     * 业务拓展信息
     */
    @ApiModelProperty(value = "业务拓展信息")
    private String extendInfo;

    /**
     * 会员信息
     */
    @ApiModelProperty(value = "会员信息")
    private MemberListDTO memberInfo;

    /**
     * 粉丝信息
     */
    @ApiModelProperty(value = "粉丝信息")
    private MemberSocialInfoDTO memberSocialInfo;
}
