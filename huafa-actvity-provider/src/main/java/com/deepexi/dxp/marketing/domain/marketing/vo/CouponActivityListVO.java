package com.deepexi.dxp.marketing.domain.marketing.vo;

import com.deepexi.dxp.middle.marketing.domain.dto.sdk.SdkPromotionActivityLimitFindResponseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 领券活动VO
 * <AUTHOR>
 * @Date 2020/3/16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class CouponActivityListVO extends SdkPromotionActivityLimitFindResponseVO {

    /**
     * 关联优惠券名称
     */
    @ApiModelProperty("关联优惠券名称")
    private String couponName;

}
