package com.deepexi.dxp.marketing.domain.promotion.dto;

import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.middle.promotion.domain.dto.SkuCodeBaseAdminDTO;
import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020-08-17 17:19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class ComdityLimitAdminDTO extends AbstractObject {

    @ApiModelProperty("品牌")
    List<BaseActivityDTO> brandCode;
    @ApiModelProperty("所有商品")
    List<BaseActivityDTO> allCommodity;
    @ApiModelProperty("商品限制类型id")
    List<SkuCodeBaseAdminDTO> skuList;
    @ApiModelProperty("类目")
    List<BaseActivityDTO> frontCategory;

}
