package com.deepexi.dxp.marketing.geetest.task;


import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.deepexi.dxp.marketing.geetest.GeetestLib;
import com.deepexi.dxp.marketing.geetest.utils.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class CheckGeetestStatusTask {
    private static RedissonClient redissonClient;

    @Resource
    public void setRedissonClient(RedissonClient redissonClient) {
        CheckGeetestStatusTask.redissonClient = redissonClient;
    }

    @Value("${geetest.id}")
    private String gtId;
    private static final String REDIS_STATUS_KEY = "REDIS_CHECK_GEETEST_STATUS_KEY";

    @Scheduled(cron="0/10 * *  * * ? ")   //每10秒执行一次
    public void run() {
        String geetest_status = "";
        try {
            Map<String, String> paramMap = new HashMap<String, String>();
            paramMap.put("gt", gtId);
            String resBody = HttpClientUtils.doGet(GeetestLib.STATUS_URL, paramMap);
            log.debug(String.format("gtlog: CheckGeetestStatusTask: 极验云监控，返回body=%s.", resBody));
            JSONObject jsonObject = new JSONObject(resBody);
            geetest_status = jsonObject.getStr("status");
        } catch (Exception e) {
            log.error("get geetest status error",e);
        }
        if (StrUtil.isNotBlank(geetest_status)) {
            redissonClient.getBucket(REDIS_STATUS_KEY).set(geetest_status,86400, TimeUnit.SECONDS);
        } else {
            redissonClient.getBucket(REDIS_STATUS_KEY).set("fail",86400, TimeUnit.SECONDS);
        }
    }

    /**
     * 检测存入redis中的极验云状态标识
     */
    public static boolean checkGeetestStatusRedisFlag() {
        String status = (String) redissonClient.getBucket(REDIS_STATUS_KEY).get();
        if ("success".equals(status)) {
            return true;
        } else {
            return false;
        }
    }

}
