package com.deepexi.dxp.marketing.service.specify.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.deepexi.dxp.marketing.common.base.utils.CommonExceptionCode;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.config.HuafaConstantConfig;
import com.deepexi.dxp.marketing.constant.PayConstant;
import com.deepexi.dxp.marketing.constant.RedisConstants;
import com.deepexi.dxp.marketing.converter.ActivityInfoConverter;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.CustomerFeedbackQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.*;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityOrderResponseVO;
import com.deepexi.dxp.marketing.domain.marketing.response.OrderPayResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.PayOrderQueryResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.WxRefundsResponseDTO;
import com.deepexi.dxp.marketing.domain.specify.dto.ActivityFormFeedbackDTO;
import com.deepexi.dxp.marketing.enums.activity.strategy.StrategyGroupEnum;
import com.deepexi.dxp.marketing.enums.coupon.WhetherEnum;
import com.deepexi.dxp.marketing.enums.specify.OrderStatusEnum;
import com.deepexi.dxp.marketing.enums.specify.PlatformTypeEnum;
import com.deepexi.dxp.marketing.enums.specify.VerifyStatusEnum;
import com.deepexi.dxp.marketing.manager.promotion.PromotionActivityManager;
import com.deepexi.dxp.marketing.service.specify.*;
import com.deepexi.dxp.marketing.utils.GenerateIdUtil;
import com.deepexi.dxp.marketing.utils.HTTPClientUtils;
import com.deepexi.dxp.marketing.utils.RestTemplateUtil;
import com.deepexi.dxp.middle.promotion.dao.PromotionActivityDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityPartakeLogDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityVerifyDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.CustomerFeedbackDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.PromotionHisResourceDAO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityFormFeedbackDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityOrderDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityVerifyDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.PromotionHisResourceDO;
import com.deepexi.util.DateUtils;
import com.deepexi.util.JsonUtil;
import com.deepexi.util.StringUtil;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pojo.CloneDirection;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class WxPayServiceImpl implements WxPayService {

    @Resource
    private ActivityOrderService activityOrderService;

    @Autowired
    private PromotionHisResourceDAO promotionHisResourceDAO;

    @Autowired
    private ActivityPartakeLogDAO activityPartakeLogDAO;

    @Autowired
    private ActivityVerifyDAO activityVerifyDAO;

    @Autowired
    private PromotionActivityService promotionActivityService;

    @Autowired
    private ActivityVerifyService activityVerifyService;

    @Autowired
    private PromotionActivityManager promotionActivityManager;

    @Autowired
    private CustomerFeedbackDAO customerFeedbackDAO;

    @Autowired
    private GenerateIdUtil generateIdUtil;

    @Autowired
    private HuafaConstantConfig huafaConstantConfig;

    @Resource
    private InteractionCenterService interactionCenterService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private PromotionActivityDAO promotionActivityDAO;

    @Override
    public Data<OrderPayResponseDTO> orderPay(OrderPayRequestDTO requestDTO,String url,Integer type){
        try{
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            log.info("支付请求参数：{},请求url:{}",JSON.toJSONString(requestDTO),url);
            String postFormData = RestTemplateUtil.orderMonitorPost(url, JSON.toJSONString(requestDTO),headers);
            log.info("支付接口返回：{}",postFormData);
            if(StringUtil.isEmpty(postFormData)){
                return new Data<>(CommonExceptionCode.ERROR_CODE,"支付请求失败");
            }
            JSONObject response = JSONUtil.parseObj(postFormData);
            String code = response.getStr("code");
            if(CommonExceptionCode.SUCCESS.equals(code)){
                //String data = response.getStr("data");
                JSONObject dataObj = (JSONObject)response.get("data");
                OrderPayResponseDTO orderPayResponseDTO = new OrderPayResponseDTO();
                if(PlatformTypeEnum.DY_MINIPROGRAM.getId().equals(type)){//抖音支付
                    OrderPayResponseDTO.DyPayInfo payInfo = JSONUtil.toBean(response.getJSONObject("data"), OrderPayResponseDTO.DyPayInfo.class);
                    orderPayResponseDTO.setOrderNo(payInfo.getOut_order_no());
                    orderPayResponseDTO.setDyPayInfo(payInfo);
                }else{//其他
                    String packageRes = dataObj.getStr("package");
                    orderPayResponseDTO = JSONUtil.toBean(response.getJSONObject("data"), OrderPayResponseDTO.class);
                    orderPayResponseDTO.setPackageRes(packageRes);
                }
                return new Data<>(orderPayResponseDTO);
            }
                return new Data<>(code,response.getStr("msg"));
        }catch(Exception e){
            log.error("支付请求异常");
        }
        return new Data<>(CommonExceptionCode.ERROR_CODE,"支付请求异常");
    }

    @Override
    @Transactional
    public Map<String,Object> payCallBack(WxPayCallBackRequestDTO dto) {


        Map<String,Object> returnMap = new HashMap<>();
        log.info("支付订单pay回调:{}", JsonUtil.bean2JsonString(dto));
        if(Objects.isNull(dto)){
            return null;
        }
        //查询订单
        ActivityOrderDO activityOrderDO = activityOrderService.getByCode(dto.getBizOrderNo());

        if(Objects.isNull(activityOrderDO)){
            log.error("下单回调返回订单不存在!");
            return null;
        }

        //如果订单不是未支付，则直接返回success
        if(!activityOrderDO.getStatus().equals(OrderStatusEnum.UNPAY.getId())){
            returnMap.put("code","SUCCESS");
            returnMap.put("message","成功");
            return returnMap;
        }

        if(this.orderHandle(dto,activityOrderDO)){
            returnMap.put("code","SUCCESS");
            returnMap.put("message","成功");
        }else{
            returnMap.put("code","FAIL");
            returnMap.put("message","失败");
        }
        return returnMap;

    }

    @Override
    public Map<String,Object> refundCallBack(WxRefundCallBackRequestDTO dto) {

        Map<String,Object> returnMap = new HashMap<>();


        log.info("退款回调:{}", JsonUtil.bean2JsonString(dto));

        if(Objects.isNull(dto)){
            returnMap.put("code","FAIL");
            returnMap.put("message","退款通知内容为空");
            return returnMap;
        }
        Date refundsTime = StringUtil.isNotEmpty(dto.getSuccessTime()) ? DateUtils.getDate(dto.getSuccessTime(), DateUtils.DEFAULT_DATE_TIME_FORMAT) : null;

        if(activityVerifyService.refundsProcess(dto.getBizOrderNo(),dto.getOrderNo(), dto.getStatus(),refundsTime)){
            returnMap.put("code","SUCCESS");
            returnMap.put("message","成功");
        }else{
            returnMap.put("code","FAIL");
            returnMap.put("message","失败");
        }
        return returnMap;

    }

    @Override
    public PayOrderQueryResponseDTO queryOrder(Long id) {
        ActivityOrderResponseVO order = activityOrderService.getDetail(id);

        if(Objects.nonNull(order)){
            String url = huafaConstantConfig.PAY_BASE_URL+PayConstant.ORDER_RESULT_URL;//同下单接口
            try{
                Map<String, Object> map = new HashMap<>();
                map.put("orderNo",order.getWxOrderNo());
                String result = HTTPClientUtils.get(url,map);
                log.info("支付下单订单：{}查询返回:{}",order.getWxOrderNo(),result);
                PayOrderQueryResponseDTO payOrderQueryResponseDTO = JSONUtil.toBean(result, PayOrderQueryResponseDTO.class);
                return payOrderQueryResponseDTO;
            }catch(Exception e){
                log.error("支付下单查询失败{}",e);
            }
        }
        return null;
    }

    @Override
    public WxRefundsResponseDTO refunds(WxRefundsRequestDTO requestDTO) {
        try{
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            log.info("=====================申请退款=====================");
            String postFormData = RestTemplateUtil.orderMonitorPost(huafaConstantConfig.PAY_BASE_URL+PayConstant.REFUNDS_URL, JSON.toJSONString(requestDTO),headers);
            log.info("退款返回：{}",postFormData);
            if(StringUtil.isEmpty(postFormData)){
                throw new ApplicationException("退款失败!");
            }
            return JSONUtil.toBean(postFormData, WxRefundsResponseDTO.class);
        }catch (Exception e){
            log.info("退款异常请求参数:{}",JsonUtil.bean2JsonString(requestDTO));
            log.error("发起退款异常:",e);
        }
        return null;
    }

    @Override
    public Data closeOrderNo(String payOrderNo) {
        try{
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            log.info("=====================申请关闭订单=====================");
            String postFormData = RestTemplateUtil.orderMonitorPost(huafaConstantConfig.PAY_BASE_URL+PayConstant.CLOSE_ORDER_URL+"?orderNo="+payOrderNo,null,headers);
            log.info("关闭订单返回：{}",postFormData);
            if(StringUtil.isEmpty(postFormData)){
                return new Data<>(CommonExceptionCode.ERROR_CODE,"关闭订单失败");
            }
            //return JSONUtil.toBean(postFormData, Data.class);
            com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(postFormData);
            return new Data<>(jsonObject.getString("code"),jsonObject.getString("msg"));
        }catch (Exception e){
            log.error("支付订单号:{}关闭异常:{}",payOrderNo,e);
        }
        return new Data<>(CommonExceptionCode.ERROR_CODE,"关闭订单失败!");
    }

    /**
     * 订单处理
     * @param dto
     */
    @Override
    public boolean orderHandle(WxPayCallBackRequestDTO dto, ActivityOrderDO activityOrderDO){
        if("SUCCESS".equals(dto.getStatus())){

            String lockKey = RedisConstants.CACHE_PREV_KEY_ORDER_BACK_LOCK + activityOrderDO.getWxOrderNo();
            RLock lock = redissonClient.getLock(lockKey);
            try{
                if (lock.tryLock(100, 10 * 1000, TimeUnit.MILLISECONDS)) {

                    if (!promotionActivityManager.isExistsUnpayOrderInfo(activityOrderDO.getPhone(),activityOrderDO.getActivityId(),activityOrderDO.getResourceId())) {
                        log.info("缓存中不存在未支付订单{}，说明已处理或已过期，此次跳过",activityOrderDO.getWxOrderNo());
                        if (lock.isHeldByCurrentThread()) {
                            lock.unlock();
                        }
                        return true;
                    }

                    //获取资源信息
                    PromotionHisResourceDO promotionHisResourceDO = promotionHisResourceDAO.getById(activityOrderDO.getResourceId());

                    //获取信息登记
                    CustomerFeedbackQuery query = new CustomerFeedbackQuery();
                    query.setActivityId(activityOrderDO.getActivityId());
                    //query.setUserId(activityOrderDO.getUserId());
                    query.setPhone(activityOrderDO.getPhone());
                    if (activityOrderDO.getResourceId()!= null && activityOrderDO.getResourceId() > 0){
                        query.setResourceId(activityOrderDO.getResourceId());
                    }

                    //判断如果是秒杀，则只拿第一个feedback
                    PromotionActivityDO promotionActivityDO = promotionActivityDAO.selectById(activityOrderDO.getActivityId());
                    if(StrategyGroupEnum.HF_SECKILL_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())){
                        query.setResourceId(null);
                    }

                    List<ActivityFormFeedbackDO> formFeedbackList = customerFeedbackDAO.pageList(query).getContent();
                    ActivityFormFeedbackDTO activityFormFeedbackDTO = null;
                    if(formFeedbackList.size()>0){
                        activityFormFeedbackDTO = formFeedbackList.get(0).clone(ActivityFormFeedbackDTO.class, CloneDirection.FORWARD);
                    }

                    //数据库-更新剩余资源数量(如果是订单中有数量，则使用)
                    long count = Convert.toLong(activityOrderDO.getExt().get("total"),1L);
                    promotionActivityManager.decrRemainQty(promotionHisResourceDO.getId(),count);

                    if(StringUtil.isNotEmpty(dto.getSuccessTime())){//支付时间
                        activityOrderDO.setPayTime(DateUtils.getDate(dto.getSuccessTime(),DateUtils.DEFAULT_DATE_TIME_FORMAT));
                    }

                    //生成唯一核销券码
                    String resourceCode = generateIdUtil.getResourceCode();

                    //添加参与记录
                    ActivityPartakeLogDTO activityPartakeLogDTO = ActivityInfoConverter.partakeLogConverter(activityOrderDO,activityFormFeedbackDTO, promotionHisResourceDO, resourceCode);
                    activityPartakeLogDTO.setProjectId(activityOrderDO.getExt().get("projectId") == null ? "" : activityOrderDO.getExt().get("projectId").toString());
                    activityPartakeLogDTO.setProjectName(activityOrderDO.getExt().get("projectName") == null ? "" : activityOrderDO.getExt().get("projectName").toString());
                    activityPartakeLogDAO.save(activityPartakeLogDTO);

                    //添加核销记录
                    ActivityVerifyDO activityVerifyDO = ActivityInfoConverter.verifyConverter(activityOrderDO,activityFormFeedbackDTO, promotionHisResourceDO, resourceCode, VerifyStatusEnum.NO_VERIFY.getId(), WhetherEnum.YES.getId());
                    activityVerifyDAO.save(activityVerifyDO);

                    //修改订单记录
                    activityOrderDO.setStatus(OrderStatusEnum.PAYED.getId());

                    //增加用户缓存参加活动次数
                    promotionActivityManager.incrUserPartakeCount(activityOrderDO.getPhone(),activityOrderDO.getActivityId());
                    //增加用户缓存领取或购买资源次数
                    promotionActivityManager.incrUserGetResourceCount(activityOrderDO.getPhone(),activityOrderDO.getId(),activityOrderDO.getActivityId());
                    //缓存清空未支付订单
                    promotionActivityManager.clearCacheUnpayOrderInfo(activityOrderDO.getPhone(),activityOrderDO.getActivityId(),activityOrderDO.getResourceId());

                    if(StrategyGroupEnum.HF_SECKILL_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())
                        || StrategyGroupEnum.HF_COUPON_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())){

                        //发送模板消息
                        promotionActivityService.templateSend(activityVerifyDO.getPhone(), activityVerifyDO.getUserName(), promotionHisResourceDO.getName(), resourceCode, huafaConstantConfig.COUPON_VOUCHER_TEMPLATE_ID);

                        //小程序订阅消息
                        SendTemplateNewsRequestDTO sendTemplateNews = new SendTemplateNewsRequestDTO();
                        Map<String,Object> templateParam = Maps.newHashMap();
                        templateParam.put("thing1",promotionHisResourceDO.getName());
                        templateParam.put("time2",DateUtils.format(activityVerifyDO.getCreatedTime()));
                        templateParam.put("thing3","请到我的礼品栏目查看详情");
                        sendTemplateNews.setTemplateParam(JSON.toJSONString(templateParam));
                        sendTemplateNews.setTemplateId(huafaConstantConfig.MINI_COUPON_ARRIVES_TEMPLATE_ID);
                        sendTemplateNews.setUserOpenId(activityVerifyDO.getUserId());
                        sendTemplateNews.setMpFrom(SendTemplateNewsRequestDTO.MP_FROM_ZYT);
                        sendTemplateNews.setCreateId(activityVerifyDO.getUserName());
                        interactionCenterService.miniTemplateNews(sendTemplateNews);
                    }
                    if (lock.isHeldByCurrentThread()) {
                       log.info("----------------------下单回调手动释放锁------------------");
                       lock.unlock();
                   }
                }
            }catch (InterruptedException e) {
                log.error("订单回调redis分布式锁异常,key:{},异常信息:{}", lockKey, e.getMessage());
                lock.unlock();
                return Boolean.FALSE;
            }
        }else if("PAYERROR".equals(dto.getStatus())){
            //交易错误
            activityOrderDO.setStatus(OrderStatusEnum.PAY_FAIL.getId());

        }else if("CLOSED".equals(dto.getStatus())){
            //交易关闭
            activityOrderDO.setStatus(OrderStatusEnum.CLOSED.getId());
        }
        return activityOrderService.updateById(activityOrderDO);
    }

}
