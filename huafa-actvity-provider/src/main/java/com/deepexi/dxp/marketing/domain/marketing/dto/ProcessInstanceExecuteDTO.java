package com.deepexi.dxp.marketing.domain.marketing.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Class: ProcessInstanceExecuteDTO
 * @Description: 流程实例-执行操作 DTO
 * @Author: lizhongbao
 * @Date: 2020/3/31
 **/
@Data
@ApiModel
public class ProcessInstanceExecuteDTO implements Serializable {

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID", required = true)
    private String tenantId;

    /**
     * 关联项ID
     */
    @ApiModelProperty(value = "自动营销ID/模型ID", required = true)
    private String itemId;

    /**
     * 执行类型,可用值:NONE,REPEAT_RUNNING,RECOVER_SUSPENDED_PROCESS,START_FAILURE_TASK_PROCESS,STOP,PAUSE
     */
    @ApiModelProperty(value = "执行类型")
    private String executeType;
}
