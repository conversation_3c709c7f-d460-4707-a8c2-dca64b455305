package com.deepexi.dxp.marketing.manager.promotion;

import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityCommodityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityConfigDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.OrderEditResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityParamsDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityResponseParamsDTO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionCouponDO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionCouponLoggerDO;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> xinjian.yao
 * @date 2019/12/2 20:09
 */
@Service
public interface ActivityCalculateManager {
    /**
     * 判断活动是不是有设置优惠券限制
     * @param activity
     * @return
     */
    Boolean couponLimit(ActivityConfigDTO activity);
    /**
     * 限制过滤的计算(全部校验)
     * @param activity 活动配置
     * @param params 活动参数
     * @return 是否通过限制
     */
    Boolean limitCalculate(ActivityConfigDTO activity, ActivityParamsDTO params);

    /**
     * 限制过滤的计算(可自定义限制列表)
     * @param activity 活动配置
     * @param params 活动参数
     * @param limitList 待验证限制列表
     * @return 是否通过限制
     */
    boolean limitCalculate(ActivityConfigDTO activity, ActivityParamsDTO params, List<String> limitList);

    /**
     * 活动策略计算
     * @param activityConfigDTO 活动的配置类
     * @param activityResponseParamsDTO 活动的相应请求
     */
    void strategyCalculate(ActivityConfigDTO activityConfigDTO, ActivityResponseParamsDTO activityResponseParamsDTO);


    /**
     *
     * @param activityCommodityDTOList 商品List
     * @param promotionCouponDo  优惠券do
     * @param promotionCouponLoggerDo 用户优惠券do
     * @param orderDiscountsPriceOnlyActivity 不含优惠券的订单总额
     */
    void strategyCouponCalculate(List<ActivityCommodityDTO> activityCommodityDTOList,
                                 PromotionCouponDO promotionCouponDo,
                                 PromotionCouponLoggerDO promotionCouponLoggerDo, BigDecimal orderDiscountsPriceOnlyActivity);

    /**
     * 计算不含优惠券的订单总价，即仅活动的优惠后价格
     * @param resultDTO
     * @return java.lang.Double
     * <AUTHOR>
     * @date 2020/12/10
     **/
    Double orderMoneyNoCoupon(OrderEditResponseDTO resultDTO);

    /**
     *
     * @param activityCommodityDTO 商品活动
     * @param promotionCouponDo 优惠券do 包含优惠券的配置
     *
     * @return
     */
    Boolean couponLimitCalculate(ActivityCommodityDTO activityCommodityDTO,
                                 PromotionCouponLoggerDO promotionCouponLoggerDo,
                                 PromotionCouponDO promotionCouponDo);


    /**
     *
     * @param couponDoList 优惠券list
     * @param promotionCouponLoggerDOList 优惠券日志
     * @param resultDTO 订单返回的实体类
     * @param activityConfigCache 优惠券限制
     */
    void orderCouponMoneyDealWith(List<PromotionCouponDO> couponDoList,
                                  List<PromotionCouponLoggerDO> promotionCouponLoggerDOList,
                                  OrderEditResponseDTO resultDTO,
                                  Map<Long, ActivityConfigDTO> activityConfigCache);


    /**
     * 订单下有活动的商品的优惠信息汇总
     * @param resultDTO 订单返回dto
     */
    void orderActivityCommodityMoneyGroup(OrderEditResponseDTO resultDTO);

    /**
     * 订单最终金额汇总
     * @param resultDTO 订单返回DTO
     */
    void orderMoneyGroup(OrderEditResponseDTO resultDTO);
}

