package com.deepexi.dxp.marketing.manager.promotion;


import cn.hutool.core.collection.CollectionUtil;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityConfigDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityParamsDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityResponseParamsDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.OrderEditResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.request.calculate.ActivityOrderParamsRequest;
import com.deepexi.dxp.marketing.enums.activity.limit.PATemplateBaseEnum;
import com.deepexi.dxp.marketing.manager.promotion.impl.limit.*;
import com.deepexi.dxp.marketing.manager.promotion.impl.order.PromotionOrtherActivityOrderManagerImpl;
import com.deepexi.dxp.marketing.manager.promotion.impl.strategy.*;
import com.deepexi.dxp.middle.promotion.domain.entity.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR> xinjian.yao
 * @date 2019/11/22 17:41
 */
@Service
@Data
@Slf4j
public class Context {
    /*
     * 有点类似观察者模式
     * 但是这里的作用是把一个活动的所有限制类型注册进来
     * 然后做处理
     */

    /**
     * 活动配置
     */
    private ActivityConfigDTO activity;

    /**
     * 活动参数
     */
    private ActivityParamsDTO params;

    /**
     * 活动配置
     */
    private ActivityConfigDTO activityConfigDTO;

    /**
     * 活动计算返回
     */
    private ActivityResponseParamsDTO activityResponseParamsDTO;

    /**
     * 策略计算
     */
    private List<Calculate> limitCalculate;

    /**
     * 策略计算limitCalculate
     */
    private List<Calculate> strategyCalculate;

    /**
     * 订单计算
     */
    private List<PromotionActivityOrderManager> promotionActivityOrderManagerList;


    public Context(ActivityConfigDTO activityConfigDTO, ActivityResponseParamsDTO activityResponseParamsDTO) {
        init();
        this.activityConfigDTO = activityConfigDTO;
        this.activityResponseParamsDTO = activityResponseParamsDTO;
    }

    public Context(ActivityConfigDTO activityConfigDTO) {
        init();
        this.activityConfigDTO = activityConfigDTO;
    }

    public Context() {
        init();
    }

    public Context(ActivityConfigDTO activity, ActivityParamsDTO params) {
        init();
        this.activity = activity;
        this.params = params;
    }

    private void init() {
        this.limitCalculate = new ArrayList<>();
        this.strategyCalculate = new ArrayList<>();
        this.promotionActivityOrderManagerList = new ArrayList<>();
    }

    public Context initOrderCalculate(OrderEditResponseDTO resultDTO,
                                      ActivityOrderParamsRequest activityRequestParams,
                                      List<PromotionCouponLoggerDO> promotionCouponLoggerDOList,
                                      List<PromotionCouponDO> couponDoList,
                                      List<PromotionActivityDO> promotionActivityDOList,
                                      Map<Long, List<PromotionActivityLimitDO>> activityLimitMap,
                                      Map<Long, List<PromotionStrategyDO>> activityStrategyMap,
                                      Map<Long, List<PromotionActivityLoggerDO>> activityLoggerMap) {


        promotionActivityOrderManagerList.add(new PromotionOrtherActivityOrderManagerImpl(
                resultDTO,
                activityRequestParams,
                promotionCouponLoggerDOList,
                couponDoList,
                promotionActivityDOList,
                activityLimitMap,
                activityStrategyMap,
                activityLoggerMap
        ));
        return this;
    }

    public OrderEditResponseDTO orderCalculate() {
        OrderEditResponseDTO resultDTO = null;
        // 订单计算
        for (PromotionActivityOrderManager cal : promotionActivityOrderManagerList) {
            resultDTO = cal.orderCalculate();
            if (resultDTO.getActivityList() != null || resultDTO.getNoActivityCommodityDTOList() != null) {
                break;
            }
        }
        return resultDTO;

    }

    /**
     * 限制的初始化
     */
    public Context initLimitCalculate(List<String> limitLists) {
        if (CollectionUtil.isEmpty(limitLists)) {
            return this;
        }
        limitLists.forEach(limit -> {
            switch (PATemplateBaseEnum.valueOf(limit)) {
                case COMMODITY:
                    CalculateCommodityLimit commodityLimit = new CalculateCommodityLimit(null, activity, params);
                    this.registerLimit(commodityLimit);
                    break;
                case NUMBER:
                    CalculateNumberLimit numberLimit = new CalculateNumberLimit(null, activity, params);
                    this.registerLimit(numberLimit);
                    break;
                case TENANT:
                    CalculateTenantLimit tenantLimit = new CalculateTenantLimit(null, activity, params);
                    this.registerLimit(tenantLimit);
                    break;
                case USER:
                    CalculateUserLimit userLimit = new CalculateUserLimit(null, activity, params);
                    this.registerLimit(userLimit);
                    break;
                case SHOP:
                    CalculateShopLimit shopLimit = new CalculateShopLimit(null, activity, params);
                    this.registerLimit(shopLimit);
                    break;
                case LUCKYDRAW:
                    CalculateLuckyDrawLimit luckyDrawLimit = new CalculateLuckyDrawLimit(null, activity, params);
                    this.registerLimit(luckyDrawLimit);
                    break;
                case BARGAIN:
                    CalculateBargainLimit bargainLimit = new CalculateBargainLimit(null, activity, params);
                    this.registerLimit(bargainLimit);
                    break;
                default:
            }
        });
        return this;
    }

    /**
     * 优惠价商品的初始化
     */
    public Context initCouponCommodityLimit() {
        CalculateCommodityLimit commodityLimit = new CalculateCommodityLimit(null, activity, params);
        this.registerLimit(commodityLimit);
        return this;
    }

    /**
     * 积分兑换的初始化
     */
    public Context initJFDHCalculate() {
        CalculateUserLimit userLimit = new CalculateUserLimit(null, activity, params);
        this.registerLimit(userLimit);
        return this;
    }

    /**
     * 领券活动的初始化
     */
    public Context initLJHDCalculate() {
        CalculateNumberLimit numberLimit = new CalculateNumberLimit(null, activity, params);
        CalculateUserLimit userLimit = new CalculateUserLimit(null, activity, params);
        this.registerLimit(numberLimit);
        this.registerLimit(userLimit);
        return this;
    }

    /**
     * 策略计算初始化
     */
    public Context initStrategyCalcalate() {
        ZJRXStrategy zjrxStrategy = new ZJRXStrategy(null, activityConfigDTO, null, activityResponseParamsDTO);
        MJYHStrategy mjyhStrategy = new MJYHStrategy(null, activityConfigDTO, null, activityResponseParamsDTO);
        DZCXStrategy dzcxStrategy = new DZCXStrategy(null, activityConfigDTO, null, activityResponseParamsDTO);
        MZStrategy mzStrategy = new MZStrategy(null, activityConfigDTO, null, activityResponseParamsDTO);
        PTStrategy ptStrategy = new PTStrategy(null, activityConfigDTO, null, activityResponseParamsDTO);
        YSStrategy ysStrategy = new YSStrategy(null, activityConfigDTO, null, activityResponseParamsDTO);
        SecKillStrategy secKillStrategy = new SecKillStrategy(null, activityConfigDTO, null, activityResponseParamsDTO);
        this.registerStrategy(zjrxStrategy);
        this.registerStrategy(mjyhStrategy);
        this.registerStrategy(dzcxStrategy);
        this.registerStrategy(mzStrategy);
        this.registerStrategy(ptStrategy);
        this.registerStrategy(ysStrategy);
        this.registerStrategy(secKillStrategy);
        return this;
    }

    /**
     * 积分兑换初始化
     */
    public Context initJFDHStrategyCalcalate() {
        JFDHStrategy jfdhStrategy = new JFDHStrategy(null, activityConfigDTO, null, activityResponseParamsDTO);
        this.registerStrategy(jfdhStrategy);
        return this;
    }

    private Boolean limitFlag = null;

    /**
     * 注册限制信息
     */
    public void registerLimit(Calculate limit) {
        limitCalculate.add(limit);
    }

    /**
     * 注册策略计算
     */
    public void registerStrategy(Calculate strategy) {
        strategyCalculate.add(strategy);
    }

    /**
     * 限制计算
     */
    public Boolean calculateLimit() {
        for (Calculate limit : limitCalculate) {
            Boolean calculate = limit.calculate();
            if (Boolean.FALSE.equals(calculate)) {  
                log.error("用户{} 在类型:{} id:{} 限制校验时不通过{}",
                        Optional.ofNullable(params).map(ActivityParamsDTO::getPhone).orElse(null),
                        Optional.ofNullable(activity).map(ActivityConfigDTO::getPaTemplateId).orElse(null),
                        Optional.ofNullable(activity).map(ActivityConfigDTO::getActivityId).orElse(null),
                        limit.getClass().getSimpleName());
                return Boolean.FALSE;
            }
        }
        limitFlag = Boolean.TRUE;
        return Boolean.TRUE;
    }

    public CalculateResult calculateLimitAndReturn() {
        for (Calculate limit : limitCalculate) {
            CalculateResult calculate = limit.calculateAndReturn();
            if (Boolean.FALSE.equals(calculate.getResult())) {
                log.error("用户{} 在类型:{} id:{} 限制校验时不通过{}",
                        Optional.ofNullable(params).map(ActivityParamsDTO::getPhone).orElse(null),
                        Optional.ofNullable(activity).map(ActivityConfigDTO::getPaTemplateId).orElse(null),
                        Optional.ofNullable(activity).map(ActivityConfigDTO::getActivityId).orElse(null),
                        limit.getClass().getSimpleName());
                return calculate;
            }
        }
        CalculateResult calculate = new CalculateResult();
        calculate.setResult(Boolean.TRUE);
        return calculate;
    }

    /**
     * 策略计算
     */
    public Boolean calculateStrategy() {
        for (Calculate limit : strategyCalculate) {
            limit.calculate();
        }
        return true;
    }

    /**
     * 优惠价限制初始化
     */
    public Context couponLimitCalculate() {
        CalculateCouponLimit couponLimit = new CalculateCouponLimit(null, activityConfigDTO, params);
        this.registerLimit(couponLimit);
        return this;
    }
}
