package com.deepexi.dxp.marketing.service.specify;

import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.OrderPayRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.WxPayCallBackRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.WxRefundCallBackRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.WxRefundsRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.OrderPayResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.PayOrderQueryResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.WxRefundsResponseDTO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityOrderDO;

import java.util.Map;

/**
 * 支付相关接口
 */
public interface WxPayService {

    /**
     * 下单支付
     * @param type 支付类型
     */
    Data<OrderPayResponseDTO> orderPay(OrderPayRequestDTO requestDTO,String url,Integer type);

    /**
     * 支付回调
     */
    public Map<String,Object> payCallBack(WxPayCallBackRequestDTO dto);

    /**
     * 退款回调
     */
    public Map<String,Object> refundCallBack(WxRefundCallBackRequestDTO dto);

    /**'
     * 查询订单
     * @param id
     * @return
     */
    PayOrderQueryResponseDTO queryOrder(Long id);


    /**
     * 退款
     * @param requestDTO
     * @return
     */
    WxRefundsResponseDTO refunds(WxRefundsRequestDTO requestDTO);


    /**
     * 关闭订单
     * @param payOrderNo  支付平台订单号
     * @return
     */
    Data closeOrderNo(String payOrderNo);

    /**
     * 支付成功处理订单
     * @return
     */
    boolean orderHandle(WxPayCallBackRequestDTO dto, ActivityOrderDO activityOrderDO);

}
