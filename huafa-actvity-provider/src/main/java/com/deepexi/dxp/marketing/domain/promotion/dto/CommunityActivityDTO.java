package com.deepexi.dxp.marketing.domain.promotion.dto;

import com.deepexi.dxp.marketing.domain.promotion.dto.specify.TagItemDTO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CommunityActivityDTO {
    @ApiModelProperty(value = "社群id")
    private Long communityId;
    @ApiModelProperty(value = "活动数量")
    private Integer total;
    private List<CommunityActivityItem> items;

    public CommunityActivityDTO(Long communityId, Integer total) {
        this.communityId = communityId;
        this.total = total;
    }

    @Data
    public static class CommunityActivityItem {
        private String name;
        @ApiModelProperty(value = "活动标签")
        private List<TagItemDTO> tags;
        private String principalMobile;
        private String principalName;
        @ApiModelProperty(value = "活动开始时间")
        @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
        private Date startTime;
        @ApiModelProperty(value = "活动结束时间")
        @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
        private Date endTime;
        @ApiModelProperty(value = "总用户参与数量")
        private Long total;

        public CommunityActivityItem(PromotionActivityDO activityDO, List<TagItemDTO> tags, Long total) {
            this.name = activityDO.getName();
            this.tags = tags;
            this.principalMobile = (String) activityDO.getExt().get("principalMobile");
            this.principalName = (String) activityDO.getExt().get("principalName");
            this.startTime = activityDO.getStartTime();
            this.endTime = activityDO.getEndTime();
            this.total = total;
        }
    }
}