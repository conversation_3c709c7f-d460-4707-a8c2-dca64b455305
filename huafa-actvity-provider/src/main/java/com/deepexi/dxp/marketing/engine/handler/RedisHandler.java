//package com.deepexi.dxp.marketing.engine.handler;
//import com.deepexi.dxp.marketing.engine.event.MarketingTaskEvent;
//import com.deepexi.dxp.marketing.engine.listener.MarketingTaskListener;
//import com.deepexi.dxp.marketing.enums.redis.RedisPrefixEnum;
//import com.deepexi.dxp.marketing.utils.SpringContextHolder;
//import com.deepexi.redis.service.RedisService;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * redis处理器
// *
// * <AUTHOR>
// * @version 1.0
// * @date 2020-04-15 11:51
// */
//public class RedisHandler extends MarketingTaskListener {
//    Logger logger = LoggerFactory.getLogger(RedisHandler.class);
//
//    /**
//     * 实验任务Redis Key前缀(%s表示任务组标识)
//     */
//    public final static String EXPERIMENT_TASK_PREFIX_KEY = RedisPrefixEnum.TASK_EXPERIMENT.getKey() + ":%s";
//    /**
//     * 实验任务组内各任务从Redis里取出会员的偏移量
//     */
//    public final static String EXPERIMENT_TASK_OFFSET_KEY = EXPERIMENT_TASK_PREFIX_KEY + ":offset:%s";
//
//    public static String getExperimentTaskPrefixKey(String groupCode) {
//        return String.format(EXPERIMENT_TASK_PREFIX_KEY, groupCode);
//    }
//
//    /**
//     * 实验任务会员数据Key
//     */
//    public static String getExperimentTaskDataKey(String groupCode) {
//        return getExperimentTaskPrefixKey(groupCode) + ":data";
//    }
//
//    public static String getExperimentTaskOffsetKey(String groupCode, Long taskActiveId) {
//        return String.format(EXPERIMENT_TASK_OFFSET_KEY, groupCode, taskActiveId);
//    }
//
//
//    @Override
//    public void suspend(MarketingTaskEvent event) {
//        super.suspend(event);
//    }
//
//    @Override
//    public void goOn(MarketingTaskEvent event) {
//
//    }
//
//    @Override
//    public void breakOff(MarketingTaskEvent event) {
//        end(event);
//    }
//
//    @Override
//    public void end(MarketingTaskEvent event) {
//        RedisService redisService = SpringContextHolder.getBean(RedisService.class);
//        List<String> keyList = new ArrayList<>();
//        keyList.add(RedisPrefixEnum.TASK_RECORD.getKey() + ":" + event.getTopicTagName());
//        keyList.add(RedisPrefixEnum.TASK_PRODUCE_NUMS.getKey() + ":" + event.getTopicTagName());
//        keyList.add(RedisPrefixEnum.TASK_CONSUME_NUMS.getKey() + ":" + event.getTopicTagName());
//        redisService.del(keyList);
//        logger.info("删除任务的redis数据:{}", event.getTopicTagName());
//    }
//
//    @Override
//    public void start(MarketingTaskEvent event) {
//
//    }
//}
