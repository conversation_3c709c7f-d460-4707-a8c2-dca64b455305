package com.deepexi.dxp.marketing.domain.marketing.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @Class: DolphinSchedulerProcessReleaseDTO
 * @Description: DolphinScheduler 流程定义修改发布状态DTO
 * @Author: lizhong<PERSON>
 * @Date: 2020/3/28
 **/
@Data
public class DolphinSchedulerProcessReleaseDTO implements Serializable {

    /**
     * 流程定义ID
     */
    private Integer processId;

    /**
     * 发布状态
     */
    private Integer releaseState;

}
