package com.deepexi.dxp.marketing.controller.specify.adminapi;

import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityAnalysisQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityTrendsQuery;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityAnalysisDetailResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityAnalysisResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityTrendResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.IndexPathResponseDTO;
import com.deepexi.dxp.marketing.enums.status.ActivityStatus;
import com.deepexi.dxp.marketing.service.specify.ActivityAnalysisService;
import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/admin-api/v1/promotion-activity")
@Api(value = "活动分析", description = "活动分析接口", tags = {"活动分析接口"})
public class ActivityAnalysisController {

    @Autowired
    private ActivityAnalysisService promotionActivityService;

    @PostMapping("/analysis")
    @ApiOperation(value="活动分析", notes = "活动分析")
    public Data<PageBean<ActivityAnalysisResponseDTO>> analysisList(@RequestBody ActivityAnalysisQuery query) throws Exception {
        return new Data<>(promotionActivityService.analysisList(query));
    }

    @PostMapping("/analysisDetail")
    @ApiOperation(value="活动分析详情", notes = "活动分析详情")
    public Data<ActivityAnalysisDetailResponseDTO> analysisDetail(@RequestBody ActivityAnalysisQuery query) {
        return new Data<>(promotionActivityService.analysisDetail(query));
    }

    @PostMapping("/findActivityTrend")
    @ApiOperation(value="活动趋势查询", notes = "活动趋势查询")
    public Data<List<ActivityTrendResponseDTO>> findActivityTrends(@RequestBody @Valid ActivityTrendsQuery query) {
        return new Data<>(promotionActivityService.findActivityTrends(query));
    }

    @PostMapping("/findOverviewOfIndicators")
    @ApiOperation(value="指标概览自定义查询", notes = "指标概览自定义查询")
    public Data<List<IndexPathResponseDTO>> findOverviewOfIndicators(@RequestBody @Valid ActivityAnalysisQuery query) {
        return new Data<>(promotionActivityService.findOverviewOfIndicators(query));
    }

    @ApiOperation(value = "海报分享统计")
    @PostMapping("/posterChannelStatistics")
    public PageBean<ActivityAnalysisResponseDTO> posterChannelStatistics(@RequestBody @Valid ActivityAnalysisQuery query) {
        return promotionActivityService.posterChannelStatistics(query);
    }

    @PostMapping("/getAccessAndDealInfoByActivityId")
    @ApiOperation(value="获取成交和到访数据", notes = "获取成交和到访数据")
    public Data<ActivityAnalysisDetailResponseDTO> getAccessAndDealInfoByActivityId(@RequestBody ActivityAnalysisQuery query) {
        return new Data<>(promotionActivityService.getAccessAndDealInfoByActivityId(query));
    }
}
