package com.deepexi.dxp.marketing.domain.marketing.vo;

import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 主动营销任务安全过滤配置VO
 * @Author: HuangBo.
 * @Date: 2020/3/12 16:45
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MarketingTaskSecurityFilterListVO extends SuperVO {


    /**
     * 营销任务类型(1:主动营销，2:自动营销)
     */
    @ApiModelProperty(value = "营销任务类型(1:主动营销，2:自动营销)")
    private Integer taskType;

    /**
     * 主动或自动营销任务ID
     */
    @ApiModelProperty(value = "主动或自动营销任务ID")
    private Long taskId;

    /**
     * 安全过滤配置集合
     */
    private List<MarketingTaskSecurityFilterVO> securityFilterVOList;

}
