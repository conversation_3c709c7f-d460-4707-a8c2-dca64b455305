package com.deepexi.dxp.marketing.service.promotion.impl;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepexi.common.extension.AppRuntimeEnv;
import com.deepexi.dxp.marketing.annotation.ClearCache;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.*;
import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityQuery;
import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionSeckillActivityQuery;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.ActivityCommoditValidateRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityCreatePostRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityUpdatePostRequest;
import com.deepexi.dxp.marketing.enums.activity.limit.PATemplateBaseEnum;
import com.deepexi.dxp.marketing.enums.activity.limit.PATemplateCouponEnum;
import com.deepexi.dxp.marketing.enums.activity.strategy.StrategyGroupEnum;
import com.deepexi.dxp.marketing.enums.common.AssociationTypeEnum;
import com.deepexi.dxp.marketing.enums.resource.ActivityStatusEnum;
import com.deepexi.dxp.marketing.enums.specify.ActivityTemplateNumberEnum;
import com.deepexi.dxp.marketing.enums.status.ActivityOnOffShelfStatus;
import com.deepexi.dxp.marketing.extension.HuafaRuntimeEnv;
import com.deepexi.dxp.marketing.service.promotion.PromotionActivityMiddleService;
import com.deepexi.dxp.marketing.service.specify.MiniProgramService;
import com.deepexi.dxp.marketing.utils.CheckUtils;
import com.deepexi.dxp.middle.promotion.consts.CacheConstant;
import com.deepexi.dxp.middle.promotion.converter.PromotionActivityConverter;
import com.deepexi.dxp.middle.promotion.converter.PromotionActivityLimitDoConverter;
import com.deepexi.dxp.middle.promotion.converter.PromotionActivityListPostResponseDtoConverter;
import com.deepexi.dxp.middle.promotion.dao.PromotionActivityDAO;
import com.deepexi.dxp.middle.promotion.dao.impl.specify.PromotionActivityAssociationService;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityLimitDO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionStrategyDO;
import com.deepexi.dxp.middle.promotion.mapper.PromotionActivityLimitMapper;
import com.deepexi.dxp.middle.promotion.mapper.PromotionStrategyMapper;
import com.deepexi.dxp.middle.promotion.util.CollectionsUtil;
import com.deepexi.util.BeanPowerHelper;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.StringUtil;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.exception.CommonExceptionCode;
import com.deepexi.util.pageHelper.PageBean;
import com.deepexi.util.pojo.CloneDirection;
import com.deepexi.util.pojo.ObjectCloneUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> xinjian.yao
 * @date 2019/11/21 11:52
 */
@Slf4j
@Service
public class PromotionActivityMiddleServiceImpl implements PromotionActivityMiddleService {

    @Autowired
    private PromotionActivityDAO promotionActivityDAO;
    @Autowired
    private PromotionStrategyMapper promotionStrategyMapper;
    @Autowired
    private PromotionActivityLimitMapper promotionActivityLimitMapper;

    @Autowired
    private MiniProgramService miniProgramService;
    @Resource
    private PromotionActivityAssociationService associationService;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(PromotionActivityCreatePostRequest dto) {
        dto.setAppId(AppRuntimeEnv.getAppId());
        dto.setTenantId(AppRuntimeEnv.getTenantId());
        // 活动名称限制
        List<PromotionActivityDO> existList = promotionActivityDAO.findAllByNameAndId(dto);
        if (CollectionUtil.isNotEmpty(existList)) {
            throw new ApplicationException("活动名称已经存在");
        }

        if((StrategyGroupEnum.HF_COUPON_ACT.getId().equals(dto.getPaTemplateId().toString())
                || StrategyGroupEnum.HF_LUCKYDRAW_ACT.getId().equals(dto.getPaTemplateId().toString())
                || StrategyGroupEnum.HF_FORM_ACT.getId().equals(dto.getPaTemplateId().toString())
                || StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId().equals(dto.getPaTemplateId().toString())
                || StrategyGroupEnum.HF_FISSION_REDUCEPRICE_ACT.getId().equals(dto.getPaTemplateId().toString())
                || StrategyGroupEnum.HF_CARD_COLLECTING_ACT.getId().equals(dto.getPaTemplateId().toString())
                || StrategyGroupEnum.HF_SIGN_ACT.getId().equals(dto.getPaTemplateId().toString())
                || StrategyGroupEnum.HF_LOCATION_SIGN_ACT.getId().equals(dto.getPaTemplateId().toString())
                || StrategyGroupEnum.HF_SIGN_UP_ACT.getId().equals(dto.getPaTemplateId().toString())
            )
                && dto.getStartTime().before(DateTime.now())){
            throw new ApplicationException("活动开始时间不能小于当前时间!");
        }

        this.limitCheck(dto);
        // 创建活动主表数据
        PromotionActivityDO activityDo = PromotionActivityConverter.converter(dto);

        //设置上下架状态(创建时勾选发布渠道，不作自动上架)
//        String deliveryChannel = MapUtils.getString(activityDo.getExt(), "deliveryChannel");
        activityDo.setUpperStatus(Integer.valueOf(ActivityOnOffShelfStatus.OFF.getId()));

        promotionActivityDAO.insertActivity(activityDo);
        Long id = activityDo.getId();
        dto.setActivityId(id);

        // 创建策略表的数据并保存
        //PromotionStrategyDO strategyDo = PromotionStrategyDoConverter.converter(dto);
        //promotionStrategyMapper.insert(strategyDo);
        //限制类型的表
        List<PromotionActivityLimitDO> limitDoList = PromotionActivityLimitDoConverter.converterList(dto);
        if(CollectionUtil.isNotEmpty(limitDoList)){
            promotionActivityLimitMapper.insertList(limitDoList);
        }

        //保存标签信息
        associationService.bindTags(id,dto.getTags(), AssociationTypeEnum.ACTIVITY_TAG);
        Integer communityId = (Integer) dto.getExt().get("communityId");
        if (communityId != null) {
            associationService.bindBySubId(communityId.longValue(), id, AssociationTypeEnum.COMMUNITY_ACTIVITY);
        }
        return id;
    }

    /**
     * 规则较验
     * @param dto
     */
    private void limitCheck(PromotionActivityCreatePostRequest dto){
        if(StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId().equals(dto.getPaTemplateId().toString())){
            List<BaseActivityDTO> assistLimit = dto.getAssistLimit();
            for (BaseActivityDTO base : assistLimit){
                if(ActivityTemplateNumberEnum.VALID_PERIOD.getId().equals(base.getId()) && !CheckUtils.isNum(base.getValue())){
                    throw new ApplicationException(CommonExceptionCode.ERROR_CODE, "助力有效期必须为1-999的整数");
                }else
                if(ActivityTemplateNumberEnum.HELP_NUM_SET.getId().equals(base.getId()) && !CheckUtils.isNum(base.getValue())){
                    throw new ApplicationException(CommonExceptionCode.ERROR_CODE, "助力获取数值设置必须为1-9999的整数");
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @ClearCache(cacheKey = {CacheConstant.ACTIVITY, CacheConstant.ACTIVITY_UN_START, CacheConstant.ACTIVITY_STRATEGY, CacheConstant.ACTIVITY_LIMIT})
    public Boolean updateActivityById(Long id, PromotionActivityUpdatePostRequest dto) {
        if (Objects.nonNull(dto.getActivityName())) {
            // 活动名称限制
            List<PromotionActivityDO> existList = promotionActivityDAO.findAllByNameAndId(dto.clone(PromotionActivityCreatePostRequest.class));
            if (CollectionUtil.isNotEmpty(existList)
                    && Objects.nonNull(CollectionsUtil.findOneInList(existList, val -> !val.getId().equals(id)))) {
                throw new ApplicationException("活动名称已经存在");
            }
        }
        PromotionActivityDO byId = promotionActivityDAO.getById(id);

        if(byId.getStatus() < Integer.parseInt(ActivityStatusEnum.IN_PROGRESS.getId()) && (StrategyGroupEnum.HF_COUPON_ACT.getId().equals(dto.getPaTemplateId().toString())
                || StrategyGroupEnum.HF_LUCKYDRAW_ACT.getId().equals(dto.getPaTemplateId().toString())
                || StrategyGroupEnum.HF_FORM_ACT.getId().equals(dto.getPaTemplateId().toString())
                || StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId().equals(dto.getPaTemplateId().toString()))
                && dto.getStartTime().before(DateTime.now())){
            throw new ApplicationException("活动开始时间不能小于当前时间!");
        }

        // 促销活动主表的修改
        dto.setActivityId(id);
        PromotionActivityDO updatePO = PromotionActivityConverter.converter(dto);
        Map<String, Object> ext = updatePO.getExt();
        Map<String, Object> dbExt = byId.getExt();
        //ext的值覆盖掉数据库的值
        dbExt.putAll(ext);
        updatePO.setExt(dbExt);
        //设置上下架状态(更新时勾选发布渠道，不作自动上架)
//        String deliveryChannel = MapUtils.getString(updatePO.getExt(), "deliveryChannel");
        updatePO.setUpperStatus(Integer.valueOf(ActivityOnOffShelfStatus.OFF.getId()));
        updatePO.setUpdatedTime(new Date());
        updatePO.setUpdatedBy(HuafaRuntimeEnv.getCreatedBy());
        promotionActivityDAO.updateById(updatePO);

        // todo 支持修改状态
        // 把原来的这个活动的策略表和限制表删除

        List<Long> ids = new ArrayList<>(2);
        ids.add(id);
        promotionStrategyMapper.deletedByActivityIds(ids);
        promotionActivityLimitMapper.deletedByActivityIds(ids);

        // 创建新的策略和限制数据
//        PromotionStrategyDO strategyDo = PromotionStrategyDoConverter.converter(dto);
//        promotionStrategyMapper.insert(strategyDo);
        //限制类型的表
        List<PromotionActivityLimitDO> limitDoList = PromotionActivityLimitDoConverter.converterList(dto);
        if(CollectionUtil.isNotEmpty(limitDoList)){
            promotionActivityLimitMapper.insertList(limitDoList);
        }
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @ClearCache(cacheKey = {CacheConstant.ACTIVITY, CacheConstant.ACTIVITY_UN_START, CacheConstant.ACTIVITY_STRATEGY, CacheConstant.ACTIVITY_LIMIT})
    public Boolean delete(List<Long> ids) {
        PromotionActivityDO promotionActivityDO = new PromotionActivityDO();
        promotionActivityDO.setUpdatedTime(new Date());
        Boolean activity = promotionActivityDAO.deleteBatchIds(ids);
        Boolean limit = promotionActivityLimitMapper.deletedByActivityIds(ids)>0;
        Boolean strategy = promotionStrategyMapper.deletedByActivityIds(ids) > 0;

        // todo 拼团 优惠券 日志表也要删除
        return activity && limit && strategy;
    }

    @Override
    public PromotionActivityDetailPostResponseDTO detail(Long id) {

        PromotionActivityDO po = promotionActivityDAO.selectById(id);

        return Optional.ofNullable(po)
                .map(d -> {
                    PromotionActivityDetailPostResponseDTO dto = new PromotionActivityDetailPostResponseDTO();
                    BeanPowerHelper.mapPartOverrider(d, dto);
                    return dto;
                })
                .orElse(null);

    }

    @Override
    public List<PromotionActivityListPostVO> findAll(PromotionActivityQuery dto) {

        List<PromotionActivityListPostVO> dtoList = new ArrayList<>();
        findList(dtoList, dto);
        return dtoList;
    }

    @Override
    public PageBean<PromotionActivityListPostVO> findPage(PromotionActivityQuery dto) {

        List<PromotionActivityListPostVO> dtoList = new ArrayList<>();
        Page<PromotionActivityDO> pageParam = new Page<>(dto.getPage(), dto.getSize());
        IPage<PromotionActivityDO> doList = promotionActivityDAO.page(pageParam,PromotionActivityConverter.queryWrapper(dto));

        return mergeStrategyAndLimit(dtoList, doList, dto.getPage(), dto.getSize());
    }

    private PageBean<PromotionActivityListPostVO> mergeStrategyAndLimit(List<PromotionActivityListPostVO> dtoList, IPage<PromotionActivityDO> doList, long page, long size) {
        PageBean<PromotionActivityDO> res = new PageBean<>(doList);
        // 获取活动下的策略信息并以活动分组
        Map<Long, List<PromotionStrategyDO>> activityStrategyMap = getAllPromotionStrategyList(doList.getRecords());
        // 获取活动下的限制信息以活动分组
        Map<Long, List<PromotionActivityLimitDO>> activityLimitMap = getAllPromotionActivityLimitList(doList.getRecords());

        //收集活动关联的优惠券id列表Map<活动ID, List<优惠券ID>>
        Map<Long, List<String>> activityIdAndCouponIdMap = new HashMap<>();
        //优惠券ID列表，用于批量查询
        List<Long> couponIdList = new ArrayList<>();
        doList.getRecords().forEach(promotionActivityDo -> {
            PromotionActivityListPostVO dto1 = transform(promotionActivityDo, activityStrategyMap, activityLimitMap);
            dtoList.add(dto1);
            //如果是领券活动，添加优惠券信息
            if (!ObjectUtils.isEmpty(dto1.getCouponLimit())) {
                for (BaseActivityDTO baseActivityDTO : dto1.getCouponLimit()) {
                    if (PATemplateCouponEnum.COUPON_ID.getId().equals(baseActivityDTO.getId())) {
                        if (activityIdAndCouponIdMap.containsKey(dto1.getActivityId())) {
                            activityIdAndCouponIdMap.get(dto1.getActivityId()).add(baseActivityDTO.getValue());
                        } else {
                            List<String> couponIds = new ArrayList<>();
                            couponIds.add(baseActivityDTO.getValue());
                            activityIdAndCouponIdMap.put(dto1.getActivityId(), couponIds);
                        }

                        couponIdList.add(Long.valueOf(baseActivityDTO.getValue()));
                    }
                }
            }
        });



        PageBean<PromotionActivityListPostVO> resList = new PageBean<>(dtoList);
        resList.setTotalElements(res.getTotalElements());
        resList.setTotalPages(res.getTotalPages());
        resList.setSize(size);
        resList.setNumber(page);

        return resList;
    }

    private void findList(List<PromotionActivityListPostVO> dtoList, PromotionActivityQuery dto) {
        List<PromotionActivityDO> doList = promotionActivityDAO.findAll(dto);
        // 获取活动下的策略信息并以活动分组
        Map<Long, List<PromotionStrategyDO>> activityStrategyMap = getAllPromotionStrategyList(doList);
        // 获取活动下的限制信息以活动分组
        Map<Long, List<PromotionActivityLimitDO>> activityLimitMap = getAllPromotionActivityLimitList(doList);
        doList.forEach(promotionActivityDo -> {
            PromotionActivityListPostVO dto1 = transform(promotionActivityDo, activityStrategyMap, activityLimitMap);
            dtoList.add(dto1);
        });

    }

    /**
     * 根据活动id查询限制 返回以活动id分组
     *
     * @param promotionActivityDOList 活动dos
     * @return 限制分组
     */
    private Map<Long, List<PromotionActivityLimitDO>> getAllPromotionActivityLimitList(List<PromotionActivityDO> promotionActivityDOList) {
        List<Long> collect = promotionActivityDOList.stream()
                .map(PromotionActivityDO::getId)
                .distinct()
                .collect(Collectors.toList());
        List<PromotionActivityLimitDO> doList = Lists.newArrayList();
        if(CollectionUtil.isNotEmpty(collect)){
            doList = promotionActivityLimitMapper.selectAllByActivityIdList(collect);
        }

        return doList.stream()
                .collect(Collectors.groupingBy(PromotionActivityLimitDO::getActivityId));
    }

    /**
     * 根据活动id查询策略 返回以活动id分组
     *
     * @param promotionActivityDOList 活动dos
     * @return 策略分组
     */
    private Map<Long, List<PromotionStrategyDO>> getAllPromotionStrategyList(List<PromotionActivityDO> promotionActivityDOList) {
        List<Long> collect = promotionActivityDOList.stream()
                .map(PromotionActivityDO::getId)
                .distinct()
                .collect(Collectors.toList());

        List<PromotionStrategyDO> doList = promotionStrategyMapper.selectAllByActivityIdList(collect);
        return doList.stream()
                .collect(Collectors.groupingBy(PromotionStrategyDO::getActivityId));


    }

    private PromotionActivityListPostVO transform(PromotionActivityDO promotionActivityDo,
                                                  Map<Long, List<PromotionStrategyDO>> activityStrategyMap,
                                                  Map<Long, List<PromotionActivityLimitDO>> activityLimitMap) {
        //组装limit
        List<PromotionActivityLimitDO> limitDoList = activityLimitMap.get(promotionActivityDo.getId());
        //组装strategy
        List<PromotionStrategyDO> strategyDoList = activityStrategyMap.get(promotionActivityDo.getId());

        return PromotionActivityListPostResponseDtoConverter
                .converter(promotionActivityDo, strategyDoList, limitDoList);
    }

    @Override
    public List<PromotionActivityDetailPostResponseDTO> detail(List<Long> ids) {
        List<PromotionActivityDO> poList = promotionActivityDAO.selectBatchIds(ids);
        return poList.stream().map(d -> {
            PromotionActivityDetailPostResponseDTO dto = new PromotionActivityDetailPostResponseDTO();
            BeanPowerHelper.mapPartOverrider(d, dto);
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public PromotionActivityListPostVO getActivityById(Long id) {
        PromotionActivityDO activityDo = promotionActivityDAO.selectById(id);
        if (null == activityDo) {
            return null;
        }
        List<PromotionStrategyDO> promotionStrategyDOList = promotionStrategyMapper.selectByActivityId(activityDo.getId());
        List<PromotionActivityLimitDO> promotionActivityLimitDOList = promotionActivityLimitMapper.selectByActivityId(activityDo.getId());
        return PromotionActivityListPostResponseDtoConverter
                .converter(activityDo, promotionStrategyDOList, promotionActivityLimitDOList);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @ClearCache(cacheKey = {CacheConstant.ACTIVITY, CacheConstant.ACTIVITY_UN_START})
    public boolean updateStatus(Long id, String status) {
        PromotionActivityDO activityDo = promotionActivityDAO.selectById(id);
        activityDo.setStatus(Integer.parseInt(status));
        return promotionActivityDAO.updateById(activityDo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @ClearCache(cacheKey = {CacheConstant.ACTIVITY, CacheConstant.ACTIVITY_UN_START})
    public Long updateActivityStatus(PromotionActivityStatusUpdateRequest dto) {

        promotionActivityDAO.updateById(dto.clone(PromotionActivityDO.class));

        return dto.getId();
    }

    @Override
    public PageBean<PromotionSeckillActivityVO> findSeckillPage(PromotionSeckillActivityQuery dto) {
        // 分页查询activity主表
        Page<PromotionActivityDO> pageParam = new Page<>(dto.getPage(), dto.getSize());
        IPage<PromotionActivityDO> doList = promotionActivityDAO.page(pageParam,PromotionActivityConverter.convertSeckill(dto));
        // 合并 strategy 和 limit 表中的数据
        List<PromotionActivityListPostVO> dtoList = new ArrayList<>();
        PageBean<PromotionActivityListPostVO> pageBean = mergeStrategyAndLimit(dtoList, doList, dto.getPage(), dto.getSize());
        return ObjectCloneUtils.convertPageBean(pageBean, PromotionSeckillActivityVO.class, CloneDirection.OPPOSITE);
    }


    private List<ActivityCommodityValidateDTO> matchActivityAndLimit(List<PromotionActivityLimitDO> activityLimitList, ActivityCommoditValidateRequest dto) {
        // 过滤商品信息
        List<SkuCodeBaseDTO> commodityLimitList = activityLimitList.stream().filter(p -> PATemplateBaseEnum.COMMODITY.getId().equals(p.getType()))
                .map(PromotionActivityLimitDO::getLimits).map(p -> JSON.parseObject(p, ComdityLimitDTO.class))
                .flatMap(p -> p.getSkuList().stream()).collect(Collectors.toList());
        return dto.getCommodityList().stream().map(p -> PromotionActivityConverter.convert(p, commodityLimitList)).collect(Collectors.toList());
    }

}
