package com.deepexi.dxp.marketing.domain.marketing.vo;

import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 主动营销资源(包含模板、H5、优惠券)
 * @Author: HuangBo.
 * @Date: 2020/3/14 17:55
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MarketingActiveResourcesListVO extends SuperVO {

  /**
   * 主动营销任务
   */
  @ApiModelProperty(value = "主动营销任务ID")
  @NotNull(message = "主动营销任务ID不能为空")
  private Long activeId;

  @ApiModelProperty(value = "主动营销资源集合")
  @NotNull(message = "主动营销资源不能为空")
  private List<MarketingActiveResourcesVO> resourcesVOList;

}
