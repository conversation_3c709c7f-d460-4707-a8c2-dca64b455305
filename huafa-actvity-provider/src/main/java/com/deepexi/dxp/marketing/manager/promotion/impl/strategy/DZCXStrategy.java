package com.deepexi.dxp.marketing.manager.promotion.impl.strategy;

import com.deepexi.dxp.marketing.domain.promotion.dto.activity.ActivityRuleDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityCommodityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityConfigDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.TemplateLimitDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityParamsDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityResponseParamsDTO;
import com.deepexi.dxp.marketing.enums.activity.strategy.StrategyGroupEnum;
import com.deepexi.dxp.marketing.enums.activity.strategy.condition.DZCXConditionEnum;
import com.deepexi.dxp.marketing.enums.activity.strategy.operation.DZCXOperationEnum;
import com.deepexi.dxp.marketing.enums.status.ActivityInventory;
import com.deepexi.dxp.marketing.manager.promotion.impl.BaseStrategy;
import com.deepexi.dxp.middle.promotion.util.Arith;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.exception.ApplicationException;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR> ming.zhong
 * @date created in 17:03 2019/11/26
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel
@Data
@Slf4j
public class DZCXStrategy extends BaseStrategy {

    private List<DZCXStrategyEnumsCalculate> calculateHelper = new ArrayList<>(30);


    public DZCXStrategy(TemplateLimitDTO templateLimitDTO, ActivityConfigDTO activityConfigDTO, ActivityParamsDTO params, ActivityResponseParamsDTO activityResponseParamsDTO) {
        super(templateLimitDTO, activityConfigDTO, params, activityResponseParamsDTO);
    }

    /**
     * 总价任选的枚举类处理
     */
    private interface DZCXStrategyEnumsCalculate {
        /**
         * @param activityRuleDTOList       活动的优惠rule
         * @param params                    活动的参数
         * @param activityResponseParamsDTO 优惠结果返回类
         */
        void calculate(List<ActivityRuleDTO> activityRuleDTOList, ActivityParamsDTO params, ActivityResponseParamsDTO activityResponseParamsDTO);
    }

    private DZCXStrategy.DZCXStrategyEnumsCalculate strategyDz(List<ActivityRuleDTO> activityRuleDTOList) {
        return (activityRuleList, params, activityResponseParams) -> {
            boolean enumsFlag = activityResponseParams.getPaTemplateId().equals(StrategyGroupEnum.DZCX_G.getId());
            if (!enumsFlag) {
                return;
            }
            if (CollectionUtil.isNotEmpty(activityRuleDTOList)) {
                String condition = activityRuleDTOList.get(0).getCondition().get(0).getId();
                if (DZCXConditionEnum.WMK.getId().equals(condition)) {
                    strategyWMK(activityRuleList, activityResponseParams);
                } else if (DZCXConditionEnum.AJS.getId().equals(condition)) {
                    strategyMJS(activityRuleList, activityResponseParams);
                } else if (DZCXConditionEnum.AJE.getId().equals(condition)) {
                    strategyMJE(activityRuleList, activityResponseParams);
                }
            }
        };
    }

    private BigDecimal getTotalDetailMoney(List<ActivityCommodityDTO> commodities) {
        BigDecimal totalDetailMoney = commodities.stream()
                .map(activityCommodity -> Arith.transformToBigDecimal(activityCommodity.getDetailPrice().multiply(new BigDecimal(activityCommodity.getSkuAmount())), 2))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return totalDetailMoney;
    }

    private void setActivityResponseParams(BigDecimal discount, BigDecimal totalDetailMoney, ActivityResponseParamsDTO activityResponseParamsDTO) {

        //折扣比例
        BigDecimal discountPer = discount.divide(new BigDecimal("10"));
        //获取优惠后价格
        BigDecimal totalDiscountsPrice = Arith.transformToBigDecimal(totalDetailMoney.multiply(discountPer), 2);
        //优惠金额
        BigDecimal totalSubtractPrice = totalDetailMoney.subtract(totalDiscountsPrice);

        activityResponseParamsDTO.setDiscount(discount);
        activityResponseParamsDTO.setDetailPrice(totalDetailMoney);
        activityResponseParamsDTO.setDiscountsPrice(totalDiscountsPrice);
        activityResponseParamsDTO.setSubtractPrice(totalSubtractPrice);
    }

    private void setCommodity(List<ActivityCommodityDTO> list, BigDecimal discount) {
        list.forEach(sku -> {
            //设置单品折扣
            sku.setDiscount(discount);
            //折扣比例
            BigDecimal discountPer = discount.divide(new BigDecimal("10"), 2, BigDecimal.ROUND_HALF_UP);
            log.info(discountPer + "折扣比例");
            //计算商品总额
            BigDecimal totalSkuMoney = Arith.transformToBigDecimal(sku.getDetailPrice().multiply(new BigDecimal(sku.getSkuAmount())), 2);
            log.info(totalSkuMoney + "商品总额");
            //计算商品折后总价
            BigDecimal discountsPriceAll = Arith.transformToBigDecimal(totalSkuMoney.multiply(discountPer), 2);
            log.info(discountsPriceAll + "计算商品折后总价");
            sku.setDiscountsPriceAll(discountsPriceAll);
            //计算商品优惠总额
            sku.setSubtractPriceAll(totalSkuMoney.subtract(discountsPriceAll));

            if (sku.getSubtractPriceAll().equals(BigDecimal.ZERO)) {
                sku.setStockType(ActivityInventory.SALES_INVENTORY.getId());
            }
        });
    }

    /**
     * @return 返回 无门槛的处理结果
     */
    private void strategyWMK(List<ActivityRuleDTO> activityRuleList, ActivityResponseParamsDTO activityResponseParams) {
        if (!StrategyGroupEnum.DZCX_G.getId().equals(activityResponseParams.getPaTemplateId())) {
            return;
        }
        //获取所有商品
        List<ActivityCommodityDTO> commodities = activityResponseParams.getActivityCommodityDTOList();
        //获取折扣
        if (CollectionUtil.isEmpty(activityRuleList)) {
            throw new ApplicationException("规则为空！");
        }
        if (activityRuleList.get(0).getSort() == null) {
            activityRuleList.forEach(val -> {
                val.setSort(val.getCondition().get(0).getValue());
            });
        }
        activityRuleList.sort(Comparator.comparing(ActivityRuleDTO::getSort).reversed());
        StringBuilder stringBuilder = new StringBuilder();
        activityRuleList.get(0).getOperation().forEach(operation -> {
            if (DZCXOperationEnum.ZK.getId().equals(operation.getId())) {
                stringBuilder.append(operation.getValue());
            }
        });
        BigDecimal discount = Arith.transformToBigDecimal(new BigDecimal(stringBuilder.toString()), 1);
        if (discount.doubleValue() < 0 || discount.doubleValue() > 10) {
            throw new ApplicationException("折扣设置不合理");
        }
        //计算每件商品的优惠金额
        setCommodity(commodities, discount);

        //获取总原始总价格
        BigDecimal totalDetailMoney = getTotalDetailMoney(commodities);
        //设置返回
        setActivityResponseParams(discount, totalDetailMoney, activityResponseParams);

//        };
    }


    /**
     * 返回 按金额的处理结果
     */
    private void strategyMJE(List<ActivityRuleDTO> activityRuleList, ActivityResponseParamsDTO activityResponseParams) {
        if (!StrategyGroupEnum.DZCX_G.getId().equals(activityResponseParams.getPaTemplateId())) {
            return;
        }
        //获取所有商品
        List<ActivityCommodityDTO> commodities = activityResponseParams.getActivityCommodityDTOList();
        //获取原商品总价
        BigDecimal totalDetailMoney = getTotalDetailMoney(commodities);
        //获取折扣条件
        if (CollectionUtil.isEmpty(activityRuleList)) {
            throw new ApplicationException("规则为空！");
        }
        if (activityRuleList.get(0).getSort() == null) {
            activityRuleList.forEach(val -> {
                val.setSort(val.getCondition().get(0).getValue());
            });
        }
        //反向排序
        activityRuleList.sort(Comparator.comparing((ActivityRuleDTO k) -> Integer.valueOf(k.getSort())).reversed());

        String stringBuilder = "10";
        //比较得出最大的discount
        boolean is = false;
        for (ActivityRuleDTO activityRuleDTO : activityRuleList) {
            String str = "10";
            for (BaseActivityDTO operation : activityRuleDTO.getOperation()) {
                if (DZCXOperationEnum.ZK.getId().equals(operation.getId())) {
                    str = operation.getValue();
                    break;
                }
            }
            for (BaseActivityDTO condition : activityRuleDTO.getCondition()) {
                if (totalDetailMoney.compareTo(Arith.transformToBigDecimal(new BigDecimal(condition.getValue()), 2)) >= 0) {
                    is = true;
                    break;
                }
            }
            if (is) {
                stringBuilder = str;
                break;
            }
        }
        BigDecimal discount = Arith.transformToBigDecimal(new BigDecimal(stringBuilder), 1);
        //计算每件商品的优惠金额
        setCommodity(commodities, discount);
        //设置返回
        setActivityResponseParams(discount, totalDetailMoney, activityResponseParams);
    }

    /**
     * 返回 按件数的处理结果
     */
    private void strategyMJS(List<ActivityRuleDTO> activityRuleList, ActivityResponseParamsDTO activityResponseParams) {
        if (!StrategyGroupEnum.DZCX_G.getId().equals(activityResponseParams.getPaTemplateId())) {
            return;
        }
        //获取所有商品
        List<ActivityCommodityDTO> commodities = activityResponseParams.getActivityCommodityDTOList();
        //获取原商品总价
        BigDecimal totalDetailMoney = getTotalDetailMoney(commodities);
        log.info(totalDetailMoney + "原商品总价");
        //获取商品总数量
        Integer totalNumber = commodities.stream().map(ActivityCommodityDTO::getSkuAmount).reduce(0, Integer::sum);
        //获取折扣条件
        if (CollectionUtil.isEmpty(activityRuleList)) {
            throw new ApplicationException("规则为空！");
        }
        if (activityRuleList.get(0).getSort() == null) {
            activityRuleList.forEach(val -> {
                val.setSort(val.getCondition().get(0).getValue());
            });
        }
        //反向排序
        activityRuleList.sort(Comparator.comparing(ActivityRuleDTO::getSort).reversed());
        StringBuilder stringBuilder = new StringBuilder("0");
        //比较得出最大的discount
        boolean is = false;
        for (ActivityRuleDTO activityRuleDTO : activityRuleList) {
            String str = "10";
            for (BaseActivityDTO operation : activityRuleDTO.getOperation()) {
                if (DZCXOperationEnum.ZK.getId().equals(operation.getId())) {
                    str = operation.getValue();
                    break;
                }
            }
            for (BaseActivityDTO condition : activityRuleDTO.getCondition()) {
                if (totalNumber >= Integer.parseInt(condition.getValue())) {
                    is = true;
                    break;
                }
            }
            if (is) {
                stringBuilder.append(str);
                break;
            }
        }
        if (!is) {
            return;
        }
        BigDecimal discount = Arith.transformToBigDecimal(new BigDecimal(stringBuilder.toString()), 1);
        //计算每件商品的优惠金额
        setCommodity(commodities, discount);
        //设置返回
        setActivityResponseParams(discount, totalDetailMoney, activityResponseParams);

    }

    private void init(List<ActivityRuleDTO> activityRuleDTOList) {
        calculateHelper.add(strategyDz(activityRuleDTOList));
    }

    @Override
    public Boolean calculate() {
        // 获取活动的策略
        List<ActivityRuleDTO> activityStrategiesList = super.getActivityConfigDTO().getActivityRuleDTOList();
        // 获取活动的参数
        ActivityParamsDTO params = super.getParams();
        // 活动返回的参数
        ActivityResponseParamsDTO activityResponseParamsDTO = super.getActivityResponseParamsDTO();
        init(activityStrategiesList);
        DZCXCalculate(activityStrategiesList, params, activityResponseParamsDTO);
        return true;
    }

    private void DZCXCalculate(List<ActivityRuleDTO> activityStrategiesList, ActivityParamsDTO params, ActivityResponseParamsDTO activityResponseParamsDTO) {

        for (DZCXStrategyEnumsCalculate strategy : calculateHelper) {
            strategy.calculate(activityStrategiesList, params, activityResponseParamsDTO);
        }
    }
}
