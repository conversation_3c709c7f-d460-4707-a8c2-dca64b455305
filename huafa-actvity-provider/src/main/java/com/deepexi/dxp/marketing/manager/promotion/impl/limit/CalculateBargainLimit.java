package com.deepexi.dxp.marketing.manager.promotion.impl.limit;

import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityUserRelatedDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityConfigDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityParamsDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.TemplateLimitDTO;
import com.deepexi.dxp.marketing.enums.specify.ActivityTemplateNumberEnum;
import com.deepexi.dxp.marketing.enums.specify.UserJoinTypeEnum;
import com.deepexi.dxp.marketing.manager.promotion.impl.BaseLimit;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.StringUtil;
import com.deepexi.util.exception.ApplicationException;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> z<PERSON><PERSON><PERSON><PERSON>
 * @date 2021/05/24 19:55
 */
@Slf4j
public class CalculateBargainLimit extends BaseLimit {

    private List<LuckyDrawLimitEnumsCalculate> calculateHelper = new ArrayList<>();

    public CalculateBargainLimit(TemplateLimitDTO templateLimitDTO, ActivityConfigDTO activity, ActivityParamsDTO params) {
        super(templateLimitDTO, activity, params);
    }

    @Override
    public Boolean calculate() {
        // 活动存储的值
        List<BaseActivityDTO> activityLimit = super.getActivity().getLuckyDrawLimit();
        // 需求比较的值
        ActivityParamsDTO params = super.getParams();

        // 初始化计算信息
        init();
        return calculateLimit(activityLimit, params);
    }

    /**
     * 函数式接口
     * 枚举类下的不同类型的计算接口
     * <p>
     * 用于处理不同限制类型的枚举类下的限制选项
     * <p>
     * 如渠道限制的不用限制类型 需要不同的计算
     */
    private interface LuckyDrawLimitEnumsCalculate {
        /**
         * 枚举类下的不同类型的计算接口
         *
         * @param limits 设计活动时 活动的用户限制配置
         * @param params    传过来的参数
         * @return 不同类型是非成功
         */
        Boolean calculate(List<BaseActivityDTO> limits, ActivityParamsDTO params);
    }

    /**
     * @return 枚举类里面全体会员 类型的处理方法
     */
    private LuckyDrawLimitEnumsCalculate check() {
        return (limit, params) -> {
            ActivityConfigDTO activity = super.getActivity();
            if (activity == null){
                return null;
            }
            if (StringUtil.isBlank(params.getProjectId()) && UserJoinTypeEnum.JOIN_TYPE_1.getId().equals(params.getUserJoinType())){
                throw new ApplicationException("项目id不能为空！");
            }
            return true;
        };
    }

    private String getLimitId(List<BaseActivityDTO> limit) {

        if (CollectionUtil.isEmpty(limit)) {
            return null;
        }
        return Optional.of(limit)
                .map(val -> CollectionUtil.isEmpty(val) ? null : val.get(0))
                .map(BaseActivityDTO::getId)
                .orElse(null);

    }


    /**
     * 枚举类每添加一种类型，都需要再这里初始化这张类型的处理结果，不然活动选择那种类型 会报错
     */
    private void init() {
        calculateHelper.add(check());
    }


    private Boolean calculateLimit(List<BaseActivityDTO> userLimit, ActivityParamsDTO params) {
        Boolean calculateResult = userCalculate(userLimit, params);
        if (Boolean.FALSE.equals(calculateResult)) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }


    /**
     * @param limitList 渠道限制类型的活动信息
     * @param params        当前用户获得的值
     * @return 计算的入口
     */
    private Boolean userCalculate(List<BaseActivityDTO> limitList, ActivityParamsDTO params) {

        for (LuckyDrawLimitEnumsCalculate limitEnumsCalculate : calculateHelper) {
            Boolean result = limitEnumsCalculate.calculate(limitList, params);
            boolean existFlag = Optional.ofNullable(result)
                    .isPresent();
            if (existFlag) {
                return result;
            }
        }
        return Boolean.FALSE;
    }

}

