package com.deepexi.dxp.marketing.service.specify.impl;

import cn.hutool.core.lang.Assert;
import com.deepexi.common.extension.AppRuntimeEnv;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.ActivityEvaluationDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.ActivityEvaluationQueryDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.SubmitEvaluationDTO;
import com.deepexi.dxp.marketing.enums.activity.strategy.DeliveryChannelEnum;
import com.deepexi.dxp.marketing.extension.HuafaRuntimeEnv;
import com.deepexi.dxp.marketing.service.specify.ActivityEvaluationService;
import com.deepexi.dxp.marketing.service.specify.PromotionActivityService;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityEvaluationDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityFissionLogDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityPartakeLogDAO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityEvaluationDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityFissionLogDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityPartakeLogDO;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.pageHelper.PageBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 活动评价服务实现类
 * <AUTHOR>
 */
@Slf4j
@Service
public class ActivityEvaluationServiceImpl implements ActivityEvaluationService {

    @Resource
    private ActivityEvaluationDAO activityEvaluationDAO;

    @Resource
    private ActivityFissionLogDAO activityFissionLogDAO;

    @Resource
    private ActivityPartakeLogDAO activityPartakeLogDAO;

    @Resource
    private PromotionActivityService promotionActivityService;

    @Override
    @Transactional
    public Boolean submitEvaluation(SubmitEvaluationDTO evaluationDTO) {
        // 1. 获取活动参与记录，补充用户信息
        ActivityPartakeLogDO partakeLog = activityPartakeLogDAO.getById(evaluationDTO.getPartakeLogId());
        Assert.notNull(partakeLog, "未找到用户参与记录");
        //用户id,电话要和参与记录一致
        Assert.isTrue(evaluationDTO.getUserId().equals(partakeLog.getUserId()) && evaluationDTO.getPhone().equals(partakeLog.getPhone()), "用户信息异常，无法评价");

        // 2. 检查用户是否已签到
        ActivityFissionLogDO fissionLog = activityFissionLogDAO.getByPhone(partakeLog.getActivityId(), partakeLog.getPhone());
        Assert.notNull(fissionLog, "用户未签到，无法提交评价");

        // 3. 检查用户是否已评价
        Assert.isFalse(activityEvaluationDAO.hasEvaluated(partakeLog.getActivityId(), partakeLog.getPhone()), "用户已评价过该活动，不可重复评价");

        // 5. 转换DTO为DO并保存
        ActivityEvaluationDO evaluationDO = new ActivityEvaluationDO();
        BeanUtils.copyProperties(evaluationDTO, evaluationDO);
        evaluationDO.setActivityId(partakeLog.getActivityId());
        evaluationDO.setAppId(AppRuntimeEnv.getAppId());
        evaluationDO.setTenantId(AppRuntimeEnv.getTenantId());
        evaluationDO.setCreatedTime(new Date());
        evaluationDO.setCreatedBy(evaluationDO.getPhone());
        evaluationDO.setUpdatedBy(evaluationDO.getCreatedBy());

        boolean result = activityEvaluationDAO.saveEvaluation(evaluationDO);
        Assert.isTrue(result, "评价提交失败");

        log.info("用户 {} 成功提交活动 {} 的评价", evaluationDTO.getPhone(), partakeLog.getActivityId());
        return true;
    }

    @Override
    public ActivityEvaluationDO getUserEvaluation(Long partakeLogId, String phone) {
        ActivityEvaluationDO evaluationDO = activityEvaluationDAO.getByPartakeLogId(partakeLogId);
        Assert.notNull(evaluationDO, "未找到用户评价");
        Assert.isTrue(evaluationDO.getPhone().equals(phone), "用户信息异常，无法查看评价");
        return evaluationDO;
    }

    @Override
    public PageBean<ActivityEvaluationDTO> getEvaluationList(ActivityEvaluationQueryDTO query) {

        // 添加数据权限控制
        List<Long> idList = null;
        if (Objects.equals(query.getDeliveryChannel(), DeliveryChannelEnum.SQ.getId())) {
            // 社群小程序，使用新岗位权限
            idList = promotionActivityService.getActivityIdsByUserIdV2();
        } else {
            // 其他情况，使用用户权限
            idList = promotionActivityService.getActivityIdsByUserId(HuafaRuntimeEnv.getUserId());
        }
        
        if (CollectionUtil.isEmpty(idList)) {
            return new PageBean<>();
        }
        
        // 设置查询活动ID
        query.setIdList(idList);

        // 查询活动评价数据
        PageBean<ActivityEvaluationDTO> pageBean = activityEvaluationDAO.findPage(query);
        promotionActivityService.fillActivityCommonInfo(pageBean.getContent());

        return pageBean;
    }

    @Override
    public PageBean<ActivityEvaluationDO> getEvaluationListForMiniProgram(ActivityEvaluationQueryDTO query) {
        // 参数验证
        Assert.notNull(query.getActivityId(), "活动ID不能为空");
        // 直接查询数据库，不进行权限控制（小程序端公开查询）
        return activityEvaluationDAO.findPageForMiniProgram(query);
    }
}