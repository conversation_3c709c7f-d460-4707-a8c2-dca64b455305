package com.deepexi.dxp.marketing.manager.promotion.impl.strategy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.ActivityRuleDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.UpIdDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityCommodityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityConfigDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.SendCommodityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.TemplateLimitDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityParamsDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityResponseParamsDTO;
import com.deepexi.dxp.marketing.enums.activity.strategy.LoggerCommodityEnum;
import com.deepexi.dxp.marketing.enums.activity.strategy.StrategyGroupEnum;
import com.deepexi.dxp.marketing.enums.activity.strategy.condition.MZStrategyType;
import com.deepexi.dxp.marketing.enums.activity.strategy.operation.MZOperationEnum;
import com.deepexi.dxp.marketing.enums.status.ActivityInventory;
import com.deepexi.dxp.marketing.manager.promotion.impl.BaseStrategy;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityLoggerDO;
import com.deepexi.dxp.middle.promotion.util.Arith;
import com.deepexi.util.CollectionUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> xianfeng.cai
 * @date created in 17:03 2019/11/27
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel
@Data
public class MZStrategy extends BaseStrategy {

    private List<MZStrategyEnumsCalculate> calculateHelper = new ArrayList<>(30);

    public MZStrategy(TemplateLimitDTO templateLimitDTO, ActivityConfigDTO activityConfigDTO,
                      ActivityParamsDTO params, ActivityResponseParamsDTO activityResponseParamsDTO) {
        super(templateLimitDTO, activityConfigDTO, params, activityResponseParamsDTO);
        super.setParams(activityConfigDTO.getParams());
    }

    /**
     * 买赠的枚举类处理
     */
    private interface MZStrategyEnumsCalculate {
        /**
         * @param activityRuleDTOList       活动的优惠rule
         * @param params                    活动的参数
         * @param activityResponseParamsDTO 优惠结果返回类
         */
        void calculate(List<ActivityRuleDTO> activityRuleDTOList, ActivityParamsDTO params, ActivityResponseParamsDTO activityResponseParamsDTO);
    }

    private MZStrategy.MZStrategyEnumsCalculate MZEnum() {
        return (activityRuleList, params, activityResponseParams) -> {
            boolean enumsFlag = activityResponseParams.getPaTemplateId().equals(StrategyGroupEnum.MZ_G.getId());
            if (!enumsFlag) {
                return;
            }
            if (activityRuleList.isEmpty()) {
                return;
            }
            String strategyType = activityRuleList.get(0).getStrategyType();
            if (MZStrategyType.MJE.getId().equals(strategyType)) {
                strategyMJE(activityRuleList, activityResponseParams);
            } else if (MZStrategyType.MJS.getId().equals(strategyType)) {
                strategyMJS(activityRuleList, activityResponseParams);
            } else if (MZStrategyType.AB_C.getId().equals(strategyType)) {
                strategyABC(activityRuleList, activityResponseParams);
            }
        };
    }

    /**
     * 获取原商品总金额
     */
    private BigDecimal getTotalDetailMoney(List<ActivityCommodityDTO> commodities) {

        return commodities.stream()
                .map(val -> val.getDetailPrice().multiply(new BigDecimal(val.getSkuAmount().toString())))
                .map(price -> Arith.transformToBigDecimal(price, 2))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 活动下订单的商品数量
     *
     * @param commodities
     * @return
     */
    private Integer getOrderNumber(List<ActivityCommodityDTO> commodities) {
        return commodities
                .stream()
                .map(ActivityCommodityDTO::getSkuAmount)
                .reduce(0, Integer::sum);
    }

    /**
     * 设置返回的数据对象
     *
     * @param sendCommodityDTO          赠送商品
     * @param couponList                赠送优惠券列表
     * @param point                     赠送积分
     * @param activityResponseParamsDTO 活动
     */
    private void setActivityResponseParams(SendCommodityDTO sendCommodityDTO,
                                           List<String> couponList,
                                           String point,
                                           ActivityResponseParamsDTO activityResponseParamsDTO) {
        activityResponseParamsDTO.setSendCommodityDTO(sendCommodityDTO);
        activityResponseParamsDTO.setCouponList(couponList);
        activityResponseParamsDTO.setPoint(point);
    }

    /**
     * @return 返回 按件数的处理结果
     */
    private void strategyMJE(List<ActivityRuleDTO> activityRuleDTOList, ActivityResponseParamsDTO activityResponseParamsDTO) {
        //获取通过了相关限制条件后待返回的商品列表
        List<ActivityCommodityDTO> commodities = activityResponseParamsDTO.getActivityCommodityDTOList();
        //获取原商品总价
        BigDecimal totalDetailMoney = getTotalDetailMoney(commodities);
        //反向排序【调整满足条件优先级排序---如：按金额大小、按设置优先级】
        activityRuleDTOList.sort(Comparator.comparing((ActivityRuleDTO val) -> Double.valueOf(val.getSort())).reversed());
        boolean strategyFalg = false;
        for (ActivityRuleDTO activityRuleDTO : activityRuleDTOList) {
            if (CollectionUtil.isEmpty(activityRuleDTO.getCondition()) || CollectionUtil.isEmpty(activityRuleDTO.getOperation())) {
                continue;
            }
            BaseActivityDTO conditionActivity = activityRuleDTO.getCondition().get(0);

            //普通方式 是否满足金额 = 订单金额-设置条件金额>=0
            boolean activityFlag = (Arith.sub(totalDetailMoney.doubleValue(), Double.parseDouble(conditionActivity.getValue()))) >= 0;
            // 满足条件填充返回参数
            if (activityFlag) {
                strategyFalg = true;
                this.resultOperation(activityRuleDTO.getOperation(), activityResponseParamsDTO);
                break;
            }
        }

        if (!strategyFalg) {
            activityResponseParamsDTO.getActivityCommodityDTOList().forEach(val -> val.setStockType(ActivityInventory.SALES_INVENTORY.getId()));
            activityResponseParamsDTO.setNoActivityCommodityDTOList(activityResponseParamsDTO.getActivityCommodityDTOList());
        }

    }

    /**
     * @return 返回 按金额的处理结果
     */
    private void strategyMJS(List<ActivityRuleDTO> activityRuleDTOList, ActivityResponseParamsDTO activityResponseParamsDTO) {
        //获取通过了相关限制条件后待返回的商品列表
        List<ActivityCommodityDTO> commodities = activityResponseParamsDTO.getActivityCommodityDTOList();
        //获取原商品总价
        Integer totalNumber = getOrderNumber(commodities);
        //反向排序【调整满足条件优先级排序---如：按订单件数、按设置优先级】
        activityRuleDTOList.sort(Comparator.comparing((ActivityRuleDTO val) -> Integer.valueOf(val.getSort())).reversed());
        boolean strategyFalg = false;
        for (ActivityRuleDTO activityRuleDTO : activityRuleDTOList) {
            if (CollectionUtil.isEmpty(activityRuleDTO.getCondition()) || CollectionUtil.isEmpty(activityRuleDTO.getOperation())) {
                continue;
            }
            BaseActivityDTO conditionActivity = activityRuleDTO.getCondition().get(0);

            //普通方式 是否满足件数 = 订单总数 >= 设置条件件数
            boolean activityFlag = totalNumber >= Integer.parseInt(conditionActivity.getValue());
            // 满足条件填充返回参数
            if (activityFlag) {
                strategyFalg = true;
                this.resultOperation(activityRuleDTO.getOperation(), activityResponseParamsDTO);
                break;
            }
        }
        if (!strategyFalg) {
            activityResponseParamsDTO.getActivityCommodityDTOList().forEach(val -> val.setStockType(ActivityInventory.SALES_INVENTORY.getId()));
            activityResponseParamsDTO.setNoActivityCommodityDTOList(activityResponseParamsDTO.getActivityCommodityDTOList());
        }
    }

    private void strategyABC(List<ActivityRuleDTO> activityRuleDTOList, ActivityResponseParamsDTO activityResponseParamsDTO) {

        //获取通过了相关限制条件后待返回的商品列表
        List<ActivityCommodityDTO> commodities = activityResponseParamsDTO.getActivityCommodityDTOList();
        //反向排序
        activityRuleDTOList.sort(Comparator.comparing((ActivityRuleDTO val) -> Integer.valueOf(val.getSort())).reversed());
        boolean strategyFalg = false;
        for (ActivityRuleDTO activityRuleDTO : activityRuleDTOList) {
            if (CollectionUtil.isEmpty(activityRuleDTO.getCondition()) || CollectionUtil.isEmpty(activityRuleDTO.getOperation())) {
                continue;
            }
            BaseActivityDTO conditionActivity = activityRuleDTO.getCondition().get(0);
            JSONArray jsonArray = JSON.parseArray(conditionActivity.getValue());
            Map<Long, Integer> maps = Maps.newHashMap();
            List<String> conditionSkuList = Lists.newArrayList();
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                maps.put(jsonObject.getLong("upId"), jsonObject.getIntValue("num"));
                conditionSkuList.add(jsonObject.getString("upId"));
            }
            List<String> paramsSkuList = commodities.stream().map(val -> val.getUpId().toString()).collect(Collectors.toList());
            boolean activityFlag = true;
            for (String sku : conditionSkuList) {
                // 只要规则中的sku不在传过来的参数中则代表条件不满足
                if (!paramsSkuList.contains(sku)) {
                    activityFlag = false;
                    break;
                }
            }
            // 数量限制控制
            for (ActivityCommodityDTO commodityDTO : commodities) {
                if (maps.containsKey(commodityDTO.getUpId()) &&
                        maps.get(commodityDTO.getUpId()) > commodityDTO.getSkuAmount()) {
                    activityFlag = false;
                    break;
                }
            }
            // 满足条件填充返回参数
            if (activityFlag) {
                strategyFalg = true;
                this.resultOperation(activityRuleDTO.getOperation(), activityResponseParamsDTO);
                break;
            }
        }
        if (!strategyFalg) {
            activityResponseParamsDTO.getActivityCommodityDTOList().forEach(val -> val.setStockType(ActivityInventory.SALES_INVENTORY.getId()));
            activityResponseParamsDTO.setNoActivityCommodityDTOList(activityResponseParamsDTO.getActivityCommodityDTOList());
        }
    }

    /**
     * 填充返回优惠结果
     *
     * @param operation
     * @param activityResponseParamsDTO
     */
    private void resultOperation(List<UpIdDTO> operation, ActivityResponseParamsDTO activityResponseParamsDTO) {
        // 遍历策略列表填充返回优惠结果
        SendCommodityDTO sendCommodityDTO = new SendCommodityDTO();
        sendCommodityDTO.setSelectFlag(false);
        List<String> couponList = null;
        String point = null;
        for (BaseActivityDTO operationRule : operation) {
            if (MZOperationEnum.ZP_NUM.getId().equals(operationRule.getId())) {
                sendCommodityDTO.setSelectFlag(true);
                sendCommodityDTO.setSelectNumber(Integer.parseInt(operationRule.getValue()));
            } else if (MZOperationEnum.ZP_COMMODITYS.getId().equals(operationRule.getId())) {
                if (StringUtils.isNotBlank(operationRule.getValue())) {
                    JSONArray jsonArray = JSON.parseArray(operationRule.getValue());
                    List<SendCommodityDTO.CommodityDTO> commodityDTOList = Lists.newArrayList();
                    SendCommodityDTO.CommodityDTO commodityDTO = null;
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject jsonObject = jsonArray.getJSONObject(i);
                        commodityDTO = new SendCommodityDTO.CommodityDTO();
                        String skuKey = "upId";
                        if (jsonObject.containsKey("skuCode")) {
                            skuKey = "skuCode";
                        }
                        commodityDTO.setSkuCode(jsonObject.getString(skuKey));
                        commodityDTO.setGrantTotal(jsonObject.getString("grantTotal"));
                        commodityDTO.setGrantNum(jsonObject.getString("grantNum"));
                        Integer usedNum = this.getGiftUsedNum(jsonObject.getString("skuId"));
                        Integer total = Integer.parseInt(commodityDTO.getGrantTotal());
                        if (usedNum >= total) {
                            //使用赠品大等于总量，直接跳过，不执行操作
                            continue;
                        }
                        //（添加进list，并返回）
                        commodityDTO.setGrantUsedNum(usedNum.toString());
                        commodityDTOList.add(commodityDTO);
                    }
                    sendCommodityDTO.setSelectFlag(true);
                    sendCommodityDTO.setCommodityList(commodityDTOList);
                }
            } else if (MZOperationEnum.ZSYHQ.getId().equals(operationRule.getId())) {
                String[] strs = operationRule.getValue().split(",");
                couponList = Lists.newArrayList(strs);
            } else if (MZOperationEnum.ZSJF.getId().equals(operationRule.getId())) {
                point = operationRule.getValue();
            }
        }
        if (CollectionUtil.isEmpty(sendCommodityDTO.getCommodityList())) {
            sendCommodityDTO = new SendCommodityDTO();
            sendCommodityDTO.setSelectFlag(false);
        }
        this.setActivityResponseParams(sendCommodityDTO, couponList, point, activityResponseParamsDTO);
    }

    /**
     * 获取当前活动,当前赠品的使用数量,
     *
     * @return
     */
    private Integer getGiftUsedNum(String skuId) {
        int total = 0;

        //获取用户参与信息
        List<PromotionActivityLoggerDO> loggerDOS = Optional.ofNullable(super.getParams())
                .map(v -> v.getLoggerDOList()).orElse(null);
        if (loggerDOS == null) {
            return 0;
        }
        //遍历记录，整合当前skuId的赠品使用数量
        for (PromotionActivityLoggerDO loggerDO : loggerDOS) {
            if (StringUtils.isNotBlank(loggerDO.getOrderDetail())) {
                Map<String, Map<String, Integer>> jsonMap = JSON.parseObject(loggerDO.getOrderDetail(), new TypeReference<Map<String, Map<String, Integer>>>() {
                });
                if (jsonMap.isEmpty()) {
                    continue;
                }
                //logger的赠品使用Map
                Map<String, Integer> detail = jsonMap.get(LoggerCommodityEnum.GIFT.getId());
                if (CollectionUtil.isEmpty(detail)) {
                    continue;
                }
                //累加
                total += detail.get(skuId) == null ? 0 : detail.get(skuId);
            }
        }
        //计算总值并返回
        return total;
    }

    private void init(List<ActivityRuleDTO> activityRuleDTOList) {
        calculateHelper.add(MZEnum());
    }

    @Override
    public Boolean calculate() {
        // 获取活动的策略
        List<ActivityRuleDTO> activityRuleDTOList = super.getActivityConfigDTO().getActivityRuleDTOList();
        // 获取活动的参数
        ActivityParamsDTO params = super.getParams();
        // 活动返回的参数
        ActivityResponseParamsDTO activityResponseParamsDTO = super.getActivityResponseParamsDTO();

        init(activityRuleDTOList);
        MZCalculate(activityRuleDTOList, params, activityResponseParamsDTO);
        return true;
    }

    private void MZCalculate(List<ActivityRuleDTO> activityStrategiesList, ActivityParamsDTO params, ActivityResponseParamsDTO activityResponseParamsDTO) {

        for (MZStrategyEnumsCalculate strategy : calculateHelper) {
            strategy.calculate(activityStrategiesList, params, activityResponseParamsDTO);
        }
    }
}
