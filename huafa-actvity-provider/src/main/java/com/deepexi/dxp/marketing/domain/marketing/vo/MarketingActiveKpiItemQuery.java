package com.deepexi.dxp.marketing.domain.marketing.vo;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 主动营销任务KPI配置项Query
 * @Author: HuangBo.
 * @Date: 2020/4/1 18:10
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Api(value = "KPI配置项Query")
public class MarketingActiveKpiItemQuery extends SuperQuery {




    /**
     * 任务目标类型(1:营收；2:商品；3:用户)
     */
    @ApiModelProperty(value = "任务目标类型(1:营收；2:商品；3:用户)")
    private int type;

    /**
     * 任务目标名称
     */
    @ApiModelProperty(value = "任务指标名称")
    private String name;

    /**
     * 父级指标
     */
    @ApiModelProperty(value = "父指标id")
    private Long pid;

    /**
     * 是否推荐(1是 0否)
     */
    @ApiModelProperty(value = "是否推荐(1是 0否)")
    private boolean recommend;

}
