package com.deepexi.dxp.marketing.manager.promotion.impl.limit;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.deepexi.dxp.marketing.constant.RedisConstants;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityParticipationDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.AgentInfoDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityConfigDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityParamsDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.TemplateLimitDTO;
import com.deepexi.dxp.marketing.enums.activity.limit.MemberTypeEnum;
import com.deepexi.dxp.marketing.enums.activity.limit.PATemplateUserEnum;
import com.deepexi.dxp.marketing.manager.promotion.CalculateResult;
import com.deepexi.dxp.marketing.manager.promotion.impl.BaseLimit;
import com.deepexi.dxp.marketing.utils.HuaFaHmacAuthUtil;
import com.deepexi.dxp.middle.promotion.util.SpringContextUtil;
import com.deepexi.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> xinjian.yao
 * @date 2019/11/23 11:55
 */
@Slf4j
public class CalculateUserLimit extends BaseLimit {

    private List<UserLimitEnumsCalculate> calculateHelper = new ArrayList<>();

    public CalculateUserLimit(TemplateLimitDTO templateLimitDTO, ActivityConfigDTO activity, ActivityParamsDTO params) {
        super(templateLimitDTO, activity, params);
    }

    @Override
    public Boolean calculate() {
        return calculateAndReturn().getResult();
    }
    @Override
    public CalculateResult calculateAndReturn() {
        // 初始化计算信息
        init();
        return calculateTenantLimit();
    }

    /**
     * 函数式接口
     * 枚举类下的不同类型的计算接口
     * <p>
     * 用于处理不同限制类型的枚举类下的限制选项
     * <p>
     * 如渠道限制的不用限制类型 需要不同的计算
     */
    private interface UserLimitEnumsCalculate {
        /**
         * 枚举类下的不同类型的计算接口
         *
         * @param userLimit 设计活动时 活动的用户限制配置
         * @param params    传过来的参数
         * @return 不同类型是非成功
         */
        CalculateResult calculate(BaseActivityDTO userLimit, ActivityParamsDTO params,Integer projectType, List<ActivityParticipationDTO> projectList);
    }

    /**
     * @return 优惠分组的处理方法
     */
    private UserLimitEnumsCalculate hylxEnum() {
        return (userLimit, params, projectType, projectList) -> {
            // 活动进来的id 和枚举类的id不一样时 返回null
            if (!PATemplateUserEnum.HYLX.getId().equals(userLimit.getId())) {
                return null;
            }
            if(Objects.equals(userLimit.getValue(), MemberTypeEnum.ALL.getId())){
                // 全部用户
                return CalculateResult.success();
            }else if(Objects.equals(userLimit.getValue(), MemberTypeEnum.OWNER.getId())){
                // 全国业主
                Date beginDate = null, endDate = null;
                if (Objects.equals(getActivity().getMemberTimeType(),2)) {
                    beginDate = getActivity().getStartTime();
                    endDate = getActivity().getEndTime();
                }
                HuaFaHmacAuthUtil huaFaHmacAuthUtil = SpringContextUtil.getBean(HuaFaHmacAuthUtil.class);
                try {
                    Boolean homeowners = huaFaHmacAuthUtil.checkHomeowners(params.getPhone(),null,beginDate,endDate,params.getUnionId());
                    if(homeowners){
                        return CalculateResult.success();
                    }else{
                        if (Objects.equals(getActivity().getMemberTimeType(),2)) {
                            return CalculateResult.error("活动期间内，成为举办方业主才允许参与活动");
                        }
                        return CalculateResult.error("活动仅限举办方业主参与");
                    }
                } catch (Exception e) {
                    log.error("校验业主身份接口调用失败:phoneNumber:"+params.getPhone(),e);
                    return CalculateResult.error("校验业主失败");
                }
            }else if(Objects.equals(userLimit.getValue(), MemberTypeEnum.PROJECT_OWNER.getId())){
                // 指定项目业主
                HuaFaHmacAuthUtil huaFaHmacAuthUtil = SpringContextUtil.getBean(HuaFaHmacAuthUtil.class);
                List<String> projectIdList;
                if(CollectionUtil.isNotEmpty(projectList)){
                    projectIdList = projectList.stream().map(ActivityParticipationDTO::getProjectId).collect(Collectors.toList());
                }else{
                    projectIdList = new ArrayList<>();
                }
                Date beginDate = null, endDate = null;
                if (Objects.equals(getActivity().getMemberTimeType(),2)) {
                    beginDate = getActivity().getStartTime();
                    endDate = getActivity().getEndTime();
                }
                try {
                    Boolean homeowners = huaFaHmacAuthUtil.checkHomeowners(params.getPhone(),projectIdList,beginDate,endDate,params.getUnionId());
                    if(homeowners){
                        return CalculateResult.success();
                    }else{
                        if (Objects.equals(getActivity().getMemberTimeType(),2)) {
                            return CalculateResult.error("活动期间内，成为举办方项目业主才允许参与活动");
                        }
                        return CalculateResult.error("活动仅限举办方项目的业主参与");
                    }
                } catch (Exception e) {
                    log.error("校验业主身份接口调用失败:phoneNumber:"+params.getPhone()+",projectId:"+params.getProjectId(),e);
                    return CalculateResult.error("校验业主失败");
                }
            } else if (Objects.equals(userLimit.getValue(),MemberTypeEnum.WHITE_LIST.getId())){
                //活动白名单方式
                RedissonClient redissonClient = SpringContextUtil.getBean(RedissonClient.class);
                RMapCache<String, Object> mapCache = redissonClient.getMapCache(RedisConstants.CACHE_PREV_KEY_WHITELIST + getActivity().getActivityId());
                if (mapCache.containsKey(params.getUnionId())) {
                    return CalculateResult.success();
                }
                return CalculateResult.error("您暂不符合活动参与条件");
            }else if(MemberTypeEnum.isAgent(userLimit.getValue())){
                //根据手机号查询经纪人
                HuaFaHmacAuthUtil huaFaHmacAuthUtil = SpringContextUtil.getBean(HuaFaHmacAuthUtil.class);
                AgentInfoDTO agentInfo = huaFaHmacAuthUtil.getAgentInfo(params.getPhone());
                String agentType = userLimit.getValue();
                //是否符合身份类型
                if(agentInfo != null && agentType.equals(agentInfo.getAgentTypeNo())) {
                    if (Objects.equals(getActivity().getMemberTimeType(),2)) {//活动期间注册
                        //是否符合新注册用户
                        if (agentInfo.getCreateTime() != null && DateUtil.isIn(agentInfo.getCreateTime(), getActivity().getStartTime(), getActivity().getEndTime())) {
                            return CalculateResult.success();
                        } else {
                            return CalculateResult.error(StrUtil.format("活动期间内，注册【{}】经纪人身份才允许参与活动",MemberTypeEnum.getAgentTypeName(agentType)));
                        }
                    } else {
                        //只要符合相应的经纪人身份即可
                        return CalculateResult.success();
                    }
                }
                return CalculateResult.error(StrUtil.format("仅【{}】经纪人身份才允许参与活动",MemberTypeEnum.getAgentTypeName(agentType)));
            } else{
                log.warn("参与用户条件未找到");
                return CalculateResult.success();
            }
        };

    }

//    /**
//     * @return 枚举类里面全体会员 类型的处理方法
//     */
//    private UserLimitEnumsCalculate qthyEnum() {
//        return (userLimit, params) -> {
//            String id = getLimitId(userLimit);
//
//            // 活动进来的id 和枚举类的id不一样时 返回null
//            if (!PATemplateUserEnum.QBHY.getId().equals(id)) {
//                return null;
//            }
//            // 全部会员限制 过来的用户类型是会员 返回真
//            String userType = params.getUserType();
//            return UserTypeEnum.MEMBER.getId().equals(userType);
//        };
//    }
//
//    private String getLimitId(List<BaseActivityDTO> userLimit) {
//
//        if (CollectionUtil.isEmpty(userLimit)) {
//            return null;
//        }
//        return Optional.of(userLimit)
//                .map(val -> CollectionUtil.isEmpty(val) ? null : val.get(0))
//                .map(BaseActivityDTO::getId)
//                .orElse(null);
//
//    }
//
//    /**
//     * @return 优惠分组的处理方法
//     */
//    private UserLimitEnumsCalculate hyfzEnum() {
//        return (userLimit, params) -> {
//            String limitId = getLimitId(userLimit);
//            // 活动进来的id 和枚举类的id不一样时 返回null
//            if (!PATemplateUserEnum.HYFZ.getId().equals(limitId)) {
//                return null;
//            }
//            // 获取用户所属分组
//            List<Long> groupIdList = params.getGroupIdList();
//            // 获取配置的分组信息
//            List<Long> userGroupIdList = CollectionsUtil.getPropertyList(userLimit, val -> Long.valueOf(val.getValue()));
//            return userGroupIdList.stream()
//                    .anyMatch(groupIdList::contains);
//        };
//
//    }
//
//    /**
//     * @return 指定会员的处理方法
//     */
//    private UserLimitEnumsCalculate zdhyEnum() {
//        return (userLimits, params) -> {
//            String limitId = getLimitId(userLimits);
//            // 活动进来的id 和枚举类的id不一样时 返回null
//            if (!PATemplateUserEnum.ZDHY.getId().equals(limitId)) {
//                return null;
//            }
//            String userId = params.getUserId();
//            String userType = params.getUserType();
//            if (!UserTypeEnum.MEMBER.getId().equals(userType)) {
//                return false;
//            }
//
//            //新规则，向下兼容，问题：flag哪里来？DB，来自于json，添加内容【tips：告知上层应用，此值为必填值。】
//            //例子： {"id": "assignMember", "flag": "==", "value": "true"}
//            //1、先遍历一遍，拿出限制用户，还是不限制用户的条件
//            Boolean deny = Boolean.TRUE;
//            for (BaseActivityDTO baseActivityDTO : userLimits) {
//                if ("true".equals(baseActivityDTO.getValue().trim()) || "false".equals(baseActivityDTO.getValue().trim())) {
//                    deny = Boolean.valueOf(baseActivityDTO.getValue().trim());
//                    //2、剔除当前限制用户的json对象
//                    userLimits.remove(baseActivityDTO);
//                }
//            }
//            //3、收集信息list（用户列表）
//            List<Long> userConfigList = CollectionsUtil.getPropertyList(userLimits, val -> Long.valueOf(val.getValue()));
//            //4、返回 return flag ? userConfigList.contains(userId)【限制与限制值匹配的】 : !userConfigList.contains(userId)【不限制与限制值匹配的】
//            return deny == userConfigList.contains(userId);
//        };
//    }

    /**
     * 枚举类每添加一种类型，都需要再这里初始化这张类型的处理结果，不然活动选择那种类型 会报错
     */
    private void init() {
        calculateHelper.add(hylxEnum());
    }


    private CalculateResult calculateTenantLimit() {
        // 活动存储的值
        List<BaseActivityDTO> userLimit = super.getActivity().getUserLimit();
        if(userLimit == null){
            log.info("兼容旧活动Id:{}没有业主限制条件",super.getActivity().getActivityId());
            return CalculateResult.success();
        }
        // 需求比较的值
        ActivityParamsDTO params = super.getParams();
        Integer projectType = super.getActivity().getProjectType();
        List<ActivityParticipationDTO> projectList = super.getActivity().getProjectList();
        //身份如果有多个的话，只要有成功的就优先返回成功的，一般只有一个，经纪人身份可能会有多个
        for (int i = 0; i < userLimit.size(); i++) {
            BaseActivityDTO baseActivityDTO = userLimit.get(i);
            for (UserLimitEnumsCalculate userLimitEnumsCalculate : calculateHelper) {
                CalculateResult result = userLimitEnumsCalculate.calculate(baseActivityDTO, params, projectType, projectList);
                if (result != null) {
                    //身份只要有一个符合就返回成功
                    if (result.getResult()) {
                        return result;
                    }
                    //如果是最后一个，则返回结果
                    if (i == userLimit.size() - 1) {
                        return result;
                    }
                }
            }
        }
        return CalculateResult.success();
    }
}

