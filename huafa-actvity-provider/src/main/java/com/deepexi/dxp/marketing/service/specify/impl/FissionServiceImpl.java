package com.deepexi.dxp.marketing.service.specify.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.deepexi.dxp.marketing.common.base.utils.CommonExceptionCode;
import com.deepexi.dxp.marketing.config.HuafaConstantConfig;
import com.deepexi.dxp.marketing.constant.RedisConstants;
import com.deepexi.dxp.marketing.converter.ActivityInfoConverter;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityFissionAssistResourceQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityFissionLogQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.CustomerFeedbackQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.*;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityFissionAssistResourceResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityFissionLogResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.FissionHomeInfoResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.PromotionActivityDetailDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.PromotionActivityResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.ResourceHisDetailResponseDTO;
import com.deepexi.dxp.marketing.domain.specify.dto.ActivityFormFeedbackDTO;
import com.deepexi.dxp.marketing.enums.activity.limit.PATemplateBaseEnum;
import com.deepexi.dxp.marketing.enums.activity.strategy.StrategyGroupEnum;
import com.deepexi.dxp.marketing.enums.coupon.WhetherEnum;
import com.deepexi.dxp.marketing.enums.specify.*;
import com.deepexi.dxp.marketing.manager.promotion.PromotionActivityManager;
import com.deepexi.dxp.marketing.service.specify.*;
import com.deepexi.dxp.marketing.utils.GenerateIdUtil;
import com.deepexi.dxp.middle.promotion.converter.ActivityConfigConverter;
import com.deepexi.dxp.middle.promotion.converter.specify.BargainingConverter;
import com.deepexi.dxp.middle.promotion.converter.specify.LuckyDrawConverter;
import com.deepexi.dxp.middle.promotion.dao.PromotionActivityDAO;
import com.deepexi.dxp.middle.promotion.dao.PromotionActivityLimitDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.*;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityLimitDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.*;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.DateUtils;
import com.deepexi.util.JsonUtil;
import com.deepexi.util.StringUtil;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import com.deepexi.util.pojo.ObjectCloneUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.ConvertUtils;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class FissionServiceImpl implements FissionService {

    @Resource
    private PromotionActivityDAO promotionActivityDAO;

    @Resource
    private ActivityPageDAO activityPageDAO;

    @Resource
    private ActivityPageShareDAO activityPageShareDAO;

    @Resource
    private LuckyDrawService luckyDrawService;
    
    @Resource
    private ActivityPartakeLogDAO activityPartakeLogDAO;

    @Resource
    private PromotionActivityLimitDAO promotionActivityLimitDAO;

    @Resource
    public ActivityVerifyDAO activityVerifyDAO;

    @Resource
    public PromotionActivityService promotionActivityService;

    @Resource
    public ActivityFissionLogDAO activityFissionLogDAO;

    @Resource
    public CustomerFeedbackDAO customerFeedbackDAO;

    @Resource
    private PromotionActivityManager promotionActivityManager;
    @Resource
    private PromotionHisResourceDAO promotionHisResourceDAO;

    @Resource
    public ActivityUserRelatedDAO activityUserRelatedDAO;

    @Autowired
    private ActivityFissionAssistResourceDAO activityFissionAssistResourceDAO;

    @Autowired
    private IncentiveService incentiveService;

    @Autowired
    private GenerateIdUtil generateIdUtil;
    @Resource
    private InteractionCenterService interactionCenterService;
    @Resource
    private HuafaConstantConfig huafaConstantConfig;
    @Autowired
    private RedissonClient redissonClient;
    @Resource
    public AssistActService assistActService;

    @Override
    public FissionHomeInfoResponseDTO homeInfo(ActivityPartakeRequest query) {

        /* ***********获取活动信息开始*********** */
        PromotionActivityResponseDTO clone = null;
        FissionHomeInfoResponseDTO prizeListAndSurplusNumber = new FissionHomeInfoResponseDTO();
        List<ActivityParticipationVO> projectInfoList = Lists.newArrayList();

        PromotionActivityDetailDTO actDTO = JSON.parseObject(String.valueOf(redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO + query.getActivityId()).get()), PromotionActivityDetailDTO.class);
        if (Objects.nonNull(actDTO)) {
            clone = actDTO.clone(PromotionActivityResponseDTO.class);
            clone.setName(actDTO.getActivityName());
            clone.setId(actDTO.getActivityId());

            prizeListAndSurplusNumber.setPrizeList(ObjectCloneUtils.convertList(actDTO.getPrizeList(), PromotionHisResourceDTO.class));
            prizeListAndSurplusNumber.setActivityPageDTO(actDTO.getActivityPageVO());
            prizeListAndSurplusNumber.setActivityPageShareDTO(actDTO.getActivityPageShareVO().clone(ActivityPageShareDTO.class));
            projectInfoList = actDTO.getProjectInfoList();
        } else {
            PromotionActivityDO promotionActivity = promotionActivityDAO.getById(query.getActivityId());
            if (promotionActivity == null) {
                throw new ApplicationException("活动不存在");
            }
            clone = promotionActivity.clone(PromotionActivityResponseDTO.class);
            clone.setExt(promotionActivity.getExt());

            //界面活动页配置
            ActivityPageVO activityPageDTO = Optional.ofNullable(activityPageDAO.getByActivity(query.getActivityId(), ActivityTypeEnum.ACTIVITY.getId()))
                    .map(e->{
                        ActivityPageVO activityPageVO = e.clone(ActivityPageVO.class);
                        String bottomBtnType = e.getBottomBtnType();
                        if(StringUtil.isNotEmpty(bottomBtnType)){
                            String[] split = bottomBtnType.split(",");
                            Integer[]  types = (Integer[]) ConvertUtils.convert(split,Integer.class);
                            activityPageVO.setBottomBtnTypes(Arrays.asList(types));
                        }
                        return activityPageVO;
                    }).orElse(null);
            prizeListAndSurplusNumber.setActivityPageDTO(activityPageDTO);

            //界面分享页配置
            prizeListAndSurplusNumber.setActivityPageShareDTO(Optional.ofNullable(activityPageShareDAO.getByActivity(query.getActivityId(), ActivityTypeEnum.ACTIVITY.getId())).map(e->e.clone(ActivityPageShareDTO.class)).orElse(null));
        }
        if(CollectionUtil.isEmpty(prizeListAndSurplusNumber.getPrizeList())){
            //查询奖品列表
            List<PromotionHisResourceDO> promotionHisResourceList = luckyDrawService.getPromotionHisResource(query.getActivityId());
            prizeListAndSurplusNumber.setPrizeList(ObjectCloneUtils.convertList(promotionHisResourceList, PromotionHisResourceDTO.class));
        }
        prizeListAndSurplusNumber.setPromotionActivity(clone);
        prizeListAndSurplusNumber.setUserJoinType(query.getUserJoinType());
        /* ***********获取活动信息结束*********** */

        //查询活动规则
        List<PromotionActivityLimitDO> promotionActivityLimitList = promotionActivityLimitDAO.selectByActivityId(query.getActivityId());

        if (query.getUserJoinType().equals(UserJoinTypeEnum.JOIN_TYPE_1.getId())) {
            //查询发起者用户信息
            sponsorExtracted(query, clone, prizeListAndSurplusNumber, prizeListAndSurplusNumber.getPrizeList(), promotionActivityLimitList,projectInfoList);
        }else{
            //参与者信息
            if (query.getSponsorId() == null){
                throw new ApplicationException("发起人记录id不能为空");
            }
            //发起人参与记录
            ActivityPartakeLogDO activityPartakeLog = activityPartakeLogDAO.getById(query.getSponsorId());
            if (activityPartakeLog == null){
                throw new ApplicationException("发起人信息不存在");
            }
//            else if (ObjectUtil.notEqual(activityPartakeLog.getFissonStatus(),FissonStatusEnum.PROCESSING.getId())){
//                sponsorExtracted(query, promotionActivity, prizeListAndSurplusNumber, promotionHisResource, promotionActivityLimitList);
//                return prizeListAndSurplusNumber;
//            }
            //点击自己链接
            if (activityPartakeLog.getUserId().equals(query.getUserId())){
                sponsorExtracted(query, clone, prizeListAndSurplusNumber, prizeListAndSurplusNumber.getPrizeList(), promotionActivityLimitList,projectInfoList);
                prizeListAndSurplusNumber.setUserJoinType(UserJoinTypeEnum.JOIN_TYPE_1.getId());
                return prizeListAndSurplusNumber;
            }

            List<BaseActivityDTO> numberList = BargainingConverter.getPromotionActivityLimit(promotionActivityLimitList, StrategyGroupEnum.HF_FISSION_REDUCEPRICE_ACT.getId().equals(clone.getPaTemplateId().toString()) ? PATemplateBaseEnum.BARGAIN.getId() : PATemplateBaseEnum.ASSIST.getId());
            //用户帮砍次数
            Integer helpTimes = BargainingConverter.elLimitConverter(numberList, ActivityTemplateNumberEnum.HELP_TIMES.getId());
            if (helpTimes <= 0){
                helpTimes = 1;
            }
            boolean isShowBargain = promotionActivityService.helpTimesCheck(activityPartakeLog.getId(),query.getUserId(),activityPartakeLog.getActivityId(),helpTimes);
            prizeListAndSurplusNumber.setIsShowBargain(isShowBargain);


            ActivityFissionLogQuery activityFissionLogQuery = new ActivityFissionLogQuery();
            activityFissionLogQuery.setPartakeLogId(activityPartakeLog.getId());
            activityFissionLogQuery.setUserId(activityPartakeLog.getUserId());
            activityFissionLogQuery.setPage(1);
            activityFissionLogQuery.setSize(1);
            List<ActivityFissionLogResponseDTO> fissionLogResponseList = activityFissionLogDAO.pageList(activityFissionLogQuery).getContent();
            if (CollectionUtil.isNotEmpty(fissionLogResponseList)){
                prizeListAndSurplusNumber.setFissionLogResponseDTO(fissionLogResponseList.get(0));
            }
            prizeListAndSurplusNumber.setActivityPartakeLogDTO(activityPartakeLog.clone(ActivityPartakeLogDTO.class));

            //查询用户有没有发起正在进行中的助力活动
            prizeListAndSurplusNumber.setMyActivityPartakeLogDTO(activityPartakeLogDAO.findMyActivityPartakeLogResponseDTO(query.getUserId(),query.getActivityId()));
        }
        return prizeListAndSurplusNumber;
    }

    private void sponsorExtracted(ActivityPartakeRequest query, PromotionActivityResponseDTO  promotionActivity, FissionHomeInfoResponseDTO prizeListAndSurplusNumber, List<PromotionHisResourceDTO> promotionHisResource, List<PromotionActivityLimitDO> promotionActivityLimitList,List<ActivityParticipationVO> projectInfoList) {
        //查询参与用户信息
        ActivityPartakeLogDTO activityPartakeLog = null;
        String copywriting = "";
        if (query.getUserId() != null) {
            ActivityUserRelatedDTO activityUserRelated = promotionActivityService.getActivityUserRelated(query, promotionActivityLimitList, StrategyGroupEnum.HF_FISSION_REDUCEPRICE_ACT.getId());
            int switchNumber = Objects.nonNull(activityUserRelated) ? (int) activityUserRelated.getLimits().get(ActivityTemplateNumberEnum.SWITCH_NUMBER.getId()) : 0;
            Boolean showSwitchCheck = luckyDrawService.isShowSwitchCheck(switchNumber, query.getActivityId(), projectInfoList);

            //用户参与记录
            activityPartakeLog = activityPartakeLogDAO.getList(query.getActivityId(), query.getUserId(), null, StrategyGroupEnum.HF_FISSION_REDUCEPRICE_ACT.getId().equals(String.valueOf(promotionActivity.getPaTemplateId())) ? FissonTypeEnum.Bargain.getId() : FissonTypeEnum.Boost.getId());

            //未选择项目时不允许切换项目
            if (showSwitchCheck){
                if (activityPartakeLog == null || StringUtil.isBlank(activityPartakeLog.getProjectId()) ||
                        !Objects.equals(activityPartakeLog.getFissonStatus(), FissonStatusEnum.PROCESSING.getId())){
                    showSwitchCheck = Boolean.FALSE;
                }
            }
            prizeListAndSurplusNumber.setIsShowSwitch(showSwitchCheck);

            prizeListAndSurplusNumber.setActivityPartakeLogDTO(activityPartakeLog);
        }


        //砍价最低价
        BigDecimal bigDecimal = BigDecimal.ZERO;
        if(StrategyGroupEnum.HF_FISSION_REDUCEPRICE_ACT.getId().equals(promotionActivity.getPaTemplateId().toString())){
            if (CollectionUtil.isNotEmpty(promotionActivityLimitList)) {
                List<BaseActivityDTO> baseActivity = promotionActivityLimitList
                        .stream().filter(e -> e.getType().equals(PATemplateBaseEnum.BARGAIN.getId()))
                        .map(e -> JSON.parseArray(e.getLimits(), BaseActivityDTO.class))
                        .findFirst()
                        .orElse(null);

                //砍价最低价
                bigDecimal = LuckyDrawConverter.elLimitConverterToBigDecimal(baseActivity, ActivityTemplateNumberEnum.LOWEST_PRICE.getId());
            }

            //0:未开始，1：进行中，2砍价成功，3：已领取，4砍价失败
            Integer status = BargainActTypeEnum.NOT_STARTED.getId();
            if (activityPartakeLog == null) {
                copywriting = "最低可以" + bigDecimal + "元抢购！赶紧来参与！";
            } else if (activityPartakeLog.getFissonStatus().equals(FissonStatusEnum.PROCESSING.getId())) {

                PromotionHisResourceDTO promotionHisResourceDTO = promotionHisResource.get(0);
                //活动结束时间校验
                status = this.checkFissionEndTime(activityPartakeLog,promotionHisResourceDTO.getName());
                if (status == null) {
                    BigDecimal price = promotionHisResourceDTO.getPurchasePrice();

                    if (price == null || price.compareTo(BigDecimal.ZERO) <= 0){
                        throw new ApplicationException("《砍价》资源价格异常");
                    }
                    BigDecimal subtract = price.subtract(activityPartakeLog.getCurrentFissonPrice()).subtract(bigDecimal);
                    copywriting = "已砍" + activityPartakeLog.getCurrentFissonPrice() + "元，还差" + subtract + "元砍价成功！";
                    status = BargainActTypeEnum.PROCESSING.getId();
                }else if(Objects.equals(status, BargainActTypeEnum.SUCCESSFUL.getId())){
                    copywriting = "恭喜你，砍价成功！可以" + bigDecimal + "元领取！";
                }else {
                    copywriting = "砍价失败";
                }

            } else if (activityPartakeLog.getFissonStatus().equals(FissonStatusEnum.SUCCESS.getId())) {
                copywriting = "恭喜你，砍价成功！可以" + bigDecimal + "元领取！";
                status = BargainActTypeEnum.SUCCESSFUL.getId();
                List<ActivityVerifyDO> activityVerifyList = this.getActivityVerify(query.getActivityId(), query.getUserId(),activityPartakeLog.getCode());
                if (CollectionUtil.isNotEmpty(activityVerifyList)) {
                    copywriting = "砍价成功！";
                    status = BargainActTypeEnum.RECEIVED.getId();
                }
            } else {
                copywriting = "砍价失败";
                status = BargainActTypeEnum.FAILED.getId();
            }

            prizeListAndSurplusNumber.setCopywriting(copywriting);
            prizeListAndSurplusNumber.setStatus(status);
        }


        //好友帮助记录----抽出成新的接口
        /*List<ActivityFissionLogResponseDTO> activityFissionLogResponseDTOList = getActivityFissionLogResponseDTOList(Objects.nonNull(activityPartakeLog) ? activityFissionLogDAO.listByPartakeLogId(activityPartakeLog.getId()) : Lists.newArrayList(), getHelpNumSet(promotionActivityLimitList, promotionActivity.getPaTemplateId()), getAssistNumDefinition(promotionActivityLimitList, promotionActivity.getPaTemplateId()));
        prizeListAndSurplusNumber.setActivityFissionLogResponseDTOList(activityFissionLogResponseDTOList);*/
        //是否系统发放
        if(StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId().equals(promotionActivity.getPaTemplateId().toString())){
            if(Objects.nonNull(activityPartakeLog)){
                List<BaseActivityDTO> numberList = ActivityConfigConverter.getLimitArray(promotionActivityLimitList, PATemplateBaseEnum.ASSIST.getId(), BaseActivityDTO.class);
                Integer receiveMode = numberList.stream()
                        .filter(number -> number.getId().equals(ActivityTemplateNumberEnum.HELP_RECEIVE_MODE.getId()))
                        .map(number -> Integer.parseInt(number.getValue())).findFirst().orElse(0);
                prizeListAndSurplusNumber.setReceiveMode(receiveMode);

                List<PromotionHisResourceDTO> ladderSortResourceList = promotionHisResource.stream().filter(item -> FissonResourceTypeEnum.POWER_LADDER.getId().equals(item.getFissonResourceType())).collect(Collectors.toList());

                prizeListAndSurplusNumber.setActivityFissionAssistResourceResponseDTOList(getActivityFissionAssistResourceList(ladderSortResourceList));

                //阶梯资源
                prizeListAndSurplusNumber.setLadderSortTotal(ladderSortResourceList.size());

                Long partakeLogId = Objects.nonNull(activityPartakeLog) ? activityPartakeLog.getId() : 0L;
                List<ActivityFissionAssistResourceDO> resourceList = activityFissionAssistResourceDAO.getListByPartakeLogId(partakeLogId, 0);
                if(CollectionUtil.isEmpty(resourceList)){
                    prizeListAndSurplusNumber.setIsReceiveFinish(1);//已领取完
                }
                //计算当前已助力用户数量达到某个阶梯
                prizeListAndSurplusNumber.setLadderSortFinish(getFinishLadderSort(ladderSortResourceList,activityPartakeLog.getCurrentFissonCount()));

                prizeListAndSurplusNumber.setCurrentLadderSurplusNumber(getCurrentLadderSurplusNumber(ladderSortResourceList,activityPartakeLog.getCurrentFissonCount()));
            }
            if(prizeListAndSurplusNumber.getActivityPartakeLogDTO() == null && query.getSponsorId() != null){
                prizeListAndSurplusNumber.setActivityPartakeLogDTO(activityPartakeLogDAO.getById(query.getSponsorId()).clone(ActivityPartakeLogDTO.class));
            }
        }
    }

    @Override
    public Boolean receiveNow(FissionReceiveNowDTO dto) {

        PromotionActivityDO promotionActivityDO = promotionActivityDAO.getById(dto.getActivityId());

        //表单活动奖品领取-仅对于红包其他是参与活动的同时就发放了
        if(StrategyGroupEnum.HF_FORM_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())){
            return formReceiveNow(dto,promotionActivityDO);
        }

        CustomerFeedbackQuery query = new CustomerFeedbackQuery();
        query.setActivityId(dto.getActivityId());
        //query.setUserId(dto.getUserId());
        query.setPhone(dto.getPhone());
        if(!StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString()) ){
            query.setResourceId(dto.getResourceId());
        }
        List<ActivityFormFeedbackDO> formFeedbackList = customerFeedbackDAO.pageList(query).getContent();
        if (CollectionUtil.isEmpty(formFeedbackList)){
            log.info("未填写信息登记，req={}", JsonUtil.bean2JsonString(query));
            throw new ApplicationException(CommonExceptionCode.NOT_ACTIVITY_FORM,"请填写信息登记");
        }

        ActivityPartakeLogDO partakeLogById = activityPartakeLogDAO.getPartakeLogById(dto.getSponsorId());
        if (partakeLogById == null){
            throw new ApplicationException(CommonExceptionCode.INVALIDATION,"发起人信息丢失");
        }

        //排除助力活动,助力活动不一定要达到所需助力人数
        if (!StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString()) && partakeLogById.getCurrentFissonCount() < partakeLogById.getNeedFissonCount()){
            throw new ApplicationException(CommonExceptionCode.INVALIDATION,"没有达到领取条件");
        }

        if(StrategyGroupEnum.HF_FISSION_REDUCEPRICE_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())){
            PromotionHisResourceDO byId = promotionHisResourceDAO.getById(partakeLogById.getResourceId());
            if (byId == null){
                throw new ApplicationException(CommonExceptionCode.INVALIDATION,"资源信息不存在");
            }

            //砍价核销以及库存扣减
            return promotionActivityManager.bargainActivityVerify(dto, promotionActivityDO, formFeedbackList, partakeLogById, byId);

        }else if(StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())){
            //助力活动发起者领取奖品
            //查询可领取取奖品
            //如果resourceId 有值则领取一个,没有则全部领取

            List<ActivityFissionAssistResourceDO> assistResourceList = activityFissionAssistResourceDAO.getListByPartakeLogId(dto.getSponsorId(),0);
            if(CollectionUtil.isEmpty(assistResourceList)){
                throw new ApplicationException(CommonExceptionCode.INVALIDATION,"没有可领取奖品！");
            }

            //剔除库存为0的资源
            Long quantity = null;
            Iterator<ActivityFissionAssistResourceDO> iterator = assistResourceList.iterator();
            while (iterator.hasNext()){
                ActivityFissionAssistResourceDO activityFissionAssistResourceDO = iterator.next();
                quantity = redissonClient.getAtomicLong(RedisConstants.CACHE_PREV_KEY_ACT_RESOURCE_INFO_COUNT+activityFissionAssistResourceDO.getResourceId()).get();
                if(quantity.compareTo(0L)<=0){
                    iterator.remove();
                }
            }

            if(CollectionUtil.isEmpty(assistResourceList)){
                throw new ApplicationException(CommonExceptionCode.INVALIDATION,"来晚一步哦，奖品被抢光啦~");
            }

            List<Long> resourceIdList = assistResourceList.stream().map(ActivityFissionAssistResourceDO::getResourceId).distinct().collect(Collectors.toList());

            if(dto.getResourceId() != null && resourceIdList.contains(dto.getResourceId())){
                resourceIdList = Lists.newArrayList();
                resourceIdList.add(dto.getResourceId());
                assistResourceList = assistResourceList.stream().filter(item -> item.getResourceId().equals(dto.getResourceId()) && item.getPartakeLogId().equals(dto.getSponsorId())).collect(Collectors.toList());//只取当前领取的资源
            }

            List<PromotionHisResourceDO> promotionHisResourceList = promotionHisResourceDAO.listByIds(resourceIdList);
            Map<Long, PromotionHisResourceDO> resourceMap = promotionHisResourceList.stream().collect(Collectors.toMap(PromotionHisResourceDO::getId, Function.identity()));

            List<PromotionActivityLimitDO> limitList = promotionActivityLimitDAO.selectByActivityId(promotionActivityDO.getId());
            List<BaseActivityDTO> numberList = ActivityConfigConverter.getLimitArray(limitList, PATemplateBaseEnum.ASSIST.getId(), BaseActivityDTO.class);
            Integer receiveMode = numberList.stream()
                    .filter(number -> number.getId().equals(ActivityTemplateNumberEnum.HELP_RECEIVE_MODE.getId()))
                    .map(number -> Integer.parseInt(number.getValue())).findFirst().orElse(0);
            if(ReceiveModeEnum.FREE.getId().equals(receiveMode)){//系统自动发放
                throw new ApplicationException(CommonExceptionCode.INVALIDATION,"该礼品为系统自动发放无须领取！");
            }
            //记录奖品领取信息
            StringBuilder sb = new StringBuilder();
            assistResourceList.forEach(activityFissionAssistResourceDO ->{
                Boolean flag = assistActService.initiatorFissionAssistRecive(activityFissionAssistResourceDO, resourceMap.get(activityFissionAssistResourceDO.getResourceId())
                        , partakeLogById.getProjectName(), partakeLogById.getProjectId(),promotionActivityDO.getName(),sb);
            });

            //发送模板消息
            this.templateSend(partakeLogById.getPhone(), partakeLogById.getUserName(), assistResourceList.stream().map(e-> resourceMap.get(e.getResourceId()).getName()).distinct()
                    .collect(Collectors.joining(",")), sb.toString(), huafaConstantConfig.BOOST_SUCCESS_TEMPLATE_ID);

            //小程序订阅消息
            promotionActivityService.miniTemplateNews(promotionActivityDO, partakeLogById);
            return  Boolean.TRUE;
        }
        return false;

    }

    /**
     * 表单
     * @param dto
     * @return
     */
    private Boolean formReceiveNow(FissionReceiveNowDTO dto,PromotionActivityDO promotionActivityDO) {
        //只有开启获奖才可以领取
        Integer submitFormAward = (Integer) promotionActivityDO.getExt().get("submitFormAward");
        if(!WhetherEnum.YES.getId().equals(submitFormAward)){
            throw new ApplicationException(CommonExceptionCode.INVALIDATION,"该活动未开启获奖！");
        }
        ActivityFormFeedbackDTO activityFormFeedbackDTO = customerFeedbackDAO.getActivityFormFeedUserId(dto.getActivityId(), dto.getPhone(),null);
        if(Objects.isNull(activityFormFeedbackDTO)){
            throw new ApplicationException(CommonExceptionCode.INVALIDATION,"反馈信息不存在！");
        }

        QueryWrapper<ActivityPartakeLogDO> activityPartakeLogDOQueryWrapper = new QueryWrapper<>();
        activityPartakeLogDOQueryWrapper.lambda().eq(ActivityPartakeLogDO::getActivityId,dto.getActivityId());
        activityPartakeLogDOQueryWrapper.lambda().eq(ActivityPartakeLogDO::getResourceId,dto.getResourceId());
        activityPartakeLogDOQueryWrapper.lambda().eq(ActivityPartakeLogDO::getPhone,dto.getPhone());
        List<ActivityPartakeLogDO> list = activityPartakeLogDAO.list(activityPartakeLogDOQueryWrapper);
        if(CollectionUtil.isEmpty(list)){
            throw new ApplicationException(CommonExceptionCode.INVALIDATION,"当前没有可领取奖品！");
        }
        ActivityPartakeLogDO activityPartakeLogDO = list.get(0);
        QueryWrapper<ActivityVerifyDO> verifyQueryWrapper = new QueryWrapper<>();
        verifyQueryWrapper.lambda().eq(ActivityVerifyDO::getActivityId,dto.getActivityId());
        verifyQueryWrapper.lambda().eq(ActivityVerifyDO::getResourceId,dto.getResourceId());
        verifyQueryWrapper.lambda().eq(ActivityVerifyDO::getPhone,dto.getPhone());
        ActivityVerifyDO activityVerifyDO = activityVerifyDAO.getOne(verifyQueryWrapper);
        if(Objects.nonNull(activityVerifyDO)){
            throw new ApplicationException(CommonExceptionCode.INVALIDATION,"奖品已领取过了！");
        }

        PromotionHisResourceDO promotionHisResourceDO = promotionHisResourceDAO.getById(dto.getResourceId());
        String resourceCode = generateIdUtil.getResourceCode();
        //扣减缓存库存
        boolean flag = promotionActivityManager.decrRedisQty(promotionHisResourceDO.getId(), promotionHisResourceDO.getActivityId());
        if(!flag){
            throw new ApplicationException(CommonExceptionCode.INVALIDATION,"奖品已领取完了！");
        }
        try{
            //数据库-更新剩余资源数量
            promotionActivityManager.decrRemainQty(promotionHisResourceDO.getId(),1L);

            if(PromotionResourceTypeEnum.THIRD_PARTY.getId().equals(promotionHisResourceDO.getType())){
                //第三方核销
                thirdVerify(dto, activityPartakeLogDO, promotionHisResourceDO,promotionActivityDO);
            }else {
                //添加核销记录
                Map<String, Object> projectInfo = new HashMap<>();
                if (StringUtil.isNotEmpty(activityPartakeLogDO.getProjectId()) && StringUtil.isNotEmpty(activityPartakeLogDO.getProjectName())) {
                    projectInfo.put("projectId", activityPartakeLogDO.getProjectId());
                    projectInfo.put("projectName", activityPartakeLogDO.getProjectName());
                }
                ActivityOrderDO activityOrderDO = new ActivityOrderDO();
                activityOrderDO.setType(dto.getType());
                activityOrderDO.setActivityId(dto.getActivityId());
                activityOrderDO.setUserId(dto.getUserId());
                activityOrderDO.setId(0L);
                activityOrderDO.setExt(projectInfo);
                activityVerifyDO = ActivityInfoConverter.verifyConverter(activityOrderDO, activityFormFeedbackDTO, promotionHisResourceDO, resourceCode, VerifyStatusEnum.NO_VERIFY.getId(), WhetherEnum.NO.getId());
                activityVerifyDAO.save(activityVerifyDO);
            }
            return Boolean.TRUE;
        }catch (Exception e){
            promotionActivityManager.incrRedisQty(promotionHisResourceDO.getId(),dto.getActivityId());
            throw e;
        }
    }

    private void thirdVerify(FissionReceiveNowDTO dto, ActivityPartakeLogDO activityPartakeLogDO, PromotionHisResourceDO promotionHisResourceDO,PromotionActivityDO promotionActivityDO) {
        if (ThirdCategoryEnum.PHONE_FEE.getId().equals(promotionHisResourceDO.getThirdCategory())){
            //话费充值
            incentiveService.rechargeMobileAsync(LuckyDrawConverter.rechargeMobileConverter(dto.clone(ReceiveNowDTO.class),promotionActivityDO.getName(), promotionHisResourceDO, activityPartakeLogDO.getCode()));
        }else if(ThirdCategoryEnum.CASH_BAG.getId().equals(promotionHisResourceDO.getThirdCategory())){
            //发送红包激励
            incentiveService.sendIncentiveAsync(LuckyDrawConverter.sendIncentiveConverter(dto.clone(ReceiveNowDTO.class),
                    promotionActivityDO.getName(), promotionHisResourceDO, activityPartakeLogDO.getCode(),SendTemplateNewsRequestDTO.MP_FROM_ZYT));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean switchProject(SwitchProjectDTO dto) {
        PromotionActivityDO byId = promotionActivityDAO.getById(dto.getActivityId());
        if (byId == null){
            throw new ApplicationException("活动不存在！");
        }

        //查询剩余抽奖次数
        LotteryRequestDTO query = new LotteryRequestDTO();
        query.setActivityId(dto.getActivityId());
        //query.setUserId(dto.getUserId());
        query.setPhone(dto.getPhone());
        ActivityUserRelatedDTO activityUserRelated = luckyDrawService.getActivityUserRelated(query);
        int switchNumber = (int) activityUserRelated.getLimits().get(ActivityTemplateNumberEnum.SWITCH_NUMBER.getId());
//        Boolean showSwitchCheck = luckyDrawService.isShowSwitchCheck(switchNumber, dto.getActivityId(),dto.getPhone());
//        if(!showSwitchCheck){
//            log.info("《砍价》不符合切换项目条件");
//            throw new ApplicationException("不符合切换项目条件");
//        }

        Integer fissionType;
        if (Objects.equals(String.valueOf(byId.getPaTemplateId()), StrategyGroupEnum.HF_FISSION_REDUCEPRICE_ACT.getId())){
            fissionType = FissonTypeEnum.Bargain.getId();
        }else {
            fissionType = FissonTypeEnum.Boost.getId();
        }
//
        ActivityPartakeLogDTO activityPartakeLog = activityPartakeLogDAO.getList(dto.getActivityId(), dto.getUserId(), FissonStatusEnum.PROCESSING.getId(),fissionType);
//        if (activityPartakeLog == null || activityPartakeLog.getProjectId().equals(dto.getProjectId())){
//            log.info("《砍价》切换的项目不能与之前相同！");
//            throw new ApplicationException("切换的项目不能与之前相同！");
//        }
//
//        //切换项目时砍价明细清空，包括发起砍价次数、砍价好友数、获得礼品券等
//        UpdateWrapper<ActivityFissionLogDO> updateWrapper = new UpdateWrapper<>();
//        updateWrapper.lambda().eq(ActivityFissionLogDO::getPartakeLogId,activityPartakeLog.getId());
//        activityFissionLogDAO.remove(updateWrapper);
//
//        //查询未核销的礼品券
//        List<ActivityVerifyInResponseDTO> verifyInResponseList = activityVerifyDAO.findUnGiftVouchers(dto.getActivityId(),dto.getPhone());
//
//        if (CollectionUtil.isNotEmpty(verifyInResponseList)) {
//
//            List<Long> verifyIds = verifyInResponseList.stream().map(ActivityVerifyInResponseDTO::getId).collect(Collectors.toList());
//            //获得未核销的礼品券清空
//            activityVerifyDAO.removeGiftVoucher(verifyIds);
//        }
//
//        List<PromotionActivityLimitDO> promotionActivityLimits = promotionActivityLimitDAO.selectByActivityId(query.getActivityId());
//        PromotionActivityLimitDO limitDO = promotionActivityLimits
//                .stream().filter(e -> e.getType().equals(type))
//                .findFirst()
//                .orElse(null);
//        if (limitDO == null){
//            return null;
//        }
//        List<BaseActivityDTO> numberList = JSON.parseArray(limitDO.getLimits(), BaseActivityDTO.class);
//        Integer launchTimes = LuckyDrawConverter.elLimitConverter(numberList, ActivityTemplateNumberEnum.LAUNCH_TIMES.getId());

        //累加切换项目次数,恢复发起砍价次数
        ActivityUserRelatedDO activityUserRelatedDO = new ActivityUserRelatedDO();
        activityUserRelatedDO.setId(activityUserRelated.getId());
        activityUserRelated.getLimits().put(ActivityTemplateNumberEnum.SWITCH_NUMBER.getId(),switchNumber + 1);
//        activityUserRelated.getLimits().put(ActivityTemplateNumberEnum.LAUNCH_TIMES.getId(),launchTimes);
        activityUserRelatedDO.setLimits(activityUserRelated.getLimits());
        activityUserRelatedDAO.updateById(activityUserRelatedDO);

        ActivityPartakeLogDO activityPartakeLogDO = new ActivityPartakeLogDO();
        activityPartakeLogDO.setId(activityPartakeLog.getId());
        activityPartakeLogDO.setProjectId(dto.getProjectId());
        activityPartakeLogDO.setProjectName(dto.getProjectName());
        return activityPartakeLogDAO.updateById(activityPartakeLogDO);
    }

    @Override
    public PageBean<ActivityFissionLogResponseDTO> findFissionLogList(ActivityFissionLogQuery query) {
        if(query.getPartakeLogId() == null){
            return new PageBean<>(new ArrayList<>());
        }
        ActivityPartakeLogDO activityPartakeLog = activityPartakeLogDAO.getById(query.getPartakeLogId());
        if(Objects.isNull(activityPartakeLog)){
            return new PageBean<>(new ArrayList<>());
        }
        PageBean<ActivityFissionLogResponseDTO> pageList = activityFissionLogDAO.pageList(query);
        List<ActivityFissionLogResponseDTO> content = pageList.getContent();

        if(CollectionUtil.isNotEmpty(content)){
            PromotionActivityDO promotionActivityDO = promotionActivityDAO.getById(activityPartakeLog.getActivityId());
            List<PromotionActivityLimitDO> promotionActivityLimitList = promotionActivityLimitDAO.selectByActivityId(activityPartakeLog.getActivityId());
            getActivityFissionLogResponseDTOList(content, getHelpNumSet(promotionActivityLimitList, promotionActivityDO.getPaTemplateId()), getAssistNumDefinition(promotionActivityLimitList, promotionActivityDO.getPaTemplateId()));
        }
        return pageList;
    }

    @Override
    public List<ActivityFissionAssistResourceResponseDTO> getPrizeAxisStatus(Integer activityId, Integer partakeLogId) {

        PromotionActivityDetailDTO actDTO = JSON.parseObject(String.valueOf(redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO + activityId).get()), PromotionActivityDetailDTO.class);

        List<PromotionHisResourceDTO> dtoList = ObjectCloneUtils.convertList(actDTO.getPrizeList(), PromotionHisResourceDTO.class);
        List<PromotionHisResourceDTO> ladderSortResourceList = dtoList.stream().filter(item -> FissonResourceTypeEnum.POWER_LADDER.getId().equals(item.getFissonResourceType())).collect(Collectors.toList());

        List<ActivityFissionAssistResourceResponseDTO> list = Lists.newArrayList();
        if(CollectionUtil.isEmpty(ladderSortResourceList)){
            return list;
        }

        QueryWrapper<ActivityFissionAssistResourceDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ActivityFissionAssistResourceDO::getActivityId,activityId);
        queryWrapper.lambda().eq(ActivityFissionAssistResourceDO::getDeleted, WhetherEnum.NO.getId());
        queryWrapper.lambda().eq(ActivityFissionAssistResourceDO::getPartakeLogId,partakeLogId);
        List<ActivityFissionAssistResourceDO> activityFissionAssistResourceDOList = activityFissionAssistResourceDAO.list(queryWrapper);

        Map<Long, ActivityFissionAssistResourceDO> activityFissionAssistResourceDOMap = activityFissionAssistResourceDOList.stream().collect(Collectors.toMap(ActivityFissionAssistResourceDO::getResourceId, item->item,(n1, n2) -> n1));

        ActivityFissionAssistResourceResponseDTO  activityFissionAssistResourceResponseDTO = null;
        for(PromotionHisResourceDTO promotionHisResourceDTO : ladderSortResourceList){
            activityFissionAssistResourceResponseDTO = promotionHisResourceDTO.clone(ActivityFissionAssistResourceResponseDTO.class);
            activityFissionAssistResourceResponseDTO.setResourceId(promotionHisResourceDTO.getId());

            if(Objects.nonNull(activityFissionAssistResourceDOMap.get(promotionHisResourceDTO.getId()))){
                activityFissionAssistResourceResponseDTO.setReceived(activityFissionAssistResourceDOMap.get(promotionHisResourceDTO.getId()).getReceived());
                activityFissionAssistResourceResponseDTO.setIsCompleted(1);
            }

            Long quantity = redissonClient.getAtomicLong(RedisConstants.CACHE_PREV_KEY_ACT_RESOURCE_INFO_COUNT+promotionHisResourceDTO.getId()).get();
            activityFissionAssistResourceResponseDTO.setCurrentQuantity(quantity);

            list.add(activityFissionAssistResourceResponseDTO);

        }

        return list;

    }

    @Override
    public PageBean<ActivityFissionAssistResourceResponseDTO> findCertificatesList(ActivityFissionAssistResourceQuery query) {
        PageBean<ActivityFissionAssistResourceResponseDTO> pageBean = this.activityFissionAssistResourceDAO.findCertificatesList(query);
        List<ActivityFissionAssistResourceResponseDTO> content = pageBean.getContent();
        if(CollectionUtil.isNotEmpty(content)){
            //提取资源id
            Set<Long> resourceIds = content.stream().map(ActivityFissionAssistResourceResponseDTO::getResourceId).collect(Collectors.toSet());

            Map<Long, PromotionHisResourceDO> resourceMap = Maps.newHashMap();

            QueryWrapper<PromotionHisResourceDO> queryWrapperResource = new QueryWrapper<>();
            queryWrapperResource.lambda().in(PromotionHisResourceDO::getId,resourceIds);
            List<PromotionHisResourceDO> resourceList = promotionHisResourceDAO.list(queryWrapperResource);
            if (CollectionUtil.isNotEmpty(resourceList)){
                resourceMap = resourceList.stream().collect(Collectors.toMap(PromotionHisResourceDO::getId, Function.identity()));
            }
            Map<Long, PromotionHisResourceDO> finalResourceMap = resourceMap;
            content.forEach(item ->{
                PromotionHisResourceDO promotionHisResourceDO = finalResourceMap.get(item.getResourceId());
                item.setResourceHisDetailResponseDTO(Objects.nonNull(promotionHisResourceDO) ? promotionHisResourceDO.clone(ResourceHisDetailResponseDTO.class):null);
            });
        }
        return pageBean;
    }

    private Integer checkFissionEndTime(ActivityPartakeLogDTO activityPartakeLog,String name) {
        //结束时间
        if (activityPartakeLog.getFissonEndTime().before(DateUtils.now())){
            //活动有效期已结束
            return promotionActivityService.checkFissionEndTime(activityPartakeLog,name);
        }
        return null;
    }

    private List<ActivityVerifyDO> getActivityVerify(Long activityId, String userId,String code) {
        QueryWrapper<ActivityVerifyDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ActivityVerifyDO::getActivityId,activityId);
        queryWrapper.lambda().eq(ActivityVerifyDO::getCode, code);
        queryWrapper.lambda().eq(ActivityVerifyDO::getUserId,userId);
        return activityVerifyDAO.list(queryWrapper);
    }

    /**
     * 获取能量值
     * @param limitDO
     * @return
     */
    private Integer getHelpNumSet(List<PromotionActivityLimitDO> limitDO, Integer paTemplateId){
        Integer helpNumSet = null;
        if (StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId().equals(String.valueOf(paTemplateId))) {
            List<BaseActivityDTO> numberList = ActivityConfigConverter.getLimitArray(limitDO, PATemplateBaseEnum.ASSIST.getId(), BaseActivityDTO.class);
            helpNumSet = LuckyDrawConverter.elLimitConverter(numberList, ActivityTemplateNumberEnum.HELP_NUM_SET.getId());
        }
        return helpNumSet;
    }

    /**
     * 助力获取数值定义
     * @param limitDO
     * @param paTemplateId
     * @return
     */
    private String getAssistNumDefinition(List<PromotionActivityLimitDO> limitDO, Integer paTemplateId){
        String assistNumDefinition = null;
        if (StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId().equals(String.valueOf(paTemplateId))) {
            List<BaseActivityDTO> numberList = ActivityConfigConverter.getLimitArray(limitDO, PATemplateBaseEnum.ASSIST.getId(), BaseActivityDTO.class);
            assistNumDefinition = LuckyDrawConverter.elLimitConverterToStr(numberList, ActivityTemplateNumberEnum.HELP_ASSIST_NUM_DEFINITION.getId());
        }
        return assistNumDefinition;
    }

    private  List<ActivityFissionLogResponseDTO> getActivityFissionLogResponseDTOList(List<ActivityFissionLogResponseDTO> activityFissionLogResponseDTOList,Integer helpNumSet,String assistNumDefinition){
        if(CollectionUtil.isNotEmpty(activityFissionLogResponseDTOList)){
            activityFissionLogResponseDTOList.forEach(item -> {
                item.setHelpNumSet(helpNumSet);
                item.setAssistNumDefinition(assistNumDefinition);
            });
        }
        return activityFissionLogResponseDTOList;
    }

    /**
     * 可领取奖品
     * @param ladderSortResourceList
     * @return
     */
    private List<ActivityFissionAssistResourceResponseDTO> getActivityFissionAssistResourceList(List<PromotionHisResourceDTO> ladderSortResourceList){
        List<ActivityFissionAssistResourceResponseDTO> list = Lists.newArrayList();
        if(CollectionUtil.isNotEmpty(ladderSortResourceList)){
                ladderSortResourceList.forEach(item ->{
                    ActivityFissionAssistResourceResponseDTO  activityFissionAssistResourceResponseDTO= item.clone(ActivityFissionAssistResourceResponseDTO.class);
                    activityFissionAssistResourceResponseDTO.setResourceId(item.getId());
                    ResourceHisDetailResponseDTO resourceHisDetailResponseDTO = new ResourceHisDetailResponseDTO();
                    resourceHisDetailResponseDTO.setFissonCount(item.getFissonCount());
                    activityFissionAssistResourceResponseDTO.setResourceHisDetailResponseDTO(resourceHisDetailResponseDTO);
                    list.add(activityFissionAssistResourceResponseDTO);
                });
        }
        return list;
    }

    /**
     * 当前已完成阶梯
     * @param ladderSortResourceList
     * @param currentFissonCount
     * @return
     */
    private Integer getFinishLadderSort(List<PromotionHisResourceDTO> ladderSortResourceList,Integer currentFissonCount){
        Map<Integer, PromotionHisResourceDTO> resourceDOMap = ladderSortResourceList.stream()
                .collect(Collectors.toMap(PromotionHisResourceDTO::getLadderSort, Function.identity()));
        int num = 0;
        int level = 0;
        for (int i = 1; i <= resourceDOMap.size(); i++) {
            PromotionHisResourceDTO promotionHisResourceDTO = resourceDOMap.get(i);
            num = num + promotionHisResourceDTO.getFissonCount();
            if(currentFissonCount >= num){
                level = i;
            }else {
                break;
            }
        }
        return level;
    }

    /**
     * 计算当前阶梯还剩下多少人可以完成
     * @param ladderSortResourceList
     * @param currentFissonCount
     * @return
     */
    private Integer getCurrentLadderSurplusNumber(List<PromotionHisResourceDTO> ladderSortResourceList,Integer currentFissonCount){
        Map<Integer, PromotionHisResourceDTO> resourceDOMap = ladderSortResourceList.stream()
                .collect(Collectors.toMap(PromotionHisResourceDTO::getLadderSort, Function.identity()));
        int num = 0;
        for (int i = 1; i <= resourceDOMap.size(); i++) {
            PromotionHisResourceDTO promotionHisResourceDTO = resourceDOMap.get(i);
            num = num + promotionHisResourceDTO.getFissonCount();//需要满足人数
            if(currentFissonCount < num){
                return num - currentFissonCount;//到当前阶阶所需要人数-已助力活动的人数
            }
        }
        return 0;
    }
    /**
     * 短信模板通知
     * 【华发股份】XXXX你好！恭喜你抽中“XXXXX”，核销码为(023uc)，使用规则请到小程序-我的礼品查看。
     * @param mobile 发送手机
     * @param userName 中奖人
     * @param coupon  优惠券名称
     * @param code    核销码
     * @param templateCode 短信模板编码
     */
    public void templateSend(String mobile, String userName, String coupon, String code, String templateCode) {
        log.info("准备发送短信模板通知参数，手机={},中奖人={},优惠券名称={},核销码={},模板={}",mobile,userName,coupon,code,templateCode);
        if (StringUtil.isNotBlank(mobile) && StringUtil.isNotBlank(userName) &&  StringUtil.isNotBlank(coupon) && StringUtil.isNotBlank(code)) {
            TemplateSendRequestDTO.Param param = new TemplateSendRequestDTO.Param();
            param.setUsername(userName + ",");
            param.setResource(coupon);
            param.setCode(code);

            TemplateSendRequestDTO requestDTO = new TemplateSendRequestDTO();
            requestDTO.setTemplateCode(templateCode);
            requestDTO.setMobile(mobile);
            requestDTO.setTemplateParam(JsonUtil.bean2JsonString(param));
            requestDTO.setCreateId(userName);
            interactionCenterService.templateSend(requestDTO);
        }
    }
}
