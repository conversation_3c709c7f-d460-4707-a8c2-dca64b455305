package com.deepexi.dxp.marketing.domain.promotion.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2024/12/1 8:54
 */
@Data
public class CreateSignCodeDTO {
    @ApiModelProperty("有交期，单位秒，-1表时不自动刷新")
    private int expired;
    @ApiModelProperty("签到码图片地址")
    private String url;

    public CreateSignCodeDTO(int expired, String signCodeUrl) {
        this.expired = expired;
        this.url = signCodeUrl;
    }
}
