//package com.deepexi.dxp.marketing.service.promotion.impl;
//
//import cn.hutool.core.map.MapUtil;
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.deepexi.commodity.dto.stock.ListActivityStockRequestDTO;
//import com.deepexi.commodity.dto.stock.ListActivityStockResponseDTO;
//import com.deepexi.dxp.marketing.domain.promotion.dto.opengroup.PromotionOpenGroupBaseResponseDTO;
//import com.deepexi.dxp.marketing.enums.status.PromotionOpenGroupStatusEnums;
//import com.deepexi.dxp.middle.promotion.dao.PromotionOpenGroupDAO;
//import com.deepexi.dxp.middle.promotion.domain.entity.PromotionOpenGroupDO;
//import com.deepexi.dxp.middle.promotion.mapper.PromotionOpenGroupMapper;
//import com.deepexi.dxp.marketing.remote.commodity.ActivityStockApi;
//import com.deepexi.dxp.marketing.service.promotion.OpenGroupTimingCloseService;
//import com.deepexi.util.CollectionUtil;
//import com.deepexi.util.config.Payload;
//import com.google.common.collect.Lists;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.exception.ExceptionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import org.springframework.util.ObjectUtils;
//
//import java.util.Date;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
///**
// * 拼团订单自动取消(活动过期、商品售罄时)
// *
// * <AUTHOR>
// * @date 2020/03/11 07:10
// */
//@Service
//@Slf4j
//public class OpenGroupTimingCloseServiceImpl implements OpenGroupTimingCloseService {
//
//
//    @Autowired
//    private PromotionOpenGroupDAO promotionOpenGroupDao;
//    @Autowired
//    private PromotionOpenGroupMapper promotionOpenGroupMapper;
//
//    @Autowired
//    private ActivityStockApi activityStockApi;
//
//    @Override
//    public List<PromotionOpenGroupBaseResponseDTO> timingCloseOvertime() {
//        log.info("定时器调用:定时关闭超时未成团数据");
//        QueryWrapper<PromotionOpenGroupDO> var1 = new QueryWrapper<>();
//        var1.in("status", PromotionOpenGroupStatusEnums.DCT.getId(), PromotionOpenGroupStatusEnums.XDSD.getId());
//        List<PromotionOpenGroupDO> list = promotionOpenGroupMapper.selectList(var1);
//        List<PromotionOpenGroupBaseResponseDTO> openGroupBaseDTOs = Lists.newArrayList();
//        if (!ObjectUtils.isEmpty(list)) {
//            List<PromotionOpenGroupDO> listUpdate = Lists.newArrayList();
//            //拼团活动对应的库存信息
//            List<ListActivityStockResponseDTO> listActivityStockResponseDTOList = getActivityStock(list);
//
//            for (PromotionOpenGroupDO openGroupDO : list) {
//                log.info("定时器调用:openGroupDO团活动信息：" + openGroupDO.toString());
//                log.info("定时器调用:openGroupDO团活动信息,开始时间：{},结束时间：{}", openGroupDO.getStartTime(), openGroupDO.getEndTime());
//                log.info("定时器调用:当前时间：" + System.currentTimeMillis());
//                log.info("定时器调用:团结束时间：" + openGroupDO.getEndTime().getTime());
//                log.info("定时器调用：判断->System.currentTimeMillis() >= openGroupDO.getEndTime().getTime() == " + (System.currentTimeMillis() >= openGroupDO.getEndTime().getTime()));
//                // 如果当前时间大于拼团结束时间，代表该团已超时
//                if (System.currentTimeMillis() >= openGroupDO.getEndTime().getTime() ||
//                        isHaveActivityStock(listActivityStockResponseDTOList, openGroupDO)) {
//                    log.info("定时器调用:团Id：" + openGroupDO.getId());
//                    openGroupDO.setStatus(PromotionOpenGroupStatusEnums.CSQX.getId());
//                    openGroupDO.setUpdatedTime(new Date());
//                    openGroupDO.setRemark(openGroupDO.getRemark() + ";定时器关闭时间：" + new Date());
//                    PromotionOpenGroupBaseResponseDTO baseResponseDTO = openGroupDO.clone(PromotionOpenGroupBaseResponseDTO.class);
//                    openGroupBaseDTOs.add(baseResponseDTO);
//                    listUpdate.add(openGroupDO);
//                }
//            }
//            if (!ObjectUtils.isEmpty(listUpdate)) {
//                promotionOpenGroupDao.updateBatchById(listUpdate);
//            }
//        }
//        return openGroupBaseDTOs;
//    }
//
//    //判断平团活动商品是否售罄
//    private boolean isHaveActivityStock(List<ListActivityStockResponseDTO> listActivityStockResponseDTOList ,PromotionOpenGroupDO openGroupDO){
//        boolean isHave = false;
//        if (CollectionUtil.isNotEmpty(listActivityStockResponseDTOList)){
//            for (ListActivityStockResponseDTO listActivityStockResponseDTO : listActivityStockResponseDTOList){
//                if (listActivityStockResponseDTO.getTenantId().equals(openGroupDO.getTenantId()) &&
//                        listActivityStockResponseDTO.getActivityId().equals(openGroupDO.getActivityId()) &&
//                            listActivityStockResponseDTO.getActivityStockQty() <= 0){
//                    isHave = true;
//                }
//            }
//        }
//        return isHave;
//    }
//
//    //查询出这些团对应的活动库存，校验是否已售罄
//    private List<ListActivityStockResponseDTO> getActivityStock(List<PromotionOpenGroupDO> list){
//        List<ListActivityStockResponseDTO> listActivityStockResponseDTOList = Lists.newArrayList();
//        Map<String, Long> returnMap = MapUtil.newHashMap();
//        //租户List
//        List<String> tenantIds = Lists.newArrayList();
//        list.stream().forEach(promotionOpenGroupDO -> {
//            tenantIds.add(promotionOpenGroupDO.getTenantId());
//        });
//        //去重复
//        tenantIds.stream().distinct().collect(Collectors.toList());
//        if (CollectionUtil.isNotEmpty(tenantIds)){
//            for (String tenantId : tenantIds){
//                List<Long> activityIds = Lists.newArrayList();
//                Long appId = 0L;
//                List<PromotionOpenGroupDO> listByTenantId = Lists.newArrayList();
//                list.stream().forEach(promotionOpenGroupDO -> {
//                    if (tenantId.equals(promotionOpenGroupDO.getTenantId())){
//                        activityIds.add(promotionOpenGroupDO.getActivityId());
//                        listByTenantId.add(promotionOpenGroupDO);
//                    }
//                });
//                //根据租户和活动id的集合来获取对应的活动库存信息
//                if (CollectionUtil.isNotEmpty(listByTenantId)){
//                    appId = listByTenantId.get(0).getAppId();
//                    ListActivityStockRequestDTO query = new ListActivityStockRequestDTO();
//                    query.setActivityIdList(activityIds);
//                    query.setTenantId(tenantId);
//                    query.setAppId(appId);
//                    Payload<List<ListActivityStockResponseDTO>> listPayload = activityStockApi.listActivityStocks(query);
//                    if (listPayload != null){
//                        List<ListActivityStockResponseDTO> listActivityStockResponseDTOTenantList = null;
//                        try {
//                            listActivityStockResponseDTOTenantList = listPayload.getPayload();
//                        } catch (Exception e) {
//                            log.error("json转换异常: {}", ExceptionUtils.getStackTrace(e));
//                        }
//                        listActivityStockResponseDTOList.addAll(listActivityStockResponseDTOTenantList);
//                    }
//                }
//            }
//        }
//        return listActivityStockResponseDTOList;
//    }
//
//}
