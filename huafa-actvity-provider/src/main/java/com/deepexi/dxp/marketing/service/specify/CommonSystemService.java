package com.deepexi.dxp.marketing.service.specify;

import cn.hutool.json.JSONObject;
import com.deepexi.dxp.marketing.domain.marketing.response.CommonOrgResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.CommonUserInfoResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.MenuPermissionDTO;

import java.util.List;

/**
 * 华发通用系统接口
 */
public interface CommonSystemService {
    /**
     * 用户组织查询
     * @param userId
     * @return
     */
    List<CommonOrgResponseDTO> getOrg(String userId);


    /**
     * 组织权限树列表
     * @param orgIds 组织机构id
     * @return
     */
    JSONObject getOrgTree(String orgIds);

    /**
     * 组织权限树列表
     * @param userId
     * @return
     */
    JSONObject getOrgTreeByUserAlias(String userId);


    /**
     * 获取用户信息
     * @param userId
     * @return
     */
    CommonUserInfoResponseDTO.CommonUserInfoDTO getUserInfo(String userId);

    /**
     * 无需登录获取用户信息
     * @param userId
     * @return
     */
    CommonUserInfoResponseDTO.CommonUserInfoDTO getUserInfoByUserId(String userId);

    /**
     * 用户所有菜单权限
     * @param userId
     * @param clientCode
     * @param menuId
     * @return
     */
    List<MenuPermissionDTO> getUserAllMenuPermission(String userId, String clientCode, String menuId);
}
