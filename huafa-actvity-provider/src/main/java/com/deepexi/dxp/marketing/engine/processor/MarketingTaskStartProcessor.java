//package com.deepexi.dxp.marketing.engine.processor;
//
//
//import com.deepexi.dxp.marketing.engine.MarketingTaskCallback;
//import com.deepexi.dxp.marketing.engine.event.MarketingTaskEvent;
//import com.deepexi.dxp.marketing.engine.listener.MarketingTaskListener;
//import com.deepexi.dxp.marketing.engine.task.MetaDataTask;
//import lombok.extern.slf4j.Slf4j;
//
//
///**
// * 营销任务开始处理
// *
// * <AUTHOR>
// * @date 2020/3/18 18:58
// */
//@Slf4j
//public class MarketingTaskStartProcessor extends AbstractTaskProcessor {
//
//    public MarketingTaskStartProcessor(MetaDataTask metaDataTask) {
//        super(metaDataTask);
//    }
//
//    public MarketingTaskStartProcessor(MetaDataTask metaDataTask, MarketingTaskCallback callback) {
//        super(metaDataTask, callback);
//    }
//
//    @Override
//    protected void doProcess(MarketingTaskListener listener, MarketingTaskEvent event) {
//        try {
//            log.info("task-start-start-营销任务开始执行开始");
//            listener.start(event);
//        } finally {
//            event.semaphoreDown();
//            log.info("task-start-end-营销任务开始执行结束");
//        }
//    }
//
//    @Override
//    public void callBack() {
//        System.out.println("invoker callBack.....");
//    }
//
//}
