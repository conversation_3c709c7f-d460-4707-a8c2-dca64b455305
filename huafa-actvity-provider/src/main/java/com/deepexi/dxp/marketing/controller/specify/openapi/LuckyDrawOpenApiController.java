package com.deepexi.dxp.marketing.controller.specify.openapi;

import com.cnhuafas.common.annotation.DistributedLock;
import com.deepexi.dxp.marketing.common.base.utils.CommonExceptionCode;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.LuckyDrawPartakeLogQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.PartakeLogQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.WinningRecordQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.*;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityPartakeLogResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.PrizeListAndSurplusNumberDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.WinningRecordResponseDTO;
import com.deepexi.dxp.marketing.service.specify.LuckyDrawService;
import com.deepexi.dxp.marketing.service.specify.PromotionActivityService;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

import static com.deepexi.dxp.marketing.constant.RedisConstants.CACHE_PREV_KEY_ACT_PARTAKE_LOCK;

/**
 * <AUTHOR>
 * @date  2021/05/24 17:54
 */
@RestController
@RequestMapping("/open-api/v1/open/luckyDraw/")
@Api(value = "参与抽奖活动接口",tags = {"参与抽奖活动接口"})
@Slf4j
public class LuckyDrawOpenApiController {

    @Resource
    private LuckyDrawService luckyDrawService;
    @Resource
    private PromotionActivityService promotionActivityService;
    @Resource
    private RedissonClient redissonClient;

    @PostMapping("/prizeList")
    @ApiOperation(value="抽奖移动端-抽奖首页信息", notes = "抽奖-奖品列表和剩余抽奖次数")
    public Data<PrizeListAndSurplusNumberDTO> prizeListAndSurplusNumber(@RequestBody LotteryRequestDTO query) throws ExecutionException {
        return new Data<>(luckyDrawService.prizeList(query));
    }

    @PostMapping("/receiveNow")
    @ApiOperation(value="立即领取", notes = "抽奖-立即领取")
    public Data<Boolean> receiveNow(@RequestBody @Valid ReceiveNowDTO dto) {
        //加锁
        RLock lock = redissonClient.getLock(CACHE_PREV_KEY_ACT_PARTAKE_LOCK + dto.getActivityId() + dto.getPhone());
        try {
            if (lock.tryLock(100, 10 * 1000, TimeUnit.MILLISECONDS)) {
                return new Data<>(luckyDrawService.receiveNow(dto));
            }
        } catch (IllegalArgumentException e) {
            throw e;
        } catch (ApplicationException e) {
            throw e;
        } catch (Exception e) {
            log.error("领取奖品失败", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        throw new ApplicationException(CommonExceptionCode.PARTICIPATED, "活动太繁忙，请稍后重试");
    }

    @PostMapping("/switchProject")
    @ApiOperation(value="切换项目", notes = "抽奖-切换项目")
    public Data<Boolean> switchProject(@RequestBody @Valid SwitchProjectDTO dto) {
        return new Data<>(luckyDrawService.switchProject(dto));
    }


    @PostMapping("/shareLuckyDraw")
    @ApiOperation(value="分享获得抽奖次数", notes = "抽奖-分享")
    public Data<Object> shareLuckyDraw(@RequestBody @Valid ShareLuckyDrawRequestDTO dto) {
        return new Data<>(promotionActivityService.share(dto));
    }

    @ApiOperation(value = "增加抽奖次数")
    @PostMapping("/addDrawNumber")
    @DistributedLock(suffix = "#dto.activityId + #dto.phone")
    public Data<Integer> addDrawNumber(@RequestBody @Valid AddDrawNumberDTO dto) {
        return new Data<>(luckyDrawService.addDrawNumber(dto));
    }

    @PostMapping("/winningRecord")
    @ApiOperation(value="中奖记录", notes = "抽奖-中奖记录")
    public Data<List<WinningRecordResponseDTO>> winningRecord(@RequestBody @Valid WinningRecordQuery query) {
        PartakeLogQuery partakeLogQuery = new PartakeLogQuery();
        partakeLogQuery.setActivityId(query.getActivityId());
        partakeLogQuery.setPage(Integer.parseInt(String.valueOf(query.getPage())));
        partakeLogQuery.setSize(Integer.parseInt(String.valueOf(query.getSize())));
        partakeLogQuery.setPrizeResultNot("谢谢参与");
        return new Data<>(luckyDrawService.winningRecord(partakeLogQuery));
    }

    @ApiOperation(value = "获取指定用户未领取奖品", notes = "获取指定用户未领取奖品")
    @PostMapping("/findCertificatesPage")
    public Data<PageBean<ActivityPartakeLogResponseDTO>> findCertificatesPage(@RequestBody LuckyDrawPartakeLogQuery query) {
        return new Data<>(luckyDrawService.findCertificatesList(query));
    }
}
