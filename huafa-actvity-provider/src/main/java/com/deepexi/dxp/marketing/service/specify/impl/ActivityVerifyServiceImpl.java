package com.deepexi.dxp.marketing.service.specify.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.deepexi.common.extension.AppRuntimeEnv;
import com.deepexi.dxp.marketing.common.base.utils.CommonExceptionCode;
import com.deepexi.dxp.marketing.constant.PayConstant;
import com.deepexi.dxp.marketing.constant.RedisConstants;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.MyVerifyQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.VerifyPageQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.*;
import com.deepexi.dxp.marketing.domain.marketing.response.*;
import com.deepexi.dxp.marketing.domain.promotion.dto.OrderDetailResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.VerifyOrderResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.query.specify.VerifyOrderPageQuery;
import com.deepexi.dxp.marketing.domain.specify.dto.ActivityFormFeedbackDTO;
import com.deepexi.dxp.marketing.enums.activity.strategy.DeliveryChannelEnum;
import com.deepexi.dxp.marketing.enums.activity.strategy.StrategyGroupEnum;
import com.deepexi.dxp.marketing.enums.dolphinscheduler.InstanceExecuteStatusEnum;
import com.deepexi.dxp.marketing.enums.specify.*;
import com.deepexi.dxp.marketing.extension.HuafaRuntimeEnv;
import com.deepexi.dxp.marketing.manager.promotion.PromotionActivityManager;
import com.deepexi.dxp.marketing.service.specify.*;
import com.deepexi.dxp.marketing.utils.*;
import com.deepexi.dxp.middle.promotion.common.base.SuperExtEntity;
import com.deepexi.dxp.middle.promotion.converter.specify.LuckyDrawConverter;
import com.deepexi.dxp.middle.promotion.dao.PromotionActivityDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.*;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.*;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.DateUtils;
import com.deepexi.util.StringUtil;
import com.deepexi.util.domain.entity.BaseEntity;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.deepexi.dxp.marketing.utils.MapBeanUtil.object2Map;

@Service
@Slf4j
public class ActivityVerifyServiceImpl implements ActivityVerifyService {

    @Autowired
    private ActivityVerifyDAO activityVerifyDAO;

    @Autowired
    private ActivityVerifyRejectLogDAO activityVerifyRejectLogDAO;

    @Resource
    public CustomerFeedbackDAO customerFeedbackDAO;

    @Autowired
    private WxPayService wxPayService;

    @Autowired
    private ActivityOrderDAO activityOrderDAO;

    @Autowired
    private GenerateIdUtil generateIdUtil;

    @Autowired
    private PromotionHisResourceDAO promotionHisResourceDAO;

    @Autowired
    private ActivityPartakeLogDAO activityPartakeLogDAO;

    @Autowired
    private PromotionActivityManager promotionActivityManager;

    @Autowired
    private ActivityParticipationDAO activityParticipationDAO;

    @Value("${deepexi.marketing.specify.pay-call-back-base-url}")
    private String callBackBaseUrl;

    @Resource
    public AsynchronousService asynchronousService;

    @Resource
    private PromotionActivityDAO promotionActivityDAO;

    @Resource
    private ActivityFissionAssistLogDAO activityFissionAssistLogDAO;

    @Resource
    private IncentiveService incentiveService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private PromotionActivityService promotionActivityService;

    @Autowired
    private CustomerFeedbackService customerFeedbackService;

    @Override
    public PageBean<ActivityVerifyInResponseDTO> findPage(VerifyPageQuery query) {
        List<Long> activityIds = promotionActivityService.getActivityIdsByUserId(StringUtils.isNotEmpty(query.getUserId()) ? query.getUserId() : HuafaRuntimeEnv.getUserId());
        if (CollectionUtil.isEmpty(activityIds)) {
            return new PageBean<ActivityVerifyInResponseDTO>();
        }
        query.setUserId(null);
        query.setActivityIdList(activityIds);

        if(StringUtil.isNotBlank(query.getProjectName())){
            query.setProjectNameList(Lists.newArrayList());
            String[] strL = query.getProjectName().split(",");
            for(int i=0;i<strL.length;i++){
                query.getProjectNameList().add(strL[i]);
            }
            query.setProjectName(null);
        }

        PageBean<ActivityVerifyInResponseDTO> page = activityVerifyDAO.findPage(query);
        List<ActivityVerifyInResponseDTO> content = page.getContent();
        if (CollectionUtil.isNotEmpty(content)) {
            List<Long> activityIdList = content.stream().map(ActivityVerifyInResponseDTO::getActivityId).distinct().collect(Collectors.toList());
            List<PromotionActivityDO> activityDOList = promotionActivityDAO.selectBatchIds(activityIdList);
            Map<Long, PromotionActivityDO> map = activityDOList.stream().collect(Collectors.toMap(PromotionActivityDO::getId, Function.identity()));

            content.forEach(item -> {
                PromotionActivityDO promotionActivityDO = map.get(item.getActivityId());
                PromotionHisResourceDO promotionHisResourceDO = promotionHisResourceDAO.getById(item.getResourceId());
                //优惠券活动、秒杀有resourcesAttribute属性
                if (StrategyGroupEnum.HF_COUPON_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())
                        || StrategyGroupEnum.HF_SECKILL_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())) {
                    Integer resourcesAttribute = Optional.ofNullable(promotionActivityDO.getExt().get("resourcesAttribute")).map(c -> Integer.parseInt(c.toString())).orElse(null);
                    item.setResourcesAttribute(resourcesAttribute);
                } else {
                    item.setResourcesAttribute(ResourcesAttributeEnum.APPOINT.getId());
                }
                if (Objects.nonNull(promotionHisResourceDO)) {
                    item.setResourceInfo(promotionHisResourceDO.clone(ActivityVerifyInResponseDTO.ResourceResponseVO.class));
                }
                item.setActivityName(promotionActivityDO.getName());

                //登记信息
                ActivityFormFeedbackDTO activityFormFeedbackDTO = customerFeedbackDAO.getFormFeedbackDTOByActivty(promotionActivityDO.getPaTemplateId(), item.getActivityId(), item.getPhone(), item.getResourceId());

                //处理表单登记信息
                if (promotionActivityDO.getPaTemplateId().toString().equals(StrategyGroupEnum.HF_FORM_ACT.getId())) {
                    activityFormFeedbackDTO.setLimits(customerFeedbackService.getFormLimits(activityFormFeedbackDTO.getLimits(), promotionActivityDO.getExt()));
                }

                item.setActivityFormFeedbackDTO(PhoneEncryUtils.feeBackDesensitization(activityFormFeedbackDTO));

                item.setPhone(StringUtil.isNotEmpty(item.getPhone()) ? PhoneEncryUtils.encode(item.getPhone()) : "");
            });
        }
        return page;
    }

    @Override
    public ActivityVerifyInResponseDTO getDetail(Long id) {
        ActivityVerifyDO activityVerifyDO = this.activityVerifyDAO.getById(id);
        if (Objects.nonNull(activityVerifyDO)) {
            ActivityVerifyInResponseDTO activityVerifyInResponseDTO = activityVerifyDO.clone(ActivityVerifyInResponseDTO.class);

            PromotionHisResourceDO promotionHisResourceDO = promotionHisResourceDAO.getById(activityVerifyInResponseDTO.getResourceId());
            if (Objects.nonNull(promotionHisResourceDO)) {
                ActivityVerifyInResponseDTO.ResourceResponseVO resourceResponseVO = promotionHisResourceDO.clone(ActivityVerifyInResponseDTO.ResourceResponseVO.class);
                activityVerifyInResponseDTO.setResourceInfo(resourceResponseVO);
            }

            PromotionActivityDO promotionActivityDO = promotionActivityDAO.getById(activityVerifyInResponseDTO.getActivityId());

            //优惠券活动、秒杀有resourcesAttribute属性
            if (StrategyGroupEnum.HF_COUPON_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())
                    || StrategyGroupEnum.HF_SECKILL_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())) {
                Integer resourcesAttribute = Optional.ofNullable(promotionActivityDO.getExt().get("resourcesAttribute")).map(c -> Integer.parseInt(c.toString())).orElse(null);
                activityVerifyInResponseDTO.setResourcesAttribute(resourcesAttribute);
            } else {
                activityVerifyInResponseDTO.setResourcesAttribute(ResourcesAttributeEnum.APPOINT.getId());
            }
            activityVerifyInResponseDTO.setActivityName(promotionActivityDO.getName());

            //登记信息
            ActivityFormFeedbackDTO activityFormFeedbackDTO = customerFeedbackDAO.getFormFeedbackDTOByActivty(promotionActivityDO.getPaTemplateId(), activityVerifyInResponseDTO.getActivityId(), activityVerifyInResponseDTO.getPhone(), activityVerifyInResponseDTO.getResourceId());

            //处理表单登记信息
            if (promotionActivityDO.getPaTemplateId().toString().equals(StrategyGroupEnum.HF_FORM_ACT.getId())) {
                activityFormFeedbackDTO.setLimits(customerFeedbackService.getFormLimits(activityFormFeedbackDTO.getLimits(), promotionActivityDO.getExt()));
            }

            activityVerifyInResponseDTO.setActivityFormFeedbackDTO(PhoneEncryUtils.feeBackDesensitization(activityFormFeedbackDTO));

            //手机号脱敏
            activityVerifyInResponseDTO.setPhone(StringUtil.isNotEmpty(activityVerifyInResponseDTO.getPhone()) ? PhoneEncryUtils.encode(activityVerifyInResponseDTO.getPhone()) : "");
            return activityVerifyInResponseDTO;
        }
        return null;
    }

    @Override
    public Boolean wiped(ActivityVerifyRequestDTO requestDTO) {
        ActivityVerifyDO activityVerifyDO = checkActivityVerify(requestDTO);

        //判断核销的优惠券是否过期 指定时间
        if (PromotionResourceValidTimeTypeEnum.DESIGNATED_TIME.getId().equals(activityVerifyDO.getValidTimeType())
                && activityVerifyDO.getValidEndTime().before(DateTime.now())) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "核销记录对应优惠券已过期不能核销!");
        }
        if (PromotionResourceValidTimeTypeEnum.DESIGNATED_TIME.getId().equals(activityVerifyDO.getValidTimeType())
                && Integer.valueOf(DateUtils.format(activityVerifyDO.getValidStartTime(), "yyyyMMdd")) > Integer.valueOf(DateUtils.format(DateTime.now(), "yyyyMMdd"))) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "还未到使用时间，无法核销！");
        }
        if (StringUtil.isEmpty(activityVerifyDO.getProjectId())) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "未指定核销项目，无法核销！");
        }
        if (PromotionResourceValidTimeTypeEnum.EFFECTIVE_DAYS.getId().equals(activityVerifyDO.getValidTimeType())) {
            DateTime now = DateTime.now();
            Calendar nowTime = Calendar.getInstance();
            nowTime.setTime(activityVerifyDO.getPayTime());//购买或领取时间
            nowTime.add(Calendar.DATE, activityVerifyDO.getValidDay());
            Date overTime = nowTime.getTime();
            if (overTime.before(now)) {
                throw new ApplicationException(CommonExceptionCode.INVALIDATION, "核销记录对应优惠券已过期不能核销!");
            }
        }


        return doVerify(requestDTO, activityVerifyDO);
    }

    @Override
    public boolean doVerify(ActivityVerifyRequestDTO requestDTO, ActivityVerifyDO activityVerifyDO) {
        activityVerifyDO.setVerifyStatus(VerifyStatusEnum.VERIFY_ED.getId());
        activityVerifyDO.setVerifyBy(requestDTO.getCreatedBy());
        activityVerifyDO.setVerifyTime(new Date());
        //更新  抽奖、秒杀、优惠券的用户参与记录表  activity_partake_log 核销时间、核销人

        //参与记录
        QueryWrapper<ActivityPartakeLogDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ActivityPartakeLogDO::getCode, activityVerifyDO.getCode());
        queryWrapper.lambda().eq(ActivityPartakeLogDO::getResourceId, activityVerifyDO.getResourceId());
        ActivityPartakeLogDO activityPartakeLogDO = activityPartakeLogDAO.getOne(queryWrapper);
        if (Objects.nonNull(activityPartakeLogDO)) {//助力有多个资源
            activityPartakeLogDO.setVerifyBy(requestDTO.getCreatedBy());
            activityPartakeLogDO.setVerifyTime(DateUtils.format(activityVerifyDO.getVerifyTime(), DateUtils.DEFAULT_DATE_TIME_FORMAT));
            activityPartakeLogDAO.updateById(activityPartakeLogDO);
        }

        return activityVerifyDAO.updateById(activityVerifyDO);
    }

    /**
     * 验证核销参数并返回核销记录
     *
     * @return
     */
    private ActivityVerifyDO checkActivityVerify(ActivityVerifyRequestDTO requestDTO) {
        ActivityVerifyDO activityVerifyDO = null;
        if (Objects.isNull(requestDTO)) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "核销参数不能为空!");
        }

        if (Integer.valueOf(1).equals(requestDTO.getType())) {//小程序端核销
            return miniCheck(requestDTO);
        } else {
            if (requestDTO.getId() == null) {
                throw new ApplicationException(CommonExceptionCode.INVALIDATION, "核销ID不能为空!");
            }
            activityVerifyDO = activityVerifyDAO.getById(requestDTO.getId());
        }
        if (Objects.isNull(activityVerifyDO)) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "未到使用有效期!");
        }
        if (VerifyStatusEnum.VERIFY_ED.getId().equals(activityVerifyDO.getVerifyStatus())) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "该记录已核销!");
        }
        if (!VerifyStatusEnum.NO_VERIFY.getId().equals(activityVerifyDO.getVerifyStatus())
                && !VerifyRefundStatusEnum.NO_REFUND.getId().equals(activityVerifyDO.getRefundStatus())) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "该条记录已退款!");
        }
        PromotionHisResourceDO promtionHisResourceDO = promotionHisResourceDAO.getById(activityVerifyDO.getResourceId());
        if (PromotionResourceTypeEnum.THIRD_PARTY.getId().equals(promtionHisResourceDO.getType())) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "第三方资源系统自动核销，请联系管理员!");
        }
        PromotionActivityDO activityDO = promotionActivityDAO.getById(activityVerifyDO.getActivityId());
        Integer resourcesAttribute = ResourcesAttributeEnum.APPOINT.getId();//0-不限项目（通用券）,1-指定项目(项目专属券),默认是指定项目
        if (StrategyGroupEnum.HF_COUPON_ACT.getId().equals(activityDO.getPaTemplateId().toString())
                || StrategyGroupEnum.HF_SECKILL_ACT.getId().equals(activityDO.getPaTemplateId().toString())
                || StrategyGroupEnum.HF_LUCKYDRAW_ACT.getId().equals(activityDO.getPaTemplateId().toString())) {
            resourcesAttribute = MapUtil.getInt(activityDO.getExt(), "resourcesAttribute", ResourcesAttributeEnum.UNLIMITED.getId());
        }
        if (ResourcesAttributeEnum.UNLIMITED.getId().equals(resourcesAttribute)) {//不限项目,只有优惠、秒杀活动有
            if (StringUtil.isEmpty(activityVerifyDO.getProjectId())) {
                List<ActivityParticipationDO> activityParticipationList = activityParticipationDAO.listByActivityId(activityVerifyDO.getActivityId());
                List<ActivityParticipationDO> resultList = Optional.ofNullable(activityParticipationList).map(x -> x.stream().filter(item -> item.getProjectId().equals(requestDTO.getProjectId())).collect(Collectors.toList()))
                        .orElseGet(Lists::newArrayList);
                if (CollectionUtil.isEmpty(resultList)) {
                    throw new ApplicationException(CommonExceptionCode.INVALIDATION, "项目信息未匹配不能核销!");
                }
                List<ActivityParticipationDO> filterList = activityParticipationList.stream().filter(m -> m.getProjectId().equals(requestDTO.getProjectId())).collect(Collectors.toList());
                activityVerifyDO.setProjectId(filterList.get(0).getProjectId());
                activityVerifyDO.setProjectName(filterList.get(0).getProjectName());
            }
        } else if (ResourcesAttributeEnum.APPOINT.getId().equals(resourcesAttribute)) {//指定项目--助力分享可能没有项目的
            if (StringUtil.isNotEmpty(activityVerifyDO.getProjectId()) && StringUtil.isNotEmpty(requestDTO.getProjectId()) && !requestDTO.getProjectId().equals(activityVerifyDO.getProjectId())) {
                throw new ApplicationException(CommonExceptionCode.INVALIDATION, "选择项目与核销项目未匹配不能核销!");
            }
            //查询活动下所有项目
            if (StringUtil.isNotEmpty(requestDTO.getProjectId())) {
                List<ActivityParticipationDO> activityParticipationList = activityParticipationDAO.listByActivityId(activityVerifyDO.getActivityId());
                boolean flag = activityParticipationList.stream().filter(m -> m.getProjectId().equals(requestDTO.getProjectId())).findAny().isPresent();
                if (!flag) {
                    throw new ApplicationException(CommonExceptionCode.INVALIDATION, "选择项目与核销项目未匹配不能核销");
                }
                if (StringUtil.isEmpty(activityVerifyDO.getProjectId())) {//核销记录中可能为空--助力分享可能没有项目的
                    List<ActivityParticipationDO> filterList = activityParticipationList.stream().filter(m -> m.getProjectId().equals(requestDTO.getProjectId())).collect(Collectors.toList());
                    activityVerifyDO.setProjectId(filterList.get(0).getProjectId());
                    activityVerifyDO.setProjectName(filterList.get(0).getProjectName());
                }
            }
        } else {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "未知的项目类型!");
        }
        return activityVerifyDO;
    }

    /**
     * 核销小程序较验
     * 核销小程序必须要有核销码、项目id
     *
     * @param requestDTO
     * @return
     */
    private ActivityVerifyDO miniCheck(ActivityVerifyRequestDTO requestDTO) {
        if (StringUtil.isEmpty(requestDTO.getCode()) || StringUtil.isEmpty(requestDTO.getProjectId())) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "核销码或项目信息不能为空!");
        }
        QueryWrapper<ActivityVerifyDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ActivityVerifyDO::getCode, requestDTO.getCode());
        ActivityVerifyDO activityVerifyDO = activityVerifyDAO.getOne(queryWrapper);
        if (Objects.isNull(activityVerifyDO)) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "未到使用有效期!");
        }

        PromotionHisResourceDO promtionHisResourceDO = promotionHisResourceDAO.getById(activityVerifyDO.getResourceId());
        if (PromotionResourceTypeEnum.THIRD_PARTY.getId().equals(promtionHisResourceDO.getType())) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "第三方资源系统自动核销，请联系管理员!");
        }

        if (VerifyStatusEnum.VERIFY_ED.getId().equals(activityVerifyDO.getVerifyStatus())) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "该记录已核销!");
        }
        if (!VerifyStatusEnum.NO_VERIFY.getId().equals(activityVerifyDO.getVerifyStatus())
                && !VerifyRefundStatusEnum.NO_REFUND.getId().equals(activityVerifyDO.getRefundStatus())) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "该条记录已退款!");
        }


        PromotionActivityDO activityDO = promotionActivityDAO.getById(activityVerifyDO.getActivityId());
        Integer resourcesAttribute = ResourcesAttributeEnum.APPOINT.getId();//0-不限项目（通用券）,1-指定项目(项目专属券),默认是指定项目
        if (StrategyGroupEnum.HF_COUPON_ACT.getId().equals(activityDO.getPaTemplateId().toString())
                || StrategyGroupEnum.HF_SECKILL_ACT.getId().equals(activityDO.getPaTemplateId().toString())
                || StrategyGroupEnum.HF_LUCKYDRAW_ACT.getId().equals(activityDO.getPaTemplateId().toString())) {
            resourcesAttribute = MapUtil.getInt(activityDO.getExt(), "resourcesAttribute", ResourcesAttributeEnum.UNLIMITED.getId());
        }
        if (ResourcesAttributeEnum.UNLIMITED.getId().equals(resourcesAttribute)) {//不限项目----没有项目
            List<ActivityParticipationDO> activityParticipationList = activityParticipationDAO.listByActivityId(activityVerifyDO.getActivityId());
            boolean flag = activityParticipationList.stream().filter(m -> m.getProjectId().equals(requestDTO.getProjectId())).findAny().isPresent();
            if (!flag) {
                throw new ApplicationException(CommonExceptionCode.INVALIDATION, "无该项目权限核销");
            }
            if (StringUtil.isEmpty(activityVerifyDO.getProjectId())) {
                List<ActivityParticipationDO> filterList = activityParticipationList.stream().filter(m -> m.getProjectId().equals(requestDTO.getProjectId())).collect(Collectors.toList());
                activityVerifyDO.setProjectId(filterList.get(0).getProjectId());
                activityVerifyDO.setProjectName(filterList.get(0).getProjectName());
            } else if (!activityVerifyDO.getProjectId().equals(requestDTO.getProjectId())) {
                throw new ApplicationException(CommonExceptionCode.INVALIDATION, "核销项目与券的适用项目不一致，请重新选择");
            }
        } else if (ResourcesAttributeEnum.APPOINT.getId().equals(resourcesAttribute)) {//指定项目--除助力分享可能没有项目其他都会有项目
            if (StringUtil.isNotEmpty(activityVerifyDO.getProjectId()) && StringUtil.isNotEmpty(requestDTO.getProjectId()) && !requestDTO.getProjectId().equals(activityVerifyDO.getProjectId())) {
                throw new ApplicationException(CommonExceptionCode.INVALIDATION, "选择项目与核销项目未匹配不能核销!");
            }
            //查询活动下所有项目,
            List<ActivityParticipationDO> activityParticipationList = activityParticipationDAO.listByActivityId(activityVerifyDO.getActivityId());
            boolean flag = activityParticipationList.stream().filter(m -> m.getProjectId().equals(requestDTO.getProjectId())).findAny().isPresent();
            if (!flag) {
                throw new ApplicationException(CommonExceptionCode.INVALIDATION, "无该项目权限核销");
            }

            if (StringUtil.isEmpty(activityVerifyDO.getProjectId())) {//核销记录中可能为空--即可能为助力分享
                List<ActivityParticipationDO> filterList = activityParticipationList.stream().filter(m -> m.getProjectId().equals(requestDTO.getProjectId())).collect(Collectors.toList());
                activityVerifyDO.setProjectId(filterList.get(0).getProjectId());
                activityVerifyDO.setProjectName(filterList.get(0).getProjectName());
            } else if (!activityVerifyDO.getProjectId().equals(requestDTO.getProjectId())) {
                throw new ApplicationException(CommonExceptionCode.INVALIDATION, "核销项目与券的适用项目不一致，请重新选择");
            }
        } else {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "未知的项目类型!");
        }
        return activityVerifyDO;
    }

    @Override
    public Boolean reject(ActivityVerifyRequestDTO requestDTO) {
        ActivityVerifyDO activityVerifyDO = activityVerifyDAO.getById(requestDTO.getId());
        if (Objects.isNull(activityVerifyDO)) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "未到使用有效期!");
        }
        if (!VerifyStatusEnum.VERIFY_ED.getId().equals(activityVerifyDO.getVerifyStatus())
                && !VerifyRefundStatusEnum.NO_REFUND.getId().equals(activityVerifyDO.getRefundStatus())) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "非已核销未退款不能驳回!");
        }
        //更新参与记录取清除核销信息
        QueryWrapper<ActivityPartakeLogDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ActivityPartakeLogDO::getCode, activityVerifyDO.getCode());
        queryWrapper.lambda().eq(ActivityPartakeLogDO::getResourceId, activityVerifyDO.getResourceId());
        ActivityPartakeLogDO activityPartakeLogDO = activityPartakeLogDAO.getOne(queryWrapper);
        if (Objects.nonNull(activityPartakeLogDO)) {
            activityPartakeLogDO.setVerifyBy(null);
            activityPartakeLogDO.setVerifyTime(null);
            activityPartakeLogDAO.updateById(activityPartakeLogDO);
        }
        PromotionActivityDO promotionActivityDO = promotionActivityDAO.getById(activityVerifyDO.getActivityId());
        //优惠券活动有resourcesAttribute属性
        Integer resourcesAttribute = Optional.ofNullable(promotionActivityDO.getExt().get("resourcesAttribute")).map(c -> Integer.parseInt(c.toString())).orElse(ResourcesAttributeEnum.APPOINT.getId());

        //还原之前的项目
        PromotionHisResourceDO promotionHisResource = promotionHisResourceDAO.getById(activityVerifyDO.getResourceId());
        if (PromotionResourceTypeEnum.COUPON.getId().equals(promotionHisResource.getType())) {//排除第三方资源
            this.reductionProjectInfo(activityVerifyDO, promotionActivityDO.getPaTemplateId());
        }
        activityVerifyDAO.updateRejectVerify(VerifyStatusEnum.NO_VERIFY.getId(), activityVerifyDO.getId(), requestDTO.getCreatedBy(), activityVerifyDO.getProjectId(), activityVerifyDO.getProjectName());

        //添加驳回记录
        ActivityVerifyRejectLogDO activityVerifyRejectLogDO = new ActivityVerifyRejectLogDO();
        activityVerifyRejectLogDO.setTenantId(activityVerifyDO.getTenantId());
        activityVerifyRejectLogDO.setAppId(activityVerifyDO.getAppId());
        activityVerifyRejectLogDO.setCreatedTime(new Date());
        activityVerifyRejectLogDO.setUpdatedTime(new Date());
        activityVerifyRejectLogDO.setRejectId(activityVerifyDO.getId());
        activityVerifyRejectLogDO.setCreatedBy(requestDTO.getCreatedBy());
        activityVerifyRejectLogDO.setUpdatedBy(requestDTO.getCreatedBy());
        activityVerifyRejectLogDO.setRemark(requestDTO.getRemark());
        activityVerifyRejectLogDAO.save(activityVerifyRejectLogDO);
        return Boolean.TRUE;
    }

    //还原项目
    private void reductionProjectInfo(ActivityVerifyDO activityVerifyDO, Integer paTemplateId) {
        //领券活动、秒杀、抽奖、砍价活动 从promotion_activity_partake_log取

        if (StrategyGroupEnum.HF_COUPON_ACT.getId().equals(paTemplateId.toString())
                || StrategyGroupEnum.HF_LUCKYDRAW_ACT.getId().equals(paTemplateId.toString())
                || StrategyGroupEnum.HF_SECKILL_ACT.getId().equals(paTemplateId.toString())) {
            ActivityPartakeLogDO activityPartakeLogDO = activityPartakeLogDAO.findByCode(activityVerifyDO.getCode());
            if (Objects.nonNull(activityPartakeLogDO)) {
                activityVerifyDO.setProjectId(activityPartakeLogDO.getProjectId());
                activityVerifyDO.setProjectName(activityPartakeLogDO.getProjectName());
            }
        } else if (StrategyGroupEnum.HF_FORM_ACT.getId().equals(paTemplateId.toString())) {
            ActivityFormFeedbackDO formFeedbackByPhone = customerFeedbackDAO.getFormFeedbackByPhone(activityVerifyDO.getActivityId(), activityVerifyDO.getPhone());
            if (Objects.nonNull(formFeedbackByPhone)) {
                activityVerifyDO.setProjectId(formFeedbackByPhone.getProjectId());
                activityVerifyDO.setProjectName(formFeedbackByPhone.getProjectName());
            }
        } else if (StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId().equals(paTemplateId.toString())) {
            ActivityFissionAssistLogDO activityFissionAssistLogDO = activityFissionAssistLogDAO.findByCode(activityVerifyDO.getCode());
            if (Objects.nonNull(activityFissionAssistLogDO)) {
                activityVerifyDO.setProjectId(activityFissionAssistLogDO.getProjectId());
                activityVerifyDO.setProjectName(activityFissionAssistLogDO.getProjectName());
            }
        } else if (StrategyGroupEnum.HF_CARD_COLLECTING_ACT.getId().equals(paTemplateId.toString())) {
            activityVerifyDO.setProjectId(null);
            activityVerifyDO.setProjectName(null);
        }

    }

    @Override
    public Boolean refunds(ActivityVerifyRequestDTO activityVerifyRequestDTO) {
        ActivityVerifyDO activityVerifyDO = null;
        if (activityVerifyRequestDTO.getId() != null) {
            activityVerifyDO = activityVerifyDAO.getById(activityVerifyRequestDTO.getId());
        } else if (StringUtil.isNotBlank(activityVerifyRequestDTO.getCode())) {
            activityVerifyDO = activityVerifyDAO.getByCode(activityVerifyRequestDTO.getCode());
        }
        if (Objects.isNull(activityVerifyDO)) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "未到使用有效期!");
        }
        if (activityVerifyDO.getIsPay() == 0) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "非付费核销记录不用进行退款!");
        }
        ActivityOrderDO activityOrderDO = activityOrderDAO.getById(activityVerifyDO.getOrderId());
        if (Objects.isNull(activityOrderDO)) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "订单不存在!");
        }

        //查询是否已存在处理中订单
        QueryWrapper<ActivityOrderDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ActivityOrderDO::getOldPayOrderNo, activityOrderDO.getWxOrderNo());
        queryWrapper.lambda().eq(ActivityOrderDO::getOrderType, OrderTypeEnum.REFUND.getId());
        List<Integer> statusList = new ArrayList<>();
        statusList.add(RefundsStatusEnum.PROCESSING.getId());
        statusList.add(RefundsStatusEnum.SUCCESS.getId());
        queryWrapper.lambda().in(ActivityOrderDO::getStatus, statusList);
        List<ActivityOrderDO> refundsList = activityOrderDAO.list(queryWrapper);
        if (CollectionUtil.isNotEmpty(refundsList) && refundsList.size() > 0) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "退款正在处理或者已处理，请稍后刷新!");
        }

        Long refundsOrderId = createRefundsOrder(activityOrderDO, activityVerifyRequestDTO);

        WxRefundsRequestDTO requestDTO = new WxRefundsRequestDTO();
        requestDTO.setBizNotifyUrl(callBackBaseUrl + PayConstant.REFUND_CALL_BACK_URL);
        requestDTO.setReason(StringUtil.isNotEmpty(activityVerifyRequestDTO.getRemark()) ? activityVerifyRequestDTO.getRemark() : "退款");
        requestDTO.setOrderNo(activityOrderDO.getWxOrderNo());
        WxRefundsResponseDTO refunds = wxPayService.refunds(requestDTO);

        if (refunds == null) {
            return Boolean.FALSE;
        }
        //退款成功 不会再有回调
        if ("200".equals(refunds.getCode())) {
            //退款处理
            boolean flag = refundsProcess(refunds.getData().getBizOrderNo(), refunds.getData().getOrderNo(), refunds.getData().getStatus(), DateTime.now());
            if ("PROCESSING".equals(refunds.getData().getStatus())) {
                flag = Boolean.TRUE;
            }

            return flag;
        } else if ("RESOURCE_NOT_EXISTS".equals(refunds.getCode())) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "下单订单不存在!");
        } else {
            ActivityOrderDO refundsOrder = activityOrderDAO.getById(refundsOrderId);
            Map<String, Object> ext = refundsOrder.getExt();
            if (ext == null) {
                refundsOrder.setExt(new HashMap<>());
            }
            refundsOrder.getExt().put("errorMsg", refunds.getMsg());
            refundsOrder.setStatus(RefundsStatusEnum.FAIL.getId());//处理失败
            activityOrderDAO.updateById(refundsOrder);
        }
        return Boolean.FALSE;
    }

    @Override
    public PageBean<ActivityVerifyInResponseDTO> myList(MyVerifyQuery query) {
        query.setUserId(null);
        VerifyPageQuery verifyPageQuery = query.clone(VerifyPageQuery.class);
        if (VerifyStatusEnum.INVALID.getId().equals(query.getVerifyStatus())) {//等于已失效状态
            verifyPageQuery.setVerifyStatus(null);
            verifyPageQuery.setInvalidType(1);
        }
        PageBean<ActivityVerifyInResponseDTO> page = activityVerifyDAO.findPage(verifyPageQuery);
        List<ActivityVerifyInResponseDTO> content = page.getContent();
        if (CollectionUtil.isNotEmpty(content)) {
            //活动信息
            List<Long> activityIdList = content.stream().map(ActivityVerifyInResponseDTO::getActivityId).distinct().collect(Collectors.toList());
            List<PromotionActivityDO> activityDOList = promotionActivityDAO.selectBatchIds(activityIdList);
            Map<Long, PromotionActivityDO> activityMap = activityDOList.stream().collect(Collectors.toMap(PromotionActivityDO::getId, Function.identity()));
            //资源信息
            List<Long> resourceIds = content.stream().map(ActivityVerifyInResponseDTO::getResourceId).distinct().collect(Collectors.toList());
            List<PromotionHisResourceDO> promotionHisResourceList = promotionHisResourceDAO.listByIds(resourceIds);
            Map<Long, PromotionHisResourceDO> hisResourceMap = promotionHisResourceList.stream().collect(Collectors.toMap(PromotionHisResourceDO::getId, Function.identity()));

            content.forEach(item -> {
                PromotionHisResourceDO promotionHisResourceDO = hisResourceMap.get(item.getResourceId());
                if (Objects.nonNull(promotionHisResourceDO)) {
                    ActivityVerifyInResponseDTO.ResourceResponseVO resourceResponseVO = promotionHisResourceDO.clone(ActivityVerifyInResponseDTO.ResourceResponseVO.class);
                    item.setResourceInfo(resourceResponseVO);
                } else {
                    item.setResourceInfo(new ActivityVerifyInResponseDTO.ResourceResponseVO());
                }

                PromotionActivityDO promotionActivityDO = activityMap.get(item.getActivityId());
                //信息登记
                ActivityFormFeedbackDTO activityFormFeedbackDTO = customerFeedbackDAO.getFormFeedbackDTOByActivty(promotionActivityDO.getPaTemplateId(), item.getActivityId(), item.getPhone(), item.getResourceId());

                if (promotionActivityDO.getPaTemplateId().toString().equals(StrategyGroupEnum.HF_FORM_ACT.getId())) {
                    activityFormFeedbackDTO.setLimits(customerFeedbackService.getFormLimits(activityFormFeedbackDTO.getLimits(), promotionActivityDO.getExt()));
                }

                item.setActivityFormFeedbackDTO(PhoneEncryUtils.feeBackDesensitization(activityFormFeedbackDTO));

                item.setPhone(StringUtil.isNotEmpty(item.getPhone()) ? PhoneEncryUtils.encode(item.getPhone()) : "");
            });
        }
        return page;
    }

    /**
     * 退款处理
     *
     * @param oldPayOrderNo 业务订单号
     * @param status        处理状态
     * @param refundTime    退款时间
     */
    @Override
    public Boolean refundsProcess(String oldPayOrderNo, String wxOrderNo, String status, Date refundTime) {
        String lockKey = RedisConstants.CACHE_PREV_KEY_REFUND_BACK_LOCK + wxOrderNo;
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (lock.tryLock(100, 10 * 1000, TimeUnit.MILLISECONDS)) {
                //支付订单
                QueryWrapper<ActivityOrderDO> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(ActivityOrderDO::getCode, oldPayOrderNo);
                queryWrapper.lambda().eq(ActivityOrderDO::getOrderType, OrderTypeEnum.PAY.getId());
                ActivityOrderDO payOrder = activityOrderDAO.getOne(queryWrapper);
                if (Objects.isNull(payOrder)) {
                    return false;
                }

                //退款订单
                queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda().eq(ActivityOrderDO::getOldPayOrderNo, payOrder.getWxOrderNo());
                queryWrapper.lambda().eq(ActivityOrderDO::getOrderType, OrderTypeEnum.REFUND.getId());
                queryWrapper.lambda().eq(ActivityOrderDO::getStatus, RefundsStatusEnum.PROCESSING.getId());
                ActivityOrderDO refundsOrder = activityOrderDAO.getOne(queryWrapper);

                if (Objects.isNull(refundsOrder)) {
                    return false;
                }

                if (InstanceExecuteStatusEnum.SUCCESS.getValue().equals(status)) {//订单处理成功
                    //通过订单id、资源id获取核销记录
                    QueryWrapper<ActivityVerifyDO> verifyWrapper = new QueryWrapper<>();
                    verifyWrapper.lambda().eq(ActivityVerifyDO::getOrderId, payOrder.getId());
                    verifyWrapper.lambda().eq(ActivityVerifyDO::getResourceId, payOrder.getResourceId());
                    ActivityVerifyDO activityVerifyDO = activityVerifyDAO.getOne(verifyWrapper);

                    //如果是待核销，则改成已失效
                    if (VerifyStatusEnum.NO_VERIFY.getId().equals(activityVerifyDO.getVerifyStatus())) {
                        activityVerifyDO.setVerifyStatus(VerifyStatusEnum.INVALID.getId());//已失效
                    }
                    activityVerifyDO.setRefundStatus(VerifyRefundStatusEnum.REFUNDED.getId());//已退款
                    activityVerifyDO.setRefundBy(StringUtil.isNotEmpty(refundsOrder.getCreatedBy()) ? refundsOrder.getCreatedBy() : "admin");//退款人
                    activityVerifyDO.setRefundTime(refundTime);

                    activityVerifyDAO.updateById(activityVerifyDO);

                    //除了签到退押金，其它则需要退回库存
                    if (refundsOrder.getExt() == null || !(Boolean) refundsOrder.getExt().getOrDefault("skipQtyReturn",false)) {
                        Long total  = Convert.toLong(payOrder.getExt().get("total"),1L);//默认参与人数为1

                        //扣减用户缓存领取或购买资源次数
                        promotionActivityManager.decrUserGetResourceCount(payOrder.getPhone(), payOrder.getId(), payOrder.getActivityId());
                        //库存返还（数据库库存和缓存库存）
                        promotionActivityManager.incrRemainQty(activityVerifyDO.getResourceId(), total);
                        promotionActivityManager.incrRedisQty(activityVerifyDO.getResourceId(), activityVerifyDO.getActivityId(),total.intValue());
                    }

                    refundsOrder.setStatus(RefundsStatusEnum.SUCCESS.getId());
                    refundsOrder.setPayTime(refundTime);//退款时间
                    refundsOrder.setWxOrderNo(wxOrderNo);
                    afterRefund(payOrder,refundsOrder);

                    //退款成功 神策埋点
                    asynchronousService.sensorsBuriedPoint(refundsOrder);
                } else if ("CLOSED".equals(status)) {//退款关闭
                    refundsOrder.setStatus(RefundsStatusEnum.CLOSED.getId());
                } else if ("ABNORMAL".equals(status)) {//退款异常
                    refundsOrder.setStatus(RefundsStatusEnum.ABNORMAL.getId());
                } else if ("PROCESSING".equals(status)) {
                    //设置原订单状态为处理中
                    payOrder.setStatus(OrderStatusEnum.PENDING.getId());
                    activityOrderDAO.updateById(payOrder);
                    return false;
                }
                activityOrderDAO.updateById(refundsOrder);
            }
        } catch (Exception e) {
            log.error("退款回调redis分布式锁异常,key:{},异常信息:{}", lockKey, e.getMessage());
            return Boolean.FALSE;
        } finally {
            if (lock.isHeldByCurrentThread()) {
                log.info("----------------------退款回调手动释放锁------------------");
                lock.unlock();
            }
        }
        return true;
    }

    /**
     * 成功退款事件处理
     * @param payOrder
     * @param refundsOrder
     */
    private void afterRefund(ActivityOrderDO payOrder, ActivityOrderDO refundsOrder) {
        //设置原订单状态为退款
        payOrder.setStatus(OrderStatusEnum.REFUND.getId());
        activityOrderDAO.updateById(payOrder);

        //如果是报名活动且不是退押金，删除活动参与记录
        PromotionActivityDO activityDO = promotionActivityDAO.getById(payOrder.getActivityId());
        if (StrategyGroupEnum.HF_SIGN_UP_ACT.getId().equals(activityDO.getPaTemplateId().toString())
            && (refundsOrder.getExt() == null || !(Boolean) refundsOrder.getExt().getOrDefault("skipQtyReturn",false))) {
            boolean re = activityPartakeLogDAO.lambdaUpdate().eq(ActivityPartakeLogDO::getActivityId, activityDO.getId())
                    .eq(ActivityPartakeLogDO::getPhone, payOrder.getPhone())
                    .eq(ActivityPartakeLogDO::getOrderId, payOrder.getId())
                    .set(ActivityPartakeLogDO::getDeleted, 1)
                    .set(SuperExtEntity::getUpdatedBy,refundsOrder.getCreatedBy())
                    .set(BaseEntity::getUpdatedTime, new Date())
                    .update();
            log.info("取消订单，删除用户参与记录结果：{}",re);
        }
    }

    @Override
    public String getQrCode(String code) {
        QueryWrapper<ActivityVerifyDO> verifyWrapper = new QueryWrapper<>();
        verifyWrapper.lambda().eq(ActivityVerifyDO::getCode, code);
        ActivityVerifyDO activityVerifyDO = activityVerifyDAO.getOne(verifyWrapper);
        if (Objects.isNull(activityVerifyDO)) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "未到使用有效期!");
        }
        PromotionActivityDO activityDO = promotionActivityDAO.getById(activityVerifyDO.getActivityId());
        Integer projectType = 0;//0全国项目,1选择项目
        if (Objects.nonNull(activityDO) && activityDO.getExt() != null) {
            projectType = Optional.ofNullable(activityDO.getExt().get("projectType")).map(c -> Integer.parseInt(c.toString())).orElse(projectType);
        }
        Integer resourcesAttribute = 1;
        if (Objects.nonNull(activityDO) && activityDO.getExt() != null) {
            resourcesAttribute = Optional.ofNullable(activityDO.getExt().get("resourcesAttribute")).map(c -> Integer.parseInt(c.toString())).orElse(resourcesAttribute);
        }
        Map<String, Object> map = Maps.newHashMap();
        map.put("projectType", projectType);
        map.put("projectId", activityVerifyDO.getProjectId());
        map.put("projectName", activityVerifyDO.getProjectName());
        map.put("code", code);
        map.put("resourcesAttribute", resourcesAttribute);//资源属性:0-通用券,1-项目专属券
        return QrCodeUtils.createQrCodeImg(JSON.toJSONString(map));
    }

    @Override
    public List<ActivityVerifyInResponseDTO> findOverList() {
       /* QueryWrapper<ActivityVerifyDO> verifyWrapper = new QueryWrapper<>();
        verifyWrapper.lambda().eq(ActivityVerifyDO::getValidTimeType,PromotionResourceValidTimeTypeEnum.DESIGNATED_TIME.getId());
        verifyWrapper.lambda().le(ActivityVerifyDO::getValidEndTime,DateTime.now());*/
        return this.activityVerifyDAO.findOverList();
    }

    @Override
    public void activityVerifyToOver(ActivityVerifyInResponseDTO activityVerifyDO) {
        this.activityVerifyDAO.updateOver(VerifyStatusEnum.EXPIRED.getId(), activityVerifyDO.getId());
    }

    @Override
    public Boolean sendIncentiveManual(Long verifyId) {
        ActivityVerifyDO byId = activityVerifyDAO.getById(verifyId);
        if (byId == null) {
            throw new ApplicationException("未到使用有效期！");
        }
        if (byId.getOrderId() == null) {
            throw new ApplicationException("订单id不存在！");
        }
        ActivityOrderDO activityOrderDO = activityOrderDAO.getById(byId.getOrderId());
        if (activityOrderDO == null) {
            throw new ApplicationException("订单不存在！");
        }
        try {
            PromotionActivityDO activityDO = promotionActivityDAO.getById(byId.getActivityId());
            PromotionHisResourceDO promotionHisResourceDO = promotionHisResourceDAO.getById(byId.getResourceId());
            ReceiveNowDTO receiveNow = new ReceiveNowDTO();
            receiveNow.setProjectId(byId.getProjectId());
            receiveNow.setProjectName(byId.getProjectName());
            receiveNow.setUserId(byId.getUserId());
            receiveNow.setUnionId(byId.getUnionId());
            receiveNow.setType(byId.getType());
            receiveNow.setActivityId(byId.getActivityId());

            String mpFrom = SendTemplateNewsRequestDTO.MP_FROM_ZYT;
            if (StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId().equals(String.valueOf(activityDO.getPaTemplateId())) ||
                    StrategyGroupEnum.HF_FISSION_REDUCEPRICE_ACT.getId().equals(String.valueOf(activityDO.getPaTemplateId()))) {
                mpFrom = SendTemplateNewsRequestDTO.MP_FROM_USH;
            }

            String postFormData = incentiveService.sendTransfer(LuckyDrawConverter.sendIncentiveConverter(receiveNow, activityDO.getName(), promotionHisResourceDO, null, mpFrom));
            IncentiveResponseDTO incentiveResponseDTO = JSONUtil.toBean(postFormData, IncentiveResponseDTO.class);
            if (!incentiveResponseDTO.getCode().equals(CommonExceptionCode.SUCCESS)) {
                throw new ApplicationException("发送激励失败：-----" + postFormData);
            }
            IncentiveResponseDTO.IncentiveResponseResult data = incentiveResponseDTO.getData();
            byId.setVerifyStatus(VerifyStatusEnum.VERIFY_ED.getId());
            activityVerifyDAO.updateById(byId);

            activityOrderDO.setStatus(OrderStatusEnum.PAYED.getId());
            if (data != null) {
                activityOrderDO.setExt(object2Map(data));
                activityOrderDO.setWxOrderNo(data.getOrderNo());
            }
            activityOrderDAO.updateById(activityOrderDO);
        } catch (Exception exception) {
            exception.printStackTrace();
            throw new ApplicationException("发送激励异常：-----" + exception.getMessage());
        }
        return true;
    }


    /**
     * 创建退款订单
     *
     * @param payOrder
     */
    private Long createRefundsOrder(ActivityOrderDO payOrder, ActivityVerifyRequestDTO activityVerifyRequestDTO) {
        ActivityOrderDO refundsActivityOrderDO = new ActivityOrderDO();
        DateTime now = DateTime.now();
        refundsActivityOrderDO.setPayMoney(payOrder.getPayMoney());
        refundsActivityOrderDO.setAppId(AppRuntimeEnv.getAppId());
        refundsActivityOrderDO.setTenantId(AppRuntimeEnv.getTenantId());
        refundsActivityOrderDO.setCreatedTime(now);
        refundsActivityOrderDO.setUpdatedTime(now);
        refundsActivityOrderDO.setCreatedBy(activityVerifyRequestDTO.getCreatedBy());
        refundsActivityOrderDO.setUpdatedBy(activityVerifyRequestDTO.getCreatedBy());
        refundsActivityOrderDO.setResourceId(payOrder.getResourceId());
        refundsActivityOrderDO.setActivityId(payOrder.getActivityId());
        refundsActivityOrderDO.setUserId(payOrder.getUserId());
        refundsActivityOrderDO.setStatus(RefundsStatusEnum.PROCESSING.getId());
        refundsActivityOrderDO.setCode(generateIdUtil.getOrderNo(GenerateTypeEnum.REFUNDS_TYPE));
        refundsActivityOrderDO.setUnionId(payOrder.getUnionId());
        refundsActivityOrderDO.setType(payOrder.getType());
        refundsActivityOrderDO.setOrderType(OrderTypeEnum.REFUND.getId());
        refundsActivityOrderDO.setOldPayOrderNo(payOrder.getWxOrderNo());
        refundsActivityOrderDO.setPhone(payOrder.getPhone());
        refundsActivityOrderDO.setRemark(activityVerifyRequestDTO.getRemark());
        refundsActivityOrderDO.setExt(activityVerifyRequestDTO.getExt());
        activityOrderDAO.save(refundsActivityOrderDO);
        return refundsActivityOrderDO.getId();
    }

    @Override
    public ExcelExportResponseDTO getExportVerifyExcelData(VerifyPageQuery query) {
        if(StringUtils.isBlank(query.getProjectName())){
            throw new ApplicationException("项目名称不能缺少");
        }

        query.setProjectNameList(Lists.newArrayList());
        String[] strL = query.getProjectName().split(",");
        for(int i=0;i<strL.length;i++){
            query.getProjectNameList().add(strL[i]);
        }

        if (query.getProjectNameList().size()>10) {
            throw new ApplicationException("最多只能选择10个项目");
        }

        query.setProjectName(null);
        query.setUserId(null);
        List<ActivityVerifyInResponseDTO> content = activityVerifyDAO.getExcelData(query);
        if (CollectionUtil.isEmpty(content)) {
            throw new ApplicationException("数据为空，无法导出");
        }

        ExcelExportResponseDTO responseDTO = null;
        try {
            GetExcelExportActVerifyUtil util = GetExcelExportActVerifyUtil.getInstance();
            responseDTO = util.exportVerifyInfo(content);

        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ApplicationException("导出失败");
        }
        return responseDTO;
    }

    @Override
    public ActivityVerifyDO getByOrderId(Long orderId) {
        return activityVerifyDAO.lambdaQuery().eq(ActivityVerifyDO::getOrderId, orderId).one();
    }

    @Override
    public PageBean<VerifyOrderResponseDTO> orderList(VerifyOrderPageQuery query) {
        Assert.notBlank(HuafaRuntimeEnv.getUserId(),"用户Id不能为空");
        List<Long> activityIds = null;
        if (DeliveryChannelEnum.SQ.getId().equals(query.getDeliveryChannel())) {
            activityIds = promotionActivityService.getActivityIdsByUserIdV2();
        } else {
            activityIds = promotionActivityService.getActivityIdsByUserId(HuafaRuntimeEnv.getUserId());
        }
        if (CollectionUtil.isEmpty(activityIds)) {
            return new PageBean<>();
        }
        query.setActivityIdList(activityIds);
        PageBean<VerifyOrderResponseDTO> orderList = activityVerifyDAO.getOrderList(query);
        List<VerifyOrderResponseDTO> content = orderList.getContent();
        if (CollUtil.isNotEmpty(content)) {
            List<Long> activityIdList = content.stream().filter(x -> ResourcesAttributeEnum.APPOINT.getId().equals(
                    x.getProjectType())).map(VerifyOrderResponseDTO::getActivityId).collect(Collectors.toList());
            Map<Long, Set<String>> areaNameMap = activityParticipationDAO.getAreaNamesByActivityIds(activityIdList);
            Map<Long, Set<String>> cityNameMap = activityParticipationDAO.getCityNamesByActivityIds(activityIdList);
            Map<Long, Set<String>> projectNameMap = activityParticipationDAO.getProjectNamesByActivityId(activityIdList);
            content.forEach(item -> {
                Map<String, Object> extMap = JSON.parseObject(item.getExtStr(), Map.class);
                item.setExtStr(null);
                item.setExt(extMap);
                item.setAreas(areaNameMap.containsKey(item.getActivityId()) ? areaNameMap.get(item.getActivityId()).stream().collect(Collectors.joining(",")) : "全国");
                item.setCities(cityNameMap.containsKey(item.getActivityId()) ? cityNameMap.get(item.getActivityId()).stream().collect(Collectors.joining(",")) : "全国");
                item.setProjects(projectNameMap.containsKey(item.getActivityId()) ? projectNameMap.get(item.getActivityId()).stream().collect(Collectors.joining(",")) : "全国");
            });
        }
        return orderList;
    }

    @Override
    public OrderDetailResponseDTO orderDetail(String code) {
        //订单信息
        ActivityOrderDO orderDO = activityOrderDAO.getByCode(code);
        Assert.notNull(orderDO,"订单不存在");
        PromotionActivityDO activityDO = promotionActivityDAO.getById(orderDO.getActivityId());
        //核销信息
        ActivityVerifyDO verifyDO = activityVerifyDAO.getByOrderId(orderDO.getActivityId(),orderDO.getId());
        ActivityOrderDO refundOrder = null;
        //如果订单状态是处理中或是已退款，则查询退款订单
        if (orderDO.getStatus().equals(OrderStatusEnum.PENDING.getId()) || orderDO.getStatus().equals(OrderStatusEnum.REFUND.getId())) {
            refundOrder = activityOrderDAO.getRefundOrderByPayOrderNo(orderDO.getWxOrderNo());
        }
        return new OrderDetailResponseDTO(verifyDO,orderDO,refundOrder,activityDO);
    }

}
