//package com.deepexi.dxp.marketing.engine.handler;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.deepexi.dxp.marketing.config.RocketMQConfig;
//import com.deepexi.dxp.marketing.engine.event.MarketingTaskEvent;
//import com.deepexi.dxp.marketing.engine.listener.MarketingTaskListener;
//import com.deepexi.dxp.marketing.engine.task.Task;
//import com.deepexi.dxp.marketing.engine.task.TaskManager;
//import com.deepexi.dxp.marketing.enums.redis.RedisPrefixEnum;
//import com.deepexi.dxp.marketing.enums.tipic.RocketMQTopicTagEnum;
//import com.deepexi.dxp.marketing.enums.tipic.RocketMqTopicEnum;
//import com.deepexi.dxp.marketing.utils.SpringContextHolder;
//import com.deepexi.dxp.middle.marketing.domain.dto.TaskSendDataDTO;
//import com.deepexi.redis.service.RedisService;
//import com.deepexi.util.exception.ApplicationException;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.commons.lang3.exception.ExceptionUtils;
//import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
//import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
//import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
//import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
//import org.apache.rocketmq.client.exception.MQClientException;
//import org.apache.rocketmq.client.producer.DefaultMQProducer;
//import org.apache.rocketmq.common.message.Message;
//import org.apache.rocketmq.common.message.MessageExt;
//
//import java.nio.charset.StandardCharsets;
//import java.util.List;
//import java.util.Objects;
//import java.util.UUID;
//
///**
// * 营销任务发送器，对于启动、暂停、终止事件监听器
// *
// * <AUTHOR>
// * @date 2020/3/18 19:16
// */
//@Slf4j
//public class SendHandler extends MarketingTaskListener implements MessageListenerConcurrently {
//
//    private DefaultMQPushConsumer consumer;
//
//    @Override
//    public void suspend(MarketingTaskEvent event) {
//        log.info("SendHandler【暂停】执行主动营销任务【Key：{0}】【开始】...", event.getMetaDataTask().getKey());
//
//        if (consumer != null) {
//            consumer.suspend();
//        }
//
//        log.info("SendHandler【暂停】执行主动营销任务【Key：{0}】【结束】...", event.getMetaDataTask().getKey());
//
//    }
//
//    @Override
//    public void goOn(MarketingTaskEvent event) {
//        try {
//            log.info("SendHandler【继续执行】主动营销任务【Key：{0}】【开始】...", event.getMetaDataTask().getKey());
//            if (Objects.isNull(consumer)) {
//                log.info("发送引擎发送器消费者不存在.");
//                throw new ApplicationException("发送引擎组装器消费者不存在.");
//            }
//            consumer.resume();
//            log.info("SendHandler【继续执行】主动营销任务【Key：{0}】【结束】...", event.getMetaDataTask().getKey());
//        } catch (Exception e) {
//            log.error("继续启动消费【SendConsumer】失败", e);
//            throw new ApplicationException("继续执行主动营销任务【Key：{0}】失败", e);
//        }
//    }
//
//    @Override
//    public void breakOff(MarketingTaskEvent event) {
//        end(event);
//    }
//
//    @Override
//    public void end(MarketingTaskEvent event) {
//        if (consumer != null) {
//            consumer.shutdown();
//        }
//    }
//
//    @Override
//    public void start(MarketingTaskEvent event) {
//        try {
//            String instanceName = StringUtils.join("App-", UUID.randomUUID().toString().replaceAll("-", ""));
//            String tag = StringUtils.join(RocketMQTopicTagEnum.MTA_MEMBER_FOR_SEND,  "_", event.getTopicTagName());
//
//            consumer = SpringContextHolder.getBean(RocketMQConfig.class)
//                    .taskActiveConsumerTemplate(RocketMqTopicEnum.APPLICATION_TOPIC.getName(), tag,
//                            this,
//                            MarketingTaskEvent.GROUP_META_SEND_PREFIX + event.getTopicTagName(),
//                            instanceName);
//            log.info("任务【id：{}】【消息发送器】开始执行，消费端已成功启动等待消费################", event.getMetaDataTask().getKey());
//        } catch (MQClientException e) {
//            log.error("error : {}", ExceptionUtils.getStackTrace(e));
//        }
//    }
//
//
//    @Override
//    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> messageExtList, ConsumeConcurrentlyContext context) {
//        {
//            TaskManager taskManager = SpringContextHolder.getBean(TaskManager.class);
//            String topic = RocketMqTopicEnum.APPLICATION_TOPIC.getName();
//            String tag = RocketMQTopicTagEnum.MTA_RESULT.getName();
//            DefaultMQProducer rocketProducerTemplate = SpringContextHolder.getBean(DefaultMQProducer.class);
//            RedisService redisService = SpringContextHolder.getBean(RedisService.class);
//            for (MessageExt messageExt : messageExtList) {
//                try {
//                    TaskSendDataDTO dataDto = JSONObject.parseObject(new String(messageExt.getBody(), StandardCharsets.UTF_8), TaskSendDataDTO.class);
//                    log.info("任务执行监听器接收数据:{}", dataDto);
//
//                    //会员数据
//                    if (null == dataDto.getMemberContent()) {
//                        log.info("任务执行监听器接收数据:会员数据为空{}", dataDto);
//                    }
//
//                    //任务数据
//                    if (null == dataDto.getTaskContent()) {
//                        log.info("任务执行监听器接收数据:任務数据为空{}", dataDto);
//                    }
//
//                    //查询消费记录
//                    String key = RedisPrefixEnum.TASK_RECORD.getKey() + ":" + dataDto.getTenantId() + "_" + dataDto.getTaskContent().getTaskType() + "_" + dataDto.getTaskContent().getTaskId();
//                    log.info("redis查询consumeRecord, key{}", key);
//                    boolean consumeRecord = redisService.sismember(key, dataDto.getMemberContent().getMemberId().toString());
//                    if (!consumeRecord) {
//                        TaskSendDataDTO.TaskContent taskContent = dataDto.getTaskContent();
//                        //查找任务
//                        Task task = taskManager.getTask(taskContent.getTaskTypeShortName());
//
//                        //执行任务
//                        boolean executeResult = task.execute(dataDto);
//
//                        //执行结果
//                        dataDto.getTaskContent().setExecuteStatus(executeResult);
//                        log.info("任务发送执行监听器执行结果:{},{}", executeResult, dataDto);
//
//                        //发送给结果处理器
//                        Message resultMessage = new Message(topic, tag, JSON.toJSONString(dataDto).getBytes(StandardCharsets.UTF_8));
//                        DefaultMQProducer producer = rocketProducerTemplate;
//                        producer.send(resultMessage);
//                    } else {
//                        log.info("mq消费记录已存在，不进行继续消费:{}", dataDto);
//                    }
//                } catch (Throwable t) {
//                    log.error("RocketMQ Consume Throwable", t);
//                }
//            }
//            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
//        }
//    }
//}
