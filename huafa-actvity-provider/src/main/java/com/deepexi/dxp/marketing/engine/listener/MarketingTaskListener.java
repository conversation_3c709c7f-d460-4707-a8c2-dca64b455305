//package com.deepexi.dxp.marketing.engine.listener;
//
//
//import com.deepexi.dxp.marketing.engine.event.MarketingTaskEvent;
//
//import java.util.concurrent.locks.Lock;
//import java.util.concurrent.locks.ReentrantLock;
//
///**
// * 营销任务事件监听器，比如营销任务规则组装器、发送器等
// *
// * <AUTHOR>
// * @date 2020/3/18 18:47
// */
//public abstract class MarketingTaskListener<E extends MarketingTaskEvent> implements AbstractListener<MarketingTaskEvent> {
//
//
//    /**
//     * 锁对象，用来防止多个地方同时调用监听器方法
//     */
//    protected Lock lock = new ReentrantLock();
//
//
//    /**
//     * 营销任务暂停执行监听事件
//     */
//    public void suspend(E event) {
//
//    }
//
//    /**
//     * 营销任务暂停后继续执行
//     */
//    public void goOn(E event) {
//    }
//
//    /**
//     * 营销任务终止执行监听事件
//     */
//    public void breakOff(E event) {
//    }
//
//    /**
//     * 营销任务启动执行监听事件
//     */
//    public void start(E event) {
//    }
//
//    /**
//     * 监听器生命周期结束时需要处理的内容
//     */
//    public void end(E event) {
//    }
//
//    @Override
//    public void onEvent(MarketingTaskEvent event) {
//    }
//
//}
