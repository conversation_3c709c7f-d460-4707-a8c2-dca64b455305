package com.deepexi.dxp.marketing.domain.promotion.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 审核活动入参
 * <AUTHOR>
 * @version 1.0
 * @date 2020-08-17 16:21
 */
@Data
public class ActivityAuditRequest {
    @NotNull
    private Long id;
    @NotNull
    @ApiModelProperty(value = "审核状态：1-通过，2-不通过")
    @Min(1)@Max(2)
    private Integer status ;
    @ApiModelProperty(value = "不通过原因")
    private String rejectReason;
}
