package com.deepexi.dxp.marketing.manager.promotion.impl.order;

import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityCommodityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityConfigDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.OrderEditResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.request.calculate.ActivityOrderParamsRequest;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityParamsDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityResponseParamsDTO;
import com.deepexi.dxp.marketing.enums.activity.result.NoActivityMsgEnum;
import com.deepexi.dxp.marketing.enums.activity.strategy.StrategyGroupEnum;
import com.deepexi.dxp.marketing.manager.promotion.ActivityCalculateManager;
import com.deepexi.dxp.marketing.manager.promotion.PromotionActivityOrderManager;
import com.deepexi.dxp.middle.promotion.converter.ActivityConfigConverter;
import com.deepexi.dxp.middle.promotion.converter.ActivityParamsConverter;
import com.deepexi.dxp.middle.promotion.converter.ActivityResponseParamsConverter;
import com.deepexi.dxp.middle.promotion.domain.entity.*;
import com.deepexi.dxp.middle.promotion.util.CollectionsUtil;
import com.deepexi.dxp.middle.promotion.util.SpringContextUtil;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.exception.ApplicationException;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR> xinjian.yao
 * @date 2019/12/14 11:42
 */
@Slf4j
public class PromotionOrtherActivityOrderManagerImpl implements PromotionActivityOrderManager {


    /**
     * 订单返回的参数
     */
    private OrderEditResponseDTO resultDTO;
    /**
     * 订单入参
     */
    private ActivityOrderParamsRequest activityRequestParams;

    /**
     * 用户过滤 日志dto
     */
    private List<PromotionCouponLoggerDO> promotionCouponLoggerDOList;

    /**
     * 优惠价 数据
     */
    private List<PromotionCouponDO> couponDoList;

    /**
     * 所有活动
     */
    private List<PromotionActivityDO> promotionActivityDOList;

    /**
     * 获取活动下的限制信息以活动分组
     */
    private Map<Long, List<PromotionActivityLimitDO>> activityLimitMap;

    /**
     * 获取活动下的策略信息并以活动分组
     */
    private Map<Long, List<PromotionStrategyDO>> activityStrategyMap;

    /**
     * 获取活动下的日志信息以活动分组
     */
    private Map<Long, List<PromotionActivityLoggerDO>> activityLoggerMap;
    /**
     * 活动计算接口
     */
    private ActivityCalculateManager activityCalculateManager;

    public PromotionOrtherActivityOrderManagerImpl(OrderEditResponseDTO resultDTO,
                                                   ActivityOrderParamsRequest activityRequestParams,
                                                   List<PromotionCouponLoggerDO> promotionCouponLoggerDOList,
                                                   List<PromotionCouponDO> couponDoList,
                                                   List<PromotionActivityDO> promotionActivityDOList,
                                                   Map<Long, List<PromotionActivityLimitDO>> activityLimitMap,
                                                   Map<Long, List<PromotionStrategyDO>> activityStrategyMap,
                                                   Map<Long, List<PromotionActivityLoggerDO>> activityLoggerMap) {
        this.resultDTO = resultDTO;
        this.activityRequestParams = activityRequestParams;
        this.promotionCouponLoggerDOList = promotionCouponLoggerDOList;
        this.couponDoList = couponDoList;
        this.promotionActivityDOList = promotionActivityDOList;
        this.activityLimitMap = activityLimitMap;
        this.activityStrategyMap = activityStrategyMap;
        this.activityLoggerMap = activityLoggerMap;
        this.activityCalculateManager = SpringContextUtil.getBean(ActivityCalculateManager.class);
    }

    /**
     * 构造活动配置信息
     *
     * @param promotionActivityDo 活动do
     * @param activityStrategyMap 活动策略
     * @param activityLimitMap    活动限制
     * @return
     */
    private ActivityConfigDTO getActivityConfig(PromotionActivityDO promotionActivityDo,
                                                Map<Long, List<PromotionStrategyDO>> activityStrategyMap,
                                                Map<Long, List<PromotionActivityLimitDO>> activityLimitMap) {

        Long activityId = promotionActivityDo.getId();
        List<PromotionStrategyDO> strategyList = activityStrategyMap.get(activityId);
        List<PromotionActivityLimitDO> limitList = activityLimitMap.get(activityId);


        return ActivityConfigConverter.converter(promotionActivityDo, strategyList, limitList);
    }

    /**
     * 活动需要的参数
     */
    private ActivityParamsDTO getActivityParams(ActivityCommodityDTO commodity, ActivityOrderParamsRequest activityRequestParams,
                                                PromotionActivityDO promotionActivityDo,
                                                Map<Long, List<PromotionActivityLoggerDO>> promotionActivityLoggerDoList) {
        List<PromotionActivityLoggerDO> loggerList = promotionActivityLoggerDoList.get(promotionActivityDo.getId());
        return ActivityParamsConverter.converter(commodity, activityRequestParams,   promotionActivityDo, loggerList);
    }

    private List<ActivityResponseParamsDTO> initResponseList(List<ActivityCommodityDTO> commoditiesList,
                                                             Map<String, List<PromotionActivityDO>> ckuActivityMap,
                                                             Map<Long, ActivityConfigDTO> activityConfigCacheMap) {
        List<ActivityResponseParamsDTO> resultList = new ArrayList<>();
        ckuActivityMap.forEach((skuCode, activityDoList) -> {
            ActivityCommodityDTO activityCommodityDTO = CollectionsUtil
                    .findOneInList(commoditiesList,
                            val -> val.getUpId().equals(Long.valueOf(skuCode)));
            if (null == activityCommodityDTO) {
                return;
            }
            // 商品对应的活动
            PromotionActivityDO promotionActivityDo = null;
            // 如果用户有指定活动 进行匹配
            if (Objects.nonNull(activityCommodityDTO.getActivityId())) {
                promotionActivityDo = CollectionsUtil
                        .findOneInList(activityDoList, val -> val.getId().equals(activityCommodityDTO.getActivityId()));
                // 判断改活动是否已经创建
                Long activityDoId = promotionActivityDo.getId();
                ActivityResponseParamsDTO activityResponseParamsDTO = CollectionsUtil
                        .findOneInList(resultList, val -> val.getActivityId().equals(activityDoId));
                if (null == activityResponseParamsDTO) {
                    ActivityResponseParamsDTO arp = ActivityResponseParamsConverter.converter(promotionActivityDo);
                    arp.getActivityCommodityDTOList().add(activityCommodityDTO);
                    resultList.add(arp);
                } else {
                    activityResponseParamsDTO.getActivityCommodityDTOList().add(activityCommodityDTO);
                }
                return ;
            }
           //  匹配不成功 给默认的
            throw  new ApplicationException("有商品通过不了活动");


        });
        return resultList;
    }


    /**
     * 其他活动的订单计算逻辑
     */
    @Override
    public OrderEditResponseDTO orderCalculate() {
        // 返回结果
        OrderEditResponseDTO resultDTO = new OrderEditResponseDTO();
        // 商品没有活动的记录
        List<ActivityCommodityDTO> notActivityCommodityDTO = new ArrayList<>();

        // 订单下的商品
        List<ActivityCommodityDTO> orderCommodityList = activityRequestParams.getActivityCommodityDTOList();

        // 一个商品可以用多少个活动  K -> skuCode value -> List<PromotionActivityDO>
        Map<String, List<PromotionActivityDO>> ckuActivityMap = new HashMap<>();
        // 缓存一个活动的配置类
        Map<Long, ActivityConfigDTO> activityConfigCache = new HashMap<>();

        // 遍历商品
        for (ActivityCommodityDTO commodity : orderCommodityList) {
            List<PromotionActivityDO> activityFlagList = new ArrayList<>();
            for (PromotionActivityDO promotionActivityDo : promotionActivityDOList) {
                ActivityConfigDTO activityConfig = getActivityConfig(promotionActivityDo, activityStrategyMap, activityLimitMap);

                ActivityParamsDTO params = getActivityParams(commodity, activityRequestParams, promotionActivityDo, activityLoggerMap);
                activityConfig.setParams(params);
                activityConfigCache.put(promotionActivityDo.getId(), activityConfig);
                Boolean result = activityCalculateManager.limitCalculate(activityConfig, params);
                if (!result) {
                    // 商品不能通过活动的限制
                    log.error("下单计算-商品不能通过活动限制:skuId{}, upId{} , activityId{} userId{}", commodity.getSkuId(), commodity.getUpId(), promotionActivityDo.getId(), activityRequestParams.getUserId());
                    continue;
                }
                activityFlagList.add(promotionActivityDo);
            }
            if (activityFlagList.isEmpty()) {
                // 如果没有活动可以用 就加到没有活动的商品列表里面
                if (Objects.nonNull(commodity.getActivityId())) {
                    commodity.setNoActivityCode(NoActivityMsgEnum.LIMIT_ERROR.getId());
                    commodity.setNoActivityMsg(NoActivityMsgEnum.LIMIT_ERROR.getMsg());
                }
                notActivityCommodityDTO.add(commodity);
                continue;
            }
            if (Objects.nonNull(commodity.getActivityId())) {
                //商品指定活动是否在商品可参与活动列表中
                PromotionActivityDO promotionActivityDo = CollectionsUtil.findOneInList(activityFlagList, val -> val.getId().equals(commodity.getActivityId()));
                if (promotionActivityDo != null) {
                    ckuActivityMap.put(commodity.getUpId().toString(), activityFlagList);
                    continue;
                }
                commodity.setNoActivityCode(NoActivityMsgEnum.NO_MATCH.getId());
                commodity.setNoActivityMsg(NoActivityMsgEnum.NO_MATCH.getMsg());
            }
            notActivityCommodityDTO.add(commodity);
        }
        // 构造多个活动计算的入参数
        List<ActivityResponseParamsDTO> activityResponseParamsDTOList = initResponseList(orderCommodityList, ckuActivityMap, activityConfigCache);

        List<ActivityResponseParamsDTO> deletedFlags = new ArrayList<>();
        // 活动策略计算
        for (ActivityResponseParamsDTO activityResponseParamsDTO : activityResponseParamsDTOList) {
            ActivityConfigDTO activityConfigDTO = activityConfigCache.get(activityResponseParamsDTO.getActivityId());
            activityCalculateManager.strategyCalculate(activityConfigDTO, activityResponseParamsDTO);


            if(!activityResponseParamsDTO.getPaTemplateId().equals(StrategyGroupEnum.ZJRX_G.getId())){
                continue;
            }
            List<ActivityCommodityDTO> deletedFlag = new ArrayList<>();
            activityResponseParamsDTO.getActivityCommodityDTOList().forEach(val->{
                if(val.getSubtractPriceAll()==null||val.getSubtractPriceAll().equals(BigDecimal.ZERO)){
                    notActivityCommodityDTO.add(val);
                    deletedFlag.add(val);
                }
            });



            activityResponseParamsDTO.getActivityCommodityDTOList().removeAll(deletedFlag);
            if(CollectionUtil.isEmpty(activityResponseParamsDTO.getActivityCommodityDTOList())){
                deletedFlags.add(activityResponseParamsDTO);
            }
        }

        activityResponseParamsDTOList.removeAll(deletedFlags);

        resultDTO.setActivityList(activityResponseParamsDTOList);
        resultDTO.setNoActivityCommodityDTOList(notActivityCommodityDTO);

        activityCalculateManager.orderMoneyNoCoupon(resultDTO);

        // 优惠券处理
        activityCalculateManager.orderCouponMoneyDealWith(couponDoList,
                promotionCouponLoggerDOList,
                resultDTO,
                activityConfigCache);

        // 有活动下的商品优惠信息汇总
        activityCalculateManager.orderActivityCommodityMoneyGroup(resultDTO);

        // 没有活动和有获得的商品最终汇总
        activityCalculateManager.orderMoneyGroup(resultDTO);


        return resultDTO;
    }
}
