package com.deepexi.dxp.marketing.controller.specify.adminapi;

import com.deepexi.dxp.marketing.api.specify.MarketingKpiRouteMapOpenApi;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.MarketingKpiRouteMapQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.MarketingKpiCheckNameRequest;
import com.deepexi.dxp.marketing.domain.marketing.response.MarketingKpiRouteMapVO;
import com.deepexi.dxp.marketing.service.specify.MarketingKpiRouteMapService;
import com.deepexi.dxp.middle.marketing.domain.dto.MarketingKpiRouteMapDTO;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import com.deepexi.util.pojo.CloneDirection;
import com.deepexi.util.pojo.ObjectCloneUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 指标路径图Controller
 *
 * @Author: HuangBo.
 * @Date: 2020/6/18 15:48
 */

@RestController
@RequestMapping("/admin-api/v1/kpi/route/map")
@Slf4j
@Api(description = "指标管理-路径图", tags = "MarketingKpiRouteMapSdk")
public class MarketingKpiRouteMapOpenApiController implements MarketingKpiRouteMapOpenApi {

    @Autowired
    private MarketingKpiRouteMapService routeMapService;


    @PostMapping("/save")
    @ApiOperation(value = "指标管理-路径图-新增修改", notes = "指标管理-路径图—新增修改", nickname = "marketingKpiRouteMapSave")
    public Data<MarketingKpiRouteMapVO> save(@RequestBody @Valid MarketingKpiRouteMapVO routeMapVO) {
        try {
            MarketingKpiRouteMapDTO routeMapDTO = routeMapVO.clone(MarketingKpiRouteMapDTO.class, CloneDirection.OPPOSITE);
            boolean res = routeMapService.save(routeMapDTO);
            return new Data<>(routeMapDTO.clone(MarketingKpiRouteMapVO.class, CloneDirection.OPPOSITE));
        } catch (Exception e) {
            log.error("【保存指标路径图】发生错误,{}", ExceptionUtils.getStackTrace(e));
            if (e instanceof ApplicationException) {
                throw e;
            }
            return new Data<>("10001", "保存失败.");
        }
    }

    @PostMapping("/query-list")
    @ApiOperation(value = "指标管理-路径图—列表查询", notes = "指标管理-路径图—列表查询", nickname = "marketingKpiRouteMapQueryList")
    public Data<List<MarketingKpiRouteMapVO>> findList(@RequestBody @Valid MarketingKpiRouteMapQuery query) {
        try {
            return new Data<>(ObjectCloneUtils.convertList(routeMapService.queryList(query), MarketingKpiRouteMapVO.class, CloneDirection.OPPOSITE));
        } catch (Exception e) {
            log.error("【路径图-根据条件查询所有数据】发生错误,{}", ExceptionUtils.getStackTrace(e));
            if (e instanceof ApplicationException) {
                throw e;
            }
            return new Data<>("10001", "查询失败.");
        }
    }

    @GetMapping("/query-page-list")
    @ApiOperation(value = "指标管理-路径图—分页查询", notes = "指标管理-路径图—分页查询", nickname = "marketingKpiRouteMapQueryPageList")
    public Data<PageBean<MarketingKpiRouteMapVO>> pageList(@Valid MarketingKpiRouteMapQuery query) {
        try {
            PageBean<MarketingKpiRouteMapDTO> routeMapDTOPageBean = routeMapService.pageList(query);
            PageBean<MarketingKpiRouteMapVO> result = ObjectCloneUtils.convertPageBean(
                    routeMapDTOPageBean, MarketingKpiRouteMapVO.class, CloneDirection.OPPOSITE);
            return new Data<>(result);
        } catch (Exception e) {
            log.error("【路径图分页查询】发生错误,{}", ExceptionUtils.getStackTrace(e));
            if (e instanceof ApplicationException) {
                throw e;
            }
            return new Data<>("10001", "查询失败.");
        }
    }

    @PostMapping("/query-page-list-post")
    @ApiOperation(value = "指标管理-路径图—分页查询", notes = "指标管理-路径图—分页查询", nickname = "marketingKpiRouteMapQueryPageList")
    public Data<PageBean<MarketingKpiRouteMapVO>> pageListPost(@RequestBody @Valid MarketingKpiRouteMapQuery query) {
        try {
            PageBean<MarketingKpiRouteMapDTO> routeMapDTOPageBean = routeMapService.pageList(query);
            PageBean<MarketingKpiRouteMapVO> result = ObjectCloneUtils.convertPageBean(
                    routeMapDTOPageBean, MarketingKpiRouteMapVO.class, CloneDirection.OPPOSITE);
            return new Data<>(result);
        } catch (Exception e) {
            log.error("【路径图分页查询】发生错误,{}", ExceptionUtils.getStackTrace(e));
            if (e instanceof ApplicationException) {
                throw e;
            }
            return new Data<>("10001", "查询失败.");
        }
    }

    @GetMapping("/detail")
    @ApiOperation(value = "指标管理-路径图—详情", notes = "路径图—详情", nickname = "marketingKpiRouteMapDetail")
    public Data<MarketingKpiRouteMapVO> detail(@RequestParam Long id) {
        try {
            MarketingKpiRouteMapDTO routeMapDTO = routeMapService.queryById(id);
            return new Data<>(routeMapDTO.clone(MarketingKpiRouteMapVO.class, CloneDirection.OPPOSITE));
        } catch (Exception e) {
            log.error("【通过ID查询路径图】发生错误,{}", ExceptionUtils.getStackTrace(e));
            if (e instanceof ApplicationException) {
                throw e;
            }
            return new Data<>("10001", "查询失败.");
        }
    }


    @PutMapping("/process/status")
    @ApiOperation(value = "指标管理-路径图—启用禁用", notes = "指标管理-路径图—启用禁用", nickname = "marketingKpiRouteMapProcessStatus")
    public Data<Boolean> processStatus(@RequestParam Integer status, @RequestParam Long id) {
        try {
            return new Data<Boolean>(routeMapService.processStatus(id, status));
        } catch (Exception e) {
            log.error("【禁用/启用路径图】发生错误,{}", ExceptionUtils.getStackTrace(e));
            if (e instanceof ApplicationException) {
                throw e;
            }
            return new Data<>("10001", "操作路径图状态失败.");
        }
    }

    @GetMapping("/check/name")
    @ApiOperation(value = "指标管理-路径图—同名检查", notes = "指标管理-路径图—同名检查", nickname = "marketingKpiRouteMapCheckName")
    public Data<Boolean> checkName(MarketingKpiCheckNameRequest request) {
        return new Data<Boolean>(routeMapService.existByName(null, request.getName(), request.getOldName()));
    }

    @PostMapping("/check/namePost")
    @ApiOperation(value = "指标管理-路径图—同名检查", notes = "指标管理-路径图—同名检查", nickname = "marketingKpiRouteMapCheckName")
    public Data<Boolean> checkNamePost(@RequestBody MarketingKpiCheckNameRequest request) {
        return new Data<Boolean>(routeMapService.existByName(null, request.getName(), request.getOldName()));
    }

    @Override
    @DeleteMapping("/delete")
    public Data<Boolean> deleteByRoute(@RequestParam Long id) {
        return new Data<>(routeMapService.deleteByRoute(id));
    }


}
