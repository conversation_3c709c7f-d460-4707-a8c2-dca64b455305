package com.deepexi.dxp.marketing.service.promotion;

import com.deepexi.dxp.marketing.domain.promotion.dto.activity.PromotionActivityLimitFindVO;
import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityLimitQuery;

import java.util.List;

/**
 * <AUTHOR> xinjian.yao
 * @date 2020/3/11 15:58
 */
public interface PromotionActivityLimitMiddleService {

    /**
     * 活动限制分页查询
     * @param query 查询条件
     * @return PromotionActivityLimitFindVO 查询结果
     */
    List<PromotionActivityLimitFindVO> findPage(PromotionActivityLimitQuery query);
}
