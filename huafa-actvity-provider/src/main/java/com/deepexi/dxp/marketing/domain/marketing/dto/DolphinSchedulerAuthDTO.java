package com.deepexi.dxp.marketing.domain.marketing.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @Class: DolphinSchedulerAuthDTO
 * @Description: 存放登录后的权限数据
 * @Author: lizhongbao
 * @Date: 2020/3/24
 **/
@Data
public class DolphinSchedulerAuthDTO implements Serializable {

    /**
     * sessionId
     */
    private String sessionId;

    /**
     * DolphinScheduler项目
     */
    private String projectName;

    /**
     * DolphinScheduler访问路径
     */
    private String path;

    /**
     * DolphinScheduler租户ID
     */
    private String dolphinSchedulerTenantId;

}
