package com.deepexi.dxp.marketing.service.specify;

import com.deepexi.dxp.marketing.domain.promotion.dto.specify.PromotionResourceResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.query.specify.PromotionResourceQuery;
import com.deepexi.dxp.marketing.domain.promotion.request.specify.PromotionResourceRequestDTO;
import com.deepexi.util.pageHelper.PageBean;

/**
 * 资源信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-04-21 18:15:11
 */
public interface PromotionResourceService {
    /**
     * 分页
     * @param query
     * @return
     */
    PageBean<PromotionResourceResponseDTO> findPage(PromotionResourceQuery query);

    /**
     * 分页
     * @param query
     * @return
     */
    PageBean<PromotionResourceResponseDTO> noOverList(PromotionResourceQuery query);

    /**
     * 新增修改
     * @param dto
     * @return
     */
    boolean createOrUpdate(PromotionResourceRequestDTO dto);

    /**
     * 删除
     * @param id
     * @return
     */
    boolean deleteById(Long id);

    /**
     * 详情
     * @param id
     * @return
     */
    PromotionResourceResponseDTO detail(Long id);
}

