//package com.deepexi.dxp.marketing.engine.processor;
//
//
//import com.deepexi.dxp.marketing.engine.MarketingTaskCallback;
//import com.deepexi.dxp.marketing.engine.event.MarketingTaskEvent;
//import com.deepexi.dxp.marketing.engine.listener.MarketingTaskListener;
//import com.deepexi.dxp.marketing.engine.listener.MarketingTaskListenerFactory;
//import com.deepexi.dxp.marketing.engine.task.MetaDataTask;
//import com.deepexi.util.CollectionUtil;
//import com.deepexi.util.exception.ApplicationException;
//import com.google.common.collect.Sets;
//import lombok.extern.slf4j.Slf4j;
//
//import java.util.Set;
//import java.util.concurrent.CountDownLatch;
//
///**
// * 营销任务发送时暂停、终止处理对象
// *
// * <AUTHOR>
// * @date 2020/3/18 17:50
// */
//@Slf4j
//public abstract class AbstractTaskProcessor{
//
//    protected Set<MarketingTaskListener> listeners = Sets.newLinkedHashSet();
//    protected CountDownLatch semaphore = null;
//    private final MetaDataTask metaDataTask;
//    private MarketingTaskCallback callback;
//
//    public AbstractTaskProcessor(MetaDataTask metaDataTask) {
//        this.metaDataTask = metaDataTask;
//        String key = metaDataTask.getKey();
//
//        // TODO 这里需要考虑应用重启后，各任务的处理器都不存在后的问题。
//        //      比如，任务是暂停状态，若应用重启后需要再次启动，此时任务处理器对象已不存在，
//        //      再次启动时则需要再次对任务组件进行初始化，并继续处理。
//        //      那么，重启应用可以有两种方案：
//        //      1、销毁所有处于暂停状态的任务各处理器，将这些任务标记为“特殊暂停状态，
//        //          即运行中状态”待重启后重新重新再次执行全部发送，之前发送成功的人由发送器进行重复发送校验。
//        //      2、记录各消费者、生产者的偏移量，再次发送时从偏移量处进行处理，那么又会遇到再次发送时圈的人与暂停前不一致。
//
//        if (MarketingTaskListenerFactory.existKey(key)) {
//            this.listeners.add(MarketingTaskListenerFactory.getEsHandlerListener(key));
//            this.listeners.add(MarketingTaskListenerFactory.getMetaDataHandlerListener(key));
//            this.listeners.add(MarketingTaskListenerFactory.getMarketingTaskMQListener(key));
//            this.listeners.add(MarketingTaskListenerFactory.getMarketingTaskSendListener(key));
//            this.listeners.add(MarketingTaskListenerFactory.getMarketingRedisListener(key));
//        }
//        if (CollectionUtil.isNotEmpty(listeners)) {
//            semaphore = new CountDownLatch(listeners.size());
//        }
//    }
//
//    public AbstractTaskProcessor(MetaDataTask metaDataTask, MarketingTaskCallback callback) {
//        this(metaDataTask);
//        this.callback = callback;
//    }
//
//    /**
//     * 子类具体处理方法
//     *
//     * @param listener
//     */
//    protected abstract void doProcess(MarketingTaskListener listener, MarketingTaskEvent event);
//
//    /**
//     * 营销任务发送时具体暂停、终止、继续发送处理方法
//     */
//    public void doProcess() {
//
//        if (CollectionUtil.isEmpty(listeners)) {
//            throw new ApplicationException("执行发送引擎时，各处理器未初始化.");
//        }
//        MarketingTaskEvent event = new MarketingTaskEvent(this);
//
//        for (MarketingTaskListener listener : listeners) {
//            doProcess(listener, event);
//        }
//        try {
//            semaphore.await();
//        } catch (InterruptedException e) {
//            //throw new ApplicationException("执行营销任务[key:" + metaDataTask.getKey() + "]发送引擎功能时发送错误.", e);
//            log.warn("执行营销任务[key:" + metaDataTask.getKey() + "]发送引擎功能时发送错误.", e);
//            // Restore interrupted state...      
//            Thread.currentThread().interrupt();
//        } finally {
//            // TODO: 处理各监听器回滚事项
//        }
//
//        // 执行回调，比如将营销任务状态写入到数据库等操作
//        if (callback != null) {
//            callback.execute();
//        } else {
//            callBack();
//        }
//    }
//
//    /**
//     * 营销任务监听器doProcess处理完后回调方法
//     */
//    public final void semaphoreDown() {
//        semaphore.countDown();
//    }
//
//    /**
//     * 当所有处理器处理完本次操作后回调方法，回调具体逻辑由子类实现
//     */
//    public abstract void callBack();
//
//    /**
//     * 获取营销任务封装对象
//     */
//    public MetaDataTask getMetaDataTask() {
//        return this.metaDataTask;
//    }
//
//
//}
