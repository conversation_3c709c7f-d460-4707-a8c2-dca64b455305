package com.deepexi.dxp.marketing.domain.marketing.request;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/9
 */
@Data
@ApiModel
public class MarketingAutoCreateRequest extends AbstractObject {

    /**
     * 自动任务名称
     */
    @ApiModelProperty(value = "自动任务名称", required = true)
    @NotBlank(message = "任务名称不能为空")
    private String name;


    /**
     * 任务编码
     */
    @ApiModelProperty(value = "任务编码")
    private String code;


    /**
     * 任务状态(1:草稿，2:未开始，3:运行中，4:暂停，5:结束)
     */
    @ApiModelProperty(value = "任务状态(1:草稿，2:未开始，3:运行中，4:暂停，5:结束)", required = true)
    @NotNull(message = "任务状态不能为空")
    private Integer status;


    /**
     * 行动模版ID
     */
    @ApiModelProperty(value = "行动模版ID", required = true)
    @NotNull(message = "行动模版不能为空")
    private Long actionTemplateId;


    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;


    /**
     * 0 立即执行 1 延后执行
     */
    @ApiModelProperty(value = "0 立即执行 1 延后执行", required = true)
    @NotNull(message = "执行类型不能为空")
    private Integer executeNow;

    /**
     * 条件触发后时
     */
    @ApiModelProperty(value = "条件触发后时")
    private Long executeAfter;


    /**
     * 执行时间单位 0 秒 1 分 2时 3日 4月 5年
     */
    @ApiModelProperty(value = "执行时间单位 0 秒 1 分 2时 3日 4月 5年")
    private Integer executeUnit;

    @ApiModelProperty(value = "触发条件")
    @NotEmpty(message = "触发条件不能为空")
    @Valid
    private List<MarketingAutoCondition> marketingAutoConditionList;

    @ApiModelProperty(value = "安全过滤")
    @Valid
    private List<MarketingTaskSecurityFilter> marketingTaskSecurityFilterList;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createdBy;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    @NotBlank(message = "租户不能为空")
    private String tenantId;

    /**
     * 应用id
     */
    @ApiModelProperty(value = "应用id")
    @NotNull(message = "应用不能为空")
    private Long appId;

    @Data
    @ApiModel
    public static class MarketingAutoCondition extends AbstractObject {
        /**
         * 触发类型 （1 场景）
         */
        @ApiModelProperty(value = "触发类型 （1 场景）")
        @NotNull(message = "触发类型不能为空")
        private Integer type;

        /**
         * 关联对象不能为空
         */
        @NotNull(message = "关联对象不能为空")
        @ApiModelProperty(value = "关联对象id")
        private Long itemId;
    }

    @Data
    @ApiModel
    public static class MarketingTaskSecurityFilter extends AbstractObject {
        /**
         * 安全选取类型(1:安全策略，2:用户打扰过滤)
         */
        @ApiModelProperty(value = "安全选取类型(1:安全策略，2:用户打扰过滤)")
        @NotNull(message = "安全过滤类型不能为空")
        private Integer securityType;

        /**
         * 安全过滤编码
         */
        @ApiModelProperty(value = "安全过滤编码")
        private String code;

        /**
         * 安全过滤名称
         */
        @ApiModelProperty(value = "安全过滤名称")
        private String name;
    }

}
