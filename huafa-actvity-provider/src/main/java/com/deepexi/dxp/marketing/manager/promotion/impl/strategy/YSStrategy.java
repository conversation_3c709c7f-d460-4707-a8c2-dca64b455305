package com.deepexi.dxp.marketing.manager.promotion.impl.strategy;

import com.deepexi.dxp.marketing.domain.promotion.dto.activity.ActivityRuleDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityCommodityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityConfigDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.PreSalesCommodityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.TemplateLimitDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityParamsDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityResponseParamsDTO;
import com.deepexi.dxp.marketing.enums.activity.strategy.PreSalesStrategyType;
import com.deepexi.dxp.marketing.enums.activity.strategy.StrategyGroupEnum;
import com.deepexi.dxp.marketing.enums.activity.strategy.condition.YSConditionEnum;
import com.deepexi.dxp.marketing.manager.promotion.impl.BaseStrategy;
import com.deepexi.dxp.middle.promotion.util.CollectionsUtil;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.exception.ApplicationException;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * 预售活动
 *
 * <AUTHOR> xinjian.yao
 * @date 2020/3/10 11:04
 */

@EqualsAndHashCode(callSuper = true)
public class YSStrategy extends BaseStrategy {

    private List<YSStrategyEnumsCalculate> calculateHelper = new ArrayList<>();

    public YSStrategy(TemplateLimitDTO templateLimitDTO, ActivityConfigDTO activityConfigDTO,
                      ActivityParamsDTO params, ActivityResponseParamsDTO activityResponseParamsDTO) {
        super(templateLimitDTO, activityConfigDTO, params, activityResponseParamsDTO);
    }

    /**
     * 预售活动不同策略的处理
     */
    private interface YSStrategyEnumsCalculate {
        /**
         * 预售活动不同策略的处理
         *
         * @param activityRuleDTOList       活动的优惠rule
         * @param params                    活动的参数
         * @param activityResponseParamsDTO 优惠结果返回类
         */
        void calculate(List<ActivityRuleDTO> activityRuleDTOList, ActivityParamsDTO params, ActivityResponseParamsDTO activityResponseParamsDTO);
    }

    private YSStrategyEnumsCalculate depositPaymentEnum() {
        return (activityRuleList, params, activityResponseParams) -> {
            boolean enumsFlag = activityResponseParams.getPaTemplateId().equals(StrategyGroupEnum.YS_G.getId());
            // 不是预售活动 或者预售活动下的策略活动为空 返回
            if (!enumsFlag || CollectionUtil.isEmpty(activityRuleList)) {
                return;
            }
            // 不是定金加尾款 返回
            String strategyType = activityRuleList.get(0).getStrategyType();
            if (!PreSalesStrategyType.DJ_AND_WK.getId().equals(strategyType)) {
                return;
            }
            // 获取定金下的信息
            List<BaseActivityDTO> condition = activityRuleList.get(0).getCondition();
            // 商品的预售价
            String preSalesMoney = CollectionsUtil.findOneInList(condition, val -> val.getId().equals(YSConditionEnum.ORDER_TIME.getId())).getValue();
            // 商品的定金比例
            String ratio = CollectionsUtil.findOneInList(condition, val -> val.getId().equals(YSConditionEnum.RATIO_MONEY.getId())).getValue();
            // 定金开始支付时间
            String startTime = CollectionsUtil.findOneInList(condition, val -> val.getId().equals(YSConditionEnum.START_TIME.getId())).getValue();
            // 定金结束支付时间
            String endTime = CollectionsUtil.findOneInList(condition, val -> val.getId().equals(YSConditionEnum.END_TIME.getId())).getValue();
            // 结束时间后几个小时内需要支付尾款
            String endHouse = CollectionsUtil.findOneInList(condition, val -> val.getId().equals(YSConditionEnum.END_HOUSE.getId())).getValue();

            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            ActivityCommodityDTO commoditie = activityResponseParams.getActivityCommodityDTOList().get(0);
            PreSalesCommodityDTO preSalesCommodityDTO = new PreSalesCommodityDTO();
            preSalesCommodityDTO.setActivityCommodityDTO(commoditie);
            try {
                // 预售活动尾款支付时间
                preSalesCommodityDTO.setDepositStartTime(formatter.parse(startTime));
                preSalesCommodityDTO.setDepositEndTime(formatter.parse(endTime));
                Calendar cal = Calendar.getInstance();
                cal.setTime(preSalesCommodityDTO.getDepositEndTime());
                // 24小时制
                cal.add(Calendar.HOUR, Integer.parseInt(endHouse));
                preSalesCommodityDTO.setTailTime(cal.getTime());
            } catch (Exception e) {
                throw new ApplicationException("预售活动时间转换失败");
            }
            // 定金计算
            preSalesCommodityDTO.setDepositPayment(
                    new BigDecimal(preSalesMoney)
                            .multiply(BigDecimal.valueOf((Integer.parseInt(ratio) / 100.0))
                                    .multiply(new BigDecimal(commoditie.getSkuAmount())))
                            .setScale(2, BigDecimal.ROUND_HALF_UP)
            );
            // 尾款计算
            preSalesCommodityDTO.setTailPayment(
                    new BigDecimal(preSalesMoney)
                            .multiply(new BigDecimal(commoditie.getSkuAmount()))
                            .subtract(preSalesCommodityDTO.getDepositPayment()));
            // 1 定金是否可退
            preSalesCommodityDTO.setReturnDepositPaymentFlag(activityResponseParams.getReturnMoneyType().equals(1));
            preSalesCommodityDTO.setAllPayment(
                    new BigDecimal(preSalesMoney)
                            .multiply(new BigDecimal(commoditie.getSkuAmount()))
                            .setScale(2, BigDecimal.ROUND_HALF_UP));
            preSalesCommodityDTO.setPreSalesStrategyType(PreSalesStrategyType.DJ_AND_WK.getId());
            preSalesCommodityDTO.setPreSalesMoney(preSalesMoney);
            activityResponseParams.setPreSalesCommodityDTO(preSalesCommodityDTO);
        };
    }


    private YSStrategyEnumsCalculate allMoneyEnum() {
        return (activityRuleList, params, activityResponseParams) -> {
            boolean enumsFlag = activityResponseParams.getPaTemplateId().equals(StrategyGroupEnum.YS_G.getId());
            // 不是预售活动 或者预售活动下的策略活动为空 返回
            if (!enumsFlag || CollectionUtil.isEmpty(activityRuleList)) {
                return;
            }
            // 不是定金加尾款 返回
            String strategyType = activityRuleList.get(0).getStrategyType();
            if (!PreSalesStrategyType.QK.getId().equals(strategyType)) {
                return;
            }
            // 获取定金下的信息
            List<BaseActivityDTO> condition = activityRuleList.get(0).getCondition();
            // 商品的预售价
            String preSalesMoney = CollectionsUtil.findOneInList(condition, val -> val.getId().equals(YSConditionEnum.ORDER_TIME.getId())).getValue();
            // 定金开始支付时间
            String startTime = CollectionsUtil.findOneInList(condition, val -> val.getId().equals(YSConditionEnum.START_TIME.getId())).getValue();
            // 定金结束支付时间
            String endTime = CollectionsUtil.findOneInList(condition, val -> val.getId().equals(YSConditionEnum.END_TIME.getId())).getValue();

            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            ActivityCommodityDTO commoditie = activityResponseParams.getActivityCommodityDTOList().get(0);
            PreSalesCommodityDTO preSalesCommodityDTO = new PreSalesCommodityDTO();
            preSalesCommodityDTO.setActivityCommodityDTO(commoditie);
            try {
                // 预售活动尾款支付时间
                preSalesCommodityDTO.setDepositStartTime(formatter.parse(startTime));
                preSalesCommodityDTO.setDepositEndTime(formatter.parse(endTime));
            } catch (Exception e) {
                throw new ApplicationException("预售活动时间转换失败");
            }
            // 全款计算
            preSalesCommodityDTO.setAllPayment(new BigDecimal(preSalesMoney)
                    .multiply(new BigDecimal(commoditie.getSkuAmount())));
            // 1 定金是否可退
            preSalesCommodityDTO.setReturnDepositPaymentFlag(activityResponseParams.getReturnMoneyType().equals(1));

            preSalesCommodityDTO.setPreSalesStrategyType(PreSalesStrategyType.QK.getId());
            preSalesCommodityDTO.setPreSalesMoney(preSalesMoney);
            activityResponseParams.setPreSalesCommodityDTO(preSalesCommodityDTO);

        };
    }

    private void init() {
        calculateHelper.add(depositPaymentEnum());
        calculateHelper.add(allMoneyEnum());
    }

    @Override
    public Boolean calculate() {
        // 获取活动的策略
        List<ActivityRuleDTO> activityStrategiesList = super.getActivityConfigDTO().getActivityRuleDTOList();
        // 获取活动的参数
        ActivityParamsDTO params = super.getParams();
        // 活动返回的参数
        ActivityResponseParamsDTO activityResponseParamsDTO = super.getActivityResponseParamsDTO();
        init();
        calculate(activityStrategiesList, params, activityResponseParamsDTO);
        return true;
    }

    private void calculate(List<ActivityRuleDTO> activityStrategiesList, ActivityParamsDTO params, ActivityResponseParamsDTO activityResponseParamsDTO) {
        for (YSStrategyEnumsCalculate calculate : calculateHelper) {
            calculate.calculate(activityStrategiesList, params, activityResponseParamsDTO);
        }
    }

}
