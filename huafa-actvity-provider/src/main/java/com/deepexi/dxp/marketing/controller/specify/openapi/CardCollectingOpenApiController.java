package com.deepexi.dxp.marketing.controller.specify.openapi;

import com.deepexi.dxp.marketing.common.base.utils.CommonExceptionCode;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityFissionLogQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPartakeRequest;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.CardCollectingLotteryRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.LotteryResourceDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ReceiveNowDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.*;
import com.deepexi.dxp.marketing.service.specify.CardCollectingService;
import com.deepexi.dxp.marketing.service.specify.PromotionActivityService;
import com.deepexi.util.exception.ApplicationException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.deepexi.dxp.marketing.constant.RedisConstants.CACHE_PREV_KEY_ACT_PARTAKE_LOCK;

/**
 * <AUTHOR>
 * @date 2021/05/24 17:54
 */
@RestController
@RequestMapping("/open-api/v1/open/card-collecting/")
@Api(value = "集卡活动接口", tags = {"集卡活动接口"})
@Slf4j
public class CardCollectingOpenApiController {

    @Resource
    private CardCollectingService cardCollectingService;
    @Resource
    private PromotionActivityService promotionActivityService;

    @Resource
    private RedissonClient redissonClient;

    @PostMapping("/homePageInfo")
    @ApiOperation(value = "移动端-首页页面信息", notes = "首页信息")
    public Data<CardCollectingHomePageInfoResponseDTO> homePageInfo(@RequestBody ActivityPartakeRequest query) {
        return new Data<>(cardCollectingService.homePageInfo(query));
    }

    @PostMapping("/homeUserInfo")
    @ApiOperation(value = "移动端-首页用户信息", notes = "首页信息")
    public Data<CardCollectingHomeUserInfoResponseDTO> homeUserInfo(@RequestBody ActivityPartakeRequest query) {
        return new Data<>(cardCollectingService.homeUserInfo(query));
    }

    @PostMapping("/lottery")
    @ApiOperation(value = "移动端-卡片抽奖", notes = "卡片抽奖")
    public Data<LotteryResourceDTO> lottery(@RequestBody CardCollectingLotteryRequestDTO request) {
        return new Data<>(cardCollectingService.lottery(request.getPartakeLogId(), request.getId()));
    }

    @PostMapping("/compose")
    @ApiOperation(value = "移动端-卡片合成", notes = "卡片合成")
    public Data<LotteryResourceDTO> compose(@RequestParam("partakeLogId") Long partakeLogId) {
        return new Data<>(cardCollectingService.compose(partakeLogId));
    }

    @PostMapping("/receiveNow")
    @ApiOperation(value = "立即领取", notes = "裂变活动-立即领取")
    public Data<Boolean> receiveNow(@RequestBody @Valid ReceiveNowDTO dto) {
        //加锁
        RLock lock = redissonClient.getLock(CACHE_PREV_KEY_ACT_PARTAKE_LOCK + dto.getActivityId() + dto.getPhone());
        try {
            if (lock.tryLock(100, 10 * 1000, TimeUnit.MILLISECONDS)) {
                return new Data<>(cardCollectingService.receiveNow(dto));
            }
        } catch (IllegalArgumentException e) {
            throw e;
        } catch (ApplicationException e) {
            throw e;
        } catch (Exception e) {
            log.error("领取奖品失败", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        throw new ApplicationException(CommonExceptionCode.PARTICIPATED, "活动太繁忙，请稍后重试");
    }

    @PostMapping("/fissionFriendsList")
    @ApiOperation(value = "裂变好友列表", notes = "裂变好友列表")
    public Data<List<ActivityFissionLogResponseDTO>> fissionFriendsListPost(@RequestBody ActivityFissionLogQuery query) {
        return new Data<>(cardCollectingService.fissionFriendsList(query));
    }

    @PostMapping("/prizeList")
    @ApiOperation(value = "我的奖品列表", notes = "我的奖品列表")
    public Data<List<CardCollectingPrizeResponseDTO>> prizeList(@RequestBody ActivityFissionLogQuery query) {
        return new Data<>(cardCollectingService.myPrizeList(query));
    }

    @PostMapping("/rankList")
    @ApiOperation(value = "排行榜", notes = "集卡排行榜")
    public Data<List<FissionRankResponseDTO>> rankList(@RequestParam("activityId") Long activityId) {
        return new Data<>(promotionActivityService.rankList(activityId));
    }

}
