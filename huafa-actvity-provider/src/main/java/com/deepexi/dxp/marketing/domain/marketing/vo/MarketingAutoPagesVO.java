package com.deepexi.dxp.marketing.domain.marketing.vo;

import com.deepexi.dxp.marketing.common.base.vo.SuperExtVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/12
 */
@Data
public class MarketingAutoPagesVO extends SuperExtVO {
    /**
     * 条件触发后时
     */
    @ApiModelProperty("条件触发后时")
    private Long executeAfter;


    /**
     * 执行时间单位 0 秒 1 分 2时 3日 4月 5年
     */
    @ApiModelProperty("执行时间单位 0 秒 1 分 2时 3日 4月 5年")
    private Integer executeUnit;

    /**
     * 自动任务名称
     */
    @ApiModelProperty("自动任务名称")
    private String name;
    /**
     * 任务编码(暂未定生成规则)
     */
    @ApiModelProperty("任务编码(暂未定生成规则)")
    private String code;
    /**
     * 任务状态(1:草稿，2:未开始，3:运行中，4:暂停，5:结束)
     */
    @ApiModelProperty("任务状态(1:草稿，2:未开始，3:运行中，4:暂停，5:结束)")
    private Integer status;
    /**
     * 执行时机
     */
    @ApiModelProperty("执行时机")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date executeTime;

    /**
     * 行动模版ID
     */
    @ApiModelProperty("行动模版ID")
    private Long actionTemplateId;
    /**
     * 描述
     */
    @ApiModelProperty("描述")
    private String description;

    /**
     * 0 立即执行 1 延后执行
     */
    @ApiModelProperty("0 立即执行 1 延后执行")
    private Integer executeNow;

    @ApiModelProperty("成功触达人数")
    private Integer successContactPerson;

    @ApiModelProperty("成功触达人次")
    private Integer successContactPersonTimes;

    @ApiModelProperty("触达渠道+方式")
    private String sendRuleChannels;

    @ApiModelProperty("触发类型")
    private Integer type;

    @ApiModelProperty("触发类型项目的id如场景id")
    private Long itemId;

    @ApiModelProperty("行动模版资源名称")
    private String templateResourcesNames;
}
