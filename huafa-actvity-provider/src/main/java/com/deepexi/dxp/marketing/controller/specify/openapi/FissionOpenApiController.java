package com.deepexi.dxp.marketing.controller.specify.openapi;

import com.deepexi.dxp.marketing.common.base.utils.CommonExceptionCode;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityFissionAssistResourceQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.PartakeLogQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.WinningRecordQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPartakeRequest;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.FissionReceiveNowDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.SwitchProjectDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityFissionAssistResourceResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.FissionHomeInfoResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.WinningRecordResponseDTO;
import com.deepexi.dxp.marketing.enums.specify.FissonStatusEnum;
import com.deepexi.dxp.marketing.service.specify.FissionService;
import com.deepexi.dxp.marketing.service.specify.LuckyDrawService;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.deepexi.dxp.marketing.constant.RedisConstants.CACHE_PREV_KEY_ACT_PARTAKE_LOCK;

/**
 * <AUTHOR>
 * @date  2021/05/24 17:54
 */
@RestController
@RequestMapping("/open-api/v1/open/fission/")
@Api(value = "裂变活动接口",tags = {"裂变活动接口"})
@Slf4j
public class FissionOpenApiController {

    @Resource
    private FissionService fissionService;

    @Resource
    private LuckyDrawService luckyDrawService;

    @Resource
    private RedissonClient redissonClient;

    @PostMapping("/homeInfo")
    @ApiOperation(value="移动端-首页信息", notes = "首页信息")
    public Data<FissionHomeInfoResponseDTO> homeInfo(@RequestBody ActivityPartakeRequest query) {
        if (query.getUserJoinType() == null){
            throw new ApplicationException("用户参与类型不能为空");
        }
        return new Data<>(fissionService.homeInfo(query));
    }


    @PostMapping("/receiveNow")
    @ApiOperation(value="立即领取", notes = "裂变活动-立即领取")
    public Data<Boolean> receiveNow(@RequestBody @Valid FissionReceiveNowDTO dto) {
        //加锁
        RLock lock = redissonClient.getLock(CACHE_PREV_KEY_ACT_PARTAKE_LOCK + dto.getActivityId() + dto.getPhone());
        try {
            if (lock.tryLock(100, 10 * 1000, TimeUnit.MILLISECONDS)) {
                return new Data<>(fissionService.receiveNow(dto));
            }
        } catch (IllegalArgumentException e) {
            throw e;
        } catch (ApplicationException e) {
            throw e;
        } catch (Exception e) {
            log.error("领取奖品失败", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        throw new ApplicationException(CommonExceptionCode.PARTICIPATED, "活动太繁忙，请稍后重试");
    }

    @PostMapping("/fissionSuccessRecord")
    @ApiOperation(value="砍价成功记录", notes = "砍价成功记录")
    public Data<List<WinningRecordResponseDTO>> fissionSuccessRecord(@RequestBody @Valid WinningRecordQuery query) {
        PartakeLogQuery partakeLogQuery = new PartakeLogQuery();
        partakeLogQuery.setActivityId(query.getActivityId());
        partakeLogQuery.setPage(Integer.parseInt(String.valueOf(query.getPage())));
        partakeLogQuery.setSize(Integer.parseInt(String.valueOf(query.getSize())));
        partakeLogQuery.setFissonStatus(FissonStatusEnum.SUCCESS.getId());
        return new Data<>(luckyDrawService.winningRecord(partakeLogQuery));
    }

    /**
     * 2021、7、15 切换项目需求变更 3.0
     * 切换项目不改变任何记录，也不再有任何限制，只修改关联的项目信息
     * @param dto
     * @return
     */
    @PostMapping("/switchProject")
    @ApiOperation(value="切换项目", notes = "裂变活动-切换项目")
    public Data<Boolean> switchProject(@RequestBody @Valid SwitchProjectDTO dto) {
        return new Data<>(fissionService.switchProject(dto));
    }

    @ApiOperation(value = "获取指定用户未领取奖品", notes = "获取指定用户未领取奖品")
    @PostMapping("/findCertificatesPage")
    public Data<PageBean<ActivityFissionAssistResourceResponseDTO>> findCertificatesPage(@RequestBody ActivityFissionAssistResourceQuery query) {
        return new Data<>(fissionService.findCertificatesList(query));
    }

    /**
     * 获取助力活动中，奖品轴信息
     * @return
     */
    @GetMapping("/getPrizeAxisStatus")
    @ApiOperation(value="获取助力活动中，奖品轴信息", notes = "获取助力活动中，奖品轴信息")
    public Data<Object> getPrizeAxisStatus(@RequestParam Integer activityId, @RequestParam Integer partakeLogId) {
        return new Data<>(fissionService.getPrizeAxisStatus(activityId,partakeLogId));
    }

}
