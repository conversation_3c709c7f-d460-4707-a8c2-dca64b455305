package com.deepexi.dxp.marketing.domain.marketing.request;

import com.deepexi.dxp.marketing.common.base.request.SuperRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description：
 * @author：<PERSON><PERSON><PERSON><PERSON>
 * @version：1.0.0
 * @date：2021-04-03 10:17 上午
 */
@Data
@ApiModel
public class WxTemplateStatusRequest extends SuperRequest {


    /**
     * 公众号appId
     */
    @ApiModelProperty("公众号appId")
    private String authorizerAppId;

    /**
     * 正常模板
     */
    @ApiModelProperty("正常模板")
    private List<String> normalTemplateIds;


    /**
     * 不正常模板
     */
    @ApiModelProperty("不正常模板")
    private List<String> unNormalTemplateIds;

}
