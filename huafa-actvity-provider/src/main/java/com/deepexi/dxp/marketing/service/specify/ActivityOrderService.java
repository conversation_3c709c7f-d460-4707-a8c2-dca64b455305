package com.deepexi.dxp.marketing.service.specify;


import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityOrderQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.CancelOrderRequest;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityOrderRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityPartakeRequest;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.CouponPayCallBackRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityOrderResponseVO;
import com.deepexi.dxp.marketing.domain.marketing.response.OrderPayResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.PayOrderQueryResponseDTO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityOrderDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.PromotionHisResourceDO;
import com.deepexi.util.pageHelper.PageBean;

import java.util.List;

public interface ActivityOrderService {

	/**
	 * 列表查询
	 **/
	PageBean<ActivityOrderResponseVO> pageList(ActivityOrderQuery query);


	/**
	 * 保存
	 **/
	Long save(ActivityOrderRequestDTO dto);


	/**
	 * 通过订单编码查询订单
	 * @param code
	 * @return
	 */
	ActivityOrderDO getByCode(String code);


	/**
	 * 更新订单状态
	 * @param activityOrderDO
	 * @return
	 */
	Boolean updateById(ActivityOrderDO activityOrderDO);

	/**
	 * 通过状态查询订单
	 * @param status
	 * @return
	 */
	List<ActivityOrderResponseVO> findList(Integer status);

	/**
	 * 订单详情
	 * @param id
	 * @return
	 */
	ActivityOrderResponseVO getDetail(Long id);

	/**
	 * 个人订单详情
	 * @param resourceId 历史资源id
	 * @param phone
	 * @return
	 */
	ActivityOrderResponseVO getOrderDetail(Long resourceId,String phone,Long activityId);

	/**
	 * 关闭订单
	 * @param wxOrderNo
	 * @param orderId
	 * @return
	 */
	Data<Boolean> closeOrder(String wxOrderNo, Long orderId);

	/**
	 * 通过订单id查询订单信息
	 * @param orderId
	 * @return
	 */
	PayOrderQueryResponseDTO getOrderPayResult(Long orderId);

	CouponPayCallBackRequestDTO getLocalOrderPayResult(Long orderId);

	/**
	 * 主动查询支付服务中的订单
	 * @param wxOrderNo 微信订单号
	 * @return
	 */
	void checkUnPayOrder(String wxOrderNo, Long orderId);

    //检查重复订单
    void checkRepeatOrder(ActivityPartakeRequest requestVo);

    //创建订单
    Data<OrderPayResponseDTO> placeOrder(ActivityPartakeRequest requestVo, PromotionActivityDO promotionActivityDO, PromotionHisResourceDO promotionHisResourceDO);

	ActivityOrderResponseVO getOrderDetail(String code);

    Boolean cancelOrder(CancelOrderRequest dto);

	Boolean closeOrder(CancelOrderRequest dto);
}