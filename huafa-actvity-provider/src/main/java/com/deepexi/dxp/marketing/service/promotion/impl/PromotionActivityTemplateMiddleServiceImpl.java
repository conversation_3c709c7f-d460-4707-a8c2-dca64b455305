package com.deepexi.dxp.marketing.service.promotion.impl;


import com.deepexi.dxp.marketing.domain.promotion.dto.activity.PromotionActivityTemplateDetailPostDTO;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityTemplateCreatePostRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityTemplateDeletedPostRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityTemplateUpdatePostRequest;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityTemplateDO;
import com.deepexi.dxp.middle.promotion.mapper.PromotionActivityTemplateMapper;
import com.deepexi.dxp.marketing.service.promotion.PromotionActivityTemplateMiddleService;
import com.deepexi.util.BeanPowerHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> xin<PERSON>an.yao
 * @date 2019/11/20 16:54
 */
@Service
public class PromotionActivityTemplateMiddleServiceImpl implements PromotionActivityTemplateMiddleService {

    @Autowired
    private PromotionActivityTemplateMapper promotionActivityTemplateMapper;


    @Override
    public int updateBatch(List<PromotionActivityTemplateDO> list) {
        return promotionActivityTemplateMapper.updateBatch(list);
    }

    @Override
    public Boolean create(PromotionActivityTemplateCreatePostRequest dto) {
        PromotionActivityTemplateDO po = new PromotionActivityTemplateDO();
        BeanPowerHelper.mapPartOverrider(dto, po);
        Date date = new Date();
        po.setCreatedTime(date);
        po.setUpdatedTime(date);
        return promotionActivityTemplateMapper.insert(po) > 0;
    }

    @Override
    public Boolean update(Long id, PromotionActivityTemplateUpdatePostRequest dto) {
        PromotionActivityTemplateDO updatePO = new PromotionActivityTemplateDO();
        BeanPowerHelper.mapPartOverrider(dto, updatePO);
        PromotionActivityTemplateDO oldPo = promotionActivityTemplateMapper.selectById(id);
        updatePO.setUpdatedTime(new Date());
        updatePO.setId(oldPo.getId());
        updatePO.setVersion(oldPo.getVersion());
        BeanPowerHelper.mapCompleteOverrider(updatePO, oldPo);
        return promotionActivityTemplateMapper.updateById(updatePO) > 0;
    }

    @Override
    public PromotionActivityTemplateDetailPostDTO detail(Long id) {
        // 查询id 不null 转成出参DTO
        PromotionActivityTemplateDO po = promotionActivityTemplateMapper.selectById(id);

        return Optional.ofNullable(po)
                .map(DO -> {
                    PromotionActivityTemplateDetailPostDTO dto = new PromotionActivityTemplateDetailPostDTO();
                    BeanPowerHelper.mapPartOverrider(DO, dto);
                    return dto;
                })
                .orElse(null);

    }

    @Override
    public Boolean deleted(List<PromotionActivityTemplateDeletedPostRequest> dtoList) {
        // 根据idList 删除
        List<Long> ids = dtoList.stream()
                .map(PromotionActivityTemplateDeletedPostRequest::getId)
                .distinct()
                .collect(Collectors.toList());
        return promotionActivityTemplateMapper.deleteBatchIds(ids) > 0;

    }
}



