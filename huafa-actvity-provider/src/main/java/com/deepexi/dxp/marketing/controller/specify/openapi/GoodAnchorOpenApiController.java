package com.deepexi.dxp.marketing.controller.specify.openapi;

import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.response.GoodAnchorLikesRankResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.GoodAnchorVideoExtResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.GoodAnchorVideoResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.VideoResponseDTO;
import com.deepexi.dxp.marketing.service.specify.GoodAnchorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: HT
 * @CreateTime: 2022/5/11
 */
@RestController
@RequestMapping("/open-api/v1/open/good-anchor/")
@Api(value = "文传好主播活动接口", tags = {"文传好主播活动接口"})
public class GoodAnchorOpenApiController {

    @Resource
    private GoodAnchorService goodAnchorService;

    @GetMapping("/queryLikesRanking")
    @ApiOperation(value = "根据标识查询城市维度或项目维度的点赞数排名")
    public Data<GoodAnchorLikesRankResponseDTO> queryLikesRanking(@ApiParam(value = "1:城市，2:项目") @RequestParam Integer id,
                                                                  @ApiParam(value = "活动id") @RequestParam Long activityId) throws Exception {
        return new Data<>(goodAnchorService.queryLikesRanking(id, activityId));
    }

    @GetMapping("/queryVideosByCityId")
    @ApiOperation(value = "根据城市id返回对应城市下的参赛作品")
    public Data<List<GoodAnchorVideoResponseDTO>> queryVideosByCityId(@ApiParam(value = "城市id，全部传0") @RequestParam String cityId,
                                                                      @ApiParam(value = "活动id") @RequestParam Long activityId,
                                                                      @ApiParam(value = "用户手机号，非必传") @RequestParam(required = false) String mobile) throws Exception {
        return new Data<>(goodAnchorService.queryVideosByCityId(cityId, activityId, mobile));
    }

    @GetMapping("/queryVideosByAccountId")
    @ApiOperation(value = "根据主播账号id查询本次参赛的所有作品列表及主播信息")
    public Data<GoodAnchorVideoExtResponseDTO> queryVideosByAccountId(@ApiParam(value = "账号id") @RequestParam String accountId,
                                                                      @ApiParam(value = "活动id") @RequestParam Long activityId,
                                                                      @ApiParam(value = "用户手机号，非必传") @RequestParam(required = false) String mobile) throws Exception {
        return new Data<>(goodAnchorService.queryVideosByAccountId(accountId, activityId, mobile));
    }

    @GetMapping("/queryVideoById")
    @ApiOperation(value = "根据视频id获取视频信息")
    public Data<VideoResponseDTO> queryVideoById(@ApiParam(value = "视频id") @RequestParam String videoId,
                                                 @ApiParam(value = "活动id") @RequestParam Long activityId,
                                                 @ApiParam(value = "用户手机号，非必传") @RequestParam(required = false) String mobile) throws Exception {
        return new Data<>(goodAnchorService.queryVideoById(videoId, mobile,activityId));
    }

    @GetMapping("/getUUID")
    @ApiOperation(value = "好主播活动获取uuid")
    public Data<String> getUUID(@RequestParam String phone, @RequestParam Long activityId, @RequestParam Long id) {
        return new Data<>(goodAnchorService.getUUID(phone, activityId, id));
    }

}
