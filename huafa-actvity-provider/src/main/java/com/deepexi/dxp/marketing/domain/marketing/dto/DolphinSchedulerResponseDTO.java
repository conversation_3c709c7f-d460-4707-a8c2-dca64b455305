package com.deepexi.dxp.marketing.domain.marketing.dto;

import com.alibaba.fastjson.JSON;
import com.deepexi.dxp.marketing.constant.DolphinSchedulerConstant;
import com.deepexi.util.exception.ApplicationException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * @Class: DolphinSchedulerPayloadVO
 * @Description: DolphinScheduler 请求返回值VO
 * @Author: lizhongbao
 * @Date: 2020/3/26
 **/
@Data
@Slf4j
public class DolphinSchedulerResponseDTO implements Serializable {
    private String code;
    private String msg;
    private Object data;

    public static DolphinSchedulerResponseDTO build(String bean, String msg) {
        log.info("调用DolphinScheduler返回的参数为：{}", bean);
        DolphinSchedulerResponseDTO dolphinSchedulerResponseDTO = JSON.parseObject(bean, DolphinSchedulerResponseDTO.class);
        if (dolphinSchedulerResponseDTO == null) {
            throw new ApplicationException(msg + "异常");
        }
        if (!DolphinSchedulerConstant.CODE_SUCCESS.equals(dolphinSchedulerResponseDTO.getCode())) {
            throw new ApplicationException(msg + "失败");
        }
        return dolphinSchedulerResponseDTO;
    }
}
