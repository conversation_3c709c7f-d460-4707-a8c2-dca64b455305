package com.deepexi.dxp.marketing.manager.promotion.impl.strategy;

import com.deepexi.dxp.marketing.domain.promotion.dto.activity.ActivityRuleDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityParamsDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityResponseParamsDTO;
import com.deepexi.dxp.marketing.manager.promotion.impl.BaseStrategy;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> ming.zhong
 * @date created in 16:16 2019/12/2
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel
@Data
public class LJHDStrategy extends BaseStrategy {

    private List<LJHDStrategyEnumsCalculate> calculateHelper = new ArrayList<>(30);

    /**
     * 领券活动的枚举类处理
     */
    private interface LJHDStrategyEnumsCalculate {
        /**
         * 领券活动的枚举类处理
         *
         * @param activityRuleDTOList       活动的优惠rule
         * @param params                    活动的参数
         * @param activityResponseParamsDTO 优惠结果返回类
         */
        void calculate(List<ActivityRuleDTO> activityRuleDTOList, ActivityParamsDTO params, ActivityResponseParamsDTO activityResponseParamsDTO);
    }

    private LJHDStrategyEnumsCalculate strategy() {
        return (activityRuleList, params, activityResponseParams) -> {
        };
    }

    private void init() {
        calculateHelper.add(strategy());
    }

    @Override
    public Boolean calculate() {
        // 获取活动的策略
        List<ActivityRuleDTO> activityStrategiesList = super.getActivityConfigDTO().getActivityRuleDTOList();
        // 获取活动的参数
        ActivityParamsDTO params = super.getParams();
        // 活动返回的参数
        ActivityResponseParamsDTO activityResponseParamsDTO = super.getActivityResponseParamsDTO();
        init();
        LJHDCalculate(activityStrategiesList, params, activityResponseParamsDTO);
        return true;
    }

    private void LJHDCalculate(List<ActivityRuleDTO> activityStrategiesList, ActivityParamsDTO params, ActivityResponseParamsDTO activityResponseParamsDTO) {

        for (LJHDStrategyEnumsCalculate strategy : calculateHelper) {
            strategy.calculate(activityStrategiesList, params, activityResponseParamsDTO);
        }
    }
}