package com.deepexi.dxp.marketing.domain.marketing.dto;
import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import lombok.Data;


/**
 * 流程画布-执行-变更积分
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-07 16:03
 */
@Data
public class FlowCanvasExecuteSendScoreDTO extends SuperDTO {
    /**
     * 画布ID
     */
    private Long flowId;

    /**
     * 画布节点ID
     */
    private String nodeId;

    /**
     * 类型
     */
    private String type;

    /**
     * 积分
     */
    private Integer score;



    public FlowCanvasExecuteSendScoreDTO(){}

    public FlowCanvasExecuteSendScoreDTO(String nodeId, String type, Integer score, String remark){
        this.nodeId = nodeId;
        this.type = type;
        this.score = score;
        this.setRemark(remark);
    }
}
