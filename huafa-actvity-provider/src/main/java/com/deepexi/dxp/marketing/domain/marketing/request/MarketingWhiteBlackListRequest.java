package com.deepexi.dxp.marketing.domain.marketing.request;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Class: MarketingWhiteBlackListVO
 * @Description:
 * @Author: zht
 * @Date: 2020/4/2
 */
@Data
@ApiModel
public class MarketingWhiteBlackListRequest extends AbstractObject {
    /**
     * 会员id
     */
    @ApiModelProperty("会员id")
    private Long memberId;

    /**
     * 类型 0 白名单 1 黑名单
     */
    @ApiModelProperty("类型 0 白名单 1 黑名单")
    private Integer type;

}
