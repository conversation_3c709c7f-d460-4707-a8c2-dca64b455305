package com.deepexi.dxp.marketing.domain.marketing.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/11
 */
@Data
@ApiModel
public class OperationRequest extends IdRequest {

    /**
     * 操作类型(1发布2暂停3重启4终止)
     */
    @ApiModelProperty(value = "操作类型(1发布2暂停3重启4终止)", required = true)
    @NotNull(message = "操作类型不能为空")
    private Integer operationType;

    @ApiModelProperty(value = "操作用户")
    private String username;
}
