package com.deepexi.dxp.marketing.service.specify;

import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.CouponPartakeRecordQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.CouponPayCallBackRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.CouponRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ReceiveCouponRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityCouponPartakeLogResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.CouponActivityResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.OrderPayResponseDTO;
import com.deepexi.util.pageHelper.PageBean;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 优惠券service
 */
public interface ActivityCouponService {

    /**
     * 保存优惠券活动信息
     * @param requestDTO 活动详情
     * @return
     */
    boolean save(CouponRequestDTO requestDTO);

    /**
     * 修改优惠券活动信息
     * @param id 活动id
     * @param dto 活动详情
     * @return
     */
    Boolean updateActivityById(CouponRequestDTO dto);


    /**
     * 活动详情
     * @param id 活动id
     * @return
     */
    CouponActivityResponseDTO detail(Long id);

    /**
     * 优惠券活动参与明细
     * @param query
     * @return
     */
    PageBean<ActivityCouponPartakeLogResponseDTO> partakeLogList(CouponPartakeRecordQuery query);

    /**
     * 明细导出
     * @param response
     * @param query
     */
    void exportPartakeLogExcel(HttpServletResponse response, CouponPartakeRecordQuery query);

    /**
     * 领券
     * @param dto
     * @return
     */
    Boolean receiveCoupons(ReceiveCouponRequestDTO dto);

    /**
     * 领券较验
     * @param dto
     * @return
     */
    Boolean receiveCheck(ReceiveCouponRequestDTO dto);

    /**
     * 付费购买
     * @param dto
     * @return
     */
    Data<OrderPayResponseDTO> payPurchase(ReceiveCouponRequestDTO dto);

    /**
     * 优惠券支付回调
     * @param dto
     * @return
     */
    Map<String,Object> payCallBack(CouponPayCallBackRequestDTO dto);
}
