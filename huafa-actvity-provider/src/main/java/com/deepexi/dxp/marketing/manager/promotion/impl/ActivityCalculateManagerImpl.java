package com.deepexi.dxp.marketing.manager.promotion.impl;

import com.alibaba.fastjson.JSON;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.*;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityParamsDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityResponseParamsDTO;
import com.deepexi.dxp.marketing.enums.activity.limit.PATemplateBaseEnum;
import com.deepexi.dxp.marketing.enums.activity.strategy.StrategyGroupEnum;
import com.deepexi.dxp.marketing.enums.coupon.CouponTypeEnum;
import com.deepexi.dxp.marketing.manager.promotion.ActivityCalculateManager;
import com.deepexi.dxp.marketing.manager.promotion.Context;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionCouponDO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionCouponLoggerDO;
import com.deepexi.dxp.middle.promotion.util.Arith;
import com.deepexi.dxp.middle.promotion.util.CollectionsUtil;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pojo.CloneDirection;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR> xinjian.yao
 * @date 2019/12/2 20:09
 */
@Slf4j
@Service
public class ActivityCalculateManagerImpl implements ActivityCalculateManager {
    @Override
    public Boolean couponLimit(ActivityConfigDTO activity) {
        Context context = new Context(activity);
        // 限制类型初始化 并且计算
        return context
                .couponLimitCalculate()
                .calculateLimit();

    }

    @Override
    public Boolean limitCalculate(ActivityConfigDTO activity, ActivityParamsDTO params) {
        //全部校验
        List<String> limitLists = Arrays.stream(PATemplateBaseEnum.values()).map(PATemplateBaseEnum::name).collect(Collectors.toList());
        Context context = new Context(activity, params);
        // 限制类型初始化 并且计算
        return context
                .initLimitCalculate(limitLists)
                .calculateLimit();
    }

    @Override
    public boolean limitCalculate(ActivityConfigDTO activity, ActivityParamsDTO params, List<String> limitLists) {
        limitLists = initLimitList(limitLists);
        Context context = new Context(activity, params);
        // 限制类型初始化 并且计算
        return context
                .initLimitCalculate(limitLists)
                .calculateLimit();
    }

    //兼容历史功能，空和ALL，默认填充全部限制
    private List<String> initLimitList(List<String> limitList) {
        if (CollectionUtils.isEmpty(limitList) || limitList.contains("ALL")) {
            limitList = Arrays.stream(PATemplateBaseEnum.values()).map(PATemplateBaseEnum::name).collect(Collectors.toList());
        }
        else if (limitList.contains("NONE")) {
            limitList.clear();
        }
        return limitList;
    }

    @Override
    public void strategyCalculate(ActivityConfigDTO activityConfigDTO, ActivityResponseParamsDTO activityResponseParamsDTO) {

        Context context = new Context(activityConfigDTO, activityResponseParamsDTO);
        context.initStrategyCalcalate()
                .calculateStrategy();
    }

    /**
     * 优惠券限制
     *
     * @param activityCommodityDTO 商品活动
     * @param promotionCouponDo    优惠券do 包含优惠券的配置
     * @return
     */
    @Override
    public Boolean couponLimitCalculate(ActivityCommodityDTO activityCommodityDTO, PromotionCouponLoggerDO promotionCouponLoggerDo, PromotionCouponDO promotionCouponDo) {
        CouponLimitDTO couponLimitDTO = JSON.parseObject(promotionCouponDo.getLimits(), CouponLimitDTO.class);
        Date now = new Date();
        if (Objects.nonNull(promotionCouponLoggerDo.getStartTime()) && Objects.nonNull(promotionCouponLoggerDo.getEndTime())
                && (now.before(promotionCouponLoggerDo.getStartTime()) || now.after(promotionCouponLoggerDo.getEndTime()))) {
            return false;
        }

        ActivityConfigDTO activity = new ActivityConfigDTO();
        activity.setCommodityList(couponLimitDTO.getCommodity());
        ActivityParamsDTO params = new ActivityParamsDTO();
        params.setCommoditie(activityCommodityDTO);
        Context context = new Context(activity, params);
        return context.initCouponCommodityLimit().calculateLimit();
    }

    /**
     * 计算不含优惠券的订单总价，即仅活动的优惠后价格
     * @param resultDTO
     * @return java.lang.Double
     * <AUTHOR>
     * @date 2020/12/10
     **/
    @Override
    public Double orderMoneyNoCoupon(OrderEditResponseDTO resultDTO) {

        List<ActivityResponseParamsDTO> activityResponseParamsDTOList = resultDTO.getActivityList();
        List<ActivityCommodityDTO> notActivityCommodityDTO = resultDTO.getNoActivityCommodityDTOList();
        //1.无活动商品原始价格总和
        Double orderPriceNotActivity = CollectionsUtil.doubleGroup(
                Optional.ofNullable(notActivityCommodityDTO).orElse(new ArrayList<>()),
                vals -> Optional.ofNullable(vals).map(val -> val.getDetailPrice()
                        .multiply(new BigDecimal(val.getSkuAmount().toString()))
                        .setScale(2, BigDecimal.ROUND_HALF_UP))
                        .map(BigDecimal::doubleValue).orElse(0.0));


        // 2.有活动商品的活动优惠后总金额
        AtomicReference<Double> orderPriceOnlyActivity = new AtomicReference<>(0.0D);
        activityResponseParamsDTOList.stream().forEach(act -> {
            Double priceDiscountAll = CollectionsUtil.doubleGroup(act.getActivityCommodityDTOList(),
                    val -> Optional.ofNullable(val).map(price -> {
                        // 最后优惠价格 等于 商品原价 减去 活动优惠和优惠券优惠
                        BigDecimal subtractPriceAll = Optional.ofNullable(price.getSubtractPriceAll()).orElse(BigDecimal.ZERO);
                        return price.getDetailPrice()
                                .multiply(new BigDecimal(price.getSkuAmount()))
                                .subtract(subtractPriceAll)
                                .doubleValue();
                    }).orElse(0.0));

            orderPriceOnlyActivity.updateAndGet(v -> v + priceDiscountAll);
        });

        //3.有活动商品+无活动商品的总和
        Double orderPriceActivity = orderPriceNotActivity + orderPriceOnlyActivity.get();
        resultDTO.setOrderDiscountsPriceOnlyActivity(BigDecimal.valueOf(orderPriceActivity));
        return orderPriceActivity;
    }

    @Override
    public void orderCouponMoneyDealWith(List<PromotionCouponDO> couponDoList,
                                         List<PromotionCouponLoggerDO> promotionCouponLoggerDOList,
                                         OrderEditResponseDTO resultDTO,
                                         Map<Long, ActivityConfigDTO> activityConfigCache) {
        List<ActivityResponseParamsDTO> activityResponseParamsDTOList = resultDTO.getActivityList();
        List<ActivityCommodityDTO> notActivityCommodityDTO = resultDTO.getNoActivityCommodityDTOList();
        BigDecimal orderDiscountsPriceOnlyActivity = resultDTO.getOrderDiscountsPriceOnlyActivity();
        if (null != couponDoList) {
            // 进行优惠券的金额分摊
            for (PromotionCouponDO promotionCouponDo : couponDoList) {
                // 用户优惠券
                PromotionCouponLoggerDO userCoupon = CollectionsUtil.findOneInList(promotionCouponLoggerDOList, val -> val.getCouponId().equals(promotionCouponDo.getId()));
                // 存储可以进行优惠券金额分摊的商品
                List<ActivityCommodityDTO> commodityList = new ArrayList<>();
                // 遍历活动下的商品
                for (ActivityResponseParamsDTO activityResponseParamsDTO : activityResponseParamsDTOList) {

                    // 预售活动的话修改商品的价格为预售活动金额
                    PreSalesCommodityDTO preSalesCommodityDTO = activityResponseParamsDTO.getPreSalesCommodityDTO();
                    if (!Objects.isNull(preSalesCommodityDTO)) {
                        activityResponseParamsDTO.getActivityCommodityDTOList().forEach(commodity -> commodity.setDetailPrice(preSalesCommodityDTO.getAllPayment().
                                setScale(2, BigDecimal.ROUND_HALF_UP).
                                divide(new BigDecimal(commodity.getSkuAmount()), 2))
                        );
                    }


                    Boolean activityFlag = this.couponLimit(activityConfigCache.get(activityResponseParamsDTO.getActivityId()));
                    if (!activityFlag) {
                        throw new ApplicationException("活动名称[" + activityResponseParamsDTO.getActivityName() + "]不能使用优惠券");
                    }
                    activityResponseParamsDTO.setOrderCouponDTOList(new ArrayList<>());
                    List<ActivityCommodityDTO> activityCommodityDTOList = activityResponseParamsDTO.getActivityCommodityDTOList();
                    //活动下的商品 和优惠券过滤
                    couponCommodityLimit(commodityList, activityCommodityDTOList, promotionCouponDo, userCoupon, activityResponseParamsDTO);
                }
                // 遍历没有活动的商品
                couponCommodityLimit(commodityList, notActivityCommodityDTO, promotionCouponDo, userCoupon, null);
                if (commodityList.isEmpty()) {
                    continue;
                }
                PromotionCouponLoggerDO conponUserDo = CollectionsUtil.findOneInList(promotionCouponLoggerDOList, val -> val.getCouponId().equals(promotionCouponDo.getId()));
                this.strategyCouponCalculate(commodityList, promotionCouponDo, conponUserDo, orderDiscountsPriceOnlyActivity);
            }
        }
    }

    @Override
    public void orderActivityCommodityMoneyGroup(OrderEditResponseDTO resultDTO) {
        List<ActivityResponseParamsDTO> activityResponseParamsDTOList = resultDTO.getActivityList();
        List<ActivityResponseParamsDTO> deletedFlag = new ArrayList<>();
        for (ActivityResponseParamsDTO activityResponseParamsDTO : activityResponseParamsDTOList) {


            List<ActivityCommodityDTO> orderHaveActivityCommodityDTO = activityResponseParamsDTO.getActivityCommodityDTOList();
            // 原始总金额汇总
            Double pricePreAll = CollectionsUtil.doubleGroup(orderHaveActivityCommodityDTO,
                    val -> Optional.ofNullable(val).map(vals ->
                            vals.getDetailPrice().multiply(new BigDecimal(vals.getSkuAmount())).doubleValue()
                    ).orElse(0.0));
            // 优惠后总金额
            Double priceDiscountAll = CollectionsUtil.doubleGroup(orderHaveActivityCommodityDTO,
                    val -> Optional.ofNullable(val).map(price -> {
                        // 最后优惠价格 等于 商品原价 减去 活动优惠和优惠券优惠
                        BigDecimal subtractPriceAll = Optional.ofNullable(price.getSubtractPriceAll()).orElse(BigDecimal.ZERO);
                        BigDecimal couponSubtractPriceAll = Optional.ofNullable(price.getCouponSubtractPriceAll()).orElse(BigDecimal.ZERO);
                        return price.getDetailPrice()
                                .multiply(new BigDecimal(price.getSkuAmount()))
                                .subtract(subtractPriceAll)
                                .subtract(couponSubtractPriceAll)
                                .doubleValue();
                    }).orElse(0.0));
            // 活动优惠
            Double activitySubtractAll = CollectionsUtil.doubleGroup(orderHaveActivityCommodityDTO,
                    val -> Optional.ofNullable(val.getSubtractPriceAll()).map(BigDecimal::doubleValue).orElse(0.0));
            // 优惠券优惠w
            Double couponSubtractAll = CollectionsUtil.doubleGroup(orderHaveActivityCommodityDTO,
                    val -> Optional.ofNullable(val.getCouponSubtractPriceAll()).map(BigDecimal::doubleValue).orElse(0.0));
            // 活动折扣
            double activityDiscountsAll =
                    Optional.of(orderHaveActivityCommodityDTO)
                            .map(val -> val.get(0))
                            .map(ActivityCommodityDTO::getDiscount)
                            .map(BigDecimal::doubleValue)
                            .orElse(0.0);
            // 优惠券折扣
            double couponDiscountsAll =
                    Optional.of(orderHaveActivityCommodityDTO)
                            .map(val -> val.get(0))
                            .map(ActivityCommodityDTO::getCouponDiscount)
                            .map(BigDecimal::doubleValue)
                            .orElse(0.0);
            activityResponseParamsDTO.setDetailPrice(BigDecimal.valueOf(pricePreAll).setScale(2, BigDecimal.ROUND_HALF_UP));
            activityResponseParamsDTO.setDiscountsPrice(BigDecimal.valueOf(priceDiscountAll).setScale(2, BigDecimal.ROUND_HALF_UP));
            activityResponseParamsDTO.setSubtractPrice(BigDecimal.valueOf(activitySubtractAll).setScale(2, BigDecimal.ROUND_HALF_UP));
            activityResponseParamsDTO.setCouponSubtractPrice(BigDecimal.valueOf(couponSubtractAll).setScale(2, BigDecimal.ROUND_HALF_UP));
            activityResponseParamsDTO.setCouponSubtractPrice(BigDecimal.valueOf(couponSubtractAll).setScale(2, BigDecimal.ROUND_HALF_UP));
            activityResponseParamsDTO.setDiscount(BigDecimal.valueOf(activityDiscountsAll).setScale(2, BigDecimal.ROUND_HALF_UP));
            activityResponseParamsDTO.setCouponDiscount(BigDecimal.valueOf(couponDiscountsAll).setScale(2, BigDecimal.ROUND_HALF_UP));

            // nn 打折 满减活动没有享受到策略的优惠 需放到没有商品里面
            if (activityResponseParamsDTO.getDetailPrice().equals(activityResponseParamsDTO.getDiscountsPrice())
                    && (StrategyGroupEnum.ZJRX_G.getId().equals(activityResponseParamsDTO.getPaTemplateId())
                    || StrategyGroupEnum.DZCX_G.getId().equals(activityResponseParamsDTO.getPaTemplateId())
                    || StrategyGroupEnum.MJS_G.getId().equals(activityResponseParamsDTO.getPaTemplateId()))) {
                List<ActivityCommodityDTO> commodityDTOList = activityResponseParamsDTO.getActivityCommodityDTOList();
                resultDTO.getNoActivityCommodityDTOList().addAll(commodityDTOList);
                deletedFlag.add(activityResponseParamsDTO);

            }
            // 买赠活动如果没有满足条件 需要把商品放到没有活动的里面
            if (activityResponseParamsDTO.getPaTemplateId().equals(StrategyGroupEnum.MZ_G.getId()) &&
                    CollectionUtil.isNotEmpty(activityResponseParamsDTO.getNoActivityCommodityDTOList())) {
                List<ActivityCommodityDTO> commodityDTOList = activityResponseParamsDTO.getNoActivityCommodityDTOList();
                resultDTO.getNoActivityCommodityDTOList().addAll(commodityDTOList);
                deletedFlag.add(activityResponseParamsDTO);
            }
        }
        activityResponseParamsDTOList.removeAll(deletedFlag);
    }

    @Override
    public void orderMoneyGroup(OrderEditResponseDTO resultDTO) {

        // 有活动的数组
        List<ActivityResponseParamsDTO> activityResponseParamsDTOList = resultDTO.getActivityList();
        // 没有活动的数组

        List<ActivityCommodityDTO> notActivityCommodityDTO = resultDTO.getNoActivityCommodityDTOList();
        // 有活动商品原始价格
        Double orderPriceActivity = CollectionsUtil.doubleGroup(activityResponseParamsDTOList,
                val -> Optional.ofNullable(val.getDetailPrice()).map(BigDecimal::doubleValue).orElse(0.0));
        // 有活动商品优惠后价格

        //todo 计算积分兑换是错误
        Double orderDiscountPriceActivity = CollectionsUtil.doubleGroup(activityResponseParamsDTOList,
                val -> Optional.ofNullable(val.getDiscountsPrice()).map(BigDecimal::doubleValue).orElse(0.0));
        // 有活动商品优惠券优惠了多少钱
        Double couponDiscountPriceActivity = CollectionsUtil.doubleGroup(activityResponseParamsDTOList,
                val -> Optional.ofNullable(val.getCouponSubtractPrice()).map(BigDecimal::doubleValue).orElse(0.0));


        // 无活动商品原始价格
        Double orderPriceNotActivity = CollectionsUtil.doubleGroup(
                Optional.ofNullable(notActivityCommodityDTO).orElse(new ArrayList<>()),
                vals -> Optional.ofNullable(vals).map(val -> val.getDetailPrice()
                        .multiply(new BigDecimal(val.getSkuAmount().toString()))
                        .setScale(2, BigDecimal.ROUND_HALF_UP))
                        .map(BigDecimal::doubleValue).orElse(0.0));
        // 无活动商品优惠后价格
        Double orderDiscountPriceNotActivity = CollectionsUtil.doubleGroup(
                Optional.ofNullable(notActivityCommodityDTO)
                        .orElse(new ArrayList<>()),
                vals -> Optional.ofNullable(vals)
                        .map(ActivityCommodityDTO::getDiscountsPriceAll)
                        .map(BigDecimal::doubleValue)
                        .orElse(vals.getDetailPrice() == null ? 0.0 : vals.getDetailPrice()
                                .multiply(new BigDecimal(String.valueOf(vals.getSkuAmount())))
                                .setScale(2, BigDecimal.ROUND_HALF_UP)
                                .doubleValue()));


        // 无活动商品优惠券优惠了多少钱
        Double couponNotActivity = CollectionsUtil.doubleGroup(
                Optional.ofNullable(notActivityCommodityDTO).orElse(new ArrayList<>()),
                val -> Optional.ofNullable(val.getCouponSubtractPriceAll()).map(BigDecimal::doubleValue).orElse(0.0));

        resultDTO.setActivityList(activityResponseParamsDTOList);
        resultDTO.setNoActivityCommodityDTOList(notActivityCommodityDTO);

        resultDTO.setOrderDetailPrice(BigDecimal.valueOf((orderPriceActivity + orderPriceNotActivity)).setScale(2, BigDecimal.ROUND_HALF_UP));
        resultDTO.setOrderDiscountsPrice(BigDecimal.valueOf((orderDiscountPriceActivity + orderDiscountPriceNotActivity)).setScale(2, BigDecimal.ROUND_HALF_UP));
        resultDTO.setCouponDiscountsPrice(BigDecimal.valueOf((couponDiscountPriceActivity + couponNotActivity)).setScale(2, BigDecimal.ROUND_HALF_UP));

    }

    private void couponCommodityLimit
            (List<ActivityCommodityDTO> commodityList, List<ActivityCommodityDTO> activityCommodityDTOList,
             PromotionCouponDO promotionCouponDo, PromotionCouponLoggerDO userCoupon,
             ActivityResponseParamsDTO activityResponseParamsDTO) {
        List<OrderCouponDTO> orderCouponDTOS = Optional.ofNullable(activityResponseParamsDTO)
                .map(ActivityResponseParamsDTO::getOrderCouponDTOList)
                .orElse(new ArrayList<>());
        // 遍历商品
        activityCommodityDTOList.forEach(commodity -> {
            Boolean couponFlag = this.couponLimitCalculate(commodity, userCoupon, promotionCouponDo);
            if (Boolean.TRUE.equals(couponFlag)) {
                commodityList.add(commodity);
                OrderCouponDTO clone = promotionCouponDo.clone(OrderCouponDTO.class);
                OrderCouponDTO oneInList = CollectionsUtil.findOneInList(orderCouponDTOS,
                        val -> val.getId().equals(clone.getId()));
                if (oneInList != null) {
                    return;
                }
                orderCouponDTOS.add(clone);
            }
        });
        if (null != activityResponseParamsDTO) {
            activityResponseParamsDTO.setOrderCouponDTOList(orderCouponDTOS);
        }
    }

    /**
     * 优惠券金额分摊
     *
     * @param activityCommodityDTOList 商品List
     * @param promotionCouponDo        优惠券do
     * @param promotionCouponLoggerDo  用户优惠券do
     * @param orderDiscountsPriceOnlyActivity 不含优惠券的订单总价
     */
    @Override
    public void strategyCouponCalculate(List<ActivityCommodityDTO> activityCommodityDTOList, PromotionCouponDO
            promotionCouponDo, PromotionCouponLoggerDO promotionCouponLoggerDo, BigDecimal orderDiscountsPriceOnlyActivity) {
        BigDecimal discount = promotionCouponDo.getCouponValue();
        //计算商品列表原始总价格
        BigDecimal totalMoney = activityCommodityDTOList.stream().map(this::calculateTotalMoney).reduce(new BigDecimal("0"), BigDecimal::add);
        //分摊金额

        // 商品金额不满足时
        if (Objects.nonNull(promotionCouponDo.getCondition()) && totalMoney.compareTo(promotionCouponDo.getCondition()) < 0) {
            return;
        }

        //折扣券金额分配
        if (CouponTypeEnum.DISCOUNT.getId().toString().equals(promotionCouponDo.getCouponType())) {
            setCouponDiscount(activityCommodityDTOList, discount, promotionCouponDo);
            //代金券金额分配
        } else if (CouponTypeEnum.MONEY.getId().toString().equals(promotionCouponDo.getCouponType())) {

            if (Objects.nonNull(discount) && orderDiscountsPriceOnlyActivity.compareTo(discount) <= 0) {
                //优惠券面额比订单活动后的总价大
                //案例原价100，活动优惠后90，优惠券100，则把优惠券值=90，不能用100的优惠券值来均摊
                log.info("{}优惠券面额{} 大于订单总额{}，优惠券面额变为订单总额", promotionCouponLoggerDo.getId(), discount, orderDiscountsPriceOnlyActivity);
                discount = orderDiscountsPriceOnlyActivity;
            }

            setCouponMoney(activityCommodityDTOList, discount, totalMoney, promotionCouponDo);
        } else {
            throw new ApplicationException("优惠券无效！");
        }


    }

    //计算原始总金额
    private BigDecimal calculateTotalMoney(ActivityCommodityDTO activityCommodityDTO) {
        return Arith.transformToBigDecimal(activityCommodityDTO.getDetailPrice().multiply(new BigDecimal(String.valueOf(activityCommodityDTO.getSkuAmount()))), 2);
    }

    //折扣券金额分配
    private void setCouponDiscount(List<ActivityCommodityDTO> activityCommodityDTOList, BigDecimal discount, PromotionCouponDO
            promotionCouponDo) {
        BigDecimal discountPer = discount.divide(new BigDecimal("10"), 2, BigDecimal.ROUND_HALF_UP);
        //遍历商品
        for (ActivityCommodityDTO commodity : activityCommodityDTOList) {
            //获得当前商品总原始价格
            BigDecimal totalMoney = calculateTotalMoney(commodity);
            //如果通过活动优惠后价格存在
            if (null != commodity.getDiscountsPriceAll() && !new BigDecimal("0").equals(commodity.getDiscountsPriceAll())) {
                totalMoney = commodity.getDiscountsPriceAll();
            }
            //获取优惠券价格
            BigDecimal couponMoney = Arith.transformToBigDecimal(totalMoney.multiply(discountPer), 2);
            //设置优惠折扣
            commodity.setCouponDiscount(discount);
            //设置优惠券优惠价格
            BigDecimal subtract = totalMoney.subtract(couponMoney);
            commodity.setCouponSubtractPriceAll(
                    Optional
                            .ofNullable(commodity.getCouponSubtractPriceAll())
                            .map(val -> val.add(subtract))
                            .orElse(subtract)
            );
            // 更新商品状态
            updataCommodity(commodity, couponMoney, promotionCouponDo);
        }
    }

    /**
     * 更新单个商品信息
     * @param commodity
     * @param currentSubMoney 本张优惠券获得的扣减金额
     * @param promotionCouponDO 本张优惠券
     **/
    private void updataCommodity(ActivityCommodityDTO commodity, BigDecimal currentSubMoney, PromotionCouponDO
                                  promotionCouponDO) {
        commodity.setDiscountsPriceAll(
                Optional
                        .ofNullable(commodity.getDiscountsPriceAll())
                        .map(val -> val.subtract(commodity.getCouponSubtractPriceAll()))
                        .orElse(commodity
                                .getDetailPrice()
                                .multiply(new BigDecimal(commodity.getSkuAmount().toString()))
                                .subtract(commodity.getCouponSubtractPriceAll())
                                .setScale(2, RoundingMode.HALF_DOWN))
        );

        if (currentSubMoney.doubleValue() > 0) {
            //添加优惠券信息到参加了均摊的商品里
            List<OrderCouponDTO> commodityCouponDTOList = Optional.ofNullable(commodity.getCommodityCouponDTOList()).orElse(Lists.newArrayList());
            commodityCouponDTOList.add(promotionCouponDO.clone(OrderCouponDTO.class, CloneDirection.FORWARD));
            commodity.setCommodityCouponDTOList(commodityCouponDTOList);
        }
    }

    //代金券金额分配
    private void setCouponMoney(List<ActivityCommodityDTO> activityCommodityDTOList, BigDecimal
            discount, BigDecimal totalMoney, PromotionCouponDO
            promotionCouponDo) {

        //活动商品size
        int size = activityCommodityDTOList.size();
        BigDecimal addTotalMoney = new BigDecimal("0");
        //遍历商品
        for (int i = 0; i < size; i++) {
            //活动商品总原始金额
            ActivityCommodityDTO commodity = activityCommodityDTOList.get(i);
            BigDecimal perTotalMoney = calculateTotalMoney(commodity);
            if (i == size - 1) {
                //最后一个时
                BigDecimal finalOne = discount.subtract(addTotalMoney);

                commodity.setCouponSubtractPriceAll(
                        Optional
                                .ofNullable(commodity.getCouponSubtractPriceAll())
                                .map(val -> val.add(finalOne))
                                .orElse(finalOne)
                );
                updataCommodity(commodity, finalOne, promotionCouponDo);
                break;
            }
            //当前优惠分摊
            BigDecimal couponPer = perTotalMoney.multiply(discount).divide(totalMoney, 2, RoundingMode.HALF_DOWN);
            //已分摊金额
            addTotalMoney = addTotalMoney.add(couponPer);
            commodity.setCouponSubtractPriceAll(
                    Optional
                            .ofNullable(commodity.getCouponSubtractPriceAll())
                            .map(val -> val.add(couponPer))
                            .orElse(couponPer)
            );
            updataCommodity(commodity, couponPer, promotionCouponDo);
        }
    }
}
