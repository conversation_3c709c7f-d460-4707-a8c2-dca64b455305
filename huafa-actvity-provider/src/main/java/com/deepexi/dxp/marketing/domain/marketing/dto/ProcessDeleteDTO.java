package com.deepexi.dxp.marketing.domain.marketing.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Class: ProcessDeleteDTO
 * @Description: 删除流程定义DTO
 * @Author: lizhongbao
 * @Date: 2020/3/31
 **/
@Data
@ApiModel
public class ProcessDeleteDTO implements Serializable {

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID", required = true)
    private String tenantId;

    @ApiModelProperty(value = "应用id", required = true)
    private Long appId;

    /**
     * 关联项ID
     */
    @ApiModelProperty(value = "自动营销ID/模型ID", required = true)
    private String itemId;
}
