package com.deepexi.dxp.marketing.domain.marketing.vo;


import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;


/**
 * 营销任务投放标签客群
 * @Author: HuangBo.
 * @Date: 2020/3/12 19:18
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MarketingActiveTargetTagsVO extends SuperVO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 选取类型（1:标签,2:客群）
     */
    @ApiModelProperty(value = "选取类型（1:标签,2:客群）")
    @NotNull(message = "选取的类型(标签或客群)不能为空")
    private Integer type;


    /**
     * 标签子类型1 印象 2、规则 3、偏好 4、客群 5、生命周期
     */
    @ApiModelProperty(value = "标签子类型1 印象 2、规则 3、偏好 4、客群 5、生命周期")
    private Integer subType;

    /**
     * 标签或客群ID
     */
    @ApiModelProperty(value = "标签或客群ID")
    @NotNull(message = "标签或客群ID不能为空")
    private Long refId;

    /**
     * 标签或客群姓名
     */
    @ApiModelProperty(value = "标签或客群姓名")
    private String refName;

    /**
     * 标签或客群对应人数
     */
    @ApiModelProperty(value = "标签或客群对应人数")
    private Integer memberNums;

    /**
     * 有效期类型
     */
    @ApiModelProperty(value = "有效期类型")
    private Integer validityType;

    /**
     * 有效期值
     */
    @ApiModelProperty(value = "有效期值")
    private Integer validityValue;

    /**
     * 过期时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date expiredTime;
}
