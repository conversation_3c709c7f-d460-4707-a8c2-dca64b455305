package com.deepexi.dxp.marketing.service.specify.impl;

import com.deepexi.dxp.marketing.config.HuafaConstantConfig;
import com.deepexi.dxp.marketing.constant.RedisConstants;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.TemplateSendRequestDTO;
import com.deepexi.dxp.marketing.service.specify.InteractionCenterService;
import com.deepexi.dxp.marketing.service.specify.PhoneService;
import com.deepexi.util.JsonUtil;
import com.deepexi.util.exception.ApplicationException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PhoneServiceImpl implements PhoneService {
    @Resource
    private HuafaConstantConfig huafaConstantConfig;
    @Resource
    private InteractionCenterService interactionCenterService;

    @Autowired
    private RedissonClient redissonClient;
    @Override
    public Boolean sendSmsCode(String phone,String smsCode) {
        if (Objects.isNull(phone) || Objects.isNull(smsCode)){
            throw new ApplicationException("手机号或验证码不能为空");
        }

        TemplateSendRequestDTO.Param param = new TemplateSendRequestDTO.Param();
        param.setCode(smsCode);

        TemplateSendRequestDTO requestDTO = new TemplateSendRequestDTO();
        requestDTO.setTemplateCode(huafaConstantConfig.VERIFICATION_CODE_TEMPLATE_ID);
        requestDTO.setMobile(phone);
        requestDTO.setTemplateParam(JsonUtil.bean2JsonString(param));
        interactionCenterService.templateSend(requestDTO);
        return true;
    }

    @Override
    public Boolean checkSmsCode(String phone, String smsCode) {
        if (Objects.isNull(phone)){
            throw new ApplicationException("手机号不能为空");
        }
        if (Objects.isNull(smsCode)){
            throw new ApplicationException("验证码不能为空");
        }
        String vCode = (String)redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_PHONE_VERIFY_CODE+phone).get();
        if(Objects.isNull(vCode)){
            throw new ApplicationException("验证码不存在或者已过期");
        }

        if (!Objects.equals(vCode, smsCode)){
            throw new ApplicationException("验证码不正确");
        }

        redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_PHONE_VERIFY_CODE+phone).delete();
        return true;
    }
}
