package com.deepexi.dxp.marketing.domain.marketing.vo;

import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 主动营销任务触达时机
 * @Author: HuangBo.
 * @Date: 2020/3/16 16:45
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MarketingActiveTouchTimeVO extends SuperVO {

  private Long id;

  /**
   * 主动营销任务
   */
  @ApiModelProperty(value = "主动营销任务ID")
  @NotNull(message = "主动营销任务ID不能为空")
  private Long activeId;

  /**
   * 立即执行(0:否，1:是)
   */

  @ApiModelProperty(value = "是否立即执行(0:否，1:是)")
  @NotNull(message = "是否立即执行不能为空")
  private Boolean immediate;

  /**
   * 执行时间
   */
  @ApiModelProperty(value = "执行时间")
  @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date touchTime;

  /**
   * 营销任务开始时间
   */
  @ApiModelProperty(value = "营销任务资源有效期开始时间")
  @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date validityStartTime;

  /**
   * 营销任务结束时间
   */
  @ApiModelProperty(value = "营销任务资源有效期结束时间")
  @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date validityEndTime;

}
