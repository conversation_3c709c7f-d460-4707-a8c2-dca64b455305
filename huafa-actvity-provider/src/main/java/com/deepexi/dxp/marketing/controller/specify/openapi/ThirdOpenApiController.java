package com.deepexi.dxp.marketing.controller.specify.openapi;

import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.RechargeMobileRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.SendIncentiveCallBackRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.SendIncentiveRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.IncentiveResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.RechargeMobileResponseDTO;
import com.deepexi.dxp.marketing.service.specify.IncentiveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 第三方相关接口
 * <AUTHOR>
 */
@RestController
@RequestMapping("/open-api/v1/open/third")
@Api(value = "第三方管理openApi-",tags = {"第三方管理模块openApi"})
public class ThirdOpenApiController {

    @Resource
    private IncentiveService incentiveService;
    /**
     * 发送红包激励
     */
    @ApiOperation("发送红包激励")
    @PostMapping("/sendIncentive")
    public Data<IncentiveResponseDTO> sendIncentive(@RequestBody SendIncentiveRequestDTO requestDTO) {
        return new Data<>(incentiveService.sendIncentive(requestDTO));
    }
    /**
     * 小程序红包回调
     */
    @ApiOperation("小程序红包回调")
    @PostMapping("/sendIncentiveCallBack")
    public Data<Boolean> sendIncentiveCallBack(@RequestBody @Valid SendIncentiveCallBackRequestDTO requestDTO) {
        return new Data<>(incentiveService.sendIncentiveCallBack(requestDTO));
    }

    /**
     * 话费充值方法
     * @param rechargeMobile 	请求流水号
     * @return 结果
     */
    @ApiOperation("话费充值方法")
    @PostMapping("/rechargeMobile")
    public Data<RechargeMobileResponseDTO> rechargeMobile(@RequestBody RechargeMobileRequestDTO rechargeMobile) {
        return new Data<>(incentiveService.rechargeMobile(rechargeMobile));
    }


    /**
     * 话费充值回调方法
     * @param reqNo 	请求流水号
     * @param result    充值结果 : 0充值中 1成功 9撤销/失败
     * @param state     状态码
     * @return 结果
     */
    @ApiOperation("话费充值回调方法")
    @GetMapping("/moneyCallBack")
    public Data<Boolean> moneyCallBack(@RequestParam String reqNo,@RequestParam String result,@RequestParam String state) {
        return new Data<>(incentiveService.moneyCallBack(reqNo,result,state));
    }

}
