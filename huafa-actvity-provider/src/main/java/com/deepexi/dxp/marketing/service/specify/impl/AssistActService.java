package com.deepexi.dxp.marketing.service.specify.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.deepexi.dxp.marketing.common.base.utils.CommonExceptionCode;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.constant.RedisConstants;
import com.deepexi.dxp.marketing.converter.ActivityInfoConverter;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityFissionLogQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.*;
import com.deepexi.dxp.marketing.domain.marketing.response.PromotionActivityDetailDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.specify.dto.ActivityFormFeedbackDTO;
import com.deepexi.dxp.marketing.enums.activity.limit.PATemplateBaseEnum;
import com.deepexi.dxp.marketing.enums.coupon.WhetherEnum;
import com.deepexi.dxp.marketing.enums.resource.ActivityStatusEnum;
import com.deepexi.dxp.marketing.enums.specify.*;
import com.deepexi.dxp.marketing.manager.promotion.PromotionActivityManager;
import com.deepexi.dxp.marketing.service.specify.AsynchronousService;
import com.deepexi.dxp.marketing.service.specify.IncentiveService;
import com.deepexi.dxp.marketing.utils.GenerateIdUtil;
import com.deepexi.dxp.middle.promotion.converter.specify.BargainingConverter;
import com.deepexi.dxp.middle.promotion.converter.specify.LuckyDrawConverter;
import com.deepexi.dxp.middle.promotion.dao.PromotionActivityDAO;
import com.deepexi.dxp.middle.promotion.dao.PromotionActivityLimitDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.*;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityLimitDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.*;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.StringUtil;
import com.deepexi.util.exception.ApplicationException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 助力活动模板
 * @date 2022/10/24 16:47
 */
@Service
@Slf4j
public class AssistActService {


    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private GenerateIdUtil generateIdUtil;
    @Resource
    public AsynchronousService asynchronousService;
    @Resource
    private ActivityPartakeLogDAO activityPartakeLogDAO;
    @Autowired
    private CustomerFeedbackDAO customerFeedbackDAO;
    @Autowired
    private PromotionActivityDAO promotionActivityDAO;
    @Resource
    public ActivityVerifyDAO activityVerifyDAO;
    @Autowired
    private ActivityFissionAssistLogDAO activityFissionAssistLogDAO;
    @Resource
    private ActivityFissionLogDAO activityFissionLogDAO;
    @Resource
    private PromotionHisResourceDAO promotionHisResourceDAO;

    @Resource
    private ActivityFissionAssistResourceDAO activityFissionAssistResourceDAO;

    @Resource
    public ActivityUserRelatedDAO activityUserRelatedDAO;
    @Resource
    private PromotionActivityManager promotionActivityManager;

    @Resource
    private PromotionActivityLimitDAO promotionActivityLimitDAO;

    @Autowired
    private IncentiveService incentiveService;
    @Transactional
    public Data<Object> partakeAct(ActivityPartakeRequest requestVo, ActivityUserRelatedDTO activityUserRelated, List<PromotionActivityLimitDO> promotionActivityLimitList) {
        //发起者
        if (UserJoinTypeEnum.JOIN_TYPE_1.getId().equals(requestVo.getUserJoinType())) {
            //查询用户用户助力信息
            ActivityPartakeLogDTO partakeLogDTO = activityPartakeLogDAO.getList(requestVo.getActivityId(), requestVo.getUserId(), FissonStatusEnum.PROCESSING.getId(), FissonTypeEnum.Boost.getId());
            if (partakeLogDTO != null) {
                throw new ApplicationException(CommonExceptionCode.INVALIDATION, "存在未结束的助力活动，请不要重复发起！");
            }
            //较验是否存在未领取的奖品
            if (activityFissionAssistResourceDAO.checkActivityFissionAssistResource(requestVo.getPhone(), requestVo.getActivityId())) {
                throw new ApplicationException(CommonExceptionCode.INVALIDATION, "存在未领取奖品，请领完再发起助力活动！");
            }

            //发起助力次数校验,
            int launchTimes = 0;
            if (activityUserRelated != null) {
                launchTimes = (int) activityUserRelated.getLimits().get(ActivityTemplateNumberEnum.LAUNCH_TIMES.getId());
            }

            if (launchTimes <= 0) {
                throw new ApplicationException(CommonExceptionCode.INVALIDATION, "您当前剩余发起助力次数为0，不可继续发起助力！");
            }
            List<PromotionHisResourceDO> fissionResourceList = promotionHisResourceDAO.findByFissonResourceType(requestVo.getActivityId(), FissonResourceTypeEnum.POWER_LADDER.getId());
            //较验各阶梯库存是否足够
            this.checkLadderFissionResource(fissionResourceList);

            ActivityPartakeLogDO activityPartakeLogDO = BargainingConverter.partakeLogConverter(requestVo, promotionActivityLimitList, PATemplateBaseEnum.ASSIST.getId(), generateIdUtil.getResourceCode());
            //所有阶梯人数即所需助力人数
            Integer fissonTotalCount = fissionResourceList.stream().map(PromotionHisResourceDO::getFissonCount).reduce(0, Integer::sum);
            activityPartakeLogDO.setNeedFissonCount(fissonTotalCount);

            activityPartakeLogDAO.save(activityPartakeLogDO);

            //更新当前活动周期内的单个用户的发起助力次数
            ActivityUserRelatedDO activityUserRelatedDO = new ActivityUserRelatedDO();
            activityUserRelatedDO.setId(activityUserRelated.getId());

            activityUserRelated.getLimits().put(ActivityTemplateNumberEnum.LAUNCH_TIMES.getId(), launchTimes - 1);
            activityUserRelatedDO.setLimits(activityUserRelated.getLimits());
            activityUserRelatedDAO.updateById(activityUserRelatedDO);

        } else if (UserJoinTypeEnum.JOIN_TYPE_2.getId().equals(requestVo.getUserJoinType())) {
            //参与者
            ActivityPartakeLogDO partakeLog = activityPartakeLogDAO.getById(requestVo.getSponsorId());
            if (partakeLog == null) {
                throw new ApplicationException(CommonExceptionCode.INVALIDATION, "助力信息不存在");
            } else if (partakeLog.getPhone().equals(requestVo.getPhone())) {
                throw new ApplicationException(CommonExceptionCode.INVALIDATION, "自己不能参与自己发起的助力活动");
            } else if (ObjectUtil.equal(partakeLog.getFissonStatus(), FissonStatusEnum.FAILURE.getId())) {
                throw new ApplicationException(CommonExceptionCode.INVALIDATION, "助力活动已结束");
            }
            if (partakeLog.getFissonEndTime().before(DateTime.now())) {
                throw new ApplicationException(CommonExceptionCode.INVALIDATION, "该助力有效期已截止");
            }
            ActivityFissionLogQuery activityFissionLogQuery = new ActivityFissionLogQuery();
            activityFissionLogQuery.setActivityId(partakeLog.getActivityId());
            activityFissionLogQuery.setPhone(requestVo.getPhone());
            Integer count = activityFissionLogDAO.getCount(activityFissionLogQuery);

            PromotionActivityLimitDO limitDO = promotionActivityLimitList
                    .stream().filter(e -> e.getType().equals(PATemplateBaseEnum.ASSIST.getId()))
                    .findFirst()
                    .orElse(null);
            List<BaseActivityDTO> numberList = JSON.parseArray(limitDO.getLimits(), BaseActivityDTO.class);
            Integer helpTimes = BargainingConverter.elLimitConverter(numberList, ActivityTemplateNumberEnum.HELP_TIMES.getId());
            if (count >= helpTimes) {
                throw new ApplicationException(CommonExceptionCode.INVALIDATION, "每个好友的助力活动只能助力:" + helpTimes + "次");
            }
            activityFissionLogQuery.setPartakeLogId(partakeLog.getId());
            count = activityFissionLogDAO.getCount(activityFissionLogQuery);
            if (count >= 1) {
                throw new ApplicationException(CommonExceptionCode.INVALIDATION, "当前活动您已经助力过了!");
            }
            //助力活动成功的记录参与记录并更新参与人数
            if (ObjectUtil.equal(partakeLog.getFissonStatus(), FissonStatusEnum.SUCCESS.getId())) {
                partakeLog.setCurrentFissonCount(partakeLog.getCurrentFissonCount() + 1);
                activityPartakeLogDAO.updateById(partakeLog);
                activityFissionLogDAO.save(BargainingConverter.activityFissionLogConverter(requestVo, 0, partakeLog));
                return new Data<>(Boolean.TRUE);
            }
            String resourceName = this.participantAssist(requestVo, partakeLog);
            return StringUtil.isNotEmpty(resourceName) ? new Data<>("恭喜获得奖品:" + resourceName + "，请到我的礼品查看！") : new Data<>(null);
        }
        return new Data<>(null);
    }

    /**
     * 较验助力资源库存
     *
     * @param fissionResourceList
     */
    private void checkLadderFissionResource(List<PromotionHisResourceDO> fissionResourceList) {
        if (CollectionUtil.isEmpty(fissionResourceList)) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "助力活动库存异常!");
        }
        //判断是否存在库存不足的阶梯
        boolean isCan = false;
        for (PromotionHisResourceDO item : fissionResourceList) {
            Long currentCount = redissonClient.getAtomicLong(RedisConstants.CACHE_PREV_KEY_ACT_RESOURCE_INFO_COUNT + item.getId()).get();
            if (currentCount > 0) {
                isCan = true;
                break;
            }
        }
        if (!isCan) {
            log.info("===============库存不足===============activityId:{}", fissionResourceList.get(0).getActivityId());
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "本次活动奖品都被抢光啦，去看看其他活动吧~");
        }
    }

    /**
     * 参与者助力
     *
     * @param partakeLog
     */
    private String participantAssist(ActivityPartakeRequest requestVo, ActivityPartakeLogDO partakeLog) {
        //好友参与记录
        activityFissionLogDAO.save(BargainingConverter.activityFissionLogConverter(requestVo, 0, partakeLog));

        List<PromotionHisResourceDO> fissonResourceList = promotionHisResourceDAO.findByFissonResourceType(requestVo.getActivityId(), FissonResourceTypeEnum.POWER_LADDER.getId());
        //更新已助力人数
        ActivityPartakeLogDO activityPartakeLogDO = new ActivityPartakeLogDO();
        activityPartakeLogDO.setId(partakeLog.getId());
        activityPartakeLogDO.setCurrentFissonCount(partakeLog.getCurrentFissonCount() + 1);
        if (activityPartakeLogDO.getCurrentFissonCount() >= partakeLog.getNeedFissonCount() && partakeLog.getFissonEndTime().after(DateTime.now())) {
            activityPartakeLogDO.setFissonStatus(FissonStatusEnum.SUCCESS.getId());//达到所有人数且助力活动未结束

            //裂变活动成功人数神策埋点
            asynchronousService.sensorsBuriedPointFission(activityPartakeLogDO.getId(), SensorsEventEnum.BOOST_SUCCESS.getCode());
        }
        activityPartakeLogDAO.updateById(activityPartakeLogDO);


        Map<Integer, PromotionHisResourceDO> resourceDOMap = fissonResourceList.stream()
                .collect(Collectors.toMap(PromotionHisResourceDO::getLadderSort, Function.identity()));
        for (int i = 1; i <= resourceDOMap.size(); i++) {
            int sorNum = i;
            Integer fissionNum = fissonResourceList.stream().filter(item -> item.getLadderSort() <= sorNum).map(PromotionHisResourceDO::getFissonCount).reduce(0, Integer::sum);
            createActivityFissionAssistResource(fissionNum, sorNum, resourceDOMap.get(i), partakeLog);
        }
        //参与者助力获奖
        return this.assistGetResource(requestVo);
    }

    /**
     * 参与助力获奖
     *
     * @param requestVo
     * @return
     */
    private String assistGetResource(ActivityPartakeRequest requestVo) {
        //查询活动关联奖品及奖品规则
        List<PromotionActivityLimitDO> promotionActivityLimits = promotionActivityLimitDAO.selectByActivityId(requestVo.getActivityId());
        if (CollectionUtil.isEmpty(promotionActivityLimits)) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "活动规则未配置");
        }

        PromotionActivityLimitDO limitDO = promotionActivityLimits
                .stream().filter(e -> e.getType().equals(PATemplateBaseEnum.ASSIST.getId()))
                .findFirst()
                .orElse(null);
        if (limitDO == null) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "活动规则未配置");
        }
        List<BaseActivityDTO> numberList = JSON.parseArray(limitDO.getLimits(), BaseActivityDTO.class);
        Integer isOpenHelpAwards = LuckyDrawConverter.elLimitConverter(numberList, ActivityTemplateNumberEnum.HELP_ISOPEN_HELP_AWARDS.getId());

        //开启助力获奖
        if (WhetherEnum.YES.getId().equals(isOpenHelpAwards)) {
            //查询奖品信息
            List<PromotionHisResourceDO> hisResourceList = promotionHisResourceDAO.findByFissonResourceType(requestVo.getActivityId(), FissonResourceTypeEnum.HELP_WIN_AWARDS.getId());
            if (CollectionUtil.isNotEmpty(hisResourceList)) {
                PromotionHisResourceDO promotionHisResourceDO = hisResourceList.get(0);
                //查询相同活动是否已参与过了并获奖
                List<ActivityFissionAssistLogDO> fissionAssistLogList = activityFissionAssistLogDAO.findList(requestVo.getSponsorId(), requestVo.getPhone(), requestVo.getActivityId(), FissonResourceTypeEnum.HELP_WIN_AWARDS.getId());
                if (CollectionUtil.isEmpty(fissionAssistLogList)) {
                    PromotionActivityDO byId = promotionActivityDAO.getById(requestVo.getActivityId());
                    return this.createActivityHelpFissionAssistLog(promotionHisResourceDO, requestVo, FissonResourceTypeEnum.HELP_WIN_AWARDS.getId(), byId.getName());
                }
            }
        }
        return null;
    }

    /**
     * 此为帮忙助力获奖
     *
     * @param promotionHisResourceDO
     * @param requestVo
     * @return
     */
    private String createActivityHelpFissionAssistLog(PromotionHisResourceDO promotionHisResourceDO, ActivityPartakeRequest requestVo, Integer type, String activityName) {
        if (promotionHisResourceDO.getRemainingQuantity() > 0) {
            boolean flag = promotionActivityManager.decrRedisQty(promotionHisResourceDO.getId(), requestVo.getActivityId());
            if (flag) {
                try {
                    String resourceCode = generateIdUtil.getResourceCode();
                    //数据库-更新剩余资源数量
                    promotionActivityManager.decrRemainQty(promotionHisResourceDO.getId(), 1L);

                    ActivityPartakeLogDO activityPartakeLogDO = activityPartakeLogDAO.getById(requestVo.getSponsorId());


                    //获取信息登记
                    ActivityFormFeedbackDTO activityFormFeedbackDTO = customerFeedbackDAO.getActivityFormFeedUserId(requestVo.getActivityId(), requestVo.getPhone(), null);
                    if (Objects.isNull(activityFormFeedbackDTO)) {
                        activityFormFeedbackDTO = requestVo.clone(ActivityFormFeedbackDTO.class);
                    }
                    if (PromotionResourceTypeEnum.THIRD_PARTY.getId().equals(promotionHisResourceDO.getType())) {
                        //第三方核销
                        requestVo.setProjectId(promotionHisResourceDO.getProjectId());
                        requestVo.setProjectName(promotionHisResourceDO.getProjectName());
                        incentiveService.thirdVerify(requestVo, promotionHisResourceDO, activityFormFeedbackDTO, activityName, resourceCode, SendTemplateNewsRequestDTO.MP_FROM_USH);
                    } else {
                        ActivityOrderDO activityOrderDO = new ActivityOrderDO();
                        activityOrderDO.setType(requestVo.getType());
                        activityOrderDO.setActivityId(requestVo.getActivityId());
                        activityOrderDO.setUserId(requestVo.getUserId());
                        activityOrderDO.setPhone(requestVo.getPhone());//add by 0713
                        activityOrderDO.setId(0L);
                        //添加参与记录-分享
                        ShareLuckyDrawRequestDTO shareLuckyDrawRequestDTO = requestVo.clone(ShareLuckyDrawRequestDTO.class);
                        shareLuckyDrawRequestDTO.setPartakeLogId(requestVo.getSponsorId());
                        ActivityFissionAssistLogDO activityFissionAssistLogDO = BargainingConverter.ActivityFissionAssistLogConverter(promotionHisResourceDO, shareLuckyDrawRequestDTO, type);
                        if (activityFissionAssistLogDO.getAppId() == null) {
                            activityFissionAssistLogDO.setAppId(promotionHisResourceDO.getAppId());
                        }
                        if (StringUtil.isEmpty(activityFissionAssistLogDO.getTenantId())) {
                            activityFissionAssistLogDO.setTenantId(promotionHisResourceDO.getTenantId());
                        }
                        //驳回时可取回原来的项目信息
                        activityFissionAssistLogDO.setCode(resourceCode);
                        activityFissionAssistLogDO.setProjectId(activityPartakeLogDO.getProjectId());
                        activityFissionAssistLogDO.setProjectName(activityPartakeLogDO.getProjectName());
                        activityFissionAssistLogDAO.save(activityFissionAssistLogDO);

                        //添加核销记录
                        activityOrderDO.setPayTime(DateTime.now());
                        Map<String, Object> projectInfo = new HashMap<>();
                        if (StringUtil.isNotEmpty(activityPartakeLogDO.getProjectId()) && StringUtil.isNotEmpty(activityPartakeLogDO.getProjectName())) {
                            projectInfo.put("projectId", activityPartakeLogDO.getProjectId());
                            projectInfo.put("projectName", activityPartakeLogDO.getProjectName());
                        }
                        activityOrderDO.setExt(projectInfo);
                        //分享没有项目信息
                        ActivityVerifyDO activityVerifyDO = ActivityInfoConverter.verifyConverter(activityOrderDO, activityFormFeedbackDTO, promotionHisResourceDO, resourceCode, VerifyStatusEnum.NO_VERIFY.getId(), WhetherEnum.NO.getId());
                        if (activityVerifyDO.getAppId() == null) {
                            activityVerifyDO.setAppId(promotionHisResourceDO.getAppId());
                        }
                        if (StringUtil.isEmpty(activityVerifyDO.getTenantId())) {
                            activityVerifyDO.setTenantId(promotionHisResourceDO.getTenantId());
                        }
                        activityVerifyDAO.save(activityVerifyDO);
                    }
                    return promotionHisResourceDO.getName();
                } catch (Exception e) {
                    promotionActivityManager.incrRedisQty(promotionHisResourceDO.getId(), requestVo.getActivityId());
                    throw e;
                }
            }
        }
        return null;
    }

    //发起者获奖
    public Boolean initiatorFissionAssistRecive(ActivityFissionAssistResourceDO activityFissionAssistResourceDO, PromotionHisResourceDO promotionHisResourceDO, String projectName, String projectId, String activityName, StringBuilder sb) {
        ActivityPartakeRequest activityPartakeRequest = activityFissionAssistResourceDO.clone(ActivityPartakeRequest.class);
        activityPartakeRequest.setSponsorId(activityFissionAssistResourceDO.getPartakeLogId());
        String activityHelpFissionAssistLog = initiatorFissionAssistLog(promotionHisResourceDO, activityPartakeRequest, FissonResourceTypeEnum.POWER_LADDER.getId(), activityName, sb);
        if (StringUtil.isNotEmpty(activityHelpFissionAssistLog)) {
            activityFissionAssistResourceDO.setReceived(1);
            activityFissionAssistResourceDAO.updateById(activityFissionAssistResourceDO);
        }
        return StringUtil.isNotEmpty(activityHelpFissionAssistLog) ? Boolean.TRUE : Boolean.FALSE;
    }
    //助力发起者获奖
    private String initiatorFissionAssistLog(PromotionHisResourceDO promotionHisResourceDO, ActivityPartakeRequest requestVo, Integer type, String activityName, StringBuilder sb) {
        if (promotionHisResourceDO.getRemainingQuantity() > 0) {
            boolean flag = promotionActivityManager.decrRedisQty(promotionHisResourceDO.getId(), requestVo.getActivityId());
            if (flag) {
                try {
                    String resourceCode = generateIdUtil.getResourceCode();
                    //数据库-更新剩余资源数量
                    promotionActivityManager.decrRemainQty(promotionHisResourceDO.getId(), 1L);
                    ActivityPartakeLogDO activityPartakeLogDO = activityPartakeLogDAO.getById(requestVo.getSponsorId());

                    //获取信息登记
                    ActivityFormFeedbackDTO activityFormFeedbackDTO = customerFeedbackDAO.getActivityFormFeedUserId(requestVo.getActivityId(), requestVo.getPhone(), null);
                    if (Objects.isNull(activityFormFeedbackDTO)) {
                        activityFormFeedbackDTO = requestVo.clone(ActivityFormFeedbackDTO.class);
                        activityFormFeedbackDTO.setNickName(activityPartakeLogDO.getNickName());
                        activityFormFeedbackDTO.setUserName(activityPartakeLogDO.getUserName());
                        activityFormFeedbackDTO.setPhone(activityPartakeLogDO.getPhone());
                    }
                    if (StringUtil.isEmpty(activityFormFeedbackDTO.getNickName())) {
                        activityFormFeedbackDTO.setNickName(activityPartakeLogDO.getNickName());
                    }
                    if (PromotionResourceTypeEnum.THIRD_PARTY.getId().equals(promotionHisResourceDO.getType())) {
                        //第三方核销
                        requestVo.setProjectId(promotionHisResourceDO.getProjectId());
                        requestVo.setProjectName(promotionHisResourceDO.getProjectName());
                        incentiveService.thirdVerify(requestVo, promotionHisResourceDO, activityFormFeedbackDTO, activityName, resourceCode, SendTemplateNewsRequestDTO.MP_FROM_USH);
                    } else {
                        ActivityOrderDO activityOrderDO = new ActivityOrderDO();
                        activityOrderDO.setType(requestVo.getType());
                        activityOrderDO.setActivityId(requestVo.getActivityId());
                        activityOrderDO.setUserId(requestVo.getUserId());
                        activityOrderDO.setPhone(requestVo.getPhone());//add by 0713
                        activityOrderDO.setId(0L);
                        //添加参与记录-分享
                        ShareLuckyDrawRequestDTO shareLuckyDrawRequestDTO = requestVo.clone(ShareLuckyDrawRequestDTO.class);
                        shareLuckyDrawRequestDTO.setPartakeLogId(requestVo.getSponsorId());
                        ActivityFissionAssistLogDO activityFissionAssistLogDO = BargainingConverter.ActivityFissionAssistLogConverter(promotionHisResourceDO, shareLuckyDrawRequestDTO, type);
                        if (activityFissionAssistLogDO.getAppId() == null) {
                            activityFissionAssistLogDO.setAppId(promotionHisResourceDO.getAppId());
                        }
                        if (StringUtil.isEmpty(activityFissionAssistLogDO.getTenantId())) {
                            activityFissionAssistLogDO.setTenantId(promotionHisResourceDO.getTenantId());
                        }
                        activityFissionAssistLogDO.setCode(resourceCode);
                        activityFissionAssistLogDO.setProjectId(activityPartakeLogDO.getProjectId());
                        activityFissionAssistLogDO.setProjectName(activityPartakeLogDO.getProjectName());
                        activityFissionAssistLogDAO.save(activityFissionAssistLogDO);

                        //添加核销记录
                        activityOrderDO.setPayTime(DateTime.now());
                        Map<String, Object> projectInfo = new HashMap<>();
                        if (StringUtil.isNotEmpty(activityPartakeLogDO.getProjectId()) && StringUtil.isNotEmpty(activityPartakeLogDO.getProjectName())) {
                            projectInfo.put("projectId", activityPartakeLogDO.getProjectId());
                            projectInfo.put("projectName", activityPartakeLogDO.getProjectName());
                        }
                        activityOrderDO.setExt(projectInfo);
                        //分享没有项目信息
                        ActivityVerifyDO activityVerifyDO = ActivityInfoConverter.verifyConverter(activityOrderDO, activityFormFeedbackDTO, promotionHisResourceDO, resourceCode, VerifyStatusEnum.NO_VERIFY.getId(), WhetherEnum.NO.getId());
                        if (activityVerifyDO.getAppId() == null) {
                            activityVerifyDO.setAppId(promotionHisResourceDO.getAppId());
                        }
                        if (StringUtil.isEmpty(activityVerifyDO.getTenantId())) {
                            activityVerifyDO.setTenantId(promotionHisResourceDO.getTenantId());
                        }
                        activityVerifyDAO.save(activityVerifyDO);
                    }
                    sb.append(resourceCode).append(",");
                    return promotionHisResourceDO.getName();
                } catch (Exception e) {
                    promotionActivityManager.incrRedisQty(promotionHisResourceDO.getId(), requestVo.getActivityId());
                    throw e;
                }
            } else {
                throw new ApplicationException(CommonExceptionCode.INVALIDATION, "来晚一步哦，奖品被抢光啦~");
            }
        } else {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "来晚一步哦，奖品被抢光啦~");
        }
    }

    /**
     * 生成助力可领取资源
     *
     * @param ladder                 阶梯编号
     * @param promotionHisResourceDO 当前阶梯资源
     * @param partakeLog             助力活动
     */
    private void createActivityFissionAssistResource(Integer fissionNum, Integer ladder, PromotionHisResourceDO promotionHisResourceDO, ActivityPartakeLogDO partakeLog) {
        //查询当前阶梯已分配可领取资源
        ActivityFissionAssistResourceDO activityFissionAssistResourceDO = activityFissionAssistResourceDAO.getByLadderSort(ladder, partakeLog.getId(), promotionHisResourceDO.getId());

        //计算达到当前阶梯所需助力人数
        if (Objects.isNull(activityFissionAssistResourceDO) && fissionNum <= partakeLog.getCurrentFissonCount() + 1) {
            //添加可领取记录
            activityFissionAssistResourceDAO.save(BargainingConverter.activityFissionAssistResourceConverter(partakeLog, promotionHisResourceDO));
        }
    }

    /**
     * 助力分享
     * @param dto
     * 1、分享出去后，是否就立即跳转至信息登记页去领取奖品？
     * 2、我已帮别人助力至进度30%，这时候，会跳转至信息登记页然后去领取奖品吗？
     * >>>
     * 分享是发起人自己分享，不用再填写信息了，分享完系统自动发放礼品并弹窗提示“分享成功，系统已自动奖励XXXX，
     * 请到我的礼品查看”，限制发一次
     * 帮忙助力也是，授权完系统自动发放礼品并弹窗提示“帮忙助力成功，系统已自动奖励XX，请到我的礼品查看”
     * @return
     */
    public String share(ShareLuckyDrawRequestDTO dto,PromotionActivityDetailDTO activity){
        if(StringUtils.isEmpty(dto.getPhone())){
            throw new ApplicationException(CommonExceptionCode.INVALIDATION,"手机号不能为空!");
        }

        //查询活动关联奖品及奖品规则
        List<PromotionActivityLimitDO> promotionActivityLimits = promotionActivityLimitDAO.selectByActivityId(dto.getActivityId());
        if (CollectionUtil.isEmpty(promotionActivityLimits)){
            throw new ApplicationException(CommonExceptionCode.INVALIDATION,"活动规则未配置");
        }

        PromotionActivityLimitDO limitDO = promotionActivityLimits
                .stream().filter(e -> e.getType().equals(PATemplateBaseEnum.ASSIST.getId()))
                .findFirst()
                .orElse(null);
        if (limitDO == null){
            throw new ApplicationException(CommonExceptionCode.INVALIDATION,"活动规则未配置");
        }
        List<BaseActivityDTO> numberList = JSON.parseArray(limitDO.getLimits(), BaseActivityDTO.class);
        Integer isOpenShareAwards = LuckyDrawConverter.elLimitConverter(numberList, ActivityTemplateNumberEnum.HELP_ISOPEN_SHARE_AWARDS.getId());

        //开启分享获奖 且活动进行中才可以获取奖品
        if(WhetherEnum.YES.getId().equals(isOpenShareAwards) && activity.getStatus().equals(Integer.parseInt(ActivityStatusEnum.IN_PROGRESS.getId()))){
            //查询奖品信息
            List<PromotionHisResourceDO> hisResourceList = promotionHisResourceDAO.findByFissonResourceType(dto.getActivityId(),FissonResourceTypeEnum.SHARE_AWARDS.getId());
            if(CollectionUtil.isNotEmpty(hisResourceList)){
                PromotionHisResourceDO promotionHisResourceDO = hisResourceList.get(0);
                //查询相同活动是否已参与过了
                List<ActivityFissionAssistLogDO> fissionAssistLogList = activityFissionAssistLogDAO.findList(null, dto.getPhone(), dto.getActivityId(), FissonResourceTypeEnum.SHARE_AWARDS.getId());
                if(CollectionUtil.isEmpty(fissionAssistLogList)){
                    String resuourceName = this.createActivityFissionAssistLog(promotionHisResourceDO, dto, activity.getActivityName());
                    return StringUtil.isNotEmpty(resuourceName)?"恭喜获得奖品："+resuourceName+"，请到我的礼品查看！":null;
                }
            }
        }
        return null;
    }

    /**
     * 创建分享获奖记录
     * @param promotionHisResourceDO
     * @param dto
     */
    private  String createActivityFissionAssistLog(PromotionHisResourceDO promotionHisResourceDO,ShareLuckyDrawRequestDTO dto,String activityName){
        if(promotionHisResourceDO.getRemainingQuantity() > 0){
            boolean flag = promotionActivityManager.decrRedisQty(promotionHisResourceDO.getId(), dto.getActivityId());
            if(flag){
                try{
                    String resourceCode = generateIdUtil.getResourceCode();
                    //数据库-更新剩余资源数量
                    promotionActivityManager.decrRemainQty(promotionHisResourceDO.getId(),1L);
                    String projectName = "";
                    String projectId = "";
                    if(dto.getPartakeLogId() != null){
                        ActivityPartakeLogDO activityPartakeLogDO = activityPartakeLogDAO.getById(dto.getPartakeLogId());
                        if(Objects.nonNull(activityPartakeLogDO)){
                            projectName = activityPartakeLogDO.getProjectName();
                            projectId = activityPartakeLogDO.getProjectId();
                        }
                    }

                    //获取信息登记
                    ActivityFormFeedbackDTO activityFormFeedbackDTO = customerFeedbackDAO.getActivityFormFeedUserId(dto.getActivityId(),dto.getPhone(),promotionHisResourceDO.getId());
                    if(Objects.isNull(activityFormFeedbackDTO)){
                        activityFormFeedbackDTO = dto.clone(ActivityFormFeedbackDTO.class);
                    }

                    if(PromotionResourceTypeEnum.THIRD_PARTY.getId().equals(promotionHisResourceDO.getType())){
                        //第三方核销
                        ActivityPartakeRequest requestVo = dto.clone(ActivityPartakeRequest.class);
                        requestVo.setProjectId(promotionHisResourceDO.getProjectId());
                        requestVo.setProjectName(promotionHisResourceDO.getProjectName());
                        incentiveService.thirdVerify(requestVo, promotionHisResourceDO, activityFormFeedbackDTO, activityName, resourceCode, SendTemplateNewsRequestDTO.MP_FROM_USH);
                    }else {
                        ActivityOrderDO activityOrderDO = new ActivityOrderDO();
                        activityOrderDO.setType(dto.getType());
                        activityOrderDO.setActivityId(dto.getActivityId());
                        activityOrderDO.setUserId(dto.getUserId());
                        activityOrderDO.setPhone(dto.getPhone());//add by 0713
                        activityOrderDO.setId(0L);


                        //添加核销记录
                        activityOrderDO.setPayTime(DateTime.now());
                        Map<String, Object> projectInfo = new HashMap<>();
                        if(StringUtil.isNotEmpty(projectName) && StringUtil.isNotEmpty(projectId)){
                            projectInfo.put("projectId",projectId);
                            projectInfo.put("projectName",projectName);
                        }
                        activityOrderDO.setExt(projectInfo);
                        //分享没有项目信息
                        ActivityVerifyDO activityVerifyDO = ActivityInfoConverter.verifyConverter(activityOrderDO,activityFormFeedbackDTO, promotionHisResourceDO, resourceCode, VerifyStatusEnum.NO_VERIFY.getId(), WhetherEnum.NO.getId());
                        activityVerifyDAO.save(activityVerifyDO);
                    }
                    //添加参与记录-分享，以此判断是否已发放分享奖品
                    ActivityFissionAssistLogDO activityFissionAssistLogDO = BargainingConverter.ActivityFissionAssistLogConverter(promotionHisResourceDO, dto,FissonResourceTypeEnum.SHARE_AWARDS.getId());
                    activityFissionAssistLogDO.setCode(resourceCode);
                    activityFissionAssistLogDO.setProjectName(projectName);
                    activityFissionAssistLogDO.setProjectId(projectId);
                    activityFissionAssistLogDAO.save(activityFissionAssistLogDO);
                    return promotionHisResourceDO.getName();
                }catch (Exception e){
                    promotionActivityManager.incrRedisQty(promotionHisResourceDO.getId(),dto.getActivityId());
                    throw e;
                }
            }
        }
        return null;
    }
}
