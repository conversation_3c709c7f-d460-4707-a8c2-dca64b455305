package com.deepexi.dxp.marketing.service.promotion;


import com.deepexi.dxp.marketing.domain.promotion.dto.opengroup.PromotionOpenGroupBaseResponseDTO;

import java.util.List;

/**
 * 拼团订单自动取消(活动过期、商品售罄时)
 *
 * <AUTHOR>
 * @date 2020/03/11 07:10
 */
public interface OpenGroupTimingCloseService {

    /**
     * 定时关闭超时未成团数据
     *
     * @return 并返回团Id
     */
    List<PromotionOpenGroupBaseResponseDTO> timingCloseOvertime();
}
