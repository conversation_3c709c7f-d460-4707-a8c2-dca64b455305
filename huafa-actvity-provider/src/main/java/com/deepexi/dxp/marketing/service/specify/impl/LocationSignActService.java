package com.deepexi.dxp.marketing.service.specify.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.deepexi.common.extension.AppRuntimeEnv;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.*;
import com.deepexi.dxp.marketing.domain.marketing.response.*;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.LocationSignRuleDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.LocationSignTypeDTO;
import com.deepexi.dxp.marketing.enums.activity.limit.PATemplateBaseEnum;
import com.deepexi.dxp.marketing.enums.specify.ActivityTemplateNumberEnum;
import com.deepexi.dxp.marketing.service.specify.LuckyDrawService;
import com.deepexi.dxp.middle.promotion.converter.ActivityConfigConverter;
import com.deepexi.dxp.middle.promotion.converter.specify.BargainingConverter;
import com.deepexi.dxp.middle.promotion.converter.specify.LuckyDrawConverter;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityFissionLogDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityPartakeLogDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityUserRelatedDAO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityLimitDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityFissionLogDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityPartakeLogDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityUserRelatedDO;
import com.deepexi.util.exception.ApplicationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @description: 地点签到活动模板
 * @date 2022/10/24 16:47
 */
@Service
@Slf4j
public class LocationSignActService {
    @Resource
    private ActivityFissionLogDAO activityFissionLogDAO;
    @Resource
    private ActivityPartakeLogDAO activityPartakeLogDAO;
    @Resource
    private LuckyDrawService luckyDrawService;

    @Resource
    public ActivityUserRelatedDAO activityUserRelatedDAO;

    @Transactional
    public LocationSignResponseDTO partakeAct(ActivityPartakeRequest requestVo, List<PromotionActivityLimitDO> limitList,
                                              ActivityUserRelatedDTO activityUserRelated) {
        LocationSignResponseDTO result = new LocationSignResponseDTO();
        List<BaseActivityDTO> numberLimit = ActivityConfigConverter.getLimitArray(limitList, PATemplateBaseEnum.NUMBER.getId(), BaseActivityDTO.class);
        Map<Integer,List<String>> checkedMap = new HashMap<>();
        //先看打卡地点是否合法
        LocationSignTypeDTO.LocationSignItemDTO foundedLocation = checkLocation(requestVo,numberLimit,checkedMap);
        result.setLocation(foundedLocation.getName());
        List<LocationSignRuleVO> applyRules = checkRules(requestVo, activityUserRelated, numberLimit, checkedMap);
        result.setApplyRules(applyRules);
        return result;
    }

    private List<LocationSignRuleVO> checkRules(ActivityPartakeRequest requestVo, ActivityUserRelatedDTO activityUserRelated,
                                                List<BaseActivityDTO> numberLimit, Map<Integer, List<String>> checkedMap) {
        //看用户是否符合打卡规则
        String rulesStr = LuckyDrawConverter.elLimitConverterToStr(numberLimit, ActivityTemplateNumberEnum.RULES_LIMIT.getId());
        List<LocationSignRuleVO> rules = JSON.parseArray(rulesStr, LocationSignRuleVO.class);
        List<LocationSignRuleVO> applyRules = new ArrayList<>();
        //获取用户的区域打卡次数
        int[] subTotalSignNumber = new int[checkedMap.size()];
        checkedMap.forEach((k, v) -> subTotalSignNumber[k] = v.size());

        //用户每个规则已获得的次数
        JSONArray totalGetNumber = (JSONArray) activityUserRelated.getLimits().getOrDefault(ActivityTemplateNumberEnum.TOTAL_GET_NUMBER.getId(),new JSONArray());
        if (totalGetNumber.isEmpty()) {
            //初始化为0
            for (int i = 0; i < rules.size(); i++) {
                totalGetNumber.add(0);
            }
        }
        log.info("before subTotalSignNumber={},totalGetNumber={}", subTotalSignNumber,totalGetNumber);
        for (int i = 0; i < rules.size(); i++) {
            if (rules.get(i).checkRule(subTotalSignNumber,totalGetNumber.getInteger(i))) {
                applyRules.add(rules.get(i));
                totalGetNumber.set(i,totalGetNumber.getInteger(i) + 1);
            }
        }
        log.info("after subTotalSignNumber={},totalGetNumber={}", subTotalSignNumber,totalGetNumber);
        AddDrawNumberDTO addDrawNumberDTO = requestVo.clone(AddDrawNumberDTO.class);
        addDrawNumberByRules(addDrawNumberDTO, applyRules,null);
        //保存用户的数据
        ActivityUserRelatedDO activityUserRelatedDO = new ActivityUserRelatedDO();
        activityUserRelatedDO.setId(activityUserRelated.getId());
        activityUserRelated.getLimits().put(ActivityTemplateNumberEnum.TOTAL_GET_NUMBER.getId(),totalGetNumber);
        activityUserRelatedDO.setLimits(activityUserRelated.getLimits());
        activityUserRelatedDAO.updateById(activityUserRelatedDO);
        return applyRules;
    }

    // 检查给定的经纬度是否在某个地点的附近
    public LocationSignTypeDTO.LocationSignItemDTO checkLocation(ActivityPartakeRequest requestVo,List<BaseActivityDTO> numberLimit, Map<Integer, List<String>> checkedMap) {
        Map<String,Object> dynamicForm = (Map<String, Object>) requestVo.getDynamicForm();
        Assert.notNull(dynamicForm,"dynamicForm不能为空");
        Double longitude = (Double) dynamicForm.get("longitude");
        Double latitude = (Double) dynamicForm.get("latitude");
        Assert.isTrue(longitude != null && latitude != null, "经纬度不能为空");

        //获取用户参与记录及打卡明细
        ActivityPartakeLogDO partakeLogDO = activityPartakeLogDAO.getByPhone(requestVo.getActivityId(), requestVo.getPhone());
        if (partakeLogDO == null) {
            partakeLogDO = partakeLogConverter(requestVo);
            activityPartakeLogDAO.save(partakeLogDO);
        }
        List<ActivityFissionLogResponseDTO> list = activityFissionLogDAO.listByPartakeLogId(partakeLogDO.getId());
        list.forEach(item -> {
            checkedMap.putIfAbsent(item.getType(), new ArrayList<>());
            checkedMap.get(item.getType()).add(item.getRemark());
        });

        String locationsStr = LuckyDrawConverter.elLimitConverterToStr(numberLimit, ActivityTemplateNumberEnum.LOCATIONS_LIMIT.getId());
        List<LocationSignTypeDTO> locations = JSON.parseArray(locationsStr, LocationSignTypeDTO.class);
        Integer radius = LuckyDrawConverter.elLimitConverter(numberLimit, ActivityTemplateNumberEnum.RADIUS_LIMIT.getId());
        double radiusKm = radius / 1000.0;//转成km
        //初始化下checkedMap
        if (locations.size() != checkedMap.size()) {
            for (int i = 0; i < locations.size(); i++) {
                checkedMap.putIfAbsent(i, new ArrayList<>());
            }
        }
        LocationSignTypeDTO.LocationSignItemDTO founded = null;
        for (int i = 0; i < locations.size(); i++) {
            LocationSignTypeDTO location = locations.get(i);
            for (LocationSignTypeDTO.LocationSignItemDTO item : location.getItems()) {
                //计算两个位置的距离
                double distance = calculateDistance(latitude, longitude,item.getLatitude(), item.getLongitude());
                //如果距离小于等于半径，且没有打卡过，则返回此地点
                if (distance <= radiusKm){
                    founded = item;
                    if (!checkedMap.get(i).contains(founded.getLocation())) {
                        checkedMap.get(i).add(item.getLocation());
                        //保存打卡记录
                        addSignLog(requestVo, partakeLogDO,location,item,i);
                        return founded;
                    }
                }

            }
        }
        if (founded != null) {
            throw new ApplicationException("此地点已打卡过，不可重复打卡");
        }
        //找不到，则抛异常
        throw new ApplicationException("打卡失败，您的位置不在打卡范围内，请确认打卡地点");
    }

    // Haversine公式计算两个经纬度之间的距离
    private double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        final int R = 6371; // 地球半径，单位为公里
        double latDistance = Math.toRadians(lat2 - lat1);
        double lonDistance = Math.toRadians(lon2 - lon1);
        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c; // 返回距离，单位为公里
    }
    private void addSignLog(ActivityPartakeRequest requestVo, ActivityPartakeLogDO partakeLogDO,
                            LocationSignTypeDTO location, LocationSignTypeDTO.LocationSignItemDTO item, int index) {
        ActivityFissionLogDO fissionLogDO = BargainingConverter.activityFissionLogConverter(requestVo, 1, partakeLogDO);
        fissionLogDO.setRemark(item.getLocation());
        fissionLogDO.setType(index);//存放地点分类索引
        fissionLogDO.setAvatar(location.getName() + "-" + item.getName());
        activityFissionLogDAO.save(fissionLogDO);
        //参与记录增加打卡次数
        partakeLogDO.setCurrentFissonCount(partakeLogDO.getCurrentFissonCount() + 1);
        activityPartakeLogDAO.updateById(partakeLogDO);
    }

    public static ActivityPartakeLogDO partakeLogConverter(ActivityPartakeRequest requestVo) {
        ActivityPartakeLogDO activityPartakeLog = requestVo.clone(ActivityPartakeLogDO.class);
        activityPartakeLog.setAppId(AppRuntimeEnv.getAppId());
        activityPartakeLog.setTenantId(AppRuntimeEnv.getTenantId());
        activityPartakeLog.setCreatedBy(requestVo.getUserName());
        activityPartakeLog.setUpdatedBy(requestVo.getUserName());
        activityPartakeLog.setCurrentFissonCount(0);//初始化打卡次数0
        return activityPartakeLog;
    }

    public void homeInfo(ActivityHomeInfoRequest dto, List<PromotionActivityLimitDO> limitList, ActivityHomeInfoResponseDTO responseDTO) {
        //查询活动关联规则
        Assert.notEmpty(limitList,"活动规则未配置");
        List<BaseActivityDTO> numberLimit = ActivityConfigConverter.getLimitArray(limitList, PATemplateBaseEnum.NUMBER.getId(), BaseActivityDTO.class);

        String locationsStr = LuckyDrawConverter.elLimitConverterToStr(numberLimit, ActivityTemplateNumberEnum.LOCATIONS_LIMIT.getId());
        List<LocationSignTypeDTO> locations = JSON.parseArray(locationsStr, LocationSignTypeDTO.class);
        //获取用户参与记录及打卡明细
        ActivityPartakeLogDTO partakeLogDTO = responseDTO.getActivityPartakeLogDTO();
        Map<Integer,List<String>> checkedMap = new HashMap<>();//签到分类_经纬度
        if (partakeLogDTO.getId() != null) {
            List<ActivityFissionLogResponseDTO> list = activityFissionLogDAO.listByPartakeLogId(partakeLogDTO.getId());
            responseDTO.setFissionLogs(list);
            list.forEach(item -> {
                checkedMap.putIfAbsent(item.getType(), new ArrayList<>());
                checkedMap.get(item.getType()).add(item.getRemark());
            });
        }

        //过滤指定城市的地点
        List<String> subSignTypeName = new ArrayList<>();
        Map<String, CityRankDTO> citiesMap = new LinkedHashMap<>();
        int totalCnt = 0;
        for (int i = 0; i < locations.size(); i++) {
            LocationSignTypeDTO location = locations.get(i);
            subSignTypeName.add(location.getName());
            totalCnt += location.getItems().size();
            List<LocationSignTypeDTO.LocationSignItemDTO> items = new ArrayList<>();
            for (LocationSignTypeDTO.LocationSignItemDTO item : location.getItems()) {
                //如果没有传城市，则默认取第一个
                if (StrUtil.isBlank(dto.getRealCityId())) {
                    dto.setRealCityId(item.getCityId());
                }
                citiesMap.putIfAbsent(item.getCityId(), new CityRankDTO(item.getCityId(), item.getCityName()));
                if (item.getCityId().equals(dto.getRealCityId())) {
                    item.setChecked(checkedMap.get(i) != null && checkedMap.get(i).contains(item.getLocation()));
                    items.add(item);
                }
            }
            location.setItems(items);
        }
        partakeLogDTO.setNeedFissonCount(totalCnt);

        //增加规则列表
        String rulesStr = LuckyDrawConverter.elLimitConverterToStr(numberLimit, ActivityTemplateNumberEnum.RULES_LIMIT.getId());
        List<LocationSignRuleVO> rules = JSON.parseArray(rulesStr, LocationSignRuleVO.class);
        List<LocationSignRuleVO> singleRules = new ArrayList<>();//单个地点打卡的规则
        List<LocationSignRuleVO> multiRules = new ArrayList<>();//累计打卡的规则
        for (LocationSignRuleVO rule : rules) {
            rule.setSubSignTypeName(subSignTypeName);
            if (rule.getTotalSignNumber() == 1) {
                singleRules.add(rule);
            } else {
                multiRules.add(rule);
            }
            //找出用户的参与情况
            ActivityUserRelatedDTO activityUserRelated = activityUserRelatedDAO.getActivityUserRelated(new ActivityUserRelatedDTO(rule.getLuckDrawId(),null,dto.getPhone()));
            if(activityUserRelated != null){
                int drawNumber = (int) activityUserRelated.getLimits().get(ActivityTemplateNumberEnum.DRAW_NUMBER.getId());
                rule.setRemainingDrawNumber(drawNumber);
            }
        }
        responseDTO.setCities(citiesMap.values());
        responseDTO.setLocations(locations);
        responseDTO.setSingleRules(singleRules);
        responseDTO.setMultiRules(multiRules);
    }

    /**
     * 每次分享活动单点打卡的每个抽奖活动都获得
     *
     * @param dto
     * @param activity
     * @param activityUserRelated
     * @return
     */
    public Boolean share(ShareLuckyDrawRequestDTO dto, PromotionActivityDetailDTO activity, ActivityUserRelatedDTO activityUserRelated) {
        List<BaseActivityDTO> numberLimit = activity.getNumberLimit();
        String rulesStr = LuckyDrawConverter.elLimitConverterToStr(numberLimit, ActivityTemplateNumberEnum.RULES_LIMIT.getId());
        List<LocationSignRuleDTO> rules = JSON.parseArray(rulesStr, LocationSignRuleDTO.class);
        //每次分享获得抽奖次数
        Integer shareDrawNumber = LuckyDrawConverter.elLimitConverter(numberLimit, ActivityTemplateNumberEnum.SHARE_DRAW_NUMBER.getId());
        //找出只有单个分类地点的规则，增加抽奖次数
        AddDrawNumberDTO addDrawNumberDTO = dto.clone(AddDrawNumberDTO.class);
        addDrawNumberByRules(addDrawNumberDTO, rules,shareDrawNumber);
        return Boolean.TRUE;
    }

    /**
     * 增加抽奖次数
     * @param addDrawNumberDTO
     * @param rules 规则列表
     * @param addDrawNumber 指定增加的次数，不指定则使用规则中的次数
     */
    private void addDrawNumberByRules(AddDrawNumberDTO addDrawNumberDTO, List<? extends LocationSignRuleDTO> rules, Integer addDrawNumber) {
        //同一个活动，只增加一次
        Map<Long, Integer> addedMap = new HashMap<>();
        for (LocationSignRuleDTO rule : rules) {
            //分享时，只针对单个地点
            if ((addDrawNumber == null || rule.getTotalSignNumber() == 1)
                    && !addedMap.containsKey(rule.getLuckDrawId())) {
                //增加对应活动的抽奖次数
                addDrawNumberDTO.setActivityId(rule.getLuckDrawId());
                luckyDrawService.addDrawNumber(addDrawNumberDTO,addDrawNumber != null ? addDrawNumber : rule.getPerGetDrawNumber());
                addedMap.put(rule.getLuckDrawId(), 1);
            }
        }
    }


}
