package com.deepexi.dxp.marketing.manager.promotion.impl.strategy;

import com.deepexi.dxp.marketing.domain.promotion.dto.activity.ActivityRuleDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.UpIdDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityConfigDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.GroupInfoDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.TemplateLimitDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityParamsDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityResponseParamsDTO;
import com.deepexi.dxp.marketing.enums.activity.strategy.StrategyGroupEnum;
import com.deepexi.dxp.marketing.enums.activity.strategy.condition.PTConditionEnum;
import com.deepexi.dxp.marketing.enums.activity.strategy.operation.PTOperationEnum;
import com.deepexi.dxp.marketing.manager.promotion.impl.BaseStrategy;
import com.deepexi.dxp.middle.promotion.util.Arith;
import com.deepexi.util.CollectionUtil;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * 拼团活动策略
 *
 * <AUTHOR> xianfeng.cai
 * @date created in 17:03 2019/11/27
 */
@EqualsAndHashCode(callSuper = true)
public class PTStrategy extends BaseStrategy {

    private List<PTStrategyEnumsCalculate> calculateHelper = new ArrayList<>();

    public PTStrategy(TemplateLimitDTO templateLimitDTO, ActivityConfigDTO activityConfigDTO,
                      ActivityParamsDTO params, ActivityResponseParamsDTO activityResponseParamsDTO) {
        super(templateLimitDTO, activityConfigDTO, params, activityResponseParamsDTO);
    }

    /**
     * 买赠的枚举类处理
     */
    private interface PTStrategyEnumsCalculate {
        /**
         * 买赠的枚举类处理
         *
         * @param activityRuleDTOList       活动的优惠rule
         * @param params                    活动的参数
         * @param activityResponseParamsDTO 优惠结果返回类
         */
        void calculate(List<ActivityRuleDTO> activityRuleDTOList, ActivityParamsDTO params, ActivityResponseParamsDTO activityResponseParamsDTO);
    }

    private PTStrategy.PTStrategyEnumsCalculate strategyPT() {
        return (activityRuleList, params, activityResponseParams) -> {
            boolean enumsFlag = activityResponseParams.getPaTemplateId().equals(StrategyGroupEnum.PTHD_G.getId());
            if (!enumsFlag) {
                return;
            }
            if (CollectionUtil.isNotEmpty(activityRuleList)) {
                String condition = activityRuleList.get(0).getStrategyType();
                if (PTConditionEnum.PTPT.getId().equals(condition)) {
                    strategyPTPT(activityRuleList, activityResponseParams);
                } else if (PTConditionEnum.LDXPT.getId().equals(condition)) {
                    strategyLDXPT(activityRuleList, activityResponseParams);
                } else {
                    return;
                }
            }
        };
    }

    /**
     * 设置返回的数据对象
     *
     * @param groupInfoDTO              拼团活动信息
     * @param activityResponseParamsDTO
     */
    private void setActivityResponseParams(GroupInfoDTO groupInfoDTO,
                                           ActivityResponseParamsDTO activityResponseParamsDTO) {
        activityResponseParamsDTO.setGroupInfoDTO(groupInfoDTO);
    }

    /**
     * 根据类型返回拼团信息
     *
     * @return
     */
    private void strategyPTPT(List<ActivityRuleDTO> activityRuleDTOList, ActivityResponseParamsDTO activityResponseParamsDTO) {
        if (CollectionUtil.isEmpty(activityRuleDTOList)) {
            return;
        }
        ActivityRuleDTO activityRuleDTO = activityRuleDTOList.get(0);
        // 无策略限制处理，只需原封不动返回结果即可
        this.resultOperation(activityRuleDTO.getOperation(), activityResponseParamsDTO);
    }

    /**
     * 根据类型返回拼团信息
     */
    private void strategyLDXPT(List<ActivityRuleDTO> activityRuleDTOList, ActivityResponseParamsDTO activityResponseParamsDTO) {
        if (CollectionUtil.isEmpty(activityRuleDTOList)) {
            return;
        }
        ActivityRuleDTO activityRuleDTO = activityRuleDTOList.get(0);
        // 无策略限制处理，只需原封不动返回结果即可
        this.resultOperation(activityRuleDTO.getOperation(), activityResponseParamsDTO);
    }

    /**
     * 遍历策略列表填充返回数据信息
     */
    private void resultOperation(List<UpIdDTO> operation, ActivityResponseParamsDTO activityResponseParamsDTO) {
        GroupInfoDTO groupInfoDTO = new GroupInfoDTO();
        for (BaseActivityDTO operationRule : operation) {
            if (PTOperationEnum.HDJG.getId().equals(operationRule.getId())) {
                groupInfoDTO.setActivityPrice(Arith.transformToString(operationRule.getValue(), 2));
            } else if (PTOperationEnum.TZKG.getId().equals(operationRule.getId())) {
                groupInfoDTO.setDiscountFlag(Integer.parseInt(operationRule.getValue()));
            } else if (PTOperationEnum.TZJG.getId().equals(operationRule.getId())) {
                groupInfoDTO.setDiscountPrice(Arith.transformToString(operationRule.getValue(), 2));
            } else if (PTOperationEnum.CTRS.getId().equals(operationRule.getId())) {
                groupInfoDTO.setPeopleNumber(Integer.parseInt(operationRule.getValue()));
            } else if (PTOperationEnum.YXSJ.getId().equals(operationRule.getId())) {
                groupInfoDTO.setDuration(Long.parseLong(operationRule.getValue()));
            }
        }
        this.setActivityResponseParams(groupInfoDTO, activityResponseParamsDTO);
    }

    private void init(List<ActivityRuleDTO> activityRuleDTOList) {
        calculateHelper.add(strategyPT());
    }

    @Override
    public Boolean calculate() {
        // 获取活动的策略
        List<ActivityRuleDTO> activityRuleDTOList = super.getActivityConfigDTO().getActivityRuleDTOList();
        // 获取活动的参数
        ActivityParamsDTO params = super.getParams();
        // 活动返回的参数
        ActivityResponseParamsDTO activityResponseParamsDTO = super.getActivityResponseParamsDTO();
        init(activityRuleDTOList);
        PTCalculate(activityRuleDTOList, params, activityResponseParamsDTO);
        return true;
    }

    private void PTCalculate(List<ActivityRuleDTO> activityStrategiesList, ActivityParamsDTO params, ActivityResponseParamsDTO activityResponseParamsDTO) {
        for (PTStrategyEnumsCalculate strategy : calculateHelper) {
            strategy.calculate(activityStrategiesList, params, activityResponseParamsDTO);
        }
    }
}
