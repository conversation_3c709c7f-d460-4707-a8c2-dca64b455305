package com.deepexi.dxp.marketing.service.specify.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.deepexi.common.extension.AppRuntimeEnv;
import com.deepexi.dxp.marketing.common.base.utils.CommonExceptionCode;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.config.HuafaConstantConfig;
import com.deepexi.dxp.marketing.constant.PayConstant;
import com.deepexi.dxp.marketing.constant.RedisConstants;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityOrderQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.CancelOrderRequest;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.*;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityOrderResponseVO;
import com.deepexi.dxp.marketing.domain.marketing.response.OrderPayResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.PayOrderQueryResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.PromotionActivityDetailDTO;
import com.deepexi.dxp.marketing.enums.activity.strategy.StrategyGroupEnum;
import com.deepexi.dxp.marketing.enums.specify.*;
import com.deepexi.dxp.marketing.extension.HuafaRuntimeEnv;
import com.deepexi.dxp.marketing.manager.promotion.PromotionActivityManager;
import com.deepexi.dxp.marketing.service.specify.ActivityOrderService;
import com.deepexi.dxp.marketing.service.specify.ActivityVerifyService;
import com.deepexi.dxp.marketing.service.specify.WxPayService;
import com.deepexi.dxp.marketing.utils.GenerateIdUtil;
import com.deepexi.dxp.middle.promotion.dao.PromotionActivityDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityFissionLogDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityOrderDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityPageDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityParticipationDAO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.*;
import com.deepexi.util.StringUtil;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import com.deepexi.util.pojo.CloneDirection;
import com.deepexi.util.pojo.ObjectCloneUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class ActivityOrderServiceImpl implements ActivityOrderService {

	@Autowired
	private ActivityOrderDAO activityOrderDAO;

	@Autowired
	private RedissonClient redissonClient;

	@Autowired
	private WxPayService wxPayService;
	@Autowired
	private GenerateIdUtil generateIdUtil;

	@Value("${deepexi.marketing.specify.pay-call-back-base-url}")
	private String callBackBaseUrl;
	@Autowired
	private ActivityParticipationDAO activityParticipationDAO;
	@Autowired
	private PromotionActivityManager promotionActivityManager;
	@Autowired
	private PromotionActivityDAO promotionActivityDAO;
	@Resource
	private ActivityPageDAO activityPageDAO;
	@Autowired
	private HuafaConstantConfig huafaConstantConfig;
	@Resource
	private ActivityFissionLogDAO activityFissionLogDAO;
	@Resource
	private ActivityVerifyService activityVerifyService;
	/**
	 * 列表查询
	 **/
	@Override
	public PageBean<ActivityOrderResponseVO> pageList(ActivityOrderQuery query) {

		PageBean<ActivityOrderDO> activityOrderList = activityOrderDAO.pageList(query);

		//如果返回的数据为空
		if (CollUtil.isEmpty(activityOrderList.getContent())) {
			return new PageBean<>(Lists.newArrayList());
		}

		PageBean<ActivityOrderResponseVO> activityOrderResponseVOPageBean = ObjectCloneUtils.convertPageBean(activityOrderList, ActivityOrderResponseVO.class);
		List<Long> ids = activityOrderResponseVOPageBean.getContent().stream().map(ActivityOrderResponseVO::getActivityId).collect(Collectors.toList());
		List<ActivityPageDO> pageList = activityPageDAO.getByActivity(ids, ActivityTypeEnum.ACTIVITY.getId());
		Map<Long, ActivityPageDO> pageMap = pageList.stream().collect(Collectors.toMap(ActivityPageDO::getActivityId, item -> item));

		activityOrderResponseVOPageBean.getContent().forEach(item -> {
			//如果是报名活动，增加额外的信息
			PromotionActivityDO activityDO = promotionActivityDAO.getById(item.getActivityId());
			item.setActivityIconUrl(pageMap.get(item.getActivityId()).getActivityIconUrl());
			fillBaseInfo(activityDO,item);

			if (StrategyGroupEnum.HF_SIGN_UP_ACT.getId().equals(activityDO.getPaTemplateId().toString())) {
				fillSignUpInfo(activityDO,item);
			}
			//退款情况
			fillRefundInfo(item);
			//先去查缓存，如果订单存在，则先从缓存取订单信息
			fillUnPayInfo(item);
			removeUnUseExtInfo(item.getExt());
		});

		return activityOrderResponseVOPageBean;
	}




	/**
	 * 新增
	 **/
	@Override
	public Long save(ActivityOrderRequestDTO dto) {
		if (Objects.isNull(dto)) {
			throw new ApplicationException("入参对象不能为空");
		}
		dto.setAppId(AppRuntimeEnv.getAppId());
		dto.setTenantId(AppRuntimeEnv.getTenantId());
		ActivityOrderDO clone = dto.clone(ActivityOrderDO.class);
		clone.setStatus(dto.getStatus());
		//clone.setPayTime(new Date());//支付时间创建订阐时应为空
		clone.setOrderType(OrderTypeEnum.PAY.getId());
		Map<String, Object> ext = receiveCouponConverter(clone,dto);
		clone.setExt(ext);
		clone.setCreatedBy(dto.getNickName());
		clone.setUpdatedBy(dto.getNickName());
		boolean result = activityOrderDAO.save(clone);

		if (!result) {
			throw new ApplicationException("保存数据失败");
		}

		//缓存未支付订单信息
		promotionActivityManager.cacheUnpayOrderInfo(clone);

		return clone.getId();
	}

	private Map<String, Object> receiveCouponConverter(ActivityOrderDO activityOrderDO, ActivityOrderRequestDTO dto){
		OrderPayResponseDTO orderPayResponseDTO = dto.getOrderPayResponseDTO();
		Map<String, Object> map = new HashMap<>();
		map.put("type",activityOrderDO.getType());
		map.put("phone",dto.getPhone());
		map.put("openId",dto.getUserId());
		map.put("userId",dto.getUserId());
		map.put("unionId",dto.getUnionId());
		map.put("nickName",dto.getNickName());
		map.put("userName",dto.getUserName());
		map.put("paySign",orderPayResponseDTO.getPaySign());
		map.put("nonceStr",orderPayResponseDTO.getNonceStr());
		map.put("signType",orderPayResponseDTO.getSignType());
		map.put("timeStamp",orderPayResponseDTO.getTimeStamp());
		map.put("packageRes",orderPayResponseDTO.getPackageRes());
		map.put("h5Url",orderPayResponseDTO.getH5Url());
		map.put("activityId",activityOrderDO.getActivityId());
		//projectID可能是支付号不为空
		if(StringUtil.isNotEmpty(dto.getProjectId()) && StringUtil.isNotEmpty(dto.getProjectName())){
			map.put("projectId",dto.getProjectId());
			map.put("projectName",dto.getProjectName());
		}
		map.put("hisResourceId",dto.getResourceId());
        map.put("dyPayInfo", JSON.toJSONString(orderPayResponseDTO.getDyPayInfo()));
		//放自定义信息
		map.putAll(dto.getExt());
		return map;
	}

	@Override
	public ActivityOrderDO getByCode(String code) {
		return activityOrderDAO.getByCode(code);
	}

	@Override
	public Boolean updateById(ActivityOrderDO activityOrderDO) {
		return activityOrderDAO.updateById(activityOrderDO);
	}

	@Override
	public List<ActivityOrderResponseVO> findList(Integer status) {
		QueryWrapper<ActivityOrderDO> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(ActivityOrderDO::getStatus,status);
		queryWrapper.lambda().ne(ActivityOrderDO::getOrderType,OrderTypeEnum.REFUND.getId());//非退款单
		return  ObjectCloneUtils.convertList(activityOrderDAO.list(queryWrapper),ActivityOrderResponseVO.class);
	}

	@Override
	public ActivityOrderResponseVO getDetail(Long id) {
		ActivityOrderDO orderDo= activityOrderDAO.getById(id);
		return Objects.nonNull(orderDo) ? orderDo.clone(ActivityOrderResponseVO.class):null;
	}

	@Override
	public ActivityOrderResponseVO getOrderDetail(Long resourceId,String phone,Long activityId) {
		if(resourceId == null){
			throw new ApplicationException(CommonExceptionCode.INVALIDATION,"资源id不能为空!");
		}
		if(StringUtil.isEmpty(phone)){
			throw new ApplicationException(CommonExceptionCode.INVALIDATION,"手机号码不能为空!");
		}
		if(activityId == null){
			throw new ApplicationException(CommonExceptionCode.INVALIDATION,"活动id不能为空!");
		}

		//先去查缓存，如果订单存在，则先从缓存取订单信息
		ActivityOrderResponseVO cacheActivityOrderResponseVO = JSON.parseObject(String.valueOf(redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_USER_UNPAY_ORDER+phone+RedisConstants.UNDER_LINE+activityId+RedisConstants.UNDER_LINE+resourceId).get()),ActivityOrderResponseVO.class);
		if(Objects.nonNull(cacheActivityOrderResponseVO)){
			//订单关闭时间
			cacheActivityOrderResponseVO.setEndTime(DateUtil.offset(cacheActivityOrderResponseVO.getCreatedTime(), DateField.MINUTE, PayConstant.EXPIREMINUTES));
			return cacheActivityOrderResponseVO;
		}

		throw new ApplicationException(CommonExceptionCode.INVALIDATION,new String());

//		ActivityOrderQuery query = new ActivityOrderQuery();
//		query.setStatus(OrderStatusEnum.UNPAY.getId());
//		query.setActivityId(activityId);
//		query.setHisResourceId(resourceId);
//		query.setPhone(phone);
//		List<ActivityOrderDO> list = activityOrderDAO.findList(query);
//		if(CollectionUtil.isEmpty(list)){
//			throw new ApplicationException(CommonExceptionCode.INVALIDATION,new String());
//		}
//		if(list.size() >1){
//			throw new ApplicationException(CommonExceptionCode.INVALIDATION,new String());
//		}
//		ActivityOrderDO activityOrderDO = list.get(0);
//		if(!OrderStatusEnum.UNPAY.getId().equals(activityOrderDO.getStatus())){
//			throw new ApplicationException(CommonExceptionCode.INVALIDATION,new String());
//        }
//		ActivityOrderResponseVO activityOrderResponseVO = activityOrderDO.clone(ActivityOrderResponseVO.class);
//		//资源信息
//		PromotionHisResourceDO promotionHisResourceDO = promotionHisResourceDAO.getById(activityOrderResponseVO.getResourceId());
//		if(Objects.nonNull(promotionHisResourceDO)){
//			activityOrderResponseVO.setCouponValue(promotionHisResourceDO.getCouponValue());
//			activityOrderResponseVO.setResourceName(promotionHisResourceDO.getName());
//			activityOrderResponseVO.setUrl(promotionHisResourceDO.getUrl());
//			activityOrderResponseVO.setCostPrice(promotionHisResourceDO.getCostPrice());
//			activityOrderResponseVO.setDiscountPrice(promotionHisResourceDO.getDiscountPrice());
//			activityOrderResponseVO.setResourceType(promotionHisResourceDO.getType());
//			activityOrderResponseVO.setCouponCategory(promotionHisResourceDO.getCouponCategory());
//			activityOrderResponseVO.setCouponType(promotionHisResourceDO.getCouponType());
//		}
//
//		//订单关闭时间
//		if(OrderStatusEnum.UNPAY.getId().equals(activityOrderResponseVO.getStatus())){
//			Calendar now = Calendar.getInstance();
//			now.setTime(activityOrderResponseVO.getCreatedTime());
//			now.add(Calendar.MINUTE, PayConstant.EXPIREMINUTES);
//			Date endTime = now.getTime();
//			activityOrderResponseVO.setEndTime(endTime);//订单关闭时间
//		}
//		//订单付款相关属性
//		ActivityOrderDO byId = activityOrderDAO.getById(activityOrderDO.getId());
//		ReceiveCouponRequestDTO receiveCouponRequestDTO = (ReceiveCouponRequestDTO)MapBeanUtil.mapToObject2(byId.getExt(),ReceiveCouponRequestDTO.class);
//		if(!PlatformTypeEnum.DY_MINIPROGRAM.getId().equals(byId.getType())
//				&& byId.getExt() !=  null){
//			activityOrderResponseVO.setPaySign(byId.getExt().get("paySign") == null ? "":byId.getExt().get("paySign").toString());
//			activityOrderResponseVO.setSignType(byId.getExt().get("signType") == null ? "":byId.getExt().get("signType").toString());
//			activityOrderResponseVO.setPackageRes(byId.getExt().get("packageRes") == null ? "":byId.getExt().get("packageRes").toString());
//			activityOrderResponseVO.setNonceStr(byId.getExt().get("nonceStr") == null ? "":byId.getExt().get("nonceStr").toString());
//			activityOrderResponseVO.setTimeStamp(byId.getExt().get("timeStamp") == null ? "":byId.getExt().get("timeStamp").toString());
//
//			activityOrderResponseVO.setNickName(byId.getExt().get("nickName") == null ? "":byId.getExt().get("nickName").toString());
//			activityOrderResponseVO.setPhone(byId.getExt().get("phone") == null ? "":byId.getExt().get("phone").toString());
//			activityOrderResponseVO.setUserName(byId.getExt().get("userName") == null ? "":byId.getExt().get("userName").toString());
//			activityOrderResponseVO.setOpenId(byId.getExt().get("openId") == null ? "":byId.getExt().get("openId").toString());
//			activityOrderResponseVO.setProjectId(byId.getExt().get("projectId") == null ? "":byId.getExt().get("projectId").toString());
//			activityOrderResponseVO.setH5Url(byId.getExt().get("h5Url") == null ? "":byId.getExt().get("h5Url").toString());
//		}
//		if(PlatformTypeEnum.DY_MINIPROGRAM.getId().equals(byId.getType())
//				&& byId.getExt() !=  null && byId.getExt().get("dyPayInfo") != null){
//			if(activityOrderResponseVO   == null){
//				activityOrderResponseVO = new ActivityOrderResponseVO();
//			}
//			ReceiveCouponRequestDTO.DyPayInfo dyPayInfo = JSONUtil.toBean(byId.getExt().get("dyPayInfo").toString(), ReceiveCouponRequestDTO.DyPayInfo.class);
//			activityOrderResponseVO.setDyPayInfo(dyPayInfo.clone(ActivityOrderResponseVO.DyPayInfo.class));
//		}
//		PromotionActivityDO promotionActivityDO = promotionActivityDAO.getById(activityId);
//		activityOrderResponseVO.setPaTemplateId(promotionActivityDO.getPaTemplateId());
//		return activityOrderResponseVO;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Data closeOrder(String wxOrderNo, Long orderId) {
		ActivityOrderDO activityOrderDO = activityOrderDAO.getById(orderId);
		CancelOrderRequest dto = new CancelOrderRequest(activityOrderDO.getCode());
		return new Data<>(closeOrder(dto));
	}
	@Override
	public Boolean closeOrder(CancelOrderRequest dto) {
		ActivityOrderDO orderDO = getByCode(dto.getCode());
		Assert.notNull(orderDO,"订单不存在");
		Assert.isTrue(OrderStatusEnum.UNPAY.getId().equals(orderDO.getStatus()),"该订单状态不可以取消");

		Data data = wxPayService.closeOrderNo(orderDO.getWxOrderNo());

		if(!data.getCode().equals("200")){
			PayOrderQueryResponseDTO payOrderQueryResponseDTO = this.getOrderPayResult(orderDO.getId());
			if(payOrderQueryResponseDTO.getCode().equals("200") && payOrderQueryResponseDTO.getData().getStatus().equals(WxReturnStatusEnum.SUCCESS.getStatus())){
				return false;
			}
		}
		int count = activityOrderDAO.updateCloseStatus(orderDO.getId(),dto.getReason());
		log.info("====================================");
		log.info("orderId:{}，更新状态：{}",orderDO.getId(),count);
		log.info("====================================");
		if(count>0){
			//缓存返还库存,(如果是订单中有数量，则使用)
			count = Convert.toInt(orderDO.getExt().get("total"),1);
			promotionActivityManager.incrRedisQty(orderDO.getResourceId(),orderDO.getActivityId(),count);
			//缓存清空未支付订单
			promotionActivityManager.clearCacheUnpayOrderInfo(orderDO.getPhone(),orderDO.getActivityId(),orderDO.getResourceId());
		}
		return true;
	}
	@Override
	public PayOrderQueryResponseDTO getOrderPayResult(Long orderId) {
		return wxPayService.queryOrder(orderId);
	}

	@Override
	public CouponPayCallBackRequestDTO getLocalOrderPayResult(Long orderId) {
		ActivityOrderDO orderDO = activityOrderDAO.getById(orderId);
		CouponPayCallBackRequestDTO dto = new CouponPayCallBackRequestDTO();
		if (orderDO.getStatus().equals(OrderStatusEnum.PAYED.getId())) {
			dto.setStatus("SUCCESS");
		}
		return dto;
	}

	@Override
	@Transactional
	public void checkUnPayOrder(String wxOrderNo, Long orderId) {

		log.info("主动查询微信订单号：{}，订单号：{}",wxOrderNo,orderId);

		PayOrderQueryResponseDTO payOrderQueryResponseDTO = getOrderPayResult(orderId);
		if (payOrderQueryResponseDTO == null) {
			return;
		}

		if(!payOrderQueryResponseDTO.getCode().equals("200")){
			activityOrderDAO.updateStatus(orderId,OrderStatusEnum.CLOSED.getId());
			return;
		}

		if(WxReturnStatusEnum.NOTPAY.getStatus().equals(payOrderQueryResponseDTO.getData().getStatus())){
			return;
		}else if(WxReturnStatusEnum.CLOSED.getStatus().equals(payOrderQueryResponseDTO.getData().getStatus())){
			return;
		}else if(WxReturnStatusEnum.SUCCESS.getStatus().equals(payOrderQueryResponseDTO.getData().getStatus())){
			//查询订单
			WxPayCallBackRequestDTO wxPayCallBackRequestDTO = payOrderQueryResponseDTO.getData().clone(WxPayCallBackRequestDTO.class, CloneDirection.FORWARD);
			ActivityOrderDO activityOrderDO = activityOrderDAO.getById(orderId);
			wxPayService.orderHandle(wxPayCallBackRequestDTO,activityOrderDO);
		}


	}

	//检查重复订单
	@Override
	public void checkRepeatOrder(ActivityPartakeRequest requestVo) {

		ActivityOrderQuery activityOrderQuery = new ActivityOrderQuery();
		activityOrderQuery.setActivityId(requestVo.getActivityId());
		activityOrderQuery.setPhone(requestVo.getPhone());
		activityOrderQuery.setHisResourceId(requestVo.getHisResourceId());
		activityOrderQuery.setStatus(OrderStatusEnum.UNPAY.getId());
		if (activityOrderDAO.pageList(activityOrderQuery).getContent().size() > 0) {
			throw new ApplicationException("请勿重复下单");
		}
	}
	//创建订单
	@Override
	public Data<OrderPayResponseDTO> placeOrder(ActivityPartakeRequest requestVo, PromotionActivityDO promotionActivityDO, PromotionHisResourceDO promotionHisResourceDO) {

		//生成订单
		ActivityOrderRequestDTO activityOrderRequestDTO = requestVo.clone(ActivityOrderRequestDTO.class);
		activityOrderRequestDTO.setPayMoney(promotionHisResourceDO.getPurchasePrice());
		activityOrderRequestDTO.setCode(generateIdUtil.getOrderNo(GenerateTypeEnum.PAY_TYPE));
		activityOrderRequestDTO.setResourceId(promotionHisResourceDO.getId());

		//调用支付接口
		OrderPayRequestDTO requestDTO = new OrderPayRequestDTO();
		requestDTO.setAmount(activityOrderRequestDTO.getPayMoney().multiply(new BigDecimal(100)).intValue());//单位为分
		String url = "";
		if (PlatformTypeEnum.WX_MINIPROGRAM.getId().equals(requestVo.getType())) {
			requestDTO.setAppId(huafaConstantConfig.MINI_PROGRAM_APP_ID);
			//如果是报名活动，指定使用社群小程序
			if(StrategyGroupEnum.HF_SIGN_UP_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())){
//				requestDTO.setAppId("wx3dcdb02583414cd6");
				requestDTO.setAppId(huafaConstantConfig.COMMUNITY_APPID);
			}
			url = huafaConstantConfig.PAY_BASE_URL + PayConstant.ORDER_PAY_URL;
		} else if (PlatformTypeEnum.WX_H5.getId().equals(requestVo.getType())) {//微信内H5网页
			requestDTO.setAppId(huafaConstantConfig.WECHAT_APP_ID);
			url = huafaConstantConfig.PAY_BASE_URL + PayConstant.ORDER_PAY_URL;
		} else if (PlatformTypeEnum.H5.getId().equals(requestVo.getType())) {//微信外H5网页
			requestDTO.setAppId(huafaConstantConfig.WECHAT_APP_ID);
			url = huafaConstantConfig.PAY_BASE_URL + PayConstant.H5_PAY_URL;
		} else if (PlatformTypeEnum.DY_MINIPROGRAM.getId().equals(requestVo.getType())) {//抖音
			requestDTO.setAppId(huafaConstantConfig.DY_APPID);
			url = huafaConstantConfig.PAY_BASE_URL + PayConstant.DY_PAY_URL;
			requestDTO.setClientIp(getIpAddr());
			requestDTO.setTradeType("MWEB");//微信支付交易类型,当需要提供微信支付时，必填。可固定传 MWEB
		}
		requestDTO.setBizNotifyUrl(callBackBaseUrl + PayConstant.PAY_CALL_BACK_URL);//回调地址
		requestDTO.setBizOrderNo(activityOrderRequestDTO.getCode());
		//活动名称-资源名称-项目名称-付款下单
		String description = promotionActivityDO.getName() + "-" + promotionHisResourceDO.getName();
		if (StringUtil.isNotEmpty(requestVo.getProjectName())) {
			description = description + "-" + requestVo.getProjectName();
		}
		requestDTO.setDescription(description.length() > 42 ? description.substring(0, 42) : description);
		requestDTO.setExpireMinutes(PayConstant.EXPIREMINUTES);
		requestDTO.setOpenid(requestVo.getUserId());
		requestDTO.setProjectId(String.valueOf(requestVo.getProjectId()));

		Data<OrderPayResponseDTO> orderPayResponseDTOData = wxPayService.orderPay(requestDTO, url, requestVo.getType());

		if (CommonExceptionCode.SUCCESS.equals(orderPayResponseDTOData.getCode())) {
			activityOrderRequestDTO.setWxOrderNo(orderPayResponseDTOData.getdata().getOrderNo());
			activityOrderRequestDTO.setStatus(OrderStatusEnum.UNPAY.getId());
			activityOrderRequestDTO.setUserId(requestVo.getUserId());
			activityOrderRequestDTO.setPhone(requestVo.getPhone());
			activityOrderRequestDTO.setType(requestVo.getType());
			activityOrderRequestDTO.setOrderPayResponseDTO(orderPayResponseDTOData.getdata());
			Long orderId = save(activityOrderRequestDTO);
			activityOrderRequestDTO.setId(orderId);
			OrderPayResponseDTO orderPayResponseDTO = orderPayResponseDTOData.getdata();
			orderPayResponseDTO.setBizOrderNo(activityOrderRequestDTO.getCode());
			orderPayResponseDTO.setOrderId(orderId);

		}

		return orderPayResponseDTOData;

	}

	@Override
	public ActivityOrderResponseVO getOrderDetail(String code) {
		ActivityOrderDO orderDO = getByCode(code);
		Assert.notNull(orderDO,"订单不存在");
		PromotionActivityDO activityDO = promotionActivityDAO.getById(orderDO.getActivityId());
		ActivityOrderResponseVO responseVO = orderDO.clone(ActivityOrderResponseVO.class);
		fillBaseInfo(activityDO, responseVO);
		//如果是报名活动，则增加相关的信息
		if (StrategyGroupEnum.HF_SIGN_UP_ACT.getId().equals(activityDO.getPaTemplateId().toString())) {
			fillSignUpInfo(activityDO,responseVO);
		}
		//退款情况
		fillRefundInfo(responseVO);
		//如果是未支付，则获取支付参数
		//先去查缓存，如果订单存在，则先从缓存取订单信息
		fillUnPayInfo(responseVO);
		removeUnUseExtInfo(responseVO.getExt());
		return responseVO;
	}

	private void fillBaseInfo(PromotionActivityDO activityDO, ActivityOrderResponseVO responseVO) {
		if (responseVO.getExt() == null) {
			responseVO.setExt(new HashMap<>());
		}
		List<ActivityParticipationDO> activityParticipationDOS = activityParticipationDAO.listByActivityId(activityDO.getId());
		List<ActivityParticipationVO> activityParticipationVOList = ObjectCloneUtils.convertList(activityParticipationDOS, ActivityParticipationVO.class);
		responseVO.setProjectInfoList(activityParticipationVOList);

		responseVO.setActivityName(activityDO.getName());
		responseVO.setActivityStartTime(activityDO.getStartTime());
		responseVO.setActivityEndTime(activityDO.getEndTime());
		responseVO.setPaTemplateId(activityDO.getPaTemplateId());
		responseVO.setNickName((String) responseVO.getExt().get("nickName"));
		responseVO.setUserName((String) responseVO.getExt().get("userName"));

	}

	/**
	 * 移除无用信息
	 * @param ext
	 */
	private void removeUnUseExtInfo(Map<String, Object> ext) {
		if (ext == null) {
			return;
		}
		Set<String> keysToRemove = new HashSet<>(Arrays.asList(
				"hisResourceId", "type", "h5Url", "phone", "openId", "userId",
				"paySign", "unionId", "nickName", "nonceStr", "signType",
				"userName", "dyPayInfo", "timeStamp", "activityId", "packageRes"
		));

		ext.keySet().removeAll(keysToRemove);
	}

	public void fillSignUpInfo(PromotionActivityDO activityDO, ActivityOrderResponseVO responseVO) {
		responseVO.getExt().put("activityContactName", activityDO.getExt().get("contactName"));
		responseVO.getExt().put("activityContactPhone", activityDO.getExt().get("contactPhone"));
		responseVO.getExt().put("depositPaid", activityDO.getExt().get("depositPaid"));
		responseVO.getExt().put("address", activityDO.getExt().get("address"));
		responseVO.getExt().put("principalName", activityDO.getExt().get("principalName"));
		//设置报名费用
		responseVO.setCouponValue(BigDecimal.valueOf(Double.parseDouble(activityDO.getExt().get("amount").toString())));
		//活动图片
		responseVO.getExt().put("activityImage", activityDO.getExt().get("image"));
		//如果是付款成功，返回签到情况
		if (OrderStatusEnum.PAYED.getId().equals(responseVO.getStatus())
			|| OrderStatusEnum.REFUND.getId().equals(responseVO.getStatus())) {
			ActivityFissionLogDO fissionLogDO = activityFissionLogDAO.getByOrderId(responseVO.getId(), responseVO.getActivityId());
			if (fissionLogDO != null) {
				responseVO.getExt().put("signInTime",DateUtil.formatDateTime(fissionLogDO.getCreatedTime()));
			}
		}
	}
	private void fillRefundInfo(ActivityOrderResponseVO responseVO) {
		if (OrderStatusEnum.UNPAY.getId().equals(responseVO.getStatus())
				|| OrderStatusEnum.CLOSED.getId().equals(responseVO.getStatus())) {
			return;
		}
		ActivityOrderDO refundOrder = activityOrderDAO.getRefundOrderByPayOrderNo(responseVO.getWxOrderNo());
		if (refundOrder != null) {
			responseVO.getExt().put("refundAmount",refundOrder.getPayMoney());
			responseVO.getExt().put("cancelTime", DateUtil.formatDateTime(refundOrder.getCreatedTime()));
			responseVO.getExt().put("refundTime", DateUtil.formatDateTime(refundOrder.getPayTime()));
			responseVO.getExt().put("refundCode",refundOrder.getCode());
			responseVO.getExt().put("refundStatus",refundOrder.getStatus());
			responseVO.getExt().put("refundReason",refundOrder.getRemark());
		}
	}

	private void fillUnPayInfo(ActivityOrderResponseVO responseVO) {
		//不是未支付，直接返回
		if (!Objects.equals(responseVO.getStatus(), OrderStatusEnum.UNPAY.getId())) {
			return;
		}
		ActivityOrderResponseVO cacheActivityOrderResponseVO = JSON.parseObject(String.valueOf(redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_USER_UNPAY_ORDER + responseVO.getPhone() + RedisConstants.UNDER_LINE + responseVO.getActivityId() + RedisConstants.UNDER_LINE + responseVO.getResourceId()).get()),ActivityOrderResponseVO.class);
		if (Objects.nonNull(cacheActivityOrderResponseVO)) {
			//订单关闭时间
			responseVO.setEndTime(DateUtil.offset(responseVO.getCreatedTime(), DateField.MINUTE, PayConstant.EXPIREMINUTES));
			responseVO.setNonceStr(cacheActivityOrderResponseVO.getNonceStr());
			responseVO.setPackageRes(cacheActivityOrderResponseVO.getPackageRes());
			responseVO.setSignType(cacheActivityOrderResponseVO.getSignType());
			responseVO.setPaySign(cacheActivityOrderResponseVO.getPaySign());
			responseVO.setTimeStamp(cacheActivityOrderResponseVO.getTimeStamp());
		} else {
			//如果是未支付，但是没有支付信息，说明已过支付时间，只是定时器未刷新
			responseVO.setStatus(OrderStatusEnum.CLOSED.getId());
		}
	}

	/**
	 * 取消订单，需要退款
	 * @param dto
	 * @return
	 */
	@Override
	@Transactional
	public Boolean cancelOrder(CancelOrderRequest dto) {
		ActivityOrderDO orderDO = getByCode(dto.getCode());
		Assert.notNull(orderDO,"订单不存在");
		Assert.isTrue(OrderStatusEnum.PAYED.getId().equals(orderDO.getStatus()),"该订单状态不可以取消");
		PromotionActivityDetailDTO cacheActivity = promotionActivityManager.getCacheActivityById(orderDO.getActivityId());
		//报名活动才能取消
		Assert.isTrue(StrategyGroupEnum.HF_SIGN_UP_ACT.getId().equals(cacheActivity.getPaTemplateId().toString()),"该活动不支持取消订单");
		//活动结束不能取取消
		Assert.isFalse(cacheActivity.getStatus() == 3,"活动已结束，不能取消");
		//活动开始前24小时才能取消
		Assert.isTrue(dto.isAdmin() || DateUtil.offset(cacheActivity.getStartTime(), DateField.HOUR, -24).isAfter(new Date()),"距活动开始前24小时内不支持取消订单");
		ActivityVerifyDO activityVerifyDO = activityVerifyService.getByOrderId(orderDO.getId());
		ActivityVerifyRequestDTO verifyRequestDTO = new ActivityVerifyRequestDTO();
		verifyRequestDTO.setId(activityVerifyDO.getId());
		verifyRequestDTO.setRemark(dto.getReason());
		verifyRequestDTO.setCreatedBy(StrUtil.blankToDefault(HuafaRuntimeEnv.getCreatedBy(),orderDO.getCreatedBy()));
		try {
			boolean result = activityVerifyService.refunds(verifyRequestDTO);
			log.info("处理用户电话={}，活动id={}的报名费用退款结果={}", orderDO.getPhone(), orderDO.getActivityId(),result);
		} catch (Exception e) {
			log.error("处理用户报名费用退款失败", e);
		}
		return true;
	}

	/**
	 * 获取IP
	 *
	 * @return
	 */
	private String getIpAddr() {
		ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		HttpServletRequest request = attributes.getRequest();
		String ip = null;
		try {
			String ipAddresses = request.getHeader("X-Forwarded-For");
			if (ipAddresses == null || ipAddresses.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
				//Proxy-Client-IP：apache 服务代理
				ipAddresses = request.getHeader("Proxy-Client-IP");
			}
			if (ipAddresses == null || ipAddresses.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
				//WL-Proxy-Client-IP：weblogic 服务代理
				ipAddresses = request.getHeader("WL-Proxy-Client-IP");
			}
			if (ipAddresses == null || ipAddresses.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
				//HTTP_CLIENT_IP：有些代理服务器
				ipAddresses = request.getHeader("HTTP_CLIENT_IP");
			}
			if (ipAddresses == null || ipAddresses.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
				//X-Real-IP：nginx服务代理
				ipAddresses = request.getHeader("X-Real-IP");
			}

			//有些网络通过多层代理，那么获取到的ip就会有多个，一般都是通过逗号（,）分割开来，并且第一个ip为客户端的真实IP
			if (ipAddresses != null && ipAddresses.length() != 0) {
				ip = ipAddresses.split(",")[0];
			}

			//还是不能获取到，最后再通过request.getRemoteAddr();获取
			if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
				ip = request.getRemoteAddr();
			}
		} catch (Exception e) {
			log.error("获取erp接口访问ip异常", e);
		}
		return StringUtil.isEmpty(ip) || ip.equals("0:0:0:0:0:0:0:1") ? "127.0.0.1" : ip;
	}


}