//package com.deepexi.dxp.marketing.engine.listener;
//
//
//import com.deepexi.dxp.marketing.engine.event.MarketingTaskEvent;
//
//import java.lang.reflect.InvocationHandler;
//import java.lang.reflect.Method;
//import java.lang.reflect.Proxy;
//
//public class MarketingTaskListenerProxy<T extends MarketingTaskListener> implements InvocationHandler {
//
//    private T listener;
//
//    public AbstractListener bind(T listener) {
//        this.listener = listener;
//        return (AbstractListener) Proxy.newProxyInstance(AbstractListener.class.getClassLoader(),
//                new Class[]{AbstractListener.class}, this);
//
//    }
//
//    @Override
//    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
//        MarketingTaskEvent event = (MarketingTaskEvent) args[0];
//        method.invoke(listener, args);
//        event.semaphoreDown();
//        return null;
//    }
//}
