package com.deepexi.dxp.marketing.service.specify;

import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityEvaluationDO;
import com.deepexi.util.pageHelper.PageBean;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.ActivityEvaluationDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.ActivityEvaluationQueryDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.SubmitEvaluationDTO;

/**
 * 活动评价服务接口
 * <AUTHOR>
 */
public interface ActivityEvaluationService {

    /**
     * 提交活动评价
     * @param evaluationDTO 评价信息
     * @return 是否成功
     */
    Boolean submitEvaluation(SubmitEvaluationDTO evaluationDTO);

    /**
     * 获取用户的活动评价
     * @param partakeLogId 参与ID
     * @param phone 用户电话
     * @return 评价信息
     */
    ActivityEvaluationDO getUserEvaluation(Long partakeLogId, String phone);

    /**
     * 分页查询活动评价列表
     * @param query 查询条件
     * @return 分页数据
     */
    PageBean<ActivityEvaluationDTO> getEvaluationList(ActivityEvaluationQueryDTO query);

    /**
     * 小程序端分页查询活动评价列表
     * @return 分页数据
     */
    PageBean<ActivityEvaluationDO> getEvaluationListForMiniProgram(ActivityEvaluationQueryDTO query);
} 