package com.deepexi.dxp.marketing.controller.specify.adminapi;

import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.config.HuafaConstantConfig;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.*;
import com.deepexi.dxp.marketing.domain.marketing.response.IncentiveResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.InteractiveResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.PromotionActivityDetailDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.RechargeMobileResponseDTO;
import com.deepexi.dxp.marketing.manager.promotion.PromotionActivityManager;
import com.deepexi.dxp.marketing.service.specify.IncentiveService;
import com.deepexi.dxp.marketing.service.specify.InteractionCenterService;
import com.deepexi.dxp.marketing.service.specify.PhoneService;
import com.deepexi.dxp.marketing.service.specify.PromotionActivityService;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.util.JsonUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 手机号码登录注册controller
 *
 * <AUTHOR>
 * @date  2019/10/24 14:54
 * @see [相关类/方法]
 * @since 1.0
 */
@RestController
@RequestMapping("/admin-api/v1/phone")
@Api(value = "手机验证码模块(测试接口，请勿使用)",tags = {"手机验证码模块(测试接口，请勿使用)"})
public class PhoneController {

    @Resource
    private PhoneService phoneService;
    @Resource
    private IncentiveService incentiveService;
    @Resource
    private InteractionCenterService interactionCenterService;

    @Autowired
    private PromotionActivityService promotionActivityService;
    @Resource
    private HuafaConstantConfig huafaConstantConfig;
    @Resource
    private PromotionActivityManager promotionActivityManager;
    /**
     * 发送短信验证码
     * phone参数是AES加密串
     */
    @ApiOperation("发送短信验证码")
    @PostMapping("smsCode")
    public Data<Boolean> sendSmsCode(@RequestParam String phone,@RequestParam String smsCode) {
        return new Data<>(phoneService.sendSmsCode(phone,smsCode));
    }


    /**
     * 校验手机验证码
     * @param phone 手机号
     * @param smsCode 手机验证码
     * @return 结果
     */
    @ApiOperation("手机验证码校验")
    @PostMapping("checkSmsCode")
    public Data<Boolean> checkSmsCode(@RequestParam String phone,@RequestParam String smsCode) {
        return new Data<>(phoneService.checkSmsCode(phone,smsCode));
    }

    /**
     * 发送红包激励
     */
    @ApiOperation("发送红包激励")
    @PostMapping("sendIncentive")
    public Data<IncentiveResponseDTO> sendIncentive(@RequestBody SendIncentiveRequestDTO requestDTO) {
        return new Data<>(incentiveService.sendIncentive(requestDTO));
    }
    /**
     * 充值话费
     */
    @ApiOperation("充值话费")
    @PostMapping("rechargeMobile")
    public Data<RechargeMobileResponseDTO> rechargeMobile(@RequestBody RechargeMobileRequestDTO requestDTO) {
        return new Data<>(incentiveService.rechargeMobile(requestDTO));
    }
    /**
     * 短信通知
     */
    @ApiOperation("短信通知")
    @PostMapping("/messageInform")
    public Data<InteractiveResponseDTO> messageInform(@RequestBody MessageInformRequestDTO requestDTO) {
        //活动结束砍价活动是否完成
        PromotionActivityDetailDTO activityById = promotionActivityService.getActivityById(582L);
//        PromotionActivityDO clone = activityById.clone(PromotionActivityDO.class);
//        clone.setId(activityById.getActivityId());
//        promotionActivityService.checkBargainEndTime(clone);
        activityById.setId(activityById.getActivityId());
        promotionActivityService.checkBargainEndTime(activityById.clone(PromotionActivityDO.class));
        return new Data<>(null);
    }
    /**
     * 微信模板消息发送
     */
    @ApiOperation("微信模板消息发送")
    @PostMapping("/sendTemplateNews")
    public Data<Boolean> sendTemplateNews() {
        TemplateSendRequestDTO.Param param = new TemplateSendRequestDTO.Param();
        param.setCode("213131");

        TemplateSendRequestDTO requestDTO = new TemplateSendRequestDTO();
        requestDTO.setTemplateCode(huafaConstantConfig.VERIFICATION_CODE_TEMPLATE_ID);
        requestDTO.setMobile("15521072943");
        requestDTO.setTemplateParam(JsonUtil.bean2JsonString(param));
        interactionCenterService.templateSend(requestDTO);
        return new Data<>(true);
    }
    /**
     * 微信模板消息发送
     */
    @ApiOperation("小程序模板消息发送")
    @PostMapping("/miniTemplateNews")
    public Data<InteractiveResponseDTO> miniTemplateNews(@RequestBody SendTemplateNewsRequestDTO requestDTO) {
        return new Data<>(interactionCenterService.miniTemplateNews(requestDTO));
    }
}
