package com.deepexi.dxp.marketing.converter;

import com.deepexi.dxp.marketing.domain.marketing.response.ActivityTodayYesterdayVisitorsDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.IndexPathResponseDTO;
import com.deepexi.dxp.marketing.enums.specify.OverviewOfIndicatorsEnum;
import com.deepexi.dxp.middle.marketing.domain.dto.MarketingKpiDTO;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 活动分析数据转换
 * <AUTHOR>
 */
public class ActivityAnalysisConverter {

    private ActivityAnalysisConverter() {
    }

    public static IndexPathResponseDTO converter(OverviewOfIndicatorsEnum index, Long totalNumber, Long todayNumber,
                                                 Long yesterdayNumber, Map<String, MarketingKpiDTO> marketingKpiMap) {
        yesterdayNumber = yesterdayNumber ==null ? 0 : yesterdayNumber;
        IndexPathResponseDTO indexPathResponseDTO = new IndexPathResponseDTO();
        indexPathResponseDTO.setIndexPath(index.getValue());
        indexPathResponseDTO.setTotalNumber(totalNumber);
        indexPathResponseDTO.setTodayNumber(todayNumber);
        indexPathResponseDTO.setYesterdayNumber(yesterdayNumber);

        //增长率=(今天-昨天)/昨天×100%
        BigDecimal beforeTodayRatio =  beforeTodayRatio(new BigDecimal(todayNumber),new BigDecimal(yesterdayNumber));

        indexPathResponseDTO.setBeforeTodayRatio(beforeTodayRatio);

        //拼装指标单位和说明
        setMarketingKpiUnitRemark(index.getCode(),indexPathResponseDTO,marketingKpiMap);

        return indexPathResponseDTO;
    }

    public static IndexPathResponseDTO pageViewConverter(OverviewOfIndicatorsEnum wechatApplet,
                                                         ActivityTodayYesterdayVisitorsDTO activityTodayYesterdayVisitor,
                                                         Map<String, MarketingKpiDTO> marketingKpiMap) {
        IndexPathResponseDTO indexPathWriteOffNumber = new IndexPathResponseDTO();
        indexPathWriteOffNumber.setIndexPath(wechatApplet.getValue());
        Long todayPeopleNumber = 0L;
        Long yesterdayPeopleNumber = 0L;
        Long totalNumber = 0L;
        if (null == activityTodayYesterdayVisitor){
            indexPathWriteOffNumber.setTodayNumber(todayPeopleNumber);
            indexPathWriteOffNumber.setYesterdayNumber(yesterdayPeopleNumber);
            indexPathWriteOffNumber.setBeforeTodayRatio(BigDecimal.ZERO);
            indexPathWriteOffNumber.setTotalNumber(totalNumber);
        }else{
            indexPathWriteOffNumber.setTodayNumber(activityTodayYesterdayVisitor.getTodayNumber());
            indexPathWriteOffNumber.setYesterdayNumber(activityTodayYesterdayVisitor.getYesterdayNumber());
            indexPathWriteOffNumber.setBeforeTodayRatio(beforeTodayRatio(new BigDecimal(activityTodayYesterdayVisitor.getTodayNumber()),
                    new BigDecimal(activityTodayYesterdayVisitor.getYesterdayNumber())));
            indexPathWriteOffNumber.setTotalNumber(activityTodayYesterdayVisitor.getTotalNumber());
        }
        //拼装指标单位和说明
        setMarketingKpiUnitRemark(wechatApplet.getCode(),indexPathWriteOffNumber,marketingKpiMap);

        return indexPathWriteOffNumber;
    }

    private static void setMarketingKpiUnitRemark(String code,IndexPathResponseDTO indexPathWriteOffNumber,
                                                  Map<String, MarketingKpiDTO> marketingKpiMap) {
        if (marketingKpiMap != null && !marketingKpiMap.isEmpty()){
            MarketingKpiDTO marketingKpiDTO = marketingKpiMap.get(code);
            indexPathWriteOffNumber.setRemark(marketingKpiDTO != null ? marketingKpiDTO.getRemark() : "");
            indexPathWriteOffNumber.setUnit(marketingKpiDTO != null ? marketingKpiDTO.getUnitName() : "");
            indexPathWriteOffNumber.setKpiId(marketingKpiDTO != null ? marketingKpiDTO.getId() : null);
            indexPathWriteOffNumber.setKpiCode(code);
        }
    }

    /**
     * 增长率=(今天-昨天)/昨天×100%
     * @param todayNumber     今天
     * @param yesterdayNumber 昨天
     * @return  增长率
     */
    private static BigDecimal beforeTodayRatio(BigDecimal todayNumber,BigDecimal yesterdayNumber){
        if (yesterdayNumber.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        return  todayNumber.subtract(yesterdayNumber)
                .divide(yesterdayNumber, 4, BigDecimal.ROUND_HALF_UP)
                .multiply(new BigDecimal(100));
    }

    public static IndexPathResponseDTO deriveConverter(MarketingKpiDTO marketingKpiDTO, BigDecimal totalNumber,
                                                       BigDecimal todayNumber, BigDecimal yesterdayNumber) {
        IndexPathResponseDTO indexPathResponseDTO = new IndexPathResponseDTO();
        indexPathResponseDTO.setIndexPath(marketingKpiDTO.getName());
        indexPathResponseDTO.setTotalNumber(totalNumber.longValue());
        indexPathResponseDTO.setTodayNumber(todayNumber.longValue());
        indexPathResponseDTO.setYesterdayNumber(yesterdayNumber.longValue());
        //增长率=(今天-昨天)/昨天×100%
        BigDecimal beforeTodayRatio = beforeTodayRatio(todayNumber,yesterdayNumber);
        indexPathResponseDTO.setBeforeTodayRatio(beforeTodayRatio);

        indexPathResponseDTO.setRemark(marketingKpiDTO.getRemark());
        indexPathResponseDTO.setUnit(marketingKpiDTO.getUnitName());

        return indexPathResponseDTO;
    }

    public static IndexPathResponseDTO goToViewPeopleNumberConverter(Long goToViewPeopleTotalNumber,
                                                                     Long goToViewPeopleTodayNumber,
                                                                     Long goToViewPeopleYesterdayNumber,
                                                                     OverviewOfIndicatorsEnum activityPageView,
                                                                     Map<String, MarketingKpiDTO> marketingKpiMap) {
        IndexPathResponseDTO indexPathWriteOffNumber = new IndexPathResponseDTO();
        indexPathWriteOffNumber.setIndexPath(activityPageView.getValue());
        Long todayPeopleNumber = 0L;
        Long yesterdayPeopleNumber = 0L;
        Long totalNumber = 0L;
        if (null == goToViewPeopleTotalNumber || null == goToViewPeopleYesterdayNumber){
            indexPathWriteOffNumber.setTodayNumber(todayPeopleNumber);
            indexPathWriteOffNumber.setYesterdayNumber(yesterdayPeopleNumber);
            indexPathWriteOffNumber.setBeforeTodayRatio(BigDecimal.ZERO);
            indexPathWriteOffNumber.setTotalNumber(totalNumber);
        }else{
            indexPathWriteOffNumber.setTodayNumber(goToViewPeopleTodayNumber);
            indexPathWriteOffNumber.setYesterdayNumber(goToViewPeopleYesterdayNumber);
            indexPathWriteOffNumber.setBeforeTodayRatio(beforeTodayRatio(new BigDecimal(goToViewPeopleTodayNumber),new BigDecimal(goToViewPeopleYesterdayNumber)));
            indexPathWriteOffNumber.setTotalNumber(goToViewPeopleTotalNumber);
        }
        //拼装指标单位和说明
        setMarketingKpiUnitRemark(activityPageView.getCode(),indexPathWriteOffNumber,marketingKpiMap);

        return indexPathWriteOffNumber;
    }
}
