package com.deepexi.dxp.marketing.domain.marketing.request;

import com.deepexi.dxp.marketing.common.base.request.SuperRequest;
import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import com.deepexi.dxp.marketing.domain.marketing.vo.MarketingActivePurposeValVO;
import com.deepexi.dxp.marketing.domain.marketing.vo.MarketingKpiItemsGroupVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;
import org.springframework.util.StringUtils;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 主动营销任务目的VO
 * @Author: HuangBo.
 * @Date: 2020/3/10 16:45
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Api(value = "营销任务目的Request")
public class MarketingActivePurposeRequest extends SuperRequest {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 主动营销任务ID
     */
    @ApiModelProperty(value = "主动营销任务ID")
//    @NotNull(message = "主动营销任务ID不能为空")
    private Long activeId;

    /**
     * 受众预估人数(计划触达人数)
     */
    @ApiModelProperty(value = "计划触达人数")
    private int estimateMemberNums;

    @ApiModelProperty(value = "行动模板ID")
    private Long actionTemplateId;

    /**
     * 任务目标名称
     */
    @ApiModelProperty(value = "任务目的名称")
    @NotNull(message = "任务目的名称不能为空")
    @Length(max = 20, message = "任务名称长度不能超过20个字符")
    private String name;
    public String getName() {
        return StringUtils.trimWhitespace(name);
    }

    /**
     * 编辑前任务目标旧名称
     */
    @ApiModelProperty(value = "编辑前任务目标旧名称")
    private String oldName;

    /**
     * 是否是实验任务(0:否 1:是) v1.1.0.1
     */
    @ApiModelProperty(value = "是否是实验任务(0:否 1:是)")
    private boolean experiment;

    /**
     * 分组标识，用于实验室创建多个对照分组资源时
     */
    @ApiModelProperty(value = "分组标识，用于实验室创建多个对照分组资源时")
    private String groupCode;

    /**
     * 指标组ID
     */
    @ApiModelProperty(value = "指标组ID")
    @NotNull(message = "指标组ID不能为空")
    private Long kpiGroupId;

    /**
     * 任务目标类型(1:营收；2:商品；3:用户)
     */
    @ApiModelProperty(value = "任务目的类型(1:营收；2:商品；3:用户)")
    @NotNull(message = "任务目的类型不能为空")
    private int type;

    @ApiModelProperty(value = "任务目的指标")
    private List<MarketingActivePurposeValVO> purposeValVOList;

    @ApiModelProperty(value = "描述")
    @Length(max = 200, message = "任务描述长度不能超过200个字符")
    private String remark;

    /**
     * 主动营销任务配置的指标组 v1.1.0.1
     */
    @ApiModelProperty(value = "主动营销任务指标组对象")
    private MarketingKpiItemsGroupVO kpiItemsGroupVO;

}
