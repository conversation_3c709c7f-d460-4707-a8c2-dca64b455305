package com.deepexi.dxp.marketing.service.specify.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.deepexi.common.extension.AppRuntimeEnv;
import com.deepexi.dxp.marketing.common.base.utils.CommonExceptionCode;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.converter.ActivityExtConverter;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.*;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityHomeInfoResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.OrderPayResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.vo.OwnerInfo;
import com.deepexi.dxp.marketing.domain.promotion.dto.OneCodeAuthDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.request.PromotionActivityUpdateRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityCreatePostRequest;
import com.deepexi.dxp.marketing.enums.activity.limit.MemberTypeEnum;
import com.deepexi.dxp.marketing.enums.activity.strategy.DeliveryChannelEnum;
import com.deepexi.dxp.marketing.enums.resource.ActivityStatusEnum;
import com.deepexi.dxp.marketing.enums.resource.BottomBtnTypeEnum;
import com.deepexi.dxp.marketing.enums.specify.ReceiveModeEnum;
import com.deepexi.dxp.marketing.enums.specify.ResourcesAttributeEnum;
import com.deepexi.dxp.marketing.enums.status.ActivityAuditStatusEnum;
import com.deepexi.dxp.marketing.extension.HuafaRuntimeEnv;
import com.deepexi.dxp.marketing.manager.promotion.PromotionActivityManager;
import com.deepexi.dxp.marketing.service.specify.ActivityOrderService;
import com.deepexi.dxp.marketing.service.specify.ActivityVerifyService;
import com.deepexi.dxp.marketing.utils.HuaFaHmacAuthUtil;
import com.deepexi.dxp.middle.promotion.dao.PromotionActivityDAO;
import com.deepexi.dxp.middle.promotion.dao.impl.specify.PromotionBlackListService;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityPartakeLogDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.PromotionHisResourceDAO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityPartakeLogDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityVerifyDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.PromotionBlackListDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.PromotionHisResourceDO;
import com.deepexi.util.exception.ApplicationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import com.deepexi.dxp.marketing.enums.specify.EnrollmentInfoEnum;

/**
 * <AUTHOR>
 * @description: 报名活动模板
 * @date 2022/10/24 16:47
 */
@Service
@Slf4j
public class SignUpService {

    @Resource
    private PromotionHisResourceDAO promotionHisResourceDAO;
    @Resource
    private ActivityOrderService activityOrderService;
    @Resource
    private ActivityVerifyService activityVerifyService;
    @Resource
    private PromotionActivityManager promotionActivityManager;
    @Resource
    private PromotionActivityDAO promotionActivityDAO;

    @Resource
    private ActivityPartakeLogDAO activityPartakeLogDAO;
    @Value("${activity.default-sign-up-resource-id}")
    private Long defaultResourceId;
    @Resource
    private PromotionBlackListService promotionBlackListService;
    @Resource
    private HuaFaHmacAuthUtil huaFaHmacAuthUtil;

    public void processMini(Map<String, Object> ext, PromotionActivityCreatePostRequest requestDto, PromotionActivityUpdateRequest updateDto) {
        //小程序默认用户为不限
        if (requestDto != null) {
            requestDto.setUserLimit(ListUtil.toList(new BaseActivityDTO("memberType", MemberTypeEnum.ALL.getId(),"==")));
            ext.put("deliveryChannel", DeliveryChannelEnum.SQ.getId());
            //小程序默认项目类型为不限
            ext.put("projectType", ResourcesAttributeEnum.APPOINT.getId());
            ext.put("maxParticipantsPerPerson", 3);//每个人参与活动的最大报名人数
            ext.put("minParticipants", 1);//24小时需满足人数
            requestDto.setCreatedBy(StrUtil.blankToDefault(HuafaRuntimeEnv.getCreatedBy(),requestDto.getCreatedBy()));
            requestDto.setUserId(StrUtil.blankToDefault(HuafaRuntimeEnv.getPhone(),requestDto.getUserId()));
            //需要支持代人发布活动，所以去掉判断
//            Assert.isTrue(Objects.equals(ext.get("principalMobile"),requestDto.getUserId()),"主理人信息异常");
        }
        if (updateDto != null) {
            updateDto.setUserLimit(ListUtil.toList(new BaseActivityDTO("memberType", MemberTypeEnum.ALL.getId(),"==")));
            updateDto.setCreatedBy(StrUtil.blankToDefault(HuafaRuntimeEnv.getCreatedBy(),updateDto.getCreatedBy()));
            updateDto.setUserId(StrUtil.blankToDefault(HuafaRuntimeEnv.getPhone(),updateDto.getUserId()));
//            Assert.isTrue(Objects.equals(ext.get("principalMobile"),updateDto.getUserId()),"主理人信息异常");
        }
    }
    public void processDefaultValue(PromotionActivityCreatePostRequest requestDTO) {
        requestDTO.getExt().putIfAbsent("auditStatus", ActivityAuditStatusEnum.PASS_AUDIT.getId());//默认不需要审核
        //默认底部为4，5
        requestDTO.getActivityPageVO().setBottomBtnTypes(ListUtil.toList(BottomBtnTypeEnum.SHARE.getId(), BottomBtnTypeEnum.GENERATE_POSTER.getId()));
        String principalMobile = (String) requestDTO.getExt().get("principalMobile");
        String principalName = (String) requestDTO.getExt().get("principalName");
        Integer communityId = (Integer) requestDTO.getExt().get("communityId");
        Integer lineType = (Integer) requestDTO.getExt().get("lineType");//业务条线
        Assert.notBlank(principalMobile, "主理人电话不能为空");
        Assert.notBlank(principalName, "主理人姓名不能为空");
        Assert.notNull(communityId, "社群id不能为空");
        Assert.notNull(lineType, "业务条线不能为空");
        
        // 校验用户信息配置字段类型唯一性
        validateFeedbackInfoUniqueness(requestDTO.getExt());
        
        //如果需要在线收费（付费-线上支付，免费-有押金），收款项目
        requestDTO.setPrizeList(ListUtil.toList(processOnlinePay(requestDTO.getExt())));
    }

    public void processUpdateValue(PromotionActivityUpdateRequest dto) {
        //更新时，还原PrizeList
        PrizeConfigVO prizeConfigVO = processOnlinePay(dto.getExt());
        //设置资源id
        List<PromotionHisResourceDO> hisResourceDOS = promotionHisResourceDAO.findByActivityId(dto.getId());
        if (CollUtil.isNotEmpty(hisResourceDOS)) {
            PromotionHisResourceDO promotionHisResourceDO = hisResourceDOS.get(0);
            prizeConfigVO.setId(promotionHisResourceDO.getId());
            prizeConfigVO.setIssuedUsedQuantity(promotionHisResourceDO.getIssuedQuantity() - promotionHisResourceDO.getRemainingQuantity());
        }
        dto.setPrizeList(ListUtil.toList(prizeConfigVO));
    }
    /**
     * 如果需要在线收费（付费-线上支付，免费-有押金），收款项目
     *  "feeType":1,//1-付费，2-免费
     * 	"feeMethod":1,//1-线上支付，2-线下支付
     * 	"depositPaid":0,//0-不需要押金，1-需要押金
     * 	"amount":20,
     * 	"feeProjectId":"",//收款项目
     * 	"feeProjectName":"",//收款项目名称
     * @param ext
     */
    private PrizeConfigVO processOnlinePay(Map<String, Object> ext) {
        //构建默认奖品列表
        BigDecimal amount = Convert.toBigDecimal(ext.get("amount"),BigDecimal.ZERO);
        String feeProjectId = (String) ext.get("feeProjectId");
        String feeProjectName = (String) ext.get("feeProjectName");
        int receiveMode = 0;
        if (isPayOnline(ext) || isDepositPaidOnline(ext)) {//如果需要收款
            Assert.notBlank(feeProjectId, "收款项目不能为空");
            Assert.isTrue(amount != null && amount.compareTo(BigDecimal.ZERO) > 0, "报名金额不正确");
            receiveMode = 1;
        }
        Integer maxParticipants = Convert.toInt(ext.get("maxParticipants"));
        //如果不限，则设置为9999
        if (maxParticipants == -1) {
            maxParticipants = 9999;
        }
        PrizeConfigVO prizeConfigVO = new PrizeConfigVO();
        prizeConfigVO.setResourceId(defaultResourceId);
        prizeConfigVO.setIssuedQuantity(maxParticipants);
        prizeConfigVO.setPurchasePrice(amount);
        prizeConfigVO.setLimitTimes(maxParticipants);//限制数量为最大数量
        prizeConfigVO.setProjectId(feeProjectId);
        prizeConfigVO.setProjectName(feeProjectName);
        prizeConfigVO.setReceiveMode(receiveMode);
        return prizeConfigVO;
    }

    @Transactional
    public Data<Object> partakeAct(PromotionActivityDO promotionActivityDO, ActivityPartakeRequest requestVo) {
        // 校验用户信息
        validateUserInfo(requestVo, promotionActivityDO);

        // 校验活动时间
        String signUpEndDateStr = (String) promotionActivityDO.getExt().get("signUpEndDate");
        Assert.isTrue(StrUtil.isNotBlank(signUpEndDateStr) && DateUtil.parse(signUpEndDateStr).isAfter(new Date()), "报名时间已过，无法报名");

        // 校验活动人数限制
        validateParticipantLimit(promotionActivityDO, requestVo);

        // 校验用户是否已报名
        ActivityPartakeLogDO partakeLogDO = activityPartakeLogDAO.getByPhone(requestVo.getActivityId(), requestVo.getPhone());
        Assert.isNull(partakeLogDO, "您已经完成报名，无需重复报名");

        // 校验用户身份
        validateUserRole(requestVo);

        // 处理活动参与逻辑
        return processParticipation(promotionActivityDO, requestVo);
    }

    private void validateUserInfo(ActivityPartakeRequest requestVo, PromotionActivityDO promotionActivityDO) {
        JSONArray items = JSONUtil.parseArray(JSONUtil.toJsonStr(requestVo.getExt().get("items")));
        Assert.notEmpty(items, "报名用户信息不能为空");

        // 校验用户信息字段配置
        validateFeedbackInfo(requestVo, promotionActivityDO,items);

        Set<String> phoneList = items.stream().map(item -> ((JSONObject) item).getStr("phone")).collect(Collectors.toSet());
        Integer repeatPhoneEnable = Convert.toInt(promotionActivityDO.getExt().get("repeatPhoneEnable"), 0);
        Assert.isTrue(repeatPhoneEnable == 1 || items.size() == phoneList.size(), "报名用户信息不能有重复");

        List<PromotionBlackListDO> blackListDOS = promotionBlackListService.check(requestVo.getActivityId(), phoneList);
        Assert.isTrue(blackListDOS.isEmpty(), "用户{}不能参与此活动", blackListDOS.stream().map(PromotionBlackListDO::getPhone).collect(Collectors.joining(",")));
    }

    /**
     * 校验用户信息字段配置
     *
     * @param requestVo           报名请求
     * @param promotionActivityDO 活动信息
     * @param items 报名用户信息
     */
    private void validateFeedbackInfo(ActivityPartakeRequest requestVo, PromotionActivityDO promotionActivityDO, JSONArray items) {
        // 获取活动配置的feedbackInfo
        ActivityExtVO ext = ActivityExtConverter.converter(promotionActivityDO.getExt());
        List<EnrollmentInfoVO> feedbackInfoList = ext.getFeedbackInfo();
        // 校验每个报名用户的信息
        for (int i = 0; i < items.size(); i++) {
            JSONObject item = (JSONObject) items.get(i);
            validateUserItemInfo(item, feedbackInfoList, i + 1);
        }
    }

    /**
     * 校验用户信息配置字段类型唯一性
     * 确保每个字段类型只能配置一个
     * @param ext 活动扩展字段
     */
    private void validateFeedbackInfoUniqueness(Map<String, Object> ext) {
        ActivityExtVO extVo = ActivityExtConverter.converter(ext);
        List<EnrollmentInfoVO> feedbackInfoList = extVo.getFeedbackInfo();
        
        if (CollUtil.isEmpty(feedbackInfoList)) {
            return; // 如果列表为空，则无需校验
        }
        
        // 校验字段类型唯一性
        Set<Integer> fieldTypes = new HashSet<>();
        Set<String> fieldCodes = new HashSet<>();
        
        for (EnrollmentInfoVO fieldInfo : feedbackInfoList) {
            Integer fieldType = fieldInfo.getType();
            String fieldCode = fieldInfo.getCode();
            
            // 校验字段类型唯一性
            if (fieldTypes.contains(fieldType)) {
                String fieldName = EnrollmentInfoEnum.getValueById(fieldType);
                throw new IllegalArgumentException(String.format("字段类型[%s]重复配置，每个字段类型只能配置一个", fieldName));
            }
            fieldTypes.add(fieldType);
            
            // 校验字段编码唯一性
            if (StrUtil.isNotBlank(fieldCode) && fieldCodes.contains(fieldCode)) {
                throw new IllegalArgumentException(String.format("字段编码[%s]重复，每个字段编码必须唯一", fieldCode));
            }
            fieldCodes.add(fieldCode);
        }
        
        log.info("用户信息配置字段类型唯一性校验通过，共配置{}个字段", feedbackInfoList.size());
    }

    /**
     * 校验单个用户的信息项
     * @param item 用户信息项
     * @param feedbackInfoList 配置的字段信息
     * @param userIndex 用户索引（用于错误提示）
     */
    private void validateUserItemInfo(JSONObject item, List<EnrollmentInfoVO> feedbackInfoList, int userIndex) {
        for (EnrollmentInfoVO fieldInfo : feedbackInfoList) {
            String fieldCode = fieldInfo.getCode();
            Integer isRequired = fieldInfo.getIsRequired();
            String fieldValue = fieldInfo.getValue();
            
            // 获取用户输入的值
            Object userValue = item.get(fieldCode);
            
            // 如果是必填字段，校验不能为空
            if (Objects.equals(isRequired, 1)) {
                Assert.notBlank(StrUtil.toString(userValue), String.format("第%d位用户的%s不能为空", userIndex, fieldValue));
            }
        }
    }

    private void validateParticipantLimit(PromotionActivityDO promotionActivityDO, ActivityPartakeRequest requestVo) {
        Integer maxParticipants = Convert.toInt(promotionActivityDO.getExt().get("maxParticipants"));
        Integer maxParticipantsPerPerson = Convert.toInt(promotionActivityDO.getExt().get("maxParticipantsPerPerson"), 3);
        List<PromotionHisResourceDO> hisResourceDOS = promotionHisResourceDAO.findByActivityId(promotionActivityDO.getId());
        Assert.notEmpty(hisResourceDOS, "活动数据异常");

        PromotionHisResourceDO promotionHisResourceDO = hisResourceDOS.get(0);
        requestVo.setHisResourceId(promotionHisResourceDO.getId());
        requestVo.setProjectId(promotionHisResourceDO.getProjectId());
        requestVo.setProjectName(promotionHisResourceDO.getProjectName());

        int total = JSONUtil.parseArray(JSONUtil.toJsonStr(requestVo.getExt().get("items"))).size();
        Assert.isTrue(total > 0 && total <= maxParticipantsPerPerson, "报名人数不正确");
        requestVo.getExt().put("total", total);

        if (maxParticipants > 0) {
            Assert.isTrue(promotionHisResourceDO.getRemainingQuantity() > 0, "报名人数已满，无法报名");
        }

        boolean flag = promotionActivityManager.decrRedisQty(promotionHisResourceDO.getId(), promotionActivityDO.getId(), total);
        Assert.isTrue(flag, "报名人数已满，无法报名");
    }

    private void validateUserRole(ActivityPartakeRequest requestVo) {
        int userRole = 0; // 0-普通用户，1-业主，2-同住人，3-租客
        try {
            List<OwnerInfo> ownerInfoList = huaFaHmacAuthUtil.getOwnerInfo(requestVo.getPhone(), null, null, null, requestVo.getUnionId());
            if (CollUtil.isNotEmpty(ownerInfoList)) {
                // 定义类型与userRole的映射
                Map<Integer, Integer> typeToRole = new HashMap<>();
                typeToRole.put(10, 1); // 业主
                typeToRole.put(20, 2); // 同住人
                typeToRole.put(30, 3); // 租客

                int minRole = Integer.MAX_VALUE;
                for (OwnerInfo info : ownerInfoList) {
                    Integer type = info.getCustomerType();
                    Integer role = typeToRole.get(type);
                    if (role != null && role < minRole) {
                        minRole = role;
                    }
                }
                if (minRole != Integer.MAX_VALUE) {
                    userRole = minRole;
                }
            }
        } catch (Exception e) {
            log.error("校验业主身份接口调用失败:phoneNumber:" + requestVo.getPhone(), e);
        }
        requestVo.getExt().put("userRole", userRole);
    }

    private Data<Object> processParticipation(PromotionActivityDO promotionActivityDO, ActivityPartakeRequest requestVo) {
        PromotionHisResourceDO promotionHisResourceDO = promotionHisResourceDAO.findByActivityId(promotionActivityDO.getId()).get(0);
        int total = JSONUtil.parseArray(JSONUtil.toJsonStr(requestVo.getExt().get("items"))).size();

        if (Objects.equals(promotionHisResourceDO.getReceiveMode(), ReceiveModeEnum.PAY.getId())) {
            try {
                activityOrderService.checkRepeatOrder(requestVo);
                promotionHisResourceDO.setPurchasePrice(promotionHisResourceDO.getPurchasePrice().multiply(new BigDecimal(total)));
                Data<OrderPayResponseDTO> orderPayResponseDTOData = activityOrderService.placeOrder(requestVo, promotionActivityDO, promotionHisResourceDO);
                if (!CommonExceptionCode.SUCCESS.equals(orderPayResponseDTOData.getCode())) {
                    throw new ApplicationException(CommonExceptionCode.INVALIDATION, orderPayResponseDTOData.getMsg());
                }
                return new Data<>(orderPayResponseDTOData.getdata(), orderPayResponseDTOData.getCode(), orderPayResponseDTOData.getMsg());
            } catch (Exception e) {
                log.error("报名活动下单失败，", e);
                promotionActivityManager.incrRedisQty(promotionHisResourceDO.getId(), promotionActivityDO.getId(), total);
                throw new ApplicationException(CommonExceptionCode.INVALIDATION, e.getMessage());
            }
        } else if (Objects.equals(promotionHisResourceDO.getReceiveMode(), ReceiveModeEnum.FREE.getId())) {
            freeReceive(requestVo, promotionHisResourceDO, total);
        }
        return new Data<>(Boolean.TRUE);
    }

    private void freeReceive(ActivityPartakeRequest requestVo, PromotionHisResourceDO promotionHisResourceDO, Integer total) {
        ActivityPartakeLogDO partakeLogDO = requestVo.clone(ActivityPartakeLogDO.class);
        partakeLogDO.setAppId(AppRuntimeEnv.getAppId());
        partakeLogDO.setTenantId(AppRuntimeEnv.getTenantId());
        partakeLogDO.setCreatedBy(requestVo.getUserName());
        partakeLogDO.setUpdatedBy(requestVo.getUserName());
        partakeLogDO.setResourceId(promotionHisResourceDO.getId());
        partakeLogDO.setNeedFissonCount(total);//报名人数
        Map<String, Object> ext = new HashMap<>();
        ext.put("items", requestVo.getExt().get("items"));
        ext.put("userRole", requestVo.getExt().get("userRole"));
        partakeLogDO.setExt(ext);
        activityPartakeLogDAO.save(partakeLogDO);
        //扣减真实库存
        promotionActivityManager.decrRemainQty(promotionHisResourceDO.getId(), total.longValue());
    }

    public void homeInfo(ActivityHomeInfoResponseDTO responseDTO, List<OneCodeAuthDTO> scanList) {
        //用户报名人数
        List<ActivityPartakeLogDO> list = activityPartakeLogDAO.lambdaQuery().eq(ActivityPartakeLogDO::getActivityId, responseDTO.getPromotionActivity().getId()).list();
        responseDTO.setTotalCnt(list.stream().map(ActivityPartakeLogDO::getNeedFissonCount).reduce(0, Integer::sum));
        Map<String,Boolean> scanMap = scanList.stream().collect(Collectors.toMap(OneCodeAuthDTO::getPhone, item -> true));
        responseDTO.setTotalScanCnt(list.stream().filter(item -> scanMap.containsKey(item.getPhone())).map(ActivityPartakeLogDO::getNeedFissonCount).reduce(0, Integer::sum));
        //看本人是否有扫码
        if (scanMap.containsKey(responseDTO.getActivityPartakeLogDTO().getPhone())) {
            responseDTO.setUserScanCnt(1);
        }
    }

    /**
     * 签到后，如果是免费-有押金，处理退款
     * @param activity
     * @param partakeLog
     */
    public void afterSignIn(PromotionActivityDO activity, ActivityPartakeLogDO partakeLog) {
        if (partakeLog.getOrderId() == null) {//没有支付的，无需处理
            return;
        }
        ActivityVerifyDO activityVerifyDO = activityVerifyService.getByOrderId(partakeLog.getOrderId());
        //找到相应的核销订单
        if (activityVerifyDO == null) {
            log.warn("无法找到对应的核销订单，无法处理退款，orderId={}",partakeLog.getOrderId());
            return;
        }
        boolean result = true;
        if (isDepositPaidOnline(activity.getExt())) {
            //退款
            log.info("开始处理用户电话={}，活动id={}的报名押金退款", partakeLog.getPhone(), activity.getId());
            ActivityVerifyRequestDTO dto = new ActivityVerifyRequestDTO();
            dto.setId(activityVerifyDO.getId());
            dto.setRemark("现场签到押金退款成功");
            dto.setCreatedBy(partakeLog.getNickName());
            dto.setExt(MapUtil.of("skipQtyReturn",true));
            try {
                result = activityVerifyService.refunds(dto);
                log.info("处理用户电话={}，活动id={}的报名押金退款结果={}", partakeLog.getPhone(), activity.getId(),result);
            } catch (Exception e) {
                log.error("处理用户报名押金退款失败", e);
            }
        }
        if (result) {
            //设置为已核销
            ActivityVerifyRequestDTO requestDTO = new ActivityVerifyRequestDTO();
            //活动主理人为核销人
            requestDTO.setCreatedBy((String) activity.getExt().get("principalName"));
            activityVerifyService.doVerify(requestDTO, activityVerifyDO);
        }
    }

    //报名人数不足，取消活动并退款
    @Transactional
    public void cancelActivity(PromotionActivityDO activity) {
        //是否开启取消活动功能,0 关闭，1 开启
        Integer checkCancelEnable = Convert.toInt(activity.getExt().get("checkCancelEnable"), 1);
        if (checkCancelEnable == 0) {
            return;
        }
        //如果是状态已结束，则不处理
        if (Objects.equals(activity.getStatus(), ActivityStatusEnum.FINISH.getId())) {
            return;
        }

        //看是否在活动开始前24小时内
        if (DateUtil.offsetDay(activity.getStartTime(), -1).isAfter(new Date())) {
            return;
        }

        Integer minParticipants = Convert.toInt(activity.getExt().get("minParticipants"), 0);
        //获取报名人数
        List<ActivityPartakeLogDO> partakeLogList = activityPartakeLogDAO.lambdaQuery().eq(ActivityPartakeLogDO::getActivityId, activity.getId()).list();
        Integer total = partakeLogList.stream().mapToInt(ActivityPartakeLogDO::getNeedFissonCount).sum();
        if (total < minParticipants) {
            log.info("报名人数不足，取消活动，活动id={},报名人数={},最小报名人数={}", activity.getId(), total, minParticipants);
            //如果需要退款
            if (isDepositPaidOnline(activity.getExt()) || isPayOnline(activity.getExt())) {
                ActivityVerifyRequestDTO dto = new ActivityVerifyRequestDTO();
                dto.setRemark("因报名人数不满足，主办方取消活动");
                dto.setCreatedBy("system");
                dto.setExt(MapUtil.of("skipQtyReturn",true));
                for (ActivityPartakeLogDO partakeLog : partakeLogList) {
                    log.info("开始处理用户电话={}，活动id={}的报名退款", partakeLog.getPhone(), activity.getId());
                    ActivityVerifyDO activityVerifyDO = activityVerifyService.getByOrderId(partakeLog.getOrderId());
                    dto.setId(activityVerifyDO.getId());
                    try {
                        boolean result = activityVerifyService.refunds(dto);
                        log.info("处理用户电话={}，活动id={}的报名退款结果={}", partakeLog.getPhone(), activity.getId(),result);
                    } catch (Exception e) {
                        log.error("处理用户报名押金退款失败", e);
                    }
                }
            }
            activity.setStatus(Integer.parseInt(ActivityStatusEnum.FINISH.getId()));
            promotionActivityDAO.updateById(activity);
            //同步到缓存
            promotionActivityManager.forceCacheActInfo(activity.getId());
        }
    }

    private static boolean isDepositPaidOnline(Map<String, Object> ext) {
        Integer feeType = Convert.toInt(ext.get("feeType"));
        Integer depositPaid = Convert.toInt(ext.get("depositPaid"));
        return Objects.equals(feeType, 2) && Objects.equals(depositPaid, 1);
    }

    private static boolean isPayOnline(Map<String, Object> ext) {
        Integer feeType = Convert.toInt(ext.get("feeType"));
        Integer feeMethod = Convert.toInt(ext.get("feeMethod"));
        return Objects.equals(feeType, 1) && Objects.equals(feeMethod, 1);
    }
}
