//package com.deepexi.dxp.marketing.engine.listener;
//
//import com.deepexi.dxp.marketing.engine.handler.*;
//import com.deepexi.util.CollectionUtil;
//import com.google.common.collect.Lists;
//import com.google.common.collect.Maps;
//import lombok.extern.slf4j.Slf4j;
//
//import java.io.Serializable;
//import java.util.List;
//import java.util.Map;
//
///**
// * 营销任务监听器工厂类
// *
// * <AUTHOR>
// * @date 2020/3/20 15:26
// */
//@Slf4j
//public class MarketingTaskListenerFactory {
//
//    // 营销任务处理器Map，每个营销任务对应多个处理器
//    private static final Map<Serializable, List<MarketingTaskListener>> taskListenerMap = Maps.newHashMap();
//
//    /**
//     * 初始化某个营销任务的各处理器
//     *
//     * @param key 由"tenantId_taskType_taskId"格式组成
//     */
//    public static void initListenerForTask(Serializable key) {
//        log.info("MarketingTaskListenerFactory.initListenerForTask-初始化所有监听器");
//        if (CollectionUtil.isEmpty(taskListenerMap.get(key))) {
//            // TODO 暂时注释掉，待联调测试时开放
////            throw new ApplicationException("营销任务" + key + "已经存在对应的处理器.");
//            List<MarketingTaskListener> listeners = Lists.newLinkedList();
//            listeners.add(new CircleMemberHandler());
//            listeners.add(new MetaDataHandler());
//            listeners.add(new MqHandler());
//            listeners.add(new SendHandler());
//            listeners.add(new RedisHandler());
//            taskListenerMap.put(key, listeners);
//        }
//    }
//
//    public static boolean existKey(Serializable key) {
//        return taskListenerMap.containsKey(key);
//    }
//
//    /**
//     * 销毁所有任务处理器以及其内部元素
//     *
//     * @param key tenantId_taskType_taskId
//     */
//    public static void removeListenerForTask(Serializable key) {
//        List<MarketingTaskListener> taskListeners = taskListenerMap.get(key);
//        if (CollectionUtil.isEmpty(taskListeners)) {
//            return;
//        }
//        taskListeners.clear();
//        taskListenerMap.remove(key);
//    }
//
//    private static <T> T getMarketingTaskListener(Serializable key, Class<T> listenerClazz) {
//
//        List<MarketingTaskListener> listeners = taskListenerMap.get(key);
//        if (CollectionUtil.isNotEmpty(listeners)) {
//            for (MarketingTaskListener listener : listeners) {
//                if (listener.getClass().equals(listenerClazz)) {
//                    return (T) listener;
//                }
//            }
//        }
//        return null;
//    }
//
//    public static MarketingTaskListener getEsHandlerListener(Serializable key) {
//        return getMarketingTaskListener(key, CircleMemberHandler.class);
//    }
//
//    public static MarketingTaskListener getMetaDataHandlerListener(Serializable key) {
//        return getMarketingTaskListener(key, MetaDataHandler.class);
//    }
//
//    public static MarketingTaskListener getMarketingTaskMQListener(Serializable key) {
//        return getMarketingTaskListener(key, MqHandler.class);
//    }
//
//    public static MarketingTaskListener getMarketingTaskSendListener(Serializable key) {
//        return getMarketingTaskListener(key, SendHandler.class);
//    }
//
//    public static MarketingTaskListener getMarketingRedisListener(Serializable key) {
//        return getMarketingTaskListener(key, RedisHandler.class);
//    }
//}
