package com.deepexi.dxp.marketing.controller.specify.openapi;

import cn.hutool.json.JSONUtil;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.CustomerFeedbackQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.CustomerFeedbackRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityFormFeedbackResponseVO;
import com.deepexi.dxp.marketing.domain.specify.dto.ActivityFormFeedbackDTO;
import com.deepexi.dxp.marketing.service.specify.CustomerFeedbackService;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Objects;

/**
 * 用户反馈客户端接口
 * 2021、7、13 确定变更用户登记需求：用户登记信息添加 his资源id 关联，前端每次反显最新登记信息，保存后 原his资源id 关联登记信息不变
 * <AUTHOR>
 */
@RestController
@RequestMapping("/open-api/v1/open/customer-feedback")
@Api(value = "用户反馈",tags = {"用户反馈接口"})
public class CustomerFeedbackOpenApiController {

    @Resource
    public CustomerFeedbackService customerFeedbackService;

    @PostMapping("/save")
    @ApiOperation(value = "用户反馈信息保存", notes = "用户反馈信息保存")
    public Data<Boolean> save(@RequestBody @Valid CustomerFeedbackRequestDTO requestDTO) {
        if (Objects.isNull(requestDTO)){
            throw new ApplicationException("入参对象不能为空");
        }
        return new Data<>(customerFeedbackService.save(requestDTO));
    }

    @GetMapping("/pageList")
    @ApiOperation(value = "用户反馈信息分页查询", notes = "用户反馈信息分页查询")
    public Data<PageBean<ActivityFormFeedbackResponseVO>> pageList(CustomerFeedbackQuery query) {
        if (Objects.isNull(query)){
            throw new ApplicationException("入参对象不能为空");
        }
        return new Data<>(customerFeedbackService.pageHisList(query));
    }

    @PostMapping("/pageListPost")
    @ApiOperation(value = "用户反馈信息分页查询", notes = "用户反馈信息分页查询")
    public Data<PageBean<ActivityFormFeedbackResponseVO>> pageListPost(@RequestBody CustomerFeedbackQuery query) {
        if (Objects.isNull(query)){
            throw new ApplicationException("入参对象不能为空");
        }
        return new Data<>(customerFeedbackService.pageList(query));
    }

    @PostMapping("/pageHisList")
    @ApiOperation(value = "用户反馈信息分页查询2.0", notes = "用户反馈信息分页查询2.0")
    public Data<PageBean<ActivityFormFeedbackResponseVO>> pageHisList(@RequestBody CustomerFeedbackQuery query) {
        if (Objects.isNull(query)){
            throw new ApplicationException("入参对象不能为空");
        }
        return new Data<>(customerFeedbackService.pageHisList(query));
    }

    @GetMapping("/detail")
    @ApiOperation(value = "用户反馈信息——详情", notes = "用户反馈信息——详情")
    public Data<ActivityFormFeedbackResponseVO> detail(@RequestParam Long id) {
        return new Data<>(customerFeedbackService.detail(id));
    }

    @GetMapping("/userFormFeedbackInfo")
    @ApiOperation(value = "用户反馈信息-表单活动", notes = "用户反馈信息-表单活动")
    public Data<ActivityFormFeedbackResponseVO> getByUserId(@RequestParam Long activityId,@RequestParam String phone) {
        return new Data<>(customerFeedbackService.getByUserId(activityId,phone));
    }

    @GetMapping("/getCity")
    @ApiOperation(value = "获取城市", notes = "获取城市")
    public Data<Object> getCity() {
        return new Data<>(JSONUtil.parseArray(customerFeedbackService.getCity()));
    }

    @GetMapping("/getDetailByVerifyCode")
    @ApiOperation(value = "通过核销码获取登记信息", notes = "通过核销码获取登记信息")
    public Data<ActivityFormFeedbackDTO> getDetailByVerifyCode(@RequestParam String code) {
        return new Data<>(customerFeedbackService.getDetailByVerifyCode(code));
    }
}
