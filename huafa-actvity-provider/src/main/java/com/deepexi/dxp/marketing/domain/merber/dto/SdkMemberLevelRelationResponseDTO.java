package com.deepexi.dxp.marketing.domain.merber.dto;

import com.deepexi.util.domain.dto.BaseDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * SDK-会员等级关系响应DTO
 *
 * <AUTHOR>
 * @since 2020年08月21日 16:29
 */
@Data
public class SdkMemberLevelRelationResponseDTO extends BaseDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 会员等级组ID
     */
    private Long levelGroupId;

    /**
     * 会员等级ID
     */
    private Long levelId;

    /**
     * 等级类型
     */
    private Integer levelType;

    /**
     * 会员等级名称
     */
    private String levelName;

    /**
     * 过期时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date expiredTime;
}
