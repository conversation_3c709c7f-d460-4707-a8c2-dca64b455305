package com.deepexi.dxp.marketing.domain.marketing.request;

import com.deepexi.dxp.marketing.domain.marketing.dto.*;
import com.deepexi.dxp.middle.marketing.common.base.SuperExtEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/10
 */
@Data
@ApiModel
public class FlowCanvasCreateRequest extends SuperExtEntity {
    /**
     * 流程画布任务名称
     */
    @ApiModelProperty(value = "流程画布任务名称", required = true)
    @NotBlank(message = "画布名称不能为空")
    private String name;

    /**
     * 流程画布定义编码,用于根据版本区分的同一个画布
     */
    @ApiModelProperty(value = "流程画布定义编码")
    private String code;

    /**
     * 类型（2：模板 1：流程画布）
     */
    @ApiModelProperty(value = "类型（2：模板 1：流程画布）", required = true)
    @NotNull(message = "类型不能为空")
    private Integer type;

    /**
     * 任务状态(1:未发布，2:运行中，3:暂停中，4:已停止)
     */
    @ApiModelProperty(value = "任务状态(1:未发布，2:运行中，3:暂停中，4:已停止)", required = true)
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 流程画布与前端约定的协议json字符串
     */
    @ApiModelProperty(value = "流程画布与前端约定的协议json字符串", required = true)
    @NotNull(message = "画布json内容不能为空")
    private String contentJson;

    /**
     * 引用的模板id
     */
    @ApiModelProperty(value = "引用的模板id")
    private Long flowTemplateId;

    /**
     * 执行类型（once:单次，fixed：周期重复， event：事件触发）
     */
    @ApiModelProperty(value = "执行类型（once:单次，fixed：周期重复， event：事件触发）")
    private String executeType;

    /**
     * '立即执行 right_now/定时执行 scheduled /指定时间重复执行 fixed_at_spec_time / 每隔一段时间重复 fixed_at_spec_period / 事件触发 event'
     */
    @ApiModelProperty(value = "'立即执行 right_now/定时执行 scheduled /指定时间重复执行 fixed_at_spec_time / 每隔一段时间重复 fixed_at_spec_period / 事件触发 event'")
    private String runType;

    /**
     * cron表达式
     */
    @ApiModelProperty(value = "cron表达式")
    private String cronExpression;

    /**
     * 执行开始时间 (单次和触发的存定时执行时间，周期重复的存定义的开始时间)
     */
    @ApiModelProperty(value = "执行开始时间 (单次和触发的存定时执行时间，周期重复的存定义的开始时间)")
    private Date excTime;

    @ApiModelProperty(value = "圈选用户信息")
    @NotNull(message = "用户圈选信息不能为空")
    private FlowCanvasTargetUserDTO flowCanvasTargetUserDTO;

    @ApiModelProperty(value = "圈选用户信息-标签客群")
    private List<FlowCanvasTargetUserGroupTagsDTO> flowCanvasTargetUserGroupTags;

    @ApiModelProperty(value = "圈选用户信息-模型")
    private List<FlowCanvasTargetUserModelDTO> flowCanvasTargetUserModels;

    @ApiModelProperty(value = "圈选用户信息-场景")
    private List<FlowCanvasTargetUserSceneDTO> flowCanvasTargetUserScenes;

    @ApiModelProperty(value = "执行节点-用户属性")
    private List<FlowCanvasExecuteChangePropertyDTO> flowCanvasExecuteChangeProperties;

    @ApiModelProperty(value = "执行节点-信息")
    private List<FlowCanvasExecuteSendMsgDTO> flowCanvasExecuteSendMsgs;

    @ApiModelProperty(value = "执行节点-资源")
    private List<FlowCanvasExecuteSendResourceDTO> flowCanvasExecuteSendResources;

    @ApiModelProperty(value = "执行节点-积分")
    private List<FlowCanvasExecuteSendScoreDTO> flowCanvasExecuteSendScores;

    @ApiModelProperty(value = "执行节点-标签")
    private List<FlowCanvasExecuteTagsDTO> flowCanvasExecuteTags;

    @ApiModelProperty(value = "条件节点-指标")
    private List<FlowCanvasConditionKpiItemsDTO> flowCanvasConditionKpiItems;

    @ApiModelProperty(value = "条件节点-模型")
    private List<FlowCanvasConditionModelDTO> flowCanvasConditionModels;

    @ApiModelProperty(value = "条件节点-标签")
    private List<FlowCanvasConditionTagsGroupDTO> flowCanvasConditionTagsGroups;

    @ApiModelProperty(value = "条件节点-属性")
    private List<FlowCanvasConditionPropertyDTO> flowCanvasConditionProperties;

    @ApiModelProperty(value = "乐观锁")
    private Long version;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;
}
