package com.deepexi.dxp.marketing.domain.marketing.vo;


import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 指标组VO
 * @Author: HuangBo.
 * @Date: 2020/5/18 15:51
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MarketingKpiItemsGroupVO extends SuperVO {

    /**
     * 活动目标(1:营收；2:商品；3:用户)
     */
    @ApiModelProperty(value = "活动目标(1:营收；2:商品；3:用户)")
    @NotNull(message = "活动目的必需")
    private Integer type;

    /**
     * 指标组名称
     */
    @ApiModelProperty(value = "指标组名称")
    @NotNull(message = "指标组名称不能为空")
    private String name;

    /**
     * 核心指标ID
     */
    @ApiModelProperty(value = "核心指标ID(引用原生/派生指标)")
    @NotNull(message = "核心指标不能为空")
    private Long coreKpiItemsId;

    /**
     * 核心指标名称
     */
    @ApiModelProperty(value = "核心指标名称")
    private String coreKpiItemsName;

    /**
     * 指标路径图
     */
    @ApiModelProperty(value = "指标路径图ID")
    @NotNull(message = "指标路径图不能为空")
    private Long routeMapId;

    /**
     * 状态(1:启用; 2:禁用)
     */
    @ApiModelProperty(value = "状态(1:启用; 2:禁用)")
    private Integer status;

    /**
     * 指标组图点信息集合
     */
    @ApiModelProperty(value = "指标组图点信息集合")
    private List<MarketingKpiItemsGroupNodeVO> groupNodeVOList;

}
