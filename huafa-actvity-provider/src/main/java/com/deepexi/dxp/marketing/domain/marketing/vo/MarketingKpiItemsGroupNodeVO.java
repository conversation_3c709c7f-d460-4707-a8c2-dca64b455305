package com.deepexi.dxp.marketing.domain.marketing.vo;


import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 指标组图节点信息
 * @Author: HuangBo.
 * @Date: 2020/5/18 15:51
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MarketingKpiItemsGroupNodeVO extends SuperVO {

    /**
     * 指标组id
     */
    @ApiModelProperty(value = "指标组ID")
    private Long kpiItemsGroupId;

    /**
     *路径图id（冗余指标组路径图）
     */
    @ApiModelProperty(value = "指标路径图ID")
    private Long routeMapId;

    /**
     * 路径图节点ID
     */
    @ApiModelProperty(value = "路径图节点ID")
    private Long routeMapNodeId;

    /**
     * 路径图节点对应的指标ID
     */
    @ApiModelProperty(value = "路径图节点对应的指标ID")
    private Long nodeKpiItemsId;

    /**
     * 路径图节点对应的指标名称
     */
    @ApiModelProperty(value = "路径图节点对应的指标名称")
    private String nodeKpiItemsName;

    /**
     * 路径图节点对应的指标单位
     */
    @ApiModelProperty(value = "路径图节点对应的指标单位")
    private String nodeKpiItemsUnitName;

}
