package com.deepexi.dxp.marketing.domain.marketing.query;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 24好玩活动页面查询
 * <AUTHOR>
 * @version 1.0
 * @date 2020-06-01 10:59
 */
@Data
public class HaoWanGameQuery  extends SuperQuery {



    /**
     * 状态列表
     */
    @ApiModelProperty("状态列表")
    private List<String> statusList;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;

    /**
     * 关键字（game_id、status、name）
     */
    @ApiModelProperty("关键字（game_id、status、name）")
    private String kw;

    /**
     * 活动id
     */
    @ApiModelProperty("活动id")
    private String gameId;

    /**
     * 是否查询活动可否使用
     */
    @ApiModelProperty("是否查询活动可否使用")
    private Boolean isNotQueryAvailable = true;
}
