package com.deepexi.dxp.marketing.converter;

import com.deepexi.common.extension.AppRuntimeEnv;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.BargainInitiatorRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.CouponRequestDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.enums.specify.ActivityTemplateNumberEnum;
import com.deepexi.dxp.marketing.enums.specify.FissionReducePriceStatusEnum;
import com.deepexi.dxp.marketing.utils.DateTimeUtils;
import com.deepexi.dxp.marketing.utils.MapBeanUtil;
import com.deepexi.dxp.middle.promotion.converter.specify.LuckyDrawConverter;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityFissionReducePriceDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.PromotionHisResourceDO;
import com.deepexi.util.DateUtils;
import com.deepexi.util.exception.ApplicationException;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 砍价活动信息转换
 * <AUTHOR>
 */
public class FissionReducepPiceConverter {
    private FissionReducepPiceConverter() {
    }
    /**
     * 保存活动基本信息转换
     * @param requestDTO
     * @return
     */
    public  static ActivityFissionReducePriceDO converter(BargainInitiatorRequestDTO requestDTO, List<BaseActivityDTO> activityLimit,List<PromotionHisResourceDO> list){
        ActivityFissionReducePriceDO activityDO = new ActivityFissionReducePriceDO();
        activityDO.setStatus(FissionReducePriceStatusEnum.BARGAINING.getId());
        activityDO.setTenantId(AppRuntimeEnv.getTenantId());
        activityDO.setAppId(AppRuntimeEnv.getAppId());
        activityDO.setActivityId(requestDTO.getActivityId());
        activityDO.setCreatedBy(requestDTO.getCreatedBy());
        activityDO.setUserId(requestDTO.getUserId());
        activityDO.setUserName(requestDTO.getUserName());
        activityDO.setNickName(requestDTO.getNickName());
        activityDO.setPhone(requestDTO.getPhone());
        activityDO.setUnionId(requestDTO.getUnionId());
        activityDO.setType(requestDTO.getType());
        Date nowTime = DateUtils.now();
        activityDO.setActivityStartTime(DateUtils.toDateText(nowTime,DateUtils.DEFAULT_DATE_TIME_FORMAT));
        Integer validPeriod = LuckyDrawConverter.elLimitConverter(activityLimit, ActivityTemplateNumberEnum.VALID_PERIOD.getId());
        if (validPeriod == null){
            throw new ApplicationException("未配置活动有效期");
        }
        activityDO.setActivityEndTime(DateUtils.toDateText(DateUtils.addHour(nowTime, validPeriod),DateUtils.DEFAULT_DATE_TIME_FORMAT));

        BigDecimal minimumPrice = LuckyDrawConverter.elLimitConverterToBigDecimal(activityLimit, ActivityTemplateNumberEnum.LOWEST_PRICE.getId());
        activityDO.setMinimumPrice(minimumPrice);

        activityDO.setProjectId(requestDTO.getProjectId());

        PromotionHisResourceDO promotionHisResourceDO = list.get(0);
        if (promotionHisResourceDO != null){
            activityDO.setResourceId(promotionHisResourceDO.getResourceId());
            activityDO.setResourceName(promotionHisResourceDO.getName());
            activityDO.setResourceUrl(promotionHisResourceDO.getUrl());
            activityDO.setResourcePrice(promotionHisResourceDO.getPurchasePrice());
        }
        return activityDO;
    }

}
