package com.deepexi.dxp.marketing.manager.promotion.impl.limit;

import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityCommodityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityConfigDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.TemplateLimitDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityParamsDTO;
import com.deepexi.dxp.marketing.enums.activity.limit.PATemplateTenantEnum;
import com.deepexi.dxp.marketing.manager.promotion.impl.BaseLimit;
import com.deepexi.dxp.middle.promotion.util.CollectionsUtil;
import com.deepexi.util.CollectionUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ming.zhong
 * @date created in 16:54 2019/11/25
 */
public class CalculateTenantLimit extends BaseLimit {

    private List<TenantLimitEnumsCalculate> calculateHelper = new ArrayList<>();


    public CalculateTenantLimit(TemplateLimitDTO templateLimitDTO, ActivityConfigDTO activity, ActivityParamsDTO params) {
        super(templateLimitDTO, activity, params);
    }

    @Override
    public Boolean calculate() {
        // 活动存储的值
        List<BaseActivityDTO> activityTenantLimit = super.getActivity().getTenantLimit();
        // 需求比较的值
        ActivityParamsDTO params = super.getParams();

        // 初始化计算信息
        init();
        return calculateTenantLimit(activityTenantLimit, params);
    }

    private interface TenantLimitEnumsCalculate {
        /**
         * 计算接口定义
         *
         * @param baseActivityDTO 设计活动时 配置存储h5商城的信息
         * @param params          传过来的参数
         * @return 不同类型是非成功
         */
        Boolean calculate(List<BaseActivityDTO> baseActivityDTO, ActivityParamsDTO params);
    }

    /**
     * @return 枚举类里面H5 类型的处理方法
     */
    private TenantLimitEnumsCalculate h5Enum() {
        return (baseActivity, params) -> {
            List<BaseActivityDTO> channel = baseActivity.stream()
                    .filter(val -> PATemplateTenantEnum.CHANNEL.getId().equals(val.getId()))
                    .collect(Collectors.toList());

            if (CollectionUtil.isEmpty(channel)) {
                return null;
            }
            List<String> propertyList = CollectionsUtil.getPropertyList(channel, BaseActivityDTO::getValue);
            ActivityCommodityDTO commoditie = params.getCommoditie();
            String oneInList = CollectionsUtil.findOneInList(propertyList, val -> val.equals(String.valueOf(commoditie.getChannelId())));
            return Objects.nonNull(oneInList);
        };
    }

    private TenantLimitEnumsCalculate mdEnum() {
        return (baseActivity, params) -> null;
    }

    /**
     * 枚举类每添加一种类型，都需要再这里初始化这张类型的处理结果，不然活动选择那种类型 会报错
     */
    private void init() {
        calculateHelper.add(h5Enum());
        calculateHelper.add(mdEnum());
    }


    private Boolean calculateTenantLimit(List<BaseActivityDTO> activityTenantLimit, ActivityParamsDTO params) {

        return tenantCalculate(activityTenantLimit, params);
    }

    /**
     * @param baseActivityDTO 渠道限制类型的活动信息
     * @param params          当前渠道获得的值
     * @return 计算的入口
     */
    private Boolean tenantCalculate(List<BaseActivityDTO> baseActivityDTO, ActivityParamsDTO params) {

        for (TenantLimitEnumsCalculate tenantLimitEnumsCalculate : calculateHelper) {
            Boolean result = tenantLimitEnumsCalculate.calculate(baseActivityDTO, params);
            boolean existFlag = Optional.ofNullable(result)
                    .isPresent();
            if (existFlag) {
                return result;
            }
        }
        return Boolean.FALSE;
    }

}
