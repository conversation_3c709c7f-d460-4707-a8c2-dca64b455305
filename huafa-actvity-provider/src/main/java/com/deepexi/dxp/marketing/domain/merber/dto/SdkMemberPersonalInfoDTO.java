package com.deepexi.dxp.marketing.domain.merber.dto;

import com.deepexi.util.domain.dto.BaseDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @Class: SdkMemberPersonalInfoDTO
 * @Description:
 * @Author: lizhongbao
 * @Date: 2020/7/28
 **/
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class SdkMemberPersonalInfoDTO extends BaseDTO {

    @ApiModelProperty("age")
    private Integer age;
    @ApiModelProperty("annualIncomeMax")
    private Long annualIncomeMax;
    @ApiModelProperty("annualIncomeMin")
    private Long annualIncomeMin;
    @ApiModelProperty("areaCode")
    private String areaCode;
    @ApiModelProperty("areaName")
    private String areaName;
    @ApiModelProperty("avatarUrl")
    private String avatarUrl;
    @ApiModelProperty("birthday")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date birthday;
    @ApiModelProperty("cityCode")
    private String cityCode;
    @ApiModelProperty("cityName")
    private String cityName;
    @ApiModelProperty("constellation")
    private String constellation;
    @ApiModelProperty("contactAddress")
    private String contactAddress;
    @ApiModelProperty("countryCode")
    private String countryCode;
    @ApiModelProperty("countryName")
    private String countryName;
    @ApiModelProperty("detailAddress")
    private String detailAddress;
    @ApiModelProperty("education")
    private String education;
    @ApiModelProperty("email")
    private String email;
    @ApiModelProperty("hobbies")
    private String hobbies;
    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("inArea")
    private String inArea;
    @ApiModelProperty("job")
    private String job;
    @ApiModelProperty("maritalStatus")
    private String maritalStatus;
    @ApiModelProperty("memberId")
    private Long memberId;
    @ApiModelProperty("name")
    private String name;
    @ApiModelProperty("nationality")
    private String nationality;
    @ApiModelProperty("nickName")
    private String nickName;
    @ApiModelProperty("phone")
    private String phone;
    @ApiModelProperty("provinceCode")
    private String provinceCode;
    @ApiModelProperty("provinceName")
    private String provinceName;
    @ApiModelProperty("qq")
    private String qq;
    @ApiModelProperty("sex")
    private Integer sex;
    @ApiModelProperty("streetCode")
    private String streetCode;
    @ApiModelProperty("streetName")
    private String streetName;
    @ApiModelProperty("wechat")
    private String wechat;
}
