package com.deepexi.dxp.marketing.controller.specify.openapi;

import com.deepexi.dxp.marketing.common.base.utils.CommonExceptionCode;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.HisResourceRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.PromotionHisResourceDTO;
import com.deepexi.dxp.marketing.service.specify.PromotionHisResourceService;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@Slf4j
@RequestMapping("/open-api/v1/open/promotion-his-resource")
@Api(description = "历史资源信息", value = "历史资源信息", tags = "PromotionHisResource")
public class PromotionHisResourceOpenApiController {

    @Autowired
    private PromotionHisResourceService promotionHisResourceService;

    @GetMapping("/list")
    public Data<PageBean<PromotionHisResourceDTO>> list(@Valid  HisResourceRequestDTO query) {
        if(query == null){
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "活动信息不能为空!");
        }
        return new Data<>(promotionHisResourceService.findPage(query));
    }

    @PostMapping("/listPost")
    public Data<PageBean<PromotionHisResourceDTO>> listPost(@RequestBody @Valid HisResourceRequestDTO query) {
        if(query == null){
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "活动信息不能为空!");
        }
        return new Data<>(promotionHisResourceService.findPage(query));
    }
}
