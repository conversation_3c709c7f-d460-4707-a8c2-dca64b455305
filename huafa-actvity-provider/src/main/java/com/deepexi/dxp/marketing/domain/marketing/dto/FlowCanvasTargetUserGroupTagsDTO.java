package com.deepexi.dxp.marketing.domain.marketing.dto;
import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import lombok.Data;


/**
 * 流程画布-圈人配置-标签和客群
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-07 16:03
 */
@Data
public class FlowCanvasTargetUserGroupTagsDTO extends SuperDTO {

    /**
     * 流程画布圈选用户配置表ID
     */
    private Long targetUserId;

    /**
     * 选取类型（1:标签,2:客群）
     */
    private Integer type;

    /**
     * 标签或客群ID
     */
    private Long refId;

    /**
     * 标签或客群名称
     */
    private String refName;

    /**
     * 标签或客群对应人数
     */
    private Integer memberNums;
}
