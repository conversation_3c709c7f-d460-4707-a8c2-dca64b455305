package com.deepexi.dxp.marketing.service.specify.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.deepexi.common.extension.AppRuntimeEnv;
import com.deepexi.dxp.marketing.common.base.utils.CommonExceptionCode;
import com.deepexi.dxp.marketing.config.HuafaConstantConfig;
import com.deepexi.dxp.marketing.constant.CommonConstant;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.MiniOrgRequestDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.ActivityPromotionChannelResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.query.specify.ActivityPromotionChannelQuery;
import com.deepexi.dxp.marketing.domain.promotion.request.specify.ActivityPromotionChannelCreateRequestDTO;
import com.deepexi.dxp.marketing.domain.promotion.request.specify.ActivityPromotionChannelUpdateRequestDTO;
import com.deepexi.dxp.marketing.domain.promotion.request.specify.OneCodeSceneCreateRequestDTO;
import com.deepexi.dxp.marketing.enums.resource.ActivityStatusEnum;
import com.deepexi.dxp.marketing.enums.specify.*;
import com.deepexi.dxp.marketing.service.specify.ActivityPromotionChannelService;
import com.deepexi.dxp.marketing.service.specify.MiniProgramService;
import com.deepexi.dxp.marketing.service.specify.SensorsService;
import com.deepexi.dxp.middle.promotion.dao.PromotionActivityDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityGroupDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityGroupRelatedDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityParticipationDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityPromotionChannelDAO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityGroupDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityParticipationDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityPromotionChannelDO;
import com.deepexi.util.BeanPowerHelper;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.StringUtil;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pojo.ObjectCloneUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Slf4j
public class ActivityPromotionChannelServiceImpl implements ActivityPromotionChannelService {

    @Autowired
    private ActivityPromotionChannelDAO activityPromotionChannelDAO;

    @Autowired
    private PromotionActivityDAO promotionActivityDAO;

    @Autowired
    private ActivityGroupDAO activityGroupDAO;

    @Autowired
    private MiniProgramService miniProgramService;

    @Autowired
    private SensorsService sensorsService;

    @Autowired
    private ActivityParticipationDAO activityParticipationDAO;

    @Autowired
    private HuafaConstantConfig huafaConstantConfig;

    @Autowired
    private ActivityGroupRelatedDAO activityGroupRelatedDAO;


    @Override
    public List<ActivityPromotionChannelResponseDTO> findAll(ActivityPromotionChannelQuery query) {
        QueryWrapper<ActivityPromotionChannelDO>  queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ActivityPromotionChannelDO::getActivityId,query.getActivityId());
        queryWrapper.lambda().eq(ActivityPromotionChannelDO::getPromotionType,query.getPromotionType());
        queryWrapper.lambda().eq(ActivityPromotionChannelDO::getCustom,1);//只查自定义的
        return ObjectCloneUtils.convertList(activityPromotionChannelDAO.list(queryWrapper),ActivityPromotionChannelResponseDTO.class);
    }

    @Override
    public Boolean deleteById(Long id) {
        ActivityPromotionChannelDO activityPromotionChannelDO = activityPromotionChannelDAO.getById(id);

        //判断是否为活动推广
        if(ActivityPromotionTypeEnum.ACTIVITY.getType().equals(activityPromotionChannelDO.getPromotionType())){
            //是否关联数据
            Long cnt = sensorsService.channelCountByActivityId(activityPromotionChannelDO);
            if(cnt > 0){
                throw new ApplicationException(CommonExceptionCode.INVALIDATION,"该渠道已产生关联数据，不可删除!");
            }
        }
        return activityPromotionChannelDAO.removeById(id);
    }

    @Override
    public Boolean update(ActivityPromotionChannelUpdateRequestDTO activityPromotionChannelUpdateRequestDTO) {
        ActivityPromotionChannelDO activityPromotionChannelDO = activityPromotionChannelUpdateRequestDTO.clone(ActivityPromotionChannelDO.class);
        if(Objects.isNull(activityPromotionChannelUpdateRequestDTO.getId())){
            return false;
        }
        ActivityPromotionChannelDO old = activityPromotionChannelDAO.getById(activityPromotionChannelUpdateRequestDTO.getId());

        QueryWrapper<ActivityPromotionChannelDO>  queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ActivityPromotionChannelDO::getChannelName,activityPromotionChannelUpdateRequestDTO.getChannelName());
        queryWrapper.lambda().eq(ActivityPromotionChannelDO::getDeleted,0);
        queryWrapper.lambda().ne(ActivityPromotionChannelDO::getId,activityPromotionChannelUpdateRequestDTO.getId());
        queryWrapper.lambda().eq(ActivityPromotionChannelDO::getActivityId,old.getActivityId());
        queryWrapper.lambda().eq(ActivityPromotionChannelDO::getPromotionType,old.getPromotionType());
        List<ActivityPromotionChannelDO> list = activityPromotionChannelDAO.list(queryWrapper);

        if(CollectionUtil.isNotEmpty(list)){
            throw new ApplicationException(CommonExceptionCode.INVALIDATION,"渠道名称不可重复!");
        }

        //判断是否为活动推广
        if(ActivityPromotionTypeEnum.ACTIVITY.getType().equals(activityPromotionChannelDO.getPromotionType())){
            Long cnt = sensorsService.channelCountByActivityId(old);
            if(cnt > 0){
                throw new ApplicationException(CommonExceptionCode.INVALIDATION,"该渠道已产生关联数据，不可修改!");
            }
        }



        //平台类型，1:微信小程序，2:百度小程序，3:头条小程序，4:抖音小程序，5:H5
        Integer appType  = AppTypeEnum.NOT_STARTED.getId();
        if(ChannelTypeEnum.WECHAT_WEBSITE.getId().equals(old.getType())){//h5
            appType  = AppTypeEnum.FAILED.getId();
        }

        old.setUpdatedTime(DateTime.now());
        Integer type = old.getType();
        Long activityId = old.getActivityId();
        String codeType = old.getCodeType();
        Integer codeId = old.getCodeId();
        Integer promotionType = old.getPromotionType();

        //部分更新
        BeanPowerHelper.mapCompleteOverrider(activityPromotionChannelDO, old);
        //activityPromotionChannelDAO.updateById(old);

        // 生成新的二维码
        Map<String, Object> params = new HashMap<>();
        params.put("id",activityId);
        params.put("channelName",activityPromotionChannelUpdateRequestDTO.getChannelName());
        if(ActivityPromotionTypeEnum.ACTIVITY.getType().equals(promotionType)){
            PromotionActivityDO promotionActivityDO = promotionActivityDAO.selectById(activityId);
            if(Objects.isNull(promotionActivityDO)){
                throw new ApplicationException("活动数据不存在");
            }
            Object sceneCode = promotionActivityDO.getExt().get("sceneCode");
            if(sceneCode == null){
                throw new ApplicationException(CommonExceptionCode.INVALIDATION,"活动未生成场景码不可以更新推广渠道!");
            }
            //活动推广
            ActivityChannelInfoEnum activityChannelInfoEnum = ActivityChannelInfoEnum.getById(promotionActivityDO.getPaTemplateId());
            if(ChannelTypeEnum.WECHAT_APPLET.getId().equals(type)){
              //  old.setUrl(activityChannelInfoEnum.getMiniProgramPath());
          /*      String wxCode = miniProgramService.createWxCode(activityId + "_" + CommonConstant.WX_CHANNEL_NAME,
                        WxCodeTypeEnum.MINI_PROGRAM_PC.getId(), activityChannelInfoEnum.getMiniProgramPath(), params);*/
                JSONObject oneCode = miniProgramService.updateOneCode(activityPromotionChannelUpdateRequestDTO.getChannelName(), null, codeType, sceneCode.toString(), MiniOrgTypeEnum.NATIONAL.getId()
                        , WxCodeTypeEnum.MINI_PROGRAM_PC.getId(), activityChannelInfoEnum.getMiniProgramPath(), params,promotionActivityDO.getPaTemplateId(),appType,codeId);
                //接口只进行名称修改，url不进行更新
                if(oneCode != null && StringUtil.isNotEmpty(oneCode.getStr("codeUrl"))){
                    old.setMiniProgramCode(oneCode.getStr("codeUrl"));
                }
            }
            else if(ChannelTypeEnum.WECHAT_WEBSITE.getId().equals(type)){//h5
              //  old.setUrl(activityChannelInfoEnum.getH5Path());
        /*        String wxCode = miniProgramService.createWxCode(activityId + "_" + CommonConstant.WEB_CHANNEL_NAME,
                        WxCodeTypeEnum.WEBCHAT_WEBSITE.getId(), activityChannelInfoEnum.getH5Path(), params);*/
                JSONObject oneCode = miniProgramService.updateOneCode(activityPromotionChannelUpdateRequestDTO.getChannelName(), null, codeType, sceneCode.toString(), MiniOrgTypeEnum.NATIONAL.getId()
                        , WxCodeTypeEnum.WEBCHAT_WEBSITE.getId(), huafaConstantConfig.ACT_H5_BASE_URL+activityChannelInfoEnum.getH5Path(), params,promotionActivityDO.getPaTemplateId(),appType,codeId);
                //接口只进行名称修改，url不进行更新
                if(oneCode != null && StringUtil.isNotEmpty(oneCode.getStr("codeUrl"))){
                    old.setQrCode(oneCode.getStr("codeUrl"));
                }
            }
        }else if(ActivityPromotionTypeEnum.TOPIC.getType().equals(promotionType)){
            //活动专题推广

            ActivityGroupDO activityGroupDO = activityGroupDAO.getById(activityId);
            if(Objects.isNull(activityGroupDO)){
                throw new ApplicationException("活动专题数据不存在");
            }
            String sceneCode = activityGroupDO.getSceneCode();

            String qrcodePath = "";
            String h5Path = "";
            if(ActivityTopicArrangementEnum.LIST.getCode().equals(activityGroupDO.getArrangement())){
                qrcodePath = ActivityTopicChannelInfoEnum.HF_ACTIVITY_TOPIC_LIST.getMiniProgramPath();
                h5Path = huafaConstantConfig.ACT_H5_BASE_URL+ActivityTopicChannelInfoEnum.HF_ACTIVITY_TOPIC_LIST.getH5Path();
            }else if(ActivityTopicArrangementEnum.AREA.getCode().equals(activityGroupDO.getArrangement())){
                qrcodePath = ActivityTopicChannelInfoEnum.HF_ACTIVITY_TOPIC_AREA.getMiniProgramPath();
                h5Path = huafaConstantConfig.ACT_H5_BASE_URL+ActivityTopicChannelInfoEnum.HF_ACTIVITY_TOPIC_AREA.getH5Path();
            }

            if(ChannelTypeEnum.WECHAT_APPLET.getId().equals(type)){
                //old.setUrl(qrcodePath);
         /*       String wxCode = miniProgramService.createWxCode(activityId + "_" + CommonConstant.WX_CHANNEL_NAME,
                        WxCodeTypeEnum.MINI_PROGRAM_PC.getId(), qrcodePath, params);*/
                JSONObject oneCode = miniProgramService.updateOneCode(activityPromotionChannelUpdateRequestDTO.getChannelName(), null, codeType, sceneCode.toString(), MiniOrgTypeEnum.NATIONAL.getId()
                        , WxCodeTypeEnum.MINI_PROGRAM_PC.getId(), qrcodePath, params,null,appType,codeId);
                //接口只进行名称修改，url不进行更新
                if(oneCode != null && StringUtil.isNotEmpty(oneCode.getStr("codeUrl"))){
                    old.setMiniProgramCode(oneCode.getStr("codeUrl"));
                }
            }
            else if(ChannelTypeEnum.WECHAT_WEBSITE.getId().equals(type)){//h5
                //old.setUrl(h5Path);
                /*String wxCode = miniProgramService.createWxCode(activityId + "_" + CommonConstant.WEB_CHANNEL_NAME,
                        WxCodeTypeEnum.WEBCHAT_WEBSITE.getId(), h5Path, params);*/
                JSONObject oneCode = miniProgramService.updateOneCode(activityPromotionChannelUpdateRequestDTO.getChannelName(), null, codeType, sceneCode.toString(), MiniOrgTypeEnum.NATIONAL.getId()
                        , WxCodeTypeEnum.WEBCHAT_WEBSITE.getId(), h5Path, params,null,appType,codeId);
                //接口只进行名称修改，url不进行更新
                if(oneCode != null && StringUtil.isNotEmpty(oneCode.getStr("codeUrl"))){
                    old.setQrCode(oneCode.getStr("codeUrl"));
                }
            }
        }
        return activityPromotionChannelDAO.updateById(old);

    }

    @Override
    public String save(ActivityPromotionChannelCreateRequestDTO activityPromotionChannelCreateRequestDTO) {

        ActivityPromotionChannelDO activityPromotionChannelDO = activityPromotionChannelCreateRequestDTO.clone(ActivityPromotionChannelDO.class);
        activityPromotionChannelDO.setTenantId(AppRuntimeEnv.getTenantId());
        activityPromotionChannelDO.setAppId(AppRuntimeEnv.getAppId());
        activityPromotionChannelDO.setCreatedTime(DateTime.now());
        activityPromotionChannelDO.setUpdatedBy(activityPromotionChannelDO.getCreatedBy());

        PromotionActivityDO promotionActivityDO = promotionActivityDAO.selectById(activityPromotionChannelCreateRequestDTO.getActivityId());

        if (ActivityPromotionTypeEnum.ACTIVITY.getType().equals(activityPromotionChannelCreateRequestDTO.getPromotionType()) && Objects.nonNull(promotionActivityDO) && promotionActivityDO.getStatus() > Integer.parseInt(ActivityStatusEnum.IN_PROGRESS.getId())){
            throw new ApplicationException("活动完成或终止或下架状态下不能生成一物一码！");
        }
        if (activityPromotionChannelCreateRequestDTO.getCustom() == 1) {
            QueryWrapper<ActivityPromotionChannelDO>  queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(ActivityPromotionChannelDO::getActivityId,activityPromotionChannelCreateRequestDTO.getActivityId());
            queryWrapper.lambda().eq(ActivityPromotionChannelDO::getPromotionType,activityPromotionChannelCreateRequestDTO.getPromotionType());
            queryWrapper.lambda().eq(ActivityPromotionChannelDO::getCustom,activityPromotionChannelCreateRequestDTO.getCustom());
            List<ActivityPromotionChannelDO> list = activityPromotionChannelDAO.list(queryWrapper);
            Assert.isTrue(list.size() < 20,"最多只支持20个渠道!");
            for (ActivityPromotionChannelDO promotionChannelDO : list) {
                Assert.isFalse(promotionChannelDO.getChannelName().equals(activityPromotionChannelCreateRequestDTO.getChannelName()),"已存在相同名称的渠道");
                Assert.isFalse(promotionChannelDO.getCodeType().equals(WxCodeTypeEnum.SIGN_CODE.getId().toString()),"签到码只能创建一个");
            }
        }

        // 生成新的二维码
        Map<String, Object> params = new HashMap<>();
        params.put("id",activityPromotionChannelCreateRequestDTO.getActivityId());
        params.put("channelName",activityPromotionChannelCreateRequestDTO.getChannelName());
        params.put("codeType",activityPromotionChannelCreateRequestDTO.getCodeType());

        //平台类型，1:微信小程序，2:百度小程序，3:头条小程序，4:抖音小程序，5:H5
        Integer appType  = AppTypeEnum.NOT_STARTED.getId();
        if(ChannelTypeEnum.WECHAT_WEBSITE.getId().equals(activityPromotionChannelCreateRequestDTO.getType())){//h5
            appType  = AppTypeEnum.FAILED.getId();
        }
        if(MiniOrgTypeEnum.NATIONAL.getId().equals(activityPromotionChannelCreateRequestDTO.getOrgType())){//全国不传项目
            activityPromotionChannelCreateRequestDTO.setChannelProjectList(Lists.newArrayList());
        }

        Map<String, Object> channelExt = new HashMap<>();
        channelExt.put("hfOrgList",activityPromotionChannelCreateRequestDTO.getChannelProjectList());
        channelExt.put("orgType",activityPromotionChannelCreateRequestDTO.getOrgType());
        if (activityPromotionChannelCreateRequestDTO.getInvalidTime() != null) {
            channelExt.put("invalidTime",activityPromotionChannelCreateRequestDTO.getInvalidTime());
        }

        if(ActivityPromotionTypeEnum.ACTIVITY.getType().equals(activityPromotionChannelDO.getPromotionType())){
            //获取指定活动类型的静态参数
            if(Objects.isNull(promotionActivityDO)){
                throw new ApplicationException("活动数据不存在");
            }

            Object sceneCode = promotionActivityDO.getExt().get("sceneCode");
            if(sceneCode == null){
                //重新生成推广码
                //String oneCodeScene = miniProgramService.createOneCodeScene(promotionActivityDO.getName()+promotionActivityDO.getId().toString(), MiniOrgTypeEnum.PROJECT.getId(), orgList);
               /* String oneCodeScene = miniProgramService.activityCreateOneCodeScene(promotionActivityDO, MiniOrgTypeEnum.PROJECT.getId(), orgList);
                if(StringUtil.isEmpty(oneCodeScene)){
                    throw new ApplicationException(CommonExceptionCode.INVALIDATION,"活动未生成场景码不可以创建推广渠道!");
                }
                sceneCode = oneCodeScene;*/
                throw new ApplicationException(CommonExceptionCode.INVALIDATION,"活动未生成场景码不可以创建推广渠道!");
            }


            activityPromotionChannelCreateRequestDTO.setPaTemplateId(promotionActivityDO.getPaTemplateId());

            ActivityChannelInfoEnum activityChannelInfoEnum = ActivityChannelInfoEnum.getById(promotionActivityDO.getPaTemplateId());

            if(ChannelTypeEnum.WECHAT_APPLET.getId().equals(activityPromotionChannelCreateRequestDTO.getType())){

                JSONObject oneCode = miniProgramService.createOneCode(activityPromotionChannelCreateRequestDTO.getChannelName()
                        , ObjectCloneUtils.convertList(activityPromotionChannelCreateRequestDTO.getChannelProjectList(), MiniOrgRequestDTO.class)
                        , activityPromotionChannelCreateRequestDTO.getCodeType(), sceneCode.toString(), activityPromotionChannelCreateRequestDTO.getOrgType()
                        , WxCodeTypeEnum.MINI_PROGRAM_PC.getId(), activityChannelInfoEnum.getMiniProgramPath(), params,promotionActivityDO.getPaTemplateId()
                        ,appType,activityPromotionChannelCreateRequestDTO.getRemark(), activityPromotionChannelCreateRequestDTO.getCreatedBy(), activityPromotionChannelCreateRequestDTO.getInvalidTime());
                if(oneCode != null){
                    activityPromotionChannelDO.setMiniProgramCode(oneCode.getStr("codeUrl"));
                    activityPromotionChannelDO.setCodeId(oneCode.getInt("id"));
                    activityPromotionChannelDO.setScene(oneCode.getStr("scene"));
                    activityPromotionChannelDO.setExt(channelExt);
                }
                activityPromotionChannelDO.setMiniUrl(activityChannelInfoEnum.getMiniProgramPath());////path?id+活动ID改成只返回path
            }
            else if(ChannelTypeEnum.WECHAT_WEBSITE.getId().equals(activityPromotionChannelCreateRequestDTO.getType())){
                activityPromotionChannelDO.setUrl(huafaConstantConfig.ACT_H5_BASE_URL+activityChannelInfoEnum.getH5Path());//path?id+活动ID改成只返回path

                JSONObject oneCode = miniProgramService.createOneCode(activityPromotionChannelCreateRequestDTO.getChannelName()
                        , ObjectCloneUtils.convertList(activityPromotionChannelCreateRequestDTO.getChannelProjectList(), MiniOrgRequestDTO.class)
                        , activityPromotionChannelCreateRequestDTO.getCodeType(), sceneCode.toString(), activityPromotionChannelCreateRequestDTO.getOrgType()
                        , WxCodeTypeEnum.WEBCHAT_WEBSITE.getId(), huafaConstantConfig.ACT_H5_BASE_URL+activityChannelInfoEnum.getH5Path()
                        , params,promotionActivityDO.getPaTemplateId(),appType,activityPromotionChannelCreateRequestDTO.getRemark()
                        , activityPromotionChannelCreateRequestDTO.getCreatedBy(), activityPromotionChannelCreateRequestDTO.getInvalidTime());
                if(oneCode != null){
                    activityPromotionChannelDO.setQrCode(oneCode.getStr("codeUrl"));
                    activityPromotionChannelDO.setCodeId(oneCode.getInt("id"));
                    activityPromotionChannelDO.setScene(oneCode.getStr("scene"));
                    activityPromotionChannelDO.setExt(channelExt);
                }
            }
        }else if(ActivityPromotionTypeEnum.TOPIC.getType().equals(activityPromotionChannelDO.getPromotionType())){
            //活动专题推广
            ActivityGroupDO activityGroupDO = activityGroupDAO.getById(activityPromotionChannelCreateRequestDTO.getActivityId());
            if(Objects.isNull(activityGroupDO)){
                throw new ApplicationException("活动专题数据不存在");
            }

            if(StringUtil.isEmpty(activityGroupDO.getSceneCode())){
                /*List<ActivityParticipationDO> listByActivityList = activityParticipationDAO.getListByActivityList(activityGroupRelatedDAO.getActivityIdsByGroupId(activityGroupDO.getId()));

                String oneCodeScene = miniProgramService.createOneCodeScene(activityGroupDO.getName()+"_"+activityGroupDO.getId(), MiniOrgTypeEnum.NATIONAL.getId(), ObjectCloneUtils.convertList(listByActivityList, MiniOrgRequestDTO.class));
                if(StringUtil.isEmpty(oneCodeScene)){
                    throw new ApplicationException(CommonExceptionCode.INVALIDATION,"活动未生成场景码不可以创建推广渠道!");
                }
                activityGroupDO.setSceneCode(oneCodeScene);
                activityGroupDAO.updateById(activityGroupDO);*/

                throw new ApplicationException(CommonExceptionCode.INVALIDATION,"活动未生成场景码不可以创建推广渠道!");
            }

            String sceneCode = activityGroupDO.getSceneCode();

            String qrcodePath = "";
            String h5Path = "";
            if(ActivityTopicArrangementEnum.LIST.getCode().equals(activityGroupDO.getArrangement())){
                qrcodePath = ActivityTopicChannelInfoEnum.HF_ACTIVITY_TOPIC_LIST.getMiniProgramPath();
                h5Path = huafaConstantConfig.ACT_H5_BASE_URL+ActivityTopicChannelInfoEnum.HF_ACTIVITY_TOPIC_LIST.getH5Path();
            }else if(ActivityTopicArrangementEnum.AREA.getCode().equals(activityGroupDO.getArrangement())){
                qrcodePath = ActivityTopicChannelInfoEnum.HF_ACTIVITY_TOPIC_AREA.getMiniProgramPath();
                h5Path = huafaConstantConfig.ACT_H5_BASE_URL+ActivityTopicChannelInfoEnum.HF_ACTIVITY_TOPIC_AREA.getH5Path();
            }

            if(ChannelTypeEnum.WECHAT_APPLET.getId().equals(activityPromotionChannelCreateRequestDTO.getType())){

                JSONObject oneCode = miniProgramService.createOneCode(activityPromotionChannelCreateRequestDTO.getChannelName()
                        , ObjectCloneUtils.convertList(activityPromotionChannelCreateRequestDTO.getChannelProjectList(), MiniOrgRequestDTO.class)
                        , activityPromotionChannelCreateRequestDTO.getCodeType(), sceneCode.toString(), activityPromotionChannelCreateRequestDTO.getOrgType()
                        , WxCodeTypeEnum.MINI_PROGRAM_PC.getId(), qrcodePath, params,null, appType,activityPromotionChannelCreateRequestDTO.getRemark()
                        , activityPromotionChannelCreateRequestDTO.getCreatedBy(), activityPromotionChannelCreateRequestDTO.getInvalidTime());
                if(oneCode != null){
                    activityPromotionChannelDO.setMiniProgramCode(oneCode.getStr("codeUrl"));
                    activityPromotionChannelDO.setCodeId(oneCode.getInt("id"));
                    activityPromotionChannelDO.setScene(oneCode.getStr("scene"));
                    activityPromotionChannelDO.setExt(channelExt);
                }
                activityPromotionChannelDO.setMiniUrl(qrcodePath);////path?id+活动ID改成只返回path
            }
            else if(ChannelTypeEnum.WECHAT_WEBSITE.getId().equals(activityPromotionChannelCreateRequestDTO.getType())){
                activityPromotionChannelDO.setUrl(h5Path);//path?id+活动ID改成只返回path

                JSONObject oneCode = miniProgramService.createOneCode(activityPromotionChannelCreateRequestDTO.getChannelName()
                        , ObjectCloneUtils.convertList(activityPromotionChannelCreateRequestDTO.getChannelProjectList(), MiniOrgRequestDTO.class)
                        , activityPromotionChannelCreateRequestDTO.getCodeType(), sceneCode.toString(), activityPromotionChannelCreateRequestDTO.getOrgType()
                        , WxCodeTypeEnum.WEBCHAT_WEBSITE.getId(), h5Path, params,null,appType,activityPromotionChannelCreateRequestDTO.getRemark()
                        , activityPromotionChannelCreateRequestDTO.getCreatedBy(), activityPromotionChannelCreateRequestDTO.getInvalidTime());
                if(oneCode != null){
                    activityPromotionChannelDO.setQrCode(oneCode.getStr("codeUrl"));
                    activityPromotionChannelDO.setCodeId(oneCode.getInt("id"));
                    activityPromotionChannelDO.setScene(oneCode.getStr("scene"));
                    activityPromotionChannelDO.setExt(channelExt);
                }
            }
        }
        activityPromotionChannelDAO.save(activityPromotionChannelDO);

        return ChannelTypeEnum.WECHAT_APPLET.getId().equals(activityPromotionChannelCreateRequestDTO.getType()) ? activityPromotionChannelDO.getMiniProgramCode() : activityPromotionChannelDO.getQrCode();
    }

    /**
     * 获取自定义名称数字
     * @param channelDOList
     * @return
     */
    private Integer getMaxNum(List<ActivityPromotionChannelDO> channelDOList){
        List<Integer> numList = Lists.newArrayList();
        String regEx="[^0-9]";
        Pattern p = Pattern.compile(regEx);
        channelDOList.forEach(item ->{
            //以数字结尾且除自定义外的字符串为数字
            if(item.getChannelName().matches("^.+?\\d$") && isNumeric(item.getChannelName().replace("自定义",""))){
                Matcher m = p.matcher(item.getChannelName());
                String trim = m.replaceAll("").trim();
                if(StringUtil.isNotEmpty(trim)){
                    numList.add(Integer.valueOf(trim));
                }
            }
        });
        if(CollectionUtil.isNotEmpty(numList)){
            return numList.stream().max(Comparator.comparing(Integer::intValue)).get();
        }
        return 0;
    }

    @Override
    public void createDefaults(Long activityId, Integer promotionType, Integer paTemplateId) {
        Map<String, Object> params = new HashMap<>();
        params.put("id",activityId);

        //生成默认推广类型
        if(ActivityPromotionTypeEnum.ACTIVITY.getType().equals(promotionType)){
            //获取指定活动类型的静态参数
            ActivityChannelInfoEnum activityChannelInfoEnum = ActivityChannelInfoEnum.getById(paTemplateId);

            //1、生成微信小程序渠道
            params.put("channelName",CommonConstant.WX_CHANNEL_NAME);
            String wxCode = miniProgramService.createWxCode(activityId + "_" + CommonConstant.WX_CHANNEL_NAME, WxCodeTypeEnum.MINI_PROGRAM_PC.getId(),activityChannelInfoEnum.getMiniProgramPath(), params);
            createDefault(activityId,0, ChannelTypeEnum.WECHAT_APPLET.getId(), promotionType, wxCode,null,CommonConstant.WX_CHANNEL_NAME,null);
            //2、生成微信内网页渠道
            params.put("channelName",CommonConstant.WEB_CHANNEL_NAME);
            String webCode = miniProgramService.createWxCode(activityId + "_" + CommonConstant.WEB_CHANNEL_NAME, WxCodeTypeEnum.WEBCHAT_WEBSITE.getId(),activityChannelInfoEnum.getH5Path(), params);
            createDefault(activityId,0, ChannelTypeEnum.WECHAT_WEBSITE.getId(), promotionType,null,activityChannelInfoEnum.getH5Path(),CommonConstant.WEB_CHANNEL_NAME,webCode);
            //3、生成抖音小程序渠道
            //TODO 待实现
            //params.put("channelName",CommonConstant.DY_CHANNEL_NAME);
            //createDefault(activityId,0, ChannelTypeEnum.TIKTOK_APPLET.getId(), promotionType,null,null,CommonConstant.DY_CHANNEL_NAME,null);

        }else if(ActivityPromotionTypeEnum.TOPIC.getType().equals(promotionType)){

            ActivityGroupDO activityGroupDO = activityGroupDAO.getById(activityId);
            if(Objects.isNull(activityGroupDO)){
                throw new ApplicationException("活动专题生成推广码失败");
            }

            String qrcodePath = "";
            String h5Path = "";
            if(ActivityTopicArrangementEnum.LIST.getCode().equals(activityGroupDO.getArrangement())){
                qrcodePath = ActivityTopicChannelInfoEnum.HF_ACTIVITY_TOPIC_LIST.getMiniProgramPath();
                h5Path = ActivityTopicChannelInfoEnum.HF_ACTIVITY_TOPIC_LIST.getH5Path();
            }else if(ActivityTopicArrangementEnum.AREA.getCode().equals(activityGroupDO.getArrangement())){
                qrcodePath = ActivityTopicChannelInfoEnum.HF_ACTIVITY_TOPIC_AREA.getMiniProgramPath();
                h5Path = ActivityTopicChannelInfoEnum.HF_ACTIVITY_TOPIC_AREA.getH5Path();
            }

            //1、生成微信小程序渠道
            params.put("channelName",CommonConstant.WX_CHANNEL_NAME);
            String wxCode = miniProgramService.createWxCode(activityId + "_" + CommonConstant.WX_CHANNEL_NAME, WxCodeTypeEnum.MINI_PROGRAM_PC.getId(),qrcodePath, params);
            createDefault(activityId,0, ChannelTypeEnum.WECHAT_APPLET.getId(), promotionType, wxCode,null,CommonConstant.WX_CHANNEL_NAME,null);
            //2、生成微信内网页渠道
            params.put("channelName",CommonConstant.WEB_CHANNEL_NAME);
            String webCode = miniProgramService.createWxCode(activityId + "_" + CommonConstant.WEB_CHANNEL_NAME, WxCodeTypeEnum.WEBCHAT_WEBSITE.getId(),h5Path, params);
            createDefault(activityId,0, ChannelTypeEnum.WECHAT_WEBSITE.getId(), promotionType,null,h5Path,CommonConstant.WEB_CHANNEL_NAME,webCode);
            //3、生成抖音小程序渠道
            //TODO 待实现
            //params.put("channelName",CommonConstant.DY_CHANNEL_NAME);
            //createDefault(activityId,0, ChannelTypeEnum.TIKTOK_APPLET.getId(), promotionType,null,null,CommonConstant.DY_CHANNEL_NAME,null);
        }

    }

    @Override
    public ActivityPromotionChannelResponseDTO getByActivity(Integer type, Integer promotionType, Long activityId) {
        QueryWrapper<ActivityPromotionChannelDO>  queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ActivityPromotionChannelDO::getActivityId,activityId);
        queryWrapper.lambda().eq(ActivityPromotionChannelDO::getPromotionType,promotionType);
        queryWrapper.lambda().eq(ActivityPromotionChannelDO::getType,type);
        queryWrapper.lambda().eq(ActivityPromotionChannelDO::getCustom,0);
        return activityPromotionChannelDAO.getOne(queryWrapper).clone(ActivityPromotionChannelResponseDTO.class);
    }

    public ActivityPromotionChannelDO getByActivity(Integer type,Long activityId,String codeType) {
        QueryWrapper<ActivityPromotionChannelDO>  queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ActivityPromotionChannelDO::getActivityId,activityId);
        queryWrapper.lambda().eq(ActivityPromotionChannelDO::getType,type);
        queryWrapper.lambda().eq(ActivityPromotionChannelDO::getCodeType,codeType);
        queryWrapper.lambda().eq(ActivityPromotionChannelDO::getCustom,0);
        return activityPromotionChannelDAO.getOne(queryWrapper);
    }

    @Override
    public Integer createOneCodeScene(OneCodeSceneCreateRequestDTO oneCodeSceneCreateRequestDTO) {
        if(ActivityPromotionTypeEnum.ACTIVITY.getType().equals(oneCodeSceneCreateRequestDTO.getPromotionType())){
            PromotionActivityDO promotionActivityDO = promotionActivityDAO.selectById(oneCodeSceneCreateRequestDTO.getActivityId());
            Object sceneCode = promotionActivityDO.getExt().get("sceneCode");
            if(sceneCode != null){
                return MapUtils.getInteger(promotionActivityDO.getExt(),"orgType");
            }
            List<ActivityParticipationDO> activityParticipationList = activityParticipationDAO.listByActivityId(oneCodeSceneCreateRequestDTO.getActivityId());
            List<MiniOrgRequestDTO> orgList = ObjectCloneUtils.convertList(activityParticipationList, MiniOrgRequestDTO.class);
            String oneCodeScene = miniProgramService.activityCreateOneCodeScene(promotionActivityDO, MiniOrgTypeEnum.PROJECT.getId(), orgList);

            if(StringUtil.isNotEmpty(oneCodeScene)){
                promotionActivityDO = promotionActivityDAO.selectById(oneCodeSceneCreateRequestDTO.getActivityId());
                return MapUtils.getInteger(promotionActivityDO.getExt(),"orgType");
            }

        }else if(ActivityPromotionTypeEnum.TOPIC.getType().equals(oneCodeSceneCreateRequestDTO.getPromotionType())){
            ActivityGroupDO activityGroupDO = activityGroupDAO.getById(oneCodeSceneCreateRequestDTO.getActivityId());
            if(StringUtil.isNotEmpty(activityGroupDO.getSceneCode())){
                return MapUtils.getInteger(activityGroupDO.getExt(),"orgType");
            }
            String sceneCode = miniProgramService.activityGroupCreateOneCodeScene(activityGroupDO, MiniOrgTypeEnum.PROJECT.getId());
            if(StringUtil.isNotEmpty(sceneCode)){
                activityGroupDO = activityGroupDAO.getById(oneCodeSceneCreateRequestDTO.getActivityId());
                return MapUtils.getInteger(activityGroupDO.getExt(),"orgType");
            }
        }
        return null;
    }

    @Override
    public ActivityPromotionChannelDO getByScene(String scene) {
        return activityPromotionChannelDAO.lambdaQuery().eq(ActivityPromotionChannelDO::getScene, scene).one();
    }

    private  void createDefault(Long activityId,Integer custom,Integer type,Integer promotionType,
                                String miniProgramCode,String url,String channelName,String qrCode) {
        ActivityPromotionChannelDO activityPromotionChannelDO = new ActivityPromotionChannelDO();
        activityPromotionChannelDO.setTenantId(AppRuntimeEnv.getTenantId());
        activityPromotionChannelDO.setAppId(AppRuntimeEnv.getAppId());
        activityPromotionChannelDO.setCreatedTime(DateTime.now());
        //activityPromotionChannelDO.setCreatedBy("admin");
        //activityPromotionChannelDO.setUpdatedBy("admin");
        activityPromotionChannelDO.setUrl(url);
        activityPromotionChannelDO.setChannelName(channelName);
        activityPromotionChannelDO.setCustom(custom);//自定义
        activityPromotionChannelDO.setMiniProgramCode(miniProgramCode);
        activityPromotionChannelDO.setQrCode(qrCode);
        activityPromotionChannelDO.setType(type);
        activityPromotionChannelDO.setActivityId(activityId);
        activityPromotionChannelDO.setPromotionType(promotionType);
        activityPromotionChannelDAO.save(activityPromotionChannelDO);
    }

    private static boolean isNumeric(String str){
        Pattern pattern = Pattern.compile("[0-9]*");
        return pattern.matcher(str).matches();

    }
}
