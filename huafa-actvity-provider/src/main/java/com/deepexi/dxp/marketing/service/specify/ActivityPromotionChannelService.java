package com.deepexi.dxp.marketing.service.specify;

import com.deepexi.dxp.marketing.domain.promotion.dto.specify.ActivityPromotionChannelResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.query.specify.ActivityPromotionChannelQuery;
import com.deepexi.dxp.marketing.domain.promotion.request.specify.ActivityPromotionChannelCreateRequestDTO;
import com.deepexi.dxp.marketing.domain.promotion.request.specify.ActivityPromotionChannelUpdateRequestDTO;
import com.deepexi.dxp.marketing.domain.promotion.request.specify.OneCodeSceneCreateRequestDTO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityPromotionChannelDO;

import java.util.List;

/**
 * 活动推广方式
 */
public interface ActivityPromotionChannelService {

    /**
     * 列表
     * @return
     */
    List<ActivityPromotionChannelResponseDTO> findAll(ActivityPromotionChannelQuery query);


    /**
     * 删除
     * @param id
     * @return
     */
    Boolean deleteById(Long id);

    /**
     * 修改
     * @param activityPromotionChannelUpdateRequestDTO
     * @return
     */
    Boolean update(ActivityPromotionChannelUpdateRequestDTO activityPromotionChannelUpdateRequestDTO);

    /**
     * 自定义
     * @return
     */
    String save(ActivityPromotionChannelCreateRequestDTO activityPromotionChannelCreateRequestDTO);

    /**
     * 创建默认渠道
     * @param activityId
     */
    void createDefaults(Long activityId, Integer promotionType, Integer paTemplateId);

    /**
     * 通过活动及类型获取默认渠道
     * @param type
     * @param activityId
     * @return
     */
    ActivityPromotionChannelResponseDTO getByActivity(Integer type, Integer promotionType, Long activityId);
    ActivityPromotionChannelDO getByActivity(Integer type,Long activityId,String codeType);
    /**
     * 创建场景码
     * @param oneCodeSceneCreateRequestDTO
     * @return
     */
    Integer createOneCodeScene(OneCodeSceneCreateRequestDTO oneCodeSceneCreateRequestDTO);

    ActivityPromotionChannelDO getByScene(String scene);
}
