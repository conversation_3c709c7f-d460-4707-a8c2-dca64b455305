package com.deepexi.dxp.marketing.service.specify.impl;

import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityVerifyRejectLogQuery;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityVerifyRejectLogResponseDTO;
import com.deepexi.dxp.marketing.service.specify.ActivityVerifyRejectLogService;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityVerifyRejectLogDAO;
import com.deepexi.util.pageHelper.PageBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ActivityVerifyRejectLogServiceImpl implements ActivityVerifyRejectLogService {

    @Autowired
    private ActivityVerifyRejectLogDAO activityVerifyRejectLogDAO;

    @Override
    public PageBean<ActivityVerifyRejectLogResponseDTO> findPage(ActivityVerifyRejectLogQuery query) {
        return activityVerifyRejectLogDAO.findPage(query);
    }
}
