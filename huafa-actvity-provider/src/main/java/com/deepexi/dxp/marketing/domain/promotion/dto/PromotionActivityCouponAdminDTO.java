package com.deepexi.dxp.marketing.domain.promotion.dto;

import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.ActivityRuleDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.ComdityLimitDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.CouponLimitDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 领券活动关联优惠券信息查询响应实体
 *
 * <AUTHOR> fengjun
 * @date 2020/4/3 14:08
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel
@Data
public class PromotionActivityCouponAdminDTO extends SuperDTO {

    private static final long serialVersionUID = -4613971908571182206L;
    /**
     * 活动信息集合，包含相关的优惠券集合
     */
    @ApiModelProperty("活动信息集合，包含相关的优惠券集合")
    private List<ActivityInfo> activityList;

    /**
     * 活动信息
     */
    @ApiModel
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class ActivityInfo extends SuperDTO {

        private static final long serialVersionUID = -1491790527211715879L;
        /**
         * 优惠券信息集合
         */
        @ApiModelProperty("优惠券信息集合")
        private List<CouponInfo> couponInfoList;

        /**
         * 0 全款可退 1 定金可退 2 不可退 （默认是0）
         */
        @ApiModelProperty("0 全款可退 1 定金可退 2 不可退 （默认是0）")
        private Integer returnMoneyType;

        /**
         * 活动id
         */
        @ApiModelProperty("活动id")
        private Long activityId;


        /**
         * 模板id 活动从模板来的 所以不难为空
         */
        @ApiModelProperty(value = "模板id 活动从模板来的 所以不难为空")
        private Integer paTemplateId;
        /**
         * 名称
         */
        @ApiModelProperty(value = "名称")
        private String activityName;
        /**
         * 编码
         */
        @ApiModelProperty(value = "编码")
        private String code;
        /**
         * 描述
         */
        @ApiModelProperty(value = "描述")
        private String description;
        /**
         * 活动状态
         */
        @ApiModelProperty(value = "活动状态")
        private Integer status;
        /**
         * 活动开始时间
         */
        @ApiModelProperty(value = "活动开始时间")
        @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
        private Date startTime;

        /**
         * 活动结束时间
         */
        @ApiModelProperty(value = "活动结束时间")
        @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
        private Date endTime;

        /**
         * 商品限制
         */
        @ApiModelProperty("商品限制")
        private ComdityLimitDTO commodity;

        /**
         * 活动策略限制
         */
        @ApiModelProperty("活动策略的规则")
        private List<ActivityRuleDTO> activityRuleDTOList;

        /**
         * 渠道限制
         */
        @ApiModelProperty("渠道限制")
        private List<BaseActivityDTO> tenantLimit;

        /**
         * 会员限制
         */
        @ApiModelProperty("会员限制")
        private List<BaseActivityDTO> userLimit;

        /**
         * 数量限制
         */
        @ApiModelProperty("数量限制")
        private List<BaseActivityDTO> numberLimit;

        /**
         * 优惠券限制
         */
        @ApiModelProperty("优惠券限制")
        private List<BaseActivityDTO> couponLimit;

        /**
         * 门店限制
         */
        @ApiModelProperty("门店限制")
        private List<BaseActivityDTO> shopLimit;

    }

    /**
     * 优惠券信息
     */
    @ApiModel
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class CouponInfo extends SuperDTO {

        private static final long serialVersionUID = -8375220790862432668L;
        /**
         * 优惠券名称
         */
        @ApiModelProperty(value = "优惠券名称")
        private String couponName;

        /**
         * 优惠券类型
         */
        @ApiModelProperty(value = "优惠券类型")
        private String couponType;

        /**
         * 代金券面值|折扣券折扣
         */
        @ApiModelProperty(value = "代金券面值|折扣券折扣")
        private BigDecimal couponValue;

        /**
         * 是否启用 0 禁用 1 启用
         */
        @ApiModelProperty(value = "是否启用 0 禁用 1 启用")
        private Integer status;

        /**
         * 使用条件适用金额
         */
        @ApiModelProperty(value = "使用条件适用金额")
        private BigDecimal condition;

        /**
         * 使用限制json
         */
        @ApiModelProperty("限制")
        private CouponLimitDTO couponLimit;

        /**
         * 使用限制json
         */
        @ApiModelProperty("限制")
        private String limits;




    }

}