package com.deepexi.dxp.marketing.controller.specify.adminapi;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityParticipationQuery;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityParticipationDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.ActivityPromotionChannelResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.query.specify.ActivityPromotionChannelQuery;
import com.deepexi.dxp.marketing.domain.promotion.request.specify.ActivityPromotionChannelCreateRequestDTO;
import com.deepexi.dxp.marketing.domain.promotion.request.specify.ActivityPromotionChannelUpdateRequestDTO;
import com.deepexi.dxp.marketing.domain.promotion.request.specify.OneCodeSceneCreateRequestDTO;
import com.deepexi.dxp.marketing.service.specify.ActivityParticipationService;
import com.deepexi.dxp.marketing.service.specify.ActivityPromotionChannelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 活动推广方式
 */
@RestController
@Slf4j
@RequestMapping("/admin-api/v1/promotion-channel")
@Api(description = "活动推广方式", value = "活动推广方式", tags = "PromotionActivityChannel")
public class ActivityPromotionChannelController {

    @Autowired
    private ActivityPromotionChannelService activityPromotionChannelService;

    @Autowired
    private ActivityParticipationService activityParticipationService;

    @PostMapping("/list")
    @ApiOperation(value="活动推广方式列表", notes = "活动推广方式列表")
    public Data<List<ActivityPromotionChannelResponseDTO>> list(@RequestBody @Valid ActivityPromotionChannelQuery query){
        return new Data<>(activityPromotionChannelService.findAll(query));
    }

    @DeleteMapping("/delete")
    @ApiOperation(value="删除活动推广渠道", notes = "删除活动推广渠道")
    public Data<Boolean> delete(@RequestBody Long id) {
        return new Data<>(activityPromotionChannelService.deleteById(id));
    }


    @PostMapping("/save")
    @ApiOperation(value = "添加活动推广方式")
    public Data<Boolean> save(@RequestBody @Valid ActivityPromotionChannelCreateRequestDTO activityPromotionChannelCreateRequestDTO){
        return new Data<>(activityPromotionChannelService.save(activityPromotionChannelCreateRequestDTO) != null);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新活动推广方式")
    public Data<Boolean> update(@RequestBody @Valid ActivityPromotionChannelUpdateRequestDTO requestDTO){
        return new Data<>(activityPromotionChannelService.update(requestDTO));
    }

    @GetMapping("/detail")
    @ApiOperation(value="通用渠道详情", notes = "通用渠道详情")
    public Data<ActivityPromotionChannelResponseDTO> detail(@RequestParam Integer type,@RequestParam Integer promotionType,@RequestParam  Long activityId){
        return new Data<>(activityPromotionChannelService.getByActivity(type,promotionType,activityId));
    }

    @PostMapping("/getSelectByActivity")
    @ApiOperation(value="通用渠道下拉选项", notes = "通用渠道下拉选项")
    public Data<List<ActivityParticipationDTO>> getSelectByActivity(@RequestBody @Valid ActivityParticipationQuery query){
        return new Data<>(activityParticipationService.getSelectActivity(query));
    }

    @PostMapping("/createOneCodeScene")
    @ApiOperation(value = "手动创建场景码")
    public Data<Integer> createOneCodeScene(@RequestBody @Valid OneCodeSceneCreateRequestDTO oneCodeSceneCreateRequestDTO){
        return new Data<>(activityPromotionChannelService.createOneCodeScene(oneCodeSceneCreateRequestDTO));
    }
}
