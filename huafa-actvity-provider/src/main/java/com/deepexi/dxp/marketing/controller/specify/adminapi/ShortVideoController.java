package com.deepexi.dxp.marketing.controller.specify.adminapi;

import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityGroupQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityGroupRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityGroupRelatedResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityGroupResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.request.PromotionActivityDeleteRequest;
import com.deepexi.dxp.marketing.enums.specify.ActivityGroupEnum;
import com.deepexi.dxp.marketing.enums.status.ActivityOnOffShelfStatus;
import com.deepexi.dxp.marketing.extension.HuafaRuntimeEnv;
import com.deepexi.dxp.marketing.service.specify.ActivityGroupService;
import com.deepexi.dxp.marketing.service.specify.GoodAnchorService;
import com.deepexi.dxp.marketing.service.specify.MiniProgramService;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 短视频接口controller
 */
@RestController
@RequestMapping("/admin-api/v1/video")
@Api(value = "短视频接口",tags = {"短视频接口"})
public class ShortVideoController {

    @Resource
    private MiniProgramService miniProgramService;

    @Resource
    private GoodAnchorService goodAnchorService;

    @ApiOperation("分页获取个人上架视频列表列表")
    @PostMapping("/page")
    public String page(@RequestBody Map<String,Object> params) {
        return miniProgramService.page(params);
    }

    @ApiOperation("获取区域和公众号列表")
    @GetMapping("/getAreas")
    public String getAreas() {
        return miniProgramService.getAreas();
    }

    @ApiOperation("根据公众号获取个人账号号列表")
    @GetMapping("/getAccounts")
    public String getAccounts(String projectId) {
        return miniProgramService.getAccounts(projectId);
    }

    @ApiOperation(value = "下架")
    @PostMapping("/off")
    public Data<Boolean> offShelf(@RequestParam Long id) {
        return new Data<>(goodAnchorService.changeStauts(id, ActivityOnOffShelfStatus.OFF.getId()));
    }

    @ApiOperation(value = "上架")
    @PostMapping("/on")
    public Data<Boolean> onShelf(@RequestParam Long id) {
        return new Data<>(goodAnchorService.changeStauts(id, ActivityOnOffShelfStatus.ON.getId()));
    }

    @DeleteMapping("/delete")
    @ApiOperation(value="删除作品", notes = "删除作品")
    public Data<Boolean> delete(@RequestParam Long id) {
        return new Data<>(goodAnchorService.delete(id));
    }
}
