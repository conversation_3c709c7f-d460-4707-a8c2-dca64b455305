package com.deepexi.dxp.marketing.service.specify.impl;

import com.deepexi.common.extension.AppRuntimeEnv;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.MarketingKpiItemGroupQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.MarketingKpiQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.KpiOperationDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.MarketingKpiExtDTO;
import com.deepexi.dxp.marketing.service.specify.MarketingKpiService;
import com.deepexi.dxp.marketing.utils.CodeUtils;
import com.deepexi.dxp.middle.marketing.dao.specify.MarketingKpiDAO;
import com.deepexi.dxp.middle.marketing.dao.specify.MarketingKpiFormulaDAO;
import com.deepexi.dxp.middle.marketing.dao.specify.MarketingKpiRouteMapNodeItemsDAO;
import com.deepexi.dxp.middle.marketing.domain.dto.MarketingKpiDTO;
import com.deepexi.dxp.middle.marketing.domain.dto.MarketingKpiFormulaDTO;
import com.deepexi.dxp.middle.marketing.domain.dto.MarketingKpiRouteMapNodeItemsDTO;
import com.deepexi.dxp.middle.marketing.domain.entity.MarketingKpiDO;
import com.deepexi.dxp.middle.marketing.domain.entity.MarketingKpiFormulaDO;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.StringUtil;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import com.deepexi.util.pojo.ObjectCloneUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 主动营销kpi原生指标/派生指标Service
 *
 * @Author: HuangBo.
 * @Date: 2020/5/16 12:04
 */

@Service
@Slf4j
@Transactional(readOnly = true)
public class MarketingKpiServiceImpl implements MarketingKpiService {


    @Autowired
    private MarketingKpiFormulaDAO marketingKpiFormulaDao;

    @Autowired
    private MarketingKpiDAO marketingKpiDao;

    @Autowired
    private MarketingKpiRouteMapNodeItemsDAO marketingKpiRouteMapNodeItemsDao;


    @Override
    @Transactional(rollbackFor = ApplicationException.class, readOnly = false)
    public Boolean save(MarketingKpiDTO dtoEntity) {

        Boolean aBoolean;
        if (Objects.nonNull(dtoEntity.getId())){
            MarketingKpiDO byId = marketingKpiDao.getById(dtoEntity.getId());
            aBoolean = existByName(dtoEntity.getTenantId(), dtoEntity.getName(), byId.getName());
        }else{
            aBoolean = existByName(dtoEntity.getTenantId(), dtoEntity.getName(), null);
        }
        if (aBoolean){
            throw new ApplicationException("名称已存在");
        }

        List<MarketingKpiFormulaDTO> kpiItemsFormulaDTOList = dtoEntity.getFormulaList();
        if (Objects.isNull(dtoEntity.getId())) {
            dtoEntity.setCode(CodeUtils.unRepeatSixCode());
        }
        boolean res = false;
        if (MarketingKpiDO.DERIVATIVE_TYPE.equals(dtoEntity.getType())) {
            // 保存派生指标
            res = marketingKpiDao.save(dtoEntity);
        } else {
            // 保存原生指标
            res = marketingKpiDao.saveOriginalKpiItems(dtoEntity);
        }

        if (res && MarketingKpiDO.DERIVATIVE_TYPE.equals(dtoEntity.getType())) {
            // 先删除派生指标公式集合
            marketingKpiFormulaDao.deleteByKpiItems(dtoEntity.getId());
            kpiItemsFormulaDTOList.forEach(e -> {
                e.setKpiId(dtoEntity.getId());
            });
            // 批量保存派生指标公式集合
            List<MarketingKpiFormulaDO> kpiItemsFormulaDOList = ObjectCloneUtils.convertList(
                    kpiItemsFormulaDTOList, MarketingKpiFormulaDO.class);
            res = marketingKpiFormulaDao.saveBatch(kpiItemsFormulaDOList);
            assembleKpiItemsDTO(dtoEntity);
        }
        return res;
    }

    @Override
    public List<MarketingKpiDTO> queryList(MarketingKpiQuery query,Boolean isFormula) {
        List<MarketingKpiDTO> marketingKpiList = marketingKpiDao.queryList(query);
        if (CollectionUtil.isNotEmpty(marketingKpiList) && isFormula){
            List<Long> kpiIds = marketingKpiList.stream().map(MarketingKpiDTO::getId).collect(Collectors.toList());
            List<MarketingKpiFormulaDTO> marketingKpiFormulaList = marketingKpiFormulaDao.queryListByKpiItemsIds(kpiIds);
            Map<Long, List<MarketingKpiFormulaDTO>> collect = Maps.newHashMap();
            if (CollectionUtil.isNotEmpty(marketingKpiFormulaList)){
                collect = marketingKpiFormulaList.stream().collect(Collectors.groupingBy(MarketingKpiFormulaDTO::getKpiId));
            }
            Map<Long, List<MarketingKpiFormulaDTO>> finalCollect = collect;
            marketingKpiList.forEach(kpi->{
                kpi.setFormulaList(finalCollect.get(kpi.getId()));
            });
        }
        return marketingKpiList;
    }

    @Override
    public PageBean<MarketingKpiDTO> pageList(MarketingKpiQuery query) {
        return marketingKpiDao.pageList(query);
    }

    @Override
    public MarketingKpiDTO queryById(MarketingKpiQuery query) {
        MarketingKpiDTO kpiItemsDTO = marketingKpiDao.queryById(query.getId());
        if (Objects.isNull(kpiItemsDTO)) {
            return null;
        }
        if (MarketingKpiQuery.DERIVATIVE_TYPE.equals(kpiItemsDTO.getType())) {
            kpiItemsDTO.setFormulaList(marketingKpiFormulaDao.queryList(query.getId()));
        }
        return kpiItemsDTO;
    }

    @Override
    public List<Object> queryByColumn(MarketingKpiQuery query) {
        return marketingKpiDao.queryByColumn(query);
    }

    @Override
    @Transactional(rollbackFor = ApplicationException.class, readOnly = false)
    public Boolean processStatus(Long id, Integer status) {
        return marketingKpiDao.processStatus(id, status);
    }



    @Override
    public Boolean existByName(String tenantId, String name, String oldName) {
        if (StringUtil.isBlank(tenantId)) {
            tenantId = AppRuntimeEnv.getTenantId();
        }
        if (StringUtil.isBlank(StringUtils.trimWhitespace(name))) {
            throw new ApplicationException("名称不能为空.");
        }
        if (StringUtil.isNotBlank(oldName) && name.equals(oldName)) {
            return false;
        }
        return CollectionUtil.isNotEmpty(marketingKpiDao.queryByName(tenantId, name));
    }


    private MarketingKpiDTO assembleKpiItemsDTO(MarketingKpiDTO kpiItemsDTO) {
        if (MarketingKpiDO.DERIVATIVE_TYPE.equals(kpiItemsDTO.getType())) {
            List<MarketingKpiFormulaDTO> formulaDTOList = marketingKpiFormulaDao.queryList(kpiItemsDTO.getId());
            kpiItemsDTO.setFormulaList(formulaDTOList);
        }
        return kpiItemsDTO;
    }

    @Override
    public List<KpiOperationDTO> queryOperationList(Long id, String tenantId) {
        return marketingKpiDao.queryOperationList(id, tenantId);
    }


    @Override
    public List<MarketingKpiExtDTO> queryByKpiItemGroup(MarketingKpiItemGroupQuery query) {
        return marketingKpiDao.queryByKpiItemGroup(query.getGroupId(), query.getStatus(), AppRuntimeEnv.getTenantId());
    }

    @Override
    @Transactional(readOnly = false)
    public Boolean deleteByKpi(Long id) {
        MarketingKpiRouteMapNodeItemsDTO byKpiId = marketingKpiRouteMapNodeItemsDao.findByKpiId(id);
        if (byKpiId != null){
            throw new ApplicationException("当前指标已关联路径，不可删除");
        }
        return marketingKpiDao.deleteByKpi(id);
    }

}
