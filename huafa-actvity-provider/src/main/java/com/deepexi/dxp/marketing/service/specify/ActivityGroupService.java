package com.deepexi.dxp.marketing.service.specify;

import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityAnalysisQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityGroupQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityGroupRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityTopicRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.*;
import com.deepexi.util.pageHelper.PageBean;

/**
 * <AUTHOR>
 */
public interface ActivityGroupService {

    /**
     * 活动组添加
     * @param requestDTO
     * @return
     */
    Boolean save(ActivityGroupRequestDTO requestDTO);

    /**
     * 活动组编辑
     * @param requestDTO
     * @return
     */
    Boolean update(ActivityGroupRequestDTO requestDTO);

    /**
     * 活动列表
     * @param query
     * @return
     */
    PageBean<ActivityGroupResponseDTO> pageList(ActivityGroupQuery query);

    /**
     * 详情
     * @param id
     * @return
     */
    ActivityGroupResponseDTO detail(Long id);

    /**
     * 活动组关联活动详情
     * @param query
     * @return
     */
    PageBean<ActivityGroupRelatedResponseDTO> activityList(ActivityGroupQuery query);

    /**
     * 删除活动组
     * @param id
     * @return
     */
    Boolean delete(Long id);

    /**
     * 活动和活动组列表查询
     * @param query
     * @return
     */
    PageBean<ActivityAndActivityGroupResponseDTO> activityAndActivityGroupList(ActivityGroupQuery query);

    /**
     * 新增活动专题
     * @param requestDTO
     * @return
     */
    Boolean saveTopic(ActivityTopicRequestDTO requestDTO);

    /**
     * 编辑活动专题
     * @param requestDTO
     * @return
     */
    Boolean updateTopic(ActivityTopicRequestDTO requestDTO);

    /**
     * 活动专题分析-数据概览
     * @param id
     * @return
     */
    ActivityTopicAnalysisResponseDTO analysisOverview(Long id);

    /**
     * 活动专题分析-活动详情
     * @param query
     * @return
     */
    PageBean<ActivityTopicAnalysisActivityInfoResponseDTO> analysisActivityInfo(ActivityAnalysisQuery query);

    /**
     * 活动专题分析-资源数据
     * @param query
     * @return
     */
    PageBean<ActivityTopicAnalysisResourceResponseDTO> analysisResourceInfo(ActivityAnalysisQuery query);
}
