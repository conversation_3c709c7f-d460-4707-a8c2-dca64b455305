package com.deepexi.dxp.marketing.manager.promotion.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.deepexi.dxp.marketing.common.base.utils.CommonExceptionCode;
import com.deepexi.dxp.marketing.config.HuafaConstantConfig;
import com.deepexi.dxp.marketing.constant.PayConstant;
import com.deepexi.dxp.marketing.constant.RedisConstants;
import com.deepexi.dxp.marketing.converter.ActivityExtConverter;
import com.deepexi.dxp.marketing.converter.ActivityInfoConverter;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.CustomerFeedbackQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.*;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityOrderResponseVO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityParticipationGroupResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.PromotionActivityDetailDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.PromotionActivityDetailPostResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.PromotionActivityListPostVO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.ResourceHisDetailResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.TagItemDTO;
import com.deepexi.dxp.marketing.domain.promotion.query.specify.PromotionHisResourceQuery;
import com.deepexi.dxp.marketing.domain.specify.dto.ActivityFormFeedbackDTO;
import com.deepexi.dxp.marketing.enums.activity.limit.PATemplateBaseEnum;
import com.deepexi.dxp.marketing.enums.activity.strategy.StrategyGroupEnum;
import com.deepexi.dxp.marketing.enums.common.AssociationTypeEnum;
import com.deepexi.dxp.marketing.enums.coupon.WhetherEnum;
import com.deepexi.dxp.marketing.enums.specify.*;
import com.deepexi.dxp.marketing.manager.promotion.PromotionActivityManager;
import com.deepexi.dxp.marketing.service.promotion.PromotionActivityMiddleService;
import com.deepexi.dxp.marketing.service.specify.*;
import com.deepexi.dxp.middle.marketing.domain.dto.MarketingKpiRouteMapDTO;
import com.deepexi.dxp.middle.promotion.converter.ActivityConfigConverter;
import com.deepexi.dxp.middle.promotion.converter.specify.LuckyDrawConverter;
import com.deepexi.dxp.middle.promotion.dao.PromotionActivityDAO;
import com.deepexi.dxp.middle.promotion.dao.PromotionActivityLimitDAO;
import com.deepexi.dxp.middle.promotion.dao.impl.specify.PromotionActivityAssociationService;
import com.deepexi.dxp.middle.promotion.dao.specify.*;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityLimitDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.*;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.DateUtils;
import com.deepexi.util.JsonUtil;
import com.deepexi.util.StringUtil;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pojo.CloneDirection;
import com.deepexi.util.pojo.ObjectCloneUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.ConvertUtils;
import org.redisson.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/12/14 11:42
 */
@Slf4j
@Service
public class PromotionActivityManagerImpl implements PromotionActivityManager {

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private PromotionActivityMiddleService promotionActivityMiddleService;

    @Autowired
    private MarketingKpiRouteMapService marketingKpiRouteMapService;

    @Autowired
    private ActivityParticipationService activityParticipationService;

    @Autowired
    private PromotionHisResourceService promotionHisResourceService;

    @Autowired
    private PromotionActivityDAO promotionActivityDAO;

    @Autowired
    private ActivityPageDAO activityPageDAO;

    @Autowired
    private ActivityPageShareDAO activityPageShareDAO;

    @Autowired
    private PromotionHisResourceDAO promotionHisResourceDAO;

    @Autowired
    private ActivityPartakeLogDAO activityPartakeLogDAO;

    @Resource
    public AsynchronousService asynchronousService;
    @Resource
    private InteractionCenterService interactionCenterService;
    @Resource
    private HuafaConstantConfig huafaConstantConfig;
    @Resource
    public ActivityVerifyDAO activityVerifyDAO;
    @Resource
    private CustomerFeedbackDAO customerFeedbackDAO;
    @Autowired
    private IncentiveService incentiveService;

    @Autowired
    private PromotionActivityLimitDAO promotionActivityLimitDAO;

    @Autowired
    private UserProjectService userProjectService;

    @Lazy
    @Autowired
    private PromotionActivityService promotionActivityService;

    @Resource
    private PromotionActivityAssociationService associationService;
    private static final Integer RETRY_TIME = 3;

    @Override
    public PromotionActivityDetailDTO cacheActInfo(Long activityId) {

        PromotionActivityDetailDTO redisActivityDetailDTO = JSON.parseObject(String.valueOf(redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO + activityId).get()), PromotionActivityDetailDTO.class);
        RMap hisResourceMap = redissonClient.getMap(RedisConstants.CACHE_PREV_KEY_ACT_RESOURCE_INFO + activityId);
        if (Objects.nonNull(redisActivityDetailDTO) && Objects.nonNull(hisResourceMap) && CollectionUtil.isNotEmpty(hisResourceMap)) {
            return redisActivityDetailDTO;
        }
        return this.forceCacheActInfo(activityId);
    }

    @Override
    public PromotionActivityDetailDTO forceCacheActInfo(Long activityId) {

        PromotionActivityDetailDTO actDto = this.getDetailDto(activityId);

        List<ResourceHisDetailResponseDTO> prizeList = actDto.getPrizeList();

        //确定缓存过期时间
        long expireTime = 0L;
        if (Objects.nonNull(actDto.getEndTime())) {
            expireTime = actDto.getEndTime().getTime() - System.currentTimeMillis();
            expireTime = (expireTime / 1000) + RedisConstants.EXTRA_EXPIRE_SECOND;
        } else {
            expireTime = RedisConstants.SECKILL_EXPIRE_SECOND;
        }

        String lockKey = RedisConstants.CACHE_PREV_KEY_ACT_INFO_LOCK + activityId;
        RLock lock = redissonClient.getLock(lockKey);
        int retry = 0;
        Boolean flag = false;
        try {
            /**
             * 获取分布式锁重试次数
             */
            int RETRY = 3;
            while (retry < RETRY) {
                log.info("获取分布式锁重试次数:" + retry);
                if (lock.tryLock(100, 10 * 1000, TimeUnit.MILLISECONDS)) {
                    log.info("----------------------获取分布式锁------------------");

                    //缓存活动基本信息
                    RBucket actbucket = redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO + actDto.getActivityId());
                    //actDto.setPrizeList(null);
                    actbucket.getAndSet(JSON.toJSONString(actDto));
                    actbucket.expire(expireTime, TimeUnit.SECONDS);
                    log.info("==========缓存活动成功，活动id={}", actDto.getActivityId());

                    //缓存活动-资源列表
                    RMap resourceMap = redissonClient.getMap(RedisConstants.CACHE_PREV_KEY_ACT_RESOURCE_INFO + actDto.getActivityId());
                    Map prizeMap = prizeList.stream().collect(Collectors.toMap(item -> item.getId().intValue(), item -> JSON.toJSONString(item), (newId, oldId) -> JSON.toJSONString(newId)));
                    resourceMap.putAll(prizeMap);
                    resourceMap.expire(expireTime, TimeUnit.SECONDS);
                    log.info("==========缓存活动-资源列表成功=============");

                    //缓存活动-每个资源的剩余数量
                    RAtomicLong atomicLong = null;
                    for (ResourceHisDetailResponseDTO resourceHisDetailResponseDTO : prizeList) {
                        atomicLong = redissonClient.getAtomicLong(RedisConstants.CACHE_PREV_KEY_ACT_RESOURCE_INFO_COUNT + resourceHisDetailResponseDTO.getId());
                        atomicLong.set(resourceHisDetailResponseDTO.getRemainingQuantity());
                        atomicLong.expire(expireTime, TimeUnit.SECONDS);
                        log.info("缓存活动-资源剩余数量成功，资源id:{}", resourceHisDetailResponseDTO.getId());
                    }

                    //手动释放锁
                    if (lock.isHeldByCurrentThread()) {
                        log.info("----------------------手动释放锁------------------");
                        lock.unlock();
                        break;
                    }

                } else {
                    //获取锁失败,次数加1
                    retry++;
                }
            }
        } catch (InterruptedException e) {
            log.error("redis分布式锁异常,key:{},异常信息:{}", lockKey, e.getMessage());
            throw new ApplicationException("访问人数过多，请稍后再试");
        }

        if (retry == RETRY_TIME) {
            lock.unlock();
            throw new ApplicationException("访问人数过多，请稍后再试");
        }
        //活动状态变更，删除列表缓存
        clearAllRunningListCache();
        return actDto;
    }

    @Override
    public void clearAllRunningListCache() {
        //活动状态有变更，删除列表缓存
        RSet<String> keysSet = redissonClient.getSet(RedisConstants.CACHE_PREV_KEY_RUNNINGACT_INFO_KEYS);
        keysSet.forEach(key -> redissonClient.getBucket(key).delete());
        keysSet.clear();
    }
    @Override
    public void forceDelCacheActInfo(Long activityId) {
        log.info("正在删除已结束活动，活动id：{}", activityId);
        //删除缓存中的活动基本信息
        redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO + activityId).delete();
        //删除缓存中的活动-资源信息
        redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_RESOURCE_INFO + activityId).delete();

    }

    @Override
    public PromotionActivityDetailDTO getCacheActivityById(Long activityId) {
        Object ob = redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO + activityId).get();
        if (Objects.isNull(ob)) {
            return null;
        }
        PromotionActivityDetailDTO promotionActivityDetailDTO = JSON.parseObject(String.valueOf(ob), PromotionActivityDetailDTO.class);
        if (
                StrategyGroupEnum.HF_SECKILL_ACT.getId().equals(promotionActivityDetailDTO.getPaTemplateId().toString()) ||
                        StrategyGroupEnum.HF_COUPON_ACT.getId().equals(promotionActivityDetailDTO.getPaTemplateId().toString())
        ) {
            List<ResourceHisDetailResponseDTO> prizeList = promotionActivityDetailDTO.getPrizeList();
            for (ResourceHisDetailResponseDTO prize : prizeList) {
                Long quantity = redissonClient.getAtomicLong(RedisConstants.CACHE_PREV_KEY_ACT_RESOURCE_INFO_COUNT + prize.getId()).get();
                prize.setRemainingQuantity(quantity.intValue());
            }
        }
        return promotionActivityDetailDTO;
    }

    @Override
    public void incrUserGetResourceCount(String phone, Long hisResourceId, Long activityId) {

        PromotionActivityDetailDTO redisActivityDetailDTO = JSON.parseObject(String.valueOf(redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO + activityId).get()), PromotionActivityDetailDTO.class);
        if (Objects.isNull(redisActivityDetailDTO)) {
            return;
        }

        //确定缓存过期时间
        long expireTime = 0L;
        if (Objects.nonNull(redisActivityDetailDTO.getEndTime())) {
            expireTime = redisActivityDetailDTO.getEndTime().getTime() - System.currentTimeMillis();
            expireTime = (expireTime / 1000) + RedisConstants.EXTRA_EXPIRE_SECOND;
        } else {
            expireTime = RedisConstants.SECKILL_EXPIRE_SECOND;
        }

        Long count = redissonClient.getAtomicLong(RedisConstants.CACHE_PREV_KEY_ACT_USER_GET_RESOURCE_COUNT + phone + RedisConstants.UNDER_LINE + hisResourceId).incrementAndGet();
        if (count.compareTo(1L) <= 0) {
            redissonClient.getAtomicLong(RedisConstants.CACHE_PREV_KEY_ACT_USER_GET_RESOURCE_COUNT + phone + RedisConstants.UNDER_LINE + hisResourceId).expire(expireTime, TimeUnit.SECONDS);
        }
        log.info("当前用户：{}，领取资源：{}，{}次", phone, hisResourceId, count);

    }

    @Override
    public void decrUserGetResourceCount(String phone, Long hisResourceId, Long activityId) {

        Long count = redissonClient.getAtomicLong(RedisConstants.CACHE_PREV_KEY_ACT_USER_GET_RESOURCE_COUNT + phone + RedisConstants.UNDER_LINE + hisResourceId).get();
        if (count.compareTo(0L) <= 0) {
            return;
        }
        count = redissonClient.getAtomicLong(RedisConstants.CACHE_PREV_KEY_ACT_USER_GET_RESOURCE_COUNT + phone + RedisConstants.UNDER_LINE + hisResourceId).decrementAndGet();

        log.info("当前用户：{}，领取资源：{}，{}次", phone, hisResourceId, count);
    }

    @Override
    public Boolean decrRedisQtyCheckDay(Long hisResourceId, Long activityId) {
        //判断活动信息是否已缓存，如果没有缓存，先去跑缓存逻辑
        PromotionActivityDetailDTO promotionActivityDetailDTO = this.cacheActInfo(activityId);
        Optional<ResourceHisDetailResponseDTO> optional = promotionActivityDetailDTO.getPrizeList()
                .stream().filter(item -> Objects.equals(item.getId(), hisResourceId)).findAny();
        if (!optional.isPresent()) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "奖品错误");
        }
        ResourceHisDetailResponseDTO hisDetailResponseDTO = optional.get();
        long count = 0L;
        if (hisDetailResponseDTO.getIssuanceCap() > 0) {
            // 获取活动开始天数
            Date startTime = promotionActivityDetailDTO.getStartTime();
            long days = Math.abs(DateUtils.getDays(DateUtils.format(new Date()), DateUtils.format(startTime))) + 1L;
            count = Math.max(hisDetailResponseDTO.getIssuedQuantity() - (hisDetailResponseDTO.getIssuanceCap() * days), count);
            log.info("资源:{}每天限量:{},当前最低保留库存数:{}", hisDetailResponseDTO.getId(), hisDetailResponseDTO.getIssuanceCap(), count);
        }
        Long currentCount = redissonClient.getAtomicLong(RedisConstants.CACHE_PREV_KEY_ACT_RESOURCE_INFO_COUNT + hisResourceId).get();
        log.info("资源总库存:{},当前剩余库存[{}]",hisDetailResponseDTO.getIssuedQuantity(), currentCount);
        if (currentCount <= count) {
            return false;
        }
        Long descreCount = redissonClient.getAtomicLong(RedisConstants.CACHE_PREV_KEY_ACT_RESOURCE_INFO_COUNT + hisResourceId).decrementAndGet();
        if (descreCount >= 0) {
            return true;
        }
        //库存已经为负数，进行+1 操作
        redissonClient.getAtomicLong(RedisConstants.CACHE_PREV_KEY_ACT_RESOURCE_INFO_COUNT + hisResourceId).incrementAndGet();
        return false;

    }

    @Override
    public Boolean decrRedisQty(Long hisResourceId, Long activityId, int count) {
        Assert.isTrue(count > 0, "数量不能小于0");
        //判断活动信息是否已缓存，如果没有缓存，先去跑缓存逻辑
        this.cacheActInfo(activityId);
        RAtomicLong atomicLong = redissonClient.getAtomicLong(RedisConstants.CACHE_PREV_KEY_ACT_RESOURCE_INFO_COUNT + hisResourceId);

        Long currentCount = atomicLong.get();
        if (currentCount <= 0) {
            log.info("库存[{}]已经为0", hisResourceId);
            return false;
        }
        Long descreCount = atomicLong.addAndGet(-count);
        if (descreCount >= 0) {
            return true;
        }
        //库存已经为负数，进行+1 操作
        atomicLong.addAndGet(count);
        return false;
    }


    @Override
    public Boolean decrRedisQty(Long hisResourceId, Long activityId) {
        return decrRedisQty(hisResourceId, activityId,1);
    }

    @Override
    public Boolean incrRedisQty(Long hisResourceId, Long activityId, int count) {
        RMap resourceMap = redissonClient.getMap(RedisConstants.CACHE_PREV_KEY_ACT_RESOURCE_INFO + activityId);
        ResourceHisDetailResponseDTO resourceHisDetailResponseDTO = JSON.parseObject(String.valueOf(resourceMap.get(hisResourceId.intValue())), ResourceHisDetailResponseDTO.class);
        if (resourceHisDetailResponseDTO == null) {
            log.info("资源[{}]的库存缓存信息已清空", hisResourceId);
            return false;
        }
        RAtomicLong atomicLong = redissonClient.getAtomicLong(RedisConstants.CACHE_PREV_KEY_ACT_RESOURCE_INFO_COUNT + hisResourceId);

        Long incrCount = atomicLong.addAndGet(count);
        //库存如果比实际库存大，则不添加
        if (incrCount.compareTo(resourceHisDetailResponseDTO.getIssuedQuantity().longValue()) > 0) {
            atomicLong.addAndGet(-count);
        }
        return true;
    }
    @Override
    public Boolean incrRedisQty(Long hisResourceId, Long activityId) {
        return incrRedisQty(hisResourceId, activityId,1);
    }

    @Override
    public Boolean incrRedisQtyByCount(Long hisResourceId, Long activityId, Long count) {

        RMap resourceMap = redissonClient.getMap(RedisConstants.CACHE_PREV_KEY_ACT_RESOURCE_INFO + activityId);
        ResourceHisDetailResponseDTO resourceHisDetailResponseDTO = JSON.parseObject(String.valueOf(resourceMap.get(hisResourceId.intValue())), ResourceHisDetailResponseDTO.class);
        resourceHisDetailResponseDTO.setIssuedQuantity(resourceHisDetailResponseDTO.getIssuedQuantity() + count.intValue());
        resourceMap.put(resourceHisDetailResponseDTO.getId(), JSON.toJSONString(resourceHisDetailResponseDTO));

        Long incrCount = redissonClient.getAtomicLong(RedisConstants.CACHE_PREV_KEY_ACT_RESOURCE_INFO_COUNT + hisResourceId).get();
        //库存如果比实际库存大，则不添加
        if (Long.valueOf(incrCount + count).compareTo(resourceHisDetailResponseDTO.getIssuedQuantity().longValue()) > 0) {
            throw new ApplicationException("当前虚拟库存大于真实库存！");
        }
        redissonClient.getAtomicLong(RedisConstants.CACHE_PREV_KEY_ACT_RESOURCE_INFO_COUNT + hisResourceId).addAndGet(count);
        return true;
    }

    @Override
    public Boolean decrRemainQty(Long hisResourceId, Long count) {

        boolean flag = promotionHisResourceDAO.decreaseRemainQty(hisResourceId, count);

        return flag;
    }

    @Override
    public Boolean incrRemainQty(Long hisResourceId, Long count) {

        boolean flag = promotionHisResourceDAO.increaseRemainQty(hisResourceId, count);

        return flag;
    }

    @Override
    public Boolean incrIssuedQty(Long hisResourceId, Long count) {
        String lockKey = RedisConstants.CACHE_PREV_KEY_RESOURCE_QTY_LOCK + hisResourceId;
        RLock lock = redissonClient.getLock(lockKey);
        int retry = 0;
        Boolean flag = false;
        try {
            /**
             * 获取分布式锁重试次数
             */
            int RETRY = 3;
            while (retry < RETRY) {
                log.info("获取分布式锁重试次数:" + retry);
                if (lock.tryLock(100, 10 * 1000, TimeUnit.MILLISECONDS)) {
                    log.info("----------------------获取分布式锁------------------");

                    //扣减奖品数量
                    PromotionHisResourceQuery hisResourceQuery = new PromotionHisResourceQuery();
                    hisResourceQuery.setId(hisResourceId);
                    List<PromotionHisResourceDO> content = promotionHisResourceDAO.findPage(hisResourceQuery).getContent();
                    if (CollectionUtil.isEmpty(content)) {
                        log.error("活动奖品信息丢失,req={}", JsonUtil.bean2JsonString(hisResourceQuery));
                        throw new ApplicationException("活动奖品信息丢失");
                    }
                    flag = promotionHisResourceDAO.increaseIssuedQty(content.get(0).getId(), count);
                    if (flag) {
                        this.incrRedisQtyByCount(content.get(0).getId(), content.get(0).getActivityId(), count);
                    }

                    //手动释放锁
                    if (lock.isHeldByCurrentThread()) {
                        log.info("----------------------手动释放锁------------------");
                        lock.unlock();
                        break;
                    }
                    return flag;
                } else {
                    //获取锁失败,次数加1
                    retry++;
                }
            }
        } catch (InterruptedException e) {
            log.error("redis分布式锁异常,key:{},异常信息:{}", lockKey, e.getMessage());
            throw new ApplicationException("访问人数过多，请稍后再试");
        }

        if (retry == RETRY_TIME) {
            lock.unlock();
            throw new ApplicationException("访问人数过多，请稍后再试");
        }

        return Boolean.FALSE;
    }

    @Override
    public void incrUserPartakeCount(String phone, Long activityId) {

        PromotionActivityDetailDTO redisActivityDetailDTO = JSON.parseObject(String.valueOf(redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO + activityId).get()), PromotionActivityDetailDTO.class);
        if (Objects.isNull(redisActivityDetailDTO)) {
            return;
        }

        //确定缓存过期时间
        long expireTime = 0L;
        if (Objects.nonNull(redisActivityDetailDTO.getEndTime())) {
            expireTime = redisActivityDetailDTO.getEndTime().getTime() - System.currentTimeMillis();
            expireTime = (expireTime / 1000) + RedisConstants.EXTRA_EXPIRE_SECOND;
        } else {
            expireTime = RedisConstants.SECKILL_EXPIRE_SECOND;
        }

        Long count = redissonClient.getAtomicLong(RedisConstants.CACHE_PREV_KEY_ACT_USER_PARTAKE_COUNT + phone + RedisConstants.UNDER_LINE + activityId).incrementAndGet();
        if (count.compareTo(1L) <= 0) {
            redissonClient.getAtomicLong(RedisConstants.CACHE_PREV_KEY_ACT_USER_PARTAKE_COUNT + phone + RedisConstants.UNDER_LINE + activityId).expire(expireTime, TimeUnit.SECONDS);
        }
        log.info("当前用户：{}，参加活动：{}，{}次", phone, activityId, count);

    }

    @Override
    @Async("threadExecutor")
    public void incrUserFeedBackCount(String phone, Long activityId, Long hisResourceId) {

        PromotionActivityDetailDTO redisActivityDetailDTO = JSON.parseObject(String.valueOf(redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO + activityId).get()), PromotionActivityDetailDTO.class);
        if (Objects.isNull(redisActivityDetailDTO)) {
            return;
        }

        //确定缓存过期时间
        long expireTime = 0L;
        if (Objects.nonNull(redisActivityDetailDTO.getEndTime())) {
            expireTime = redisActivityDetailDTO.getEndTime().getTime() - System.currentTimeMillis();
            expireTime = (expireTime / 1000) + RedisConstants.EXTRA_EXPIRE_SECOND;
        } else {
            expireTime = RedisConstants.SECKILL_EXPIRE_SECOND;
        }

        Long count = redissonClient.getAtomicLong(RedisConstants.CACHE_PREV_KEY_ACT_USER_FEEDBACK_COUNT + phone + RedisConstants.UNDER_LINE + activityId).incrementAndGet();
        if (count.compareTo(1L) <= 0) {
            redissonClient.getAtomicLong(RedisConstants.CACHE_PREV_KEY_ACT_USER_FEEDBACK_COUNT + phone + RedisConstants.UNDER_LINE + activityId).expire(expireTime, TimeUnit.SECONDS);
        }
        log.info("填写信息登记：当前用户手机号：{}，活动id：{}", phone, activityId);

    }

    @Override
    public void cacheUnpayOrderInfo(ActivityOrderDO byId) {

        ActivityOrderResponseVO activityOrderResponseVO = byId.clone(ActivityOrderResponseVO.class);

        //资源信息
        PromotionHisResourceDO promotionHisResourceDO = promotionHisResourceDAO.getById(activityOrderResponseVO.getResourceId());
        if (Objects.nonNull(promotionHisResourceDO)) {
            activityOrderResponseVO.setCouponValue(promotionHisResourceDO.getCouponValue());
            activityOrderResponseVO.setResourceName(promotionHisResourceDO.getName());
            activityOrderResponseVO.setUrl(promotionHisResourceDO.getUrl());
            activityOrderResponseVO.setCostPrice(promotionHisResourceDO.getCostPrice());
            activityOrderResponseVO.setDiscountPrice(promotionHisResourceDO.getDiscountPrice());
            activityOrderResponseVO.setResourceType(promotionHisResourceDO.getType());
            activityOrderResponseVO.setCouponCategory(promotionHisResourceDO.getCouponCategory());
            activityOrderResponseVO.setCouponType(promotionHisResourceDO.getCouponType());
        }

        if (!PlatformTypeEnum.DY_MINIPROGRAM.getId().equals(byId.getType())
                && byId.getExt() != null) {
            activityOrderResponseVO.setPaySign(byId.getExt().get("paySign") == null ? "" : byId.getExt().get("paySign").toString());
            activityOrderResponseVO.setSignType(byId.getExt().get("signType") == null ? "" : byId.getExt().get("signType").toString());
            activityOrderResponseVO.setPackageRes(byId.getExt().get("packageRes") == null ? "" : byId.getExt().get("packageRes").toString());
            activityOrderResponseVO.setNonceStr(byId.getExt().get("nonceStr") == null ? "" : byId.getExt().get("nonceStr").toString());
            activityOrderResponseVO.setTimeStamp(byId.getExt().get("timeStamp") == null ? "" : byId.getExt().get("timeStamp").toString());

            activityOrderResponseVO.setNickName(byId.getExt().get("nickName") == null ? "" : byId.getExt().get("nickName").toString());
            activityOrderResponseVO.setPhone(byId.getExt().get("phone") == null ? "" : byId.getExt().get("phone").toString());
            activityOrderResponseVO.setUserName(byId.getExt().get("userName") == null ? "" : byId.getExt().get("userName").toString());
            activityOrderResponseVO.setOpenId(byId.getExt().get("openId") == null ? "" : byId.getExt().get("openId").toString());
            activityOrderResponseVO.setProjectId(byId.getExt().get("projectId") == null ? "" : byId.getExt().get("projectId").toString());
            activityOrderResponseVO.setH5Url(byId.getExt().get("h5Url") == null ? "" : byId.getExt().get("h5Url").toString());
        }
        if (PlatformTypeEnum.DY_MINIPROGRAM.getId().equals(byId.getType())
                && byId.getExt() != null && byId.getExt().get("dyPayInfo") != null) {
            if (activityOrderResponseVO == null) {
                activityOrderResponseVO = new ActivityOrderResponseVO();
            }
            ReceiveCouponRequestDTO.DyPayInfo dyPayInfo = JSONUtil.toBean(byId.getExt().get("dyPayInfo").toString(), ReceiveCouponRequestDTO.DyPayInfo.class);
            activityOrderResponseVO.setDyPayInfo(dyPayInfo.clone(ActivityOrderResponseVO.DyPayInfo.class));
        }
        PromotionActivityDO promotionActivityDO = promotionActivityDAO.getById(byId.getActivityId());
        activityOrderResponseVO.setPaTemplateId(promotionActivityDO.getPaTemplateId());

        //缓存5分钟
        redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_USER_UNPAY_ORDER + byId.getPhone() + RedisConstants.UNDER_LINE + byId.getActivityId() + RedisConstants.UNDER_LINE + byId.getResourceId()).set(JSON.toJSONString(activityOrderResponseVO), PayConstant.EXPIREMINUTES, TimeUnit.MINUTES);

    }

    @Override
    public boolean isExistsUnpayOrderInfo(String phone, Long activityId, Long resourceId) {
        return redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_USER_UNPAY_ORDER + phone + RedisConstants.UNDER_LINE + activityId + RedisConstants.UNDER_LINE + resourceId).isExists();
    }

    @Override
    public void clearCacheUnpayOrderInfo(String phone, Long activityId, Long resourceId) {

        ActivityOrderRequestDTO activityOrderRequestDTO = JSON.parseObject(String.valueOf(redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_USER_UNPAY_ORDER + phone + RedisConstants.UNDER_LINE + activityId + RedisConstants.UNDER_LINE + resourceId).get()), ActivityOrderRequestDTO.class);
        if (Objects.nonNull(activityOrderRequestDTO)) {
            redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_USER_UNPAY_ORDER + phone + RedisConstants.UNDER_LINE + activityId + RedisConstants.UNDER_LINE + resourceId).delete();
        }

    }

    @Override
    public void udpateAssistSuccessStatus() {

        log.info("定时检查裂变活动的有效期");

        List<ActivityPartakeLogDO> termEndList = activityPartakeLogDAO.findTermEndList(Arrays.asList(FissonTypeEnum.Boost.getId(), FissonTypeEnum.Bargain.getId()));
        if (CollectionUtil.isNotEmpty(termEndList)) {

            for (ActivityPartakeLogDO activityPartakeLogDO : termEndList) {

                boolean fissionStatus;
                String sensorsEvent;
                PromotionHisResourceDO promotionHisResourceDO = new PromotionHisResourceDO();
                if (Objects.equals(activityPartakeLogDO.getFissonType(), FissonTypeEnum.Boost.getId())) {
                    //2.发起助力活动后则提供有效期倒计时，倒计时结束则判断当前用户是否满足至少一个阶梯，如满足则视为助力成功，如不满足则视为助力失败。
                    //第一阶梯 资源
                    promotionHisResourceDO = promotionHisResourceDAO.findLadder(activityPartakeLogDO.getActivityId(), 1);
                    //达到助力一阶梯人数
                    fissionStatus = activityPartakeLogDO.getCurrentFissonCount() >= promotionHisResourceDO.getFissonCount();

                    sensorsEvent = SensorsEventEnum.BOOST_SUCCESS.getCode();
                } else {
                    //发起砍价活动后则提供有效期倒计时，倒计时结束则判断当前用户当前已助力人数达到所需助力人数
                    fissionStatus = activityPartakeLogDO.getCurrentFissonCount() >= activityPartakeLogDO.getNeedFissonCount();
                    List<PromotionHisResourceDO> byActivityId = promotionHisResourceDAO.findByActivityId(activityPartakeLogDO.getActivityId());
                    if (CollectionUtil.isNotEmpty(byActivityId)) {
                        promotionHisResourceDO = byActivityId.get(0);
                    }
                    sensorsEvent = SensorsEventEnum.BARGAIN_SUCCESS.getCode();
                }

                if (fissionStatus) {
                    activityPartakeLogDO.setFissonStatus(FissonStatusEnum.SUCCESS.getId());
                } else {
                    activityPartakeLogDO.setFissonStatus(FissonStatusEnum.FAILURE.getId());
                }
                boolean b = activityPartakeLogDAO.updateById(activityPartakeLogDO);
                if (b && fissionStatus) {
                    //裂变活动成功人数神策埋点
                    asynchronousService.sensorsBuriedPointFission(activityPartakeLogDO.getId(), sensorsEvent);

                    //2021-8-3  砍价活动；砍价成功未领取奖品，这时候整个砍价活动已结束或者已终止；系统统一自动发放奖品至对应用户处
                    this.bargainActivityVerify(activityPartakeLogDO, activityPartakeLogDO.getActivityId(), promotionHisResourceDO);

                    //发送砍价成功通知
//                    this.templateSend(activityPartakeLogDO.getPhone(), activityPartakeLogDO.getUserName(), promotionHisResourceDO.getName(), activityPartakeLogDO.getCode());
                } else if (Objects.equals(activityPartakeLogDO.getFissonType(), FissonTypeEnum.Bargain.getId())) {
                    //归还库存
                    incrRedisQty(promotionHisResourceDO.getId(), activityPartakeLogDO.getActivityId());
                }
            }

            //有效期结束后自动发放奖品
//            PromotionActivityDO promotionActivityDO = promotionActivityDAO.selectById(termEndList.get(0).getActivityId());
//            promotionActivityService.grantResource(promotionActivityDO);

        }
    }


    @Override
    public Boolean bargainActivityVerify(FissionReceiveNowDTO dto, PromotionActivityDO promotionActivityDO, List<ActivityFormFeedbackDO> formFeedbackList, ActivityPartakeLogDO partakeLogById, PromotionHisResourceDO byId) {

        String lockKey = RedisConstants.CACHE_PREV_KEY_FISSION_REDUCEPRICE_ACT_RESOURCE_LOCK + promotionActivityDO.getId() + "_" + partakeLogById.getId() + "_" + byId.getId();
        RLock lock = redissonClient.getLock(lockKey);
        int retry = 0;
        Boolean flag = false;
        try {
            /**
             * 获取分布式锁重试次数
             */
            int RETRY = 2;
            while (retry < RETRY) {
                log.info("获取分布式锁重试次数:" + retry);
                if (lock.tryLock(100, 10 * 1000, TimeUnit.MILLISECONDS)) {
                    log.info("----------------------获取分布式锁------------------");

                    //主要逻辑
                    try {
                        if (PromotionResourceTypeEnum.THIRD_PARTY.getId().equals(byId.getType())) {
                            //第三方核销
                            this.thirdVerify(dto, partakeLogById, byId, promotionActivityDO);
                        } else {
                            //保存核销信息
                            Map<String, Object> projectInfo = new HashMap<>(2);
                            if (StringUtil.isNotEmpty(partakeLogById.getProjectId()) && StringUtil.isNotEmpty(partakeLogById.getProjectName())) {
                                projectInfo.put("projectId", partakeLogById.getProjectId());
                                projectInfo.put("projectName", partakeLogById.getProjectName());
                            }
                            ActivityOrderDO activityOrderDO = new ActivityOrderDO();
                            activityOrderDO.setType(dto.getType());
                            activityOrderDO.setActivityId(dto.getActivityId());
                            activityOrderDO.setUserId(dto.getUserId());
                            activityOrderDO.setId(0L);
                            activityOrderDO.setExt(projectInfo);
                            activityOrderDO.setPayTime(DateUtils.now());
                            ActivityFormFeedbackDTO formFeedback = formFeedbackList.get(0).clone(ActivityFormFeedbackDTO.class);
                            ActivityVerifyDO activityVerifyDO = ActivityInfoConverter.verifyConverter(activityOrderDO, formFeedback, byId, partakeLogById.getCode(), VerifyStatusEnum.NO_VERIFY.getId(), WhetherEnum.NO.getId());
                            activityVerifyDAO.save(activityVerifyDO);
                        }
                        //发送短信通知
                        templateSend(partakeLogById.getPhone(), partakeLogById.getUserName(), byId.getName(), partakeLogById.getCode());


                        //小程序订阅消息
                        SendTemplateNewsRequestDTO sendTemplateNews = new SendTemplateNewsRequestDTO();
                        Map<String, Object> templateParam = Maps.newHashMap();
                        templateParam.put("thing1", "恭喜你！砍价成功！");
                        templateParam.put("thing2", promotionActivityDO.getName());
                        templateParam.put("thing5", "请到我的礼品栏目查看详情");
                        templateParam.put("time2", DateUtils.toDateText(promotionActivityDO.getCreatedTime()));
                        sendTemplateNews.setTemplateParam(JSON.toJSONString(templateParam));
                        templateParam.put("thing3", "请到我的礼品栏目查看详情");
                        sendTemplateNews.setTemplateId(huafaConstantConfig.MINI_BARGAIN_SUCCESS_TEMPLATE_ID);
                        sendTemplateNews.setUserOpenId(partakeLogById.getUserId());
                        sendTemplateNews.setMpFrom(SendTemplateNewsRequestDTO.MP_FROM_USH);
                        sendTemplateNews.setCreateId(partakeLogById.getUserName());
                        interactionCenterService.miniTemplateNews(sendTemplateNews);

                        //获取分布式锁并扣减数据库库存
                        flag = decrRemainQty(byId.getId(), 1L);
                    } catch (Exception ex) {
                        log.error("领取失败，报错原因：" + ex);
                        //返还库存
                        incrRedisQty(byId.getId(), dto.getActivityId());
                        throw new ApplicationException("系统异常，领取失败");
                    }

                    //手动释放锁
                    if (lock.isHeldByCurrentThread()) {
                        log.info("----------------------手动释放锁------------------");
                        lock.unlock();
                        break;
                    }

                } else {
                    //获取锁失败,次数加1
                    retry++;
                }
            }

            if (retry == RETRY_TIME) {
                lock.unlock();
                throw new ApplicationException("bargainActivityVerify() 砍价活动自动发放奖品接口锁获取失败");
            }

            return flag;
        } catch (InterruptedException e) {
            log.error("redis分布式锁异常,key:{},异常信息:{}", lockKey, e.getMessage());
            throw new ApplicationException("系统异常，领取失败");
        }


    }

    @Override
    public void updateUserProjectInof() {
        List<String> userIdList = promotionActivityDAO.findAllUserIdList();
        if (CollectionUtil.isNotEmpty(userIdList)) {
            List<String> ids = userIdList.stream().distinct().collect(Collectors.toList());
            ids.forEach(userId -> {
                userProjectService.saveToRedis(userId);
            });
        }
    }


    private void thirdVerify(FissionReceiveNowDTO dto, ActivityPartakeLogDO activityPartakeLogDO, PromotionHisResourceDO promotionHisResourceDO, PromotionActivityDO promotionActivityDO) {
        if (ThirdCategoryEnum.PHONE_FEE.getId().equals(promotionHisResourceDO.getThirdCategory())) {
            //话费充值
            incentiveService.rechargeMobileAsync(LuckyDrawConverter.rechargeMobileConverter(dto.clone(ReceiveNowDTO.class), promotionActivityDO.getName(), promotionHisResourceDO, activityPartakeLogDO.getCode()));
        } else if (ThirdCategoryEnum.CASH_BAG.getId().equals(promotionHisResourceDO.getThirdCategory())) {
            //发送红包激励
            ReceiveNowDTO receiveNowDTO = dto.clone(ReceiveNowDTO.class);
            //FissionReceiveNowDTO 没有项目信息 导致复制到的实体项目信息为空
            receiveNowDTO.setProjectId(promotionHisResourceDO.getProjectId());
            receiveNowDTO.setProjectName(promotionHisResourceDO.getProjectName());
            incentiveService.sendIncentiveAsync(LuckyDrawConverter.sendIncentiveConverter(receiveNowDTO,
                    promotionActivityDO.getName(), promotionHisResourceDO, activityPartakeLogDO.getCode(), SendTemplateNewsRequestDTO.MP_FROM_USH));
        }
    }

    private void bargainActivityVerify(ActivityPartakeLogDO activityPartakeLogDO, Long activityId, PromotionHisResourceDO promotionHisResourceDO) {
        if (!Objects.equals(activityPartakeLogDO.getFissonType(), FissonTypeEnum.Bargain.getId())) {
            return;
        }
        PromotionActivityDetailPostResponseDTO activityById = promotionActivityMiddleService.detail(activityId);

        CustomerFeedbackQuery query = new CustomerFeedbackQuery();
        query.setActivityId(activityId);
        query.setPhone(activityPartakeLogDO.getPhone());
        query.setResourceId(activityPartakeLogDO.getResourceId());
        List<ActivityFormFeedbackDO> formFeedbackList = customerFeedbackDAO.pageList(query).getContent();

        FissionReceiveNowDTO dto = new FissionReceiveNowDTO();
        dto.setType(activityPartakeLogDO.getType());
        dto.setActivityId(activityPartakeLogDO.getActivityId());
        dto.setUserId(activityPartakeLogDO.getUserId());
        this.bargainActivityVerify(dto, activityById.clone(PromotionActivityDO.class), formFeedbackList, activityPartakeLogDO, promotionHisResourceDO);
    }

    private PromotionActivityDetailDTO getDetailDto(Long id) {
        //活动基本数据
        PromotionActivityListPostVO activityDetail = promotionActivityMiddleService.getActivityById(id);
        if (activityDetail == null) {
            return new PromotionActivityDetailDTO();
        }
        PromotionActivityDetailDTO result = activityDetail.clone(PromotionActivityDetailDTO.class, CloneDirection.OPPOSITE);

        //======================华发===============================
        ActivityExtVO ext = ActivityExtConverter.converter(result.getExt());

        //1、隐藏目标管理整个菜单；
        //2、PC端创建活动-选择目标，注释隐藏掉，此功能被废弃
//        ActivityTargetResponseDTO activityTargetResponseDTO = activityTargetService.getById(ext.getActivityGoal().longValue());
//
//        //活动目标名称
//        result.setActivityTargetName(Objects.nonNull(activityTargetResponseDTO)?activityTargetResponseDTO.getTargetName():"");

        //指标路径名称
        MarketingKpiRouteMapDTO marketingKpiRouteMapDTO = marketingKpiRouteMapService.queryById(Long.valueOf(ext.getKpiPath()));
        result.setMarketingKpiRouteMapName(Objects.nonNull(marketingKpiRouteMapDTO) ? marketingKpiRouteMapDTO.getName() : "");


        //获取项目列表
        List<ActivityParticipationDO> activityParticipationList = activityParticipationService.listByActivityId(id);
        if (CollectionUtil.isNotEmpty(activityParticipationList)) {
            List<ActivityParticipationVO> activityParticipationVOList = ObjectCloneUtils.convertList(activityParticipationList, ActivityParticipationVO.class);
            result.setProjectInfoList(activityParticipationVOList);
        }
        //获取项目列表，展示给前端
        List<ActivityParticipationGroupResponseDTO> activityParticipationGroupResponseDTOList = activityParticipationService.getProjectGroupList(id);
        if (CollectionUtil.isNotEmpty(activityParticipationGroupResponseDTOList)) {
            result.setFrontProjectList(activityParticipationGroupResponseDTOList);
        }

        //活动信息
        result.setPrizeList(this.getPrizeList(id));

        //活动页
        ActivityPageDO activityPageDO = activityPageDAO.getByActivity(id, ActivityTypeEnum.ACTIVITY.getId());
        ActivityPageVO activityPageVO = Optional.ofNullable(activityPageDO).orElseGet(ActivityPageDO::new).clone(ActivityPageVO.class);
        String bottomBtnType = activityPageDO.getBottomBtnType();
        if (StringUtil.isNotEmpty(bottomBtnType)) {
            String[] split = bottomBtnType.split(",");
            Integer[] types = (Integer[]) ConvertUtils.convert(split, Integer.class);
            activityPageVO.setBottomBtnTypes(Arrays.asList(types));
        }

        result.setActivityPageVO(activityPageVO);

        //活动分享页
        ActivityPageShareDO activityPageShareDO = activityPageShareDAO.getByActivity(id, ActivityTypeEnum.ACTIVITY.getId());
        result.setActivityPageShareVO(Optional.ofNullable(activityPageShareDO).orElseGet(ActivityPageShareDO::new).clone(ActivityPageShareVO.class));

        //活动规则
        List<PromotionActivityLimitDO> limitList = promotionActivityLimitDAO.selectByActivityId(id);
        if (CollectionUtil.isNotEmpty(limitList)) {
            result.setUserLimit(ActivityConfigConverter.getLimitArray(limitList, PATemplateBaseEnum.USER.getId(), BaseActivityDTO.class));
            result.setLuckyDrawLimit(ActivityConfigConverter.getLimitArray(limitList, PATemplateBaseEnum.LUCKYDRAW.getId(), BaseActivityDTO.class));
            result.setCouponLimit(ActivityConfigConverter.getLimitArray(limitList, PATemplateBaseEnum.COUPON.getId(), BaseActivityDTO.class));
            result.setBargainLimit(ActivityConfigConverter.getLimitArray(limitList, PATemplateBaseEnum.BARGAIN.getId(), BaseActivityDTO.class));
            result.setAssistLimit(ActivityConfigConverter.getLimitArray(limitList, PATemplateBaseEnum.ASSIST.getId(), BaseActivityDTO.class));
            result.setFormLimit(ActivityConfigConverter.getLimitArray(limitList, PATemplateBaseEnum.FORM.getId(), BaseActivityDTO.class));
            result.setCardCollectingLimit(ActivityConfigConverter.getLimitArray(limitList, PATemplateBaseEnum.CARD_COLLECTING.getId(), BaseActivityDTO.class));
            result.setNumberLimit(ActivityConfigConverter.getLimitArray(limitList, PATemplateBaseEnum.NUMBER.getId(), BaseActivityDTO.class));
        }
        //处理标签
        List<TagItemDTO> tags = associationService.getAssociationList(id, AssociationTypeEnum.ACTIVITY_TAG).stream().map(associationService::convertToTagItem).collect(Collectors.toList());
        result.setTags(tags);
        //======================华发===============================

        return result;

    }

    private List<ResourceHisDetailResponseDTO> getPrizeList(Long activityId) {
        HisResourceRequestDTO hisResourceRequestDTO = new HisResourceRequestDTO();
        hisResourceRequestDTO.setActivityId(activityId);
        hisResourceRequestDTO.setPage(1L);
        hisResourceRequestDTO.setSize(999999L);
        List<PromotionHisResourceDTO> list = promotionHisResourceService.findPage(hisResourceRequestDTO).getContent();
        List<ResourceHisDetailResponseDTO> resourceHisDetailResponseDTOS = ObjectCloneUtils.convertList(list, ResourceHisDetailResponseDTO.class);
        return Optional.ofNullable(resourceHisDetailResponseDTOS).orElse(new ArrayList());
    }


    /**
     * 砍价成功通知
     * 【华发股份】XXXX你好！恭喜你砍价成功！成功获得“XXXXX”，核销码为(023uc)，使用规则请到小程序-我的礼品查看。
     *
     * @param mobile   发送手机
     * @param userName 中奖人
     * @param coupon   优惠券名称
     * @param code     核销码
     */
    private void templateSend(String mobile, String userName, String coupon, String code) {
        log.info("准备发送砍价成功通知参数，手机={},中奖人={},优惠券名称={},核销码={}", mobile, userName, coupon, code);
        if (StringUtil.isNotBlank(mobile) && StringUtil.isNotBlank(userName) && StringUtil.isNotBlank(coupon) && StringUtil.isNotBlank(code)) {
            TemplateSendRequestDTO.Param param = new TemplateSendRequestDTO.Param();
            param.setUsername(userName + ",");
            param.setResource(coupon);
            param.setCode(code);

            TemplateSendRequestDTO requestDTO = new TemplateSendRequestDTO();
            requestDTO.setTemplateCode(huafaConstantConfig.BARGAIN_SUCCESS_TEMPLATE_ID);
            requestDTO.setMobile(mobile);
            requestDTO.setTemplateParam(JsonUtil.bean2JsonString(param));
            requestDTO.setCreateId(userName);
            interactionCenterService.templateSend(requestDTO);
        }
    }
}
