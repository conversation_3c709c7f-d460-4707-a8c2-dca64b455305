package com.deepexi.dxp.marketing.domain.marketing.query;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class CommodityQuery {
    Long appId;
    Long channelId;
    List<Long> channelIds;
    Long id;
    List<Long> ids;
    Long itemId;
    List<Long> itemIds;
    Integer page;
    Long priceSystemId;
    Integer releaseVersion;
    BigDecimal salesPrice;
    Long shopId;
    List<Long> shopIds;
    List<String> shopSku;
    List<String> shopSkuVersion;
    Integer shopType;
    Integer size;
    Long skuId;
    List<Long> skuIds;
    Integer status;
    String tenantId;



    Double salePrice;
    Integer auditStatus;
    Long brandId;
    List<Long> brandIds;
    String brandNameAlias;
    String brandNameCn;
    String brandNameEn;
    String businessCode;
    Long businessId;
    String businessName;
    Long categoryId;
    List<Long> categoryIds;
    String categoryName;
    Long createdBy;
    String channelCode;
    String channelName;
    Date createdTime;
    Long cspuId;
    String itemCode;
    String itemCodeFuzzy;
    String itemName;
    String itemNameFuzzy;
    String itemSubName;
    Integer itemType;
    String name;
    Integer onlineState;
    String remark;
    Integer reviewState;
    Integer salesPoints;
    String shopCode;
    String shopName;
    String skuCode;
    String skuName;
    Integer sortType;
    Integer source;
    Long spuId;
    String spuName;
    String spuSubName;
    String taxNumber;
    Long taxPercent;
    Date updatedTime;
}
