package com.deepexi.dxp.marketing.domain.marketing.dto;

import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 流程画布-条件节点—指标数据配置
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-07 16:03
 */
@Data
public class FlowCanvasConditionKpiItemsDTO extends SuperDTO {

    /**
     * 画布ID
     */
    private Long flowId;

    /**
     * 画布节点ID
     */
    private String nodeId;

    /**
     * 判断条件名称
     */
    private String name;

    /**
     * 开始时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date endTime;

    /**
     * 指标ID
     */
    private Long kpiItemsId;

    /**
     * 指标名称
     */
    private String kpiItemsName;

    /**
     * 指标单位
     */
    private String kpiItemsUnit;

    /**
     * 计算符号
     */
    private String symbols;

    /**
     * 比对值
     */
    private String value;

    /**
     * 对比值-结束
     */
    private String valueEnd;
}
