package com.deepexi.dxp.marketing.domain.marketing.vo;

import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 主动营销任务安全过滤配置VO
 * @Author: HuangBo.
 * @Date: 2020/3/12 16:45
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MarketingTaskSecurityFilterVO extends SuperVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 营销任务类型(1:主动营销，2:自动营销)
     */
    @ApiModelProperty(value = "营销任务类型(1:主动营销，2:自动营销)")
    private Integer taskType;

    /**
     * 主动或自动营销任务ID
     */
    @ApiModelProperty(value = "主动或自动营销任务ID")
    private Long taskId;

    /**
     * 安全选取类型(1:安全策略，2:用户打扰过滤)
     */
    @ApiModelProperty(value = "安全选取类型(1:安全策略，2:用户打扰过滤)")
    private Integer securityType;

    /**
     * 安全过滤编码
     */
    @ApiModelProperty(value = "安全过滤编码")
    private String code;

    /**
     * 安全过滤名称
     */
    @ApiModelProperty(value = "安全过滤名称")
    private String name;

}
