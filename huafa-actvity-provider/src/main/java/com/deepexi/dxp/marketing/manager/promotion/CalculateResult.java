package com.deepexi.dxp.marketing.manager.promotion;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/2/17
 */
@Data
public class CalculateResult {
    private Boolean result;
    private String msg;

    public static CalculateResult success() {
        CalculateResult result = new CalculateResult();
        result.setResult(Boolean.TRUE);
        return result;
    }

    public static CalculateResult error() {
        return error(null);
    }

    public static CalculateResult error(String msg) {
        CalculateResult result = new CalculateResult();
        result.setResult(Boolean.FALSE);
        result.setMsg(msg);
        return result;
    }
}
