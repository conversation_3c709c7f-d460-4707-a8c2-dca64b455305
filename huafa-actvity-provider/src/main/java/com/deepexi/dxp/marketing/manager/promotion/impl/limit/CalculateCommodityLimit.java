package com.deepexi.dxp.marketing.manager.promotion.impl.limit;


import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.ComdityLimitDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.SkuCodeBaseDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityCommodityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityConfigDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.TemplateLimitDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityParamsDTO;
import com.deepexi.dxp.marketing.enums.activity.limit.PATemplateCommodityEnum;
import com.deepexi.dxp.marketing.enums.status.ActivityInventory;
import com.deepexi.dxp.marketing.manager.promotion.impl.BaseLimit;
import com.deepexi.dxp.middle.promotion.util.CollectionsUtil;
import com.deepexi.util.CollectionUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR> xinjian.yao
 * @date 2019/11/25 16:13
 */
public class CalculateCommodityLimit extends BaseLimit {

    private List<CommodityLimitEnumsCalculate> calculateHelper = new ArrayList<>(30);


    public CalculateCommodityLimit(TemplateLimitDTO templateLimitDTO, ActivityConfigDTO activity, ActivityParamsDTO params) {
        super(templateLimitDTO, activity, params);
    }

    @Override
    public Boolean calculate() {
        // 活动存储的值
        ComdityLimitDTO commodity = super.getActivity().getCommodityList();
        // 需求比较的值
        ActivityParamsDTO params = super.getParams();
        // 初始化计算信息
        init();
        return calculateCommodityLimit(commodity, params);
    }

    /**
     * 函数式接口
     * 枚举类下的不同类型的计算接口
     * <p>
     * 用于处理不同限制类型的枚举类下的限制选项
     * <p>
     * 如商品限制的不用限制类型 需要不同的计算
     */
    private interface CommodityLimitEnumsCalculate {

        /**
         * @param comdityLimitDTO 设计活动时 配置存储h5商城的信息
         * @param params          传过来的参数
         * @return 不同类型是非成功
         */
        Boolean calculate(ComdityLimitDTO comdityLimitDTO, ActivityParamsDTO params);
    }

    /**
     * @return 枚举类里面商品品牌 类型的处理方法
     */
    private CommodityLimitEnumsCalculate ppEnum() {
        return (baseActivity, params) -> {
            // 活动进来的id 和枚举类的id不一样时 返回null
            String value = Optional.ofNullable(baseActivity)
                    .map(ComdityLimitDTO::getBrandCode)
                    .map(val -> CollectionUtil.isEmpty(val) ? null : val.get(0))
                    .map(BaseActivityDTO::getId)
                    .orElse(null);
            if (!PATemplateCommodityEnum.PP.getId().equals(value)) {
                return null;
            }
            // 获取单前的sku 和 对应的 品牌
            String brandCode = params.getCommoditie().getBrandId();
            Long upId = params.getCommoditie().getUpId();
            // 获取配置的品牌 和 对应的指定商品不参加
            List<BaseActivityDTO> brandCodeConfigList = baseActivity.getBrandCode();
            boolean brandCodeFlag = brandCodeConfigList.stream()
                    .anyMatch(val -> val.getValue().equals(brandCode));
            boolean skuFlag = Optional.ofNullable(baseActivity.getSkuList())
                    .map(skuList -> skuList.stream()
                            .anyMatch(val -> val.getUpId().equals(String.valueOf(upId))))
                    .orElse(false);
            // 是否在这个品类内 如果在 是不是在指定商品不参加里面
            return brandCodeFlag && !skuFlag;
        };
    }

    /**
     * 枚举类里面商品sku 类型的处理方法
     *
     * @return
     */
    private CommodityLimitEnumsCalculate skuEnum() {
        return (baseActivity, params) -> {
            String value = Optional.ofNullable(baseActivity)
                    .map(ComdityLimitDTO::getSkuList)
                    .map(val -> CollectionUtil.isEmpty(val) ? null : val.get(0))
                    .map(SkuCodeBaseDTO::getId)
                    .orElse(null);
            // 活动进来的id 和枚举类的id不一样时 返回null
            if (!PATemplateCommodityEnum.SKU.getId().equals(value) || baseActivity.getBrandCode() != null) {
                return null;
            }
            // 获取当前的sku
            Long skuId = params.getCommoditie().getUpId();
            // 获取配置sku商品
            List<SkuCodeBaseDTO> skuList = baseActivity.getSkuList();
            // sku商品是否在集合内
            SkuCodeBaseDTO oneInList =
                    CollectionsUtil.findOneInList(skuList, val -> skuId.toString().equals(val.getUpId()));
            //新规则，向下兼容，问题：flag哪里来？DB，来自于json，添加内容【tips：告知上层应用，此值为必填值。】
            //1、先遍历一遍，拿出限制用户，还是不限制用户的条件
            Boolean deny = Boolean.TRUE;
            for (SkuCodeBaseDTO baseDTO : skuList) {
                if ("true".equals(baseDTO.getUpId().trim()) || "false".equals(baseDTO.getUpId().trim())) {
                    deny = Boolean.valueOf(baseDTO.getUpId().trim());
                    //2、剔除当前限制用户的json对象
                    skuList.remove(baseDTO);
                }
            }
            //4、返回 return deny ? 【原逻辑：限制】 : 【新逻辑：不限制】
            //赋值操作
            if (null != oneInList && deny) {
                if (null == params.getCommoditie().getSkuId()) {
                    params.getCommoditie().setSkuId(
                            Optional.ofNullable(oneInList.getSkuId())
                                    .map(Long::valueOf)
                                    .orElse(null)
                    );
                }
                if (null == params.getCommoditie().getStockType()) {
                    params.getCommoditie().setStockType(oneInList.getStockType());
                }
                return true;
            }

            return false;
        };
    }

    /**
     * @return 枚举类里面商品品类类型的处理方法
     */
    private CommodityLimitEnumsCalculate plEnum() {
        return (baseActivity, params) -> {
            String value = Optional.ofNullable(baseActivity)
                    .map(ComdityLimitDTO::getFrontCategory)
                    .map(val -> CollectionUtil.isEmpty(val) ? null : val.get(0))
                    .map(BaseActivityDTO::getId)
                    .orElse(null);
            // 活动进来的id 和枚举类的id不一样时 返回null
            if (!PATemplateCommodityEnum.PL.getId().equals(value)) {
                return null;
            }
            // 获取单前的sku 和 对应的 品牌
            String frontCategory = params.getCommoditie().getCategoryId();
            Long upId = params.getCommoditie().getUpId();
            // 获取配置的品牌 和 对应的指定商品不参加
            List<BaseActivityDTO> frontCategoryList = baseActivity.getFrontCategory();

            boolean brandCodeFlag = frontCategoryList.stream()
                    .anyMatch(val -> val.getValue().equals(frontCategory));
            boolean skuFlag = Optional.ofNullable(baseActivity.getSkuList())
                    .map(skuList -> skuList.stream()
                            .anyMatch(val -> val.getUpId().equals(String.valueOf(upId))))
                    .orElse(false);
            // 是否在这个品牌内 如果在 是不是在指定商品不参加里面
            return brandCodeFlag && !skuFlag;
        };
    }

    /**
     * 枚举类每添加一种类型，都需要再这里初始化这张类型的处理结果，不然活动选择那种类型 会报错
     */
    private void init() {
        calculateHelper.add(ppEnum());
        calculateHelper.add(plEnum());
        calculateHelper.add(skuEnum());
        calculateHelper.add(allCommodityEnum());
    }

    /**
     * 如果选择的所有商品
     *
     * @return 所有商品的处理类
     */
    private CommodityLimitEnumsCalculate allCommodityEnum() {

        return (baseActivity, params) -> {
            // 活动进来的id 和枚举类的id不一样时 返回null
            String value = Optional.ofNullable(baseActivity)
                    .map(ComdityLimitDTO::getAllCommodity)
                    .map(val -> CollectionUtil.isEmpty(val) ? null : val.get(0))
                    .map(BaseActivityDTO::getId)
                    .orElse(null);
            if (!PATemplateCommodityEnum.ALL_COMMODITY.getId().equals(value)) {
                return null;
            }

            // 所有商品有值 说明配置了所有商品的配置
            return CollectionUtil.isNotEmpty(baseActivity.getAllCommodity());
        };
    }

    private Boolean calculateCommodityLimit(ComdityLimitDTO comdityLimitDTO, ActivityParamsDTO params) {


        return commodityCalculate(comdityLimitDTO, params);

    }


    /**
     * @param comdityLimitDTO 商品限制类型的活动信息
     * @param params          当前用户获得的值
     * @return 计算的入口
     */
    private Boolean commodityCalculate(ComdityLimitDTO comdityLimitDTO, ActivityParamsDTO params) {

        for (CommodityLimitEnumsCalculate calculate : calculateHelper) {

            Boolean result = calculate.calculate(comdityLimitDTO, params);
            boolean existFlag = Optional.ofNullable(result)
                    .isPresent();
            if (existFlag) {
                setSaleInventory(params);
                return result;
            }
        }
        setSaleInventory(params);
        return Boolean.FALSE;
    }

    private void setSaleInventory(ActivityParamsDTO params) {
        ActivityCommodityDTO commoditie = params.getCommoditie();
        if (Objects.isNull(commoditie.getStockType())) {
            commoditie.setStockType(ActivityInventory.SALES_INVENTORY.getId());
        }
    }
}
