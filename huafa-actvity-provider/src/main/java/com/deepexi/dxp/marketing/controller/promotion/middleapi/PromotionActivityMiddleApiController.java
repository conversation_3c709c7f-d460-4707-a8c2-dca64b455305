//package com.deepexi.dxp.marketing.controller.promotion.middleapi;
//
//import com.deepexi.dxp.marketing.api.promotion.PromotionActivityMiddleApi;
//import com.deepexi.dxp.marketing.domain.promotion.dto.activity.PromotionActivityCouponVO;
//import com.deepexi.dxp.marketing.domain.promotion.dto.activity.PromotionActivityListPostVO;
//import com.deepexi.dxp.marketing.domain.promotion.dto.activity.PromotionActivityStatusUpdateRequest;
//import com.deepexi.dxp.marketing.domain.promotion.dto.activity.PromotionSeckillActivityVO;
//import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityCouponQuery;
//import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityQuery;
//import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionSeckillActivityQuery;
//import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityCreatePostRequest;
//import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityUpdatePostRequest;
//import com.deepexi.dxp.marketing.enums.status.ActivityStatus;
//import com.deepexi.dxp.marketing.service.promotion.PromotionActivityMiddleService;
//import com.deepexi.util.config.Payload;
//import com.deepexi.util.pageHelper.PageBean;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.validation.Valid;
//import java.util.List;
//
///**
// * <AUTHOR> xinjian.yao
// * @date 2019/11/21 14:11
// */
//@RestController
//@Slf4j
//@Api(value = "活动", tags = "活动相关接口")
//public class PromotionActivityMiddleApiController implements PromotionActivityMiddleApi {
//
//    @Autowired
//    private PromotionActivityMiddleService promotionActivityMiddleService;
//
//    @Override
//    @ApiOperation(value = "分页查询活动")
//    public Payload<PageBean<PromotionActivityListPostVO>> findPage(PromotionActivityQuery dto) {
//        return new Payload<>(promotionActivityMiddleService.findPage(dto));
//    }
//
//    @Override
//    @ApiOperation(value = "查询列表")
//    public Payload<List<PromotionActivityListPostVO>> findAll(PromotionActivityQuery dto) {
//        return new Payload<>(promotionActivityMiddleService.findAll(dto));
//    }
//
//    @Override
//    @ApiOperation(value = "根据id查询活动")
//    public Payload<PromotionActivityListPostVO> findActivityById(@RequestParam Long id) {
//        return new Payload<>(promotionActivityMiddleService.getActivityById(id));
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    @ApiOperation(value = "创建活动")
//    public Payload<Long> create(@RequestBody @Valid PromotionActivityCreatePostRequest dto) {
//
//        return new Payload<>(promotionActivityMiddleService.create(dto));
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    @ApiOperation(value = "修改活动")
//    public Payload<Boolean> update(@RequestParam Long id, @RequestBody PromotionActivityUpdatePostRequest dto) {
//        return new Payload<>(promotionActivityMiddleService.updateActivityById(id, dto));
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    @ApiOperation(value = "删除活动")
//    public Payload<Boolean> delete(@RequestBody List<Long> ids) {
//        return new Payload<>(promotionActivityMiddleService.delete(ids));
//    }
//
//    @Override
//    @ApiOperation(value = "发布活动")
//    public Payload<Boolean> release(Long id) {
//        return new Payload<>(promotionActivityMiddleService.updateStatus(id, ActivityStatus.IN_PROGRESS.getId()));
//    }
//
//    @Override
//    @ApiOperation(value = "终止活动")
//    public Payload<Boolean> stop(Long id) {
//        return new Payload<>(promotionActivityMiddleService.updateStatus(id, ActivityStatus.STOP.getId()));
//    }
//
//    @Override
//    @ApiOperation(value = "修改活动主表信息")
//    public Payload<Long> updateActivityStatus(@RequestBody @Valid PromotionActivityStatusUpdateRequest dto) {
//        return new Payload<>(promotionActivityMiddleService.updateActivityStatus(dto));
//    }
//
//    @Override
//    @ApiOperation(value = "查询秒杀活动分页")
//    public Payload<PageBean<PromotionSeckillActivityVO>> findSeckillPage(@RequestBody @Valid PromotionSeckillActivityQuery dto) {
//        return new Payload<>(promotionActivityMiddleService.findSeckillPage(dto));
//    }
//
//    @Override
//    @ApiOperation(value = "查询活动-优惠券分页")
//    public Payload<PageBean<PromotionActivityCouponVO>> findActivityCouponPage(@RequestBody @Valid PromotionActivityCouponQuery query) {
//        return new Payload<>(promotionActivityMiddleService.findActivityCouponPage(query));
//    }
//}
