package com.deepexi.dxp.marketing.domain.marketing.vo;


import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 营销任务受众目标VO
 * @Author: HuangBo.
 * @Date: 2020/3/13 17:18
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MarketingActiveTargetVO extends SuperVO {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 主动营销任务
     */
    @ApiModelProperty(value = "主动营销任务ID")
    @NotNull(message = "主动营销任务ID不能为空")
    private Long activeId;


    /**
     * 全部用户类型(0:否，1:全部用户 2:全部会员 3:全部粉丝)
     */
    @ApiModelProperty(value = "全部用户类型(0:否，1:全部用户 2:全部会员 3:全部粉丝)")
    @NotNull(message = "全部用户类型(0:否，1:全部用户 2:全部会员 3:全部粉丝)必填")
    private Integer usersScope;

    @ApiModelProperty(value = "单独按用户选取类型(0:否，1:全部类型 2：会员 3：粉丝)")
    private Integer byUsers;

    @ApiModelProperty(value = "是否有按标签选取(0:否，1:是)")
    private Integer byTags;

    @ApiModelProperty(value = "是否有按客群选取(0:否，1:是)")
    private Integer byGroups;


    /**
     * 选取的会员数量
     */
    @ApiModelProperty(value = "选取的会员数量")
    private int memberInfoNums;


    /**
     * 标签数量
     */
    @ApiModelProperty(value = "标签数量")
    private int tagNums;

    /**
     * 客群数量
     */
    @ApiModelProperty(value = "客群数量")
    private int groupNums;

    /**
     * 模型精选(0:不精选，1:模型单独精选，2:模型综合精选)
     */
    @ApiModelProperty(value = "模型精选(0:不精选，1:单个模型结果分别筛选，2:多模型综合结果筛选)")
    @NotNull(message = "模型精选必填")
    private int byModel;

    /**
     * 筛选后预估人数
     */
    @ApiModelProperty(value = "筛选后预估人数")
    private int exactModelNums;

    /**
     * 营销任务投放标签客群
     */
    private List<MarketingActiveTargetTagsVO> groupTagVOList;

    /**
     * 客户信息列表
     */
    private List<MarketingActiveTargetUsersVO> membersInfoVOList;

    /**
     * 粉丝信息列表
     */
    private List<MarketingActiveTargetUsersVO> fansInfoVOList;

    /**
     * 模型信息列表
     */
    private List<MarketingActiveTargetModelVO> modelVOList;

    @ApiModelProperty(value = "实验任务数量")
    private Integer experimentTaskNums;
}
