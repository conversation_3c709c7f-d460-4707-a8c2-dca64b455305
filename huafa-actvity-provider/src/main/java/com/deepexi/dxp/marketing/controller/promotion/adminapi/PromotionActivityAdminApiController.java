//package com.deepexi.dxp.marketing.controller.promotion.adminapi;
//
//import com.deepexi.dxp.marketing.domain.promotion.dto.PromotionActivityDetailDTO;
//import com.deepexi.dxp.marketing.domain.promotion.dto.activity.PromotionActivityListPostVO;
//import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityQuery;
//import com.deepexi.dxp.marketing.domain.promotion.request.PromotionActivityCreateRequest;
//import com.deepexi.dxp.marketing.domain.promotion.request.PromotionActivityStatusUpdateRequest;
//import com.deepexi.dxp.marketing.domain.promotion.request.PromotionActivityUpdateRequest;
//import com.deepexi.dxp.marketing.manager.promotion.PromotionActivityService;
//import com.deepexi.dxp.marketing.service.promotion.PromotionActivityMiddleService;
//import com.deepexi.util.config.Payload;
//import com.deepexi.util.pageHelper.PageBean;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import io.swagger.annotations.ApiParam;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//
///**
// * 活动管理
// *
// * <AUTHOR>
// * @version 1.0
// * @date 2020-08-17 16:11
// */
//@RestController
//@Slf4j
//@RequestMapping("/admin-api/v1/promotion-activity")
//@Api(value = "PromotionActivityAdminApiController", tags = "活动管理adminApi")
//public class PromotionActivityAdminApiController {
//
//    @Autowired
//    private PromotionActivityService promotionActivityService;
//
//    @Autowired
//    private PromotionActivityMiddleService promotionActivityMiddleService;
//
//    /**
//     * 分页查询活动列表
//     *
//     * @param query
//     * @return
//     */
//    @GetMapping("/find-page")
//    @ApiOperation(value = "分页查询活动列表")
//    public Payload<PageBean<PromotionActivityListPostVO>> findPage(PromotionActivityQuery query) {
//        PageBean<PromotionActivityListPostVO> pageBean = promotionActivityService.findPage(query);
//        return new Payload<>(pageBean);
//    }
//
//    /**
//     * 批量查询活动的信息
//     *
//     * @param query 活动的查询信息
//     * @return 查询出来的活动信息
//     */
//    @GetMapping("/list")
//    @ApiOperation(value = "查询列表")
//    public Payload<List<PromotionActivityListPostVO>> list(@ApiParam(name = "query", required = true) @ModelAttribute PromotionActivityQuery query) {
//        List<PromotionActivityListPostVO> resultList = promotionActivityMiddleService.findAll(query);
//        return new Payload<>(resultList);
//    }
//
//    /**
//     * 根据id查询活动
//     *
//     * @param id
//     * @return
//     */
//    @GetMapping("/find-activity-by-Id")
//    @ApiOperation(value = "根据id查询活动")
//    public Payload<PromotionActivityDetailDTO> findActivityById(@RequestParam Long id) {
//        return new Payload<>(promotionActivityService.getActivityById(id));
//    }
//
//    /**
//     * 创建活动
//     *
//     * @param vo
//     * @return
//     */
//    @PostMapping("/create")
//    @ApiOperation(value = "创建活动")
//    public Payload<Long> create(@RequestBody PromotionActivityCreateRequest vo) {
//        return new Payload<>(promotionActivityService.create(vo));
//    }
//
//    /**
//     * 修改活动
//     *
//     * @param id
//     * @param vo
//     * @return
//     */
//    @PutMapping("/update/{id}")
//    @ApiOperation(value = "修改活动")
//    public Payload<Boolean> update(@PathVariable Long id, @RequestBody PromotionActivityUpdateRequest vo) {
//        vo.setActivityId(id);
//        return new Payload<>(promotionActivityService.updateActivityById(id, vo));
//
//    }
//
//    /**
//     * 删除活动
//     *
//     * @param ids
//     * @return
//     */
//    @DeleteMapping("/delete")
//    @ApiOperation(value = "删除活动")
//    public Payload<Boolean> delete(@RequestBody List<Long> ids) {
//        return new Payload<>(promotionActivityService.delete(ids));
//    }
//
//    /**
//     * 修改活动状态
//     *
//     * @param vo 修改活动需要的vo
//     * @return 是否修改到数据
//     */
//    @PutMapping("/update-activity-status")
//    @ApiOperation(value = "修改活动状态")
//    public Payload<Long> updateActivityStatus(@RequestBody PromotionActivityStatusUpdateRequest vo) {
//        return new Payload<>(promotionActivityService.updateActivityStatus(vo));
//    }
//
//}
