//package com.deepexi.dxp.marketing.engine.event;
//
//
//import com.deepexi.dxp.marketing.engine.MarketingTaskFilter;
//import com.deepexi.dxp.marketing.engine.processor.AbstractTaskProcessor;
//import com.deepexi.dxp.marketing.engine.task.MetaDataTask;
//import com.deepexi.dxp.marketing.enums.redis.RedisPrefixEnum;
//import com.deepexi.dxp.middle.marketing.domain.dto.MarketingActiveDTO;
//
///**
// * 营销任务事件对象
// *
// * <AUTHOR>
// * @date 2020/3/18 18:50
// */
//public class MarketingTaskEvent extends AbstractEvent {
//    private final AbstractTaskProcessor processor;
//
//    private final MarketingTaskFilter taskFilter;
//
//    public final static String GROUP_META_ES_PREFIX = "dxp-domain-marketing_meta_es_";
//    public final static String GROUP_META_SEND_PREFIX = "dxp-domain-marketing_meta_send_";
//
//    public MarketingTaskEvent(Object source) {
//        super(source);
//        this.processor = (AbstractTaskProcessor) source;
//        taskFilter = new MarketingTaskFilter(((MarketingActiveDTO) this.processor
//                .getMetaDataTask().getMarketingTaskDTO()).getSecurityFilterList());
//    }
//
//    // 事件监听器处理完后进行回调方法
//    public void semaphoreDown() {
//        processor.semaphoreDown();
//    }
//
//    public MetaDataTask getMetaDataTask() {
//        return processor.getMetaDataTask();
//    }
//
//    /**
//     * 获取MQ Topic tags 名称
//     *
//     * @return
//     */
//    public String getTopicTagName() {
//        return getMetaDataTask().getKey();
//    }
//
//    /**
//     * 获取租户ID
//     *
//     * @return
//     */
//    public String getTenantId() {
//        return getMetaDataTask().getMetaDataParameters().getTenantId();
//    }
//
//    /**
//     * 获取任务圈的人数放入redis的key
//     */
//    public String getRedisProduceMemberNums() {
//        return RedisPrefixEnum.TASK_PRODUCE_NUMS.getKey() + ":" + getMetaDataTask().getKey();
//    }
//
//    /**
//     * 获取任务发送处理完毕人数放入redis的key
//     */
//    public String getRedisConsumeMemberNums() {
//        return RedisPrefixEnum.TASK_CONSUME_NUMS.getKey() + ":" + getMetaDataTask().getKey();
//    }
//
//    /**
//     * 判断用户是否需要被过滤掉，不在本次任务发送内
//     *
//     * @param memberId 用户ID
//     * @param tenantId 租户ID
//     * @return
//     */
//    public boolean isFilter(Long memberId, String tenantId) {
//        return taskFilter.isFilter(memberId, tenantId);
//    }
//
//}
