package com.deepexi.dxp.marketing.engine.event;

import java.util.EventObject;

/**
 * 基础事件对象
 *
 * <AUTHOR>
 * @date 2020/3/18 17:05
 */
public abstract class AbstractEvent extends EventObject {

    private static final long serialVersionUID = -921257946901166568L;

    /**
     * 事件发生时系统时间
     */
    private final long timestamp;

    public AbstractEvent(Object source) {
        super(source);
        this.timestamp = System.currentTimeMillis();
    }

    public final long getTimestamp() {
        return this.timestamp;
    }
}
