package com.deepexi.dxp.marketing.domain.promotion.query;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 领券活动优惠券信息查询方法入参
 * <AUTHOR> ming.zhong
 * @date created in 14:46 2019/11/28
 */
@Data
@EqualsAndHashCode
@ApiModel
public class PromotionActivityCouponActivityIdQuery extends AbstractObject {

    /**
     * 领券活动id集合
     */
    private List<Long> idS;

}
