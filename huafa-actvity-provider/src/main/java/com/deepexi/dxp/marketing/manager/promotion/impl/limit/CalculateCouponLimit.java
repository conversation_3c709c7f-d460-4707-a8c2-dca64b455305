package com.deepexi.dxp.marketing.manager.promotion.impl.limit;

import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityConfigDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.TemplateLimitDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityParamsDTO;
import com.deepexi.dxp.marketing.enums.activity.limit.PATemplateCouponEnum;
import com.deepexi.dxp.marketing.manager.promotion.impl.BaseLimit;
import com.deepexi.dxp.middle.promotion.util.SpringElUtil;
import com.deepexi.util.CollectionUtil;

import java.util.*;

/**
 * <AUTHOR> ming.zhong
 * @date created in 16:56 2019/11/25
 */
public class CalculateCouponLimit extends BaseLimit {

    private List<CouponLimitEnumsCalculate> calculateHelper = new ArrayList<>(30);


    public CalculateCouponLimit(TemplateLimitDTO templateLimitDTO, ActivityConfigDTO activity, ActivityParamsDTO params) {
        super(templateLimitDTO, activity, params);
    }

    @Override
    public Boolean calculate() {
        // 活动存储的值
        List<BaseActivityDTO> activityCouponLimit = super.getActivity().getCouponLimit();
        if (CollectionUtil.isEmpty(activityCouponLimit)) {
            return Boolean.FALSE;
        }
        // 需求比较的值
        ActivityParamsDTO params = super.getParams();

        // 初始化计算信息
        init();
        return calculateCouponLimit(activityCouponLimit, params);
    }

    private interface CouponLimitEnumsCalculate {
        /**
         * 限制计算
         *
         * @param baseActivityDTO 设计活动时 配置存储优惠券的信息
         * @param params          传过来的参数
         * @return 不同类型是非成功
         */
        Boolean calculate(BaseActivityDTO baseActivityDTO, ActivityParamsDTO params);
    }

    /**
     * @return 枚举类里面使用优惠券 类型的处理方法
     */
    private CouponLimitEnumsCalculate couponEnum() {
        return (baseActivity, params) -> {
            // 活动进来的id 和枚举类的id不一样时 返回null
            if (Objects.nonNull(baseActivity)
                    && !PATemplateCouponEnum.COUPONFLAG.getId().equals(baseActivity.getId())) {
                return null;
            }
            String el = SpringElUtil.makeSpringEL(baseActivity);
            Map<String, Object> param = new HashMap<>(4);
            param.put(baseActivity.getId(), "true");
            return SpringElUtil.judge(el, param);
        };
    }

    /**
     * 枚举类每添加一种类型，都需要再这里初始化这张类型的处理结果，不然活动选择那种类型 会报错
     */
    private void init() {
        calculateHelper.add(couponEnum());
    }


    private Boolean calculateCouponLimit(List<BaseActivityDTO> activityCouponLimit, ActivityParamsDTO params) {
        for (BaseActivityDTO baseActivityDTO : activityCouponLimit) {
            Boolean calculateResult = tenantCalculate(baseActivityDTO, params);
            if (Objects.nonNull(calculateResult)) {
                return calculateResult;
            }
        }
        return Boolean.TRUE;
    }


    /**
     * @param baseActivityDTO 优惠券限制类型的活动信息
     * @param params          当前优惠券获得的值
     * @return 计算的入口
     */
    private Boolean tenantCalculate(BaseActivityDTO baseActivityDTO, ActivityParamsDTO params) {

        for (CouponLimitEnumsCalculate tenantLimitEnumsCalculate : calculateHelper) {
            Boolean result = tenantLimitEnumsCalculate.calculate(baseActivityDTO, params);
            boolean existFlag = Optional.ofNullable(result)
                    .isPresent();
            if (existFlag) {
                return result;
            }
        }
        return Boolean.FALSE;
//        throw new ApplicationException("优惠券类型类 活动选择的类型和枚举类不匹配");
    }

}
