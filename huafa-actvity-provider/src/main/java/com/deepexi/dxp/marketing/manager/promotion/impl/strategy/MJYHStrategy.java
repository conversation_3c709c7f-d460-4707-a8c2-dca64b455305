package com.deepexi.dxp.marketing.manager.promotion.impl.strategy;

import com.deepexi.dxp.marketing.domain.promotion.dto.activity.ActivityRuleDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityConfigDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.TemplateLimitDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityParamsDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityResponseParamsDTO;
import com.deepexi.dxp.marketing.enums.activity.strategy.MJYHStrategyType;
import com.deepexi.dxp.marketing.enums.activity.strategy.StrategyGroupEnum;
import com.deepexi.dxp.marketing.enums.activity.strategy.condition.MJYHConditionEnum;
import com.deepexi.dxp.marketing.enums.activity.strategy.operation.MJYHOperationEnum;
import com.deepexi.dxp.marketing.enums.status.ActivityInventory;
import com.deepexi.dxp.marketing.manager.promotion.impl.BaseStrategy;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> xinjian.yao
 * @date 2019/11/27 20:12
 */
public class MJYHStrategy extends BaseStrategy {

    private List<MJYHtrategyEnumsCalculate> calculateHelper = new ArrayList<>();

    public MJYHStrategy(TemplateLimitDTO templateLimitDTO, ActivityConfigDTO activityConfigDTO,
                        ActivityParamsDTO params, ActivityResponseParamsDTO activityResponseParamsDTO) {
        super(templateLimitDTO, activityConfigDTO, params, activityResponseParamsDTO);
    }

    /**
     * 总价任选的枚举类处理
     */
    private interface MJYHtrategyEnumsCalculate {
        /**
         * @param activityRuleDTOList       活动的优惠rule
         * @param params                    活动的参数
         * @param activityResponseParamsDTO 优惠结果返回类
         */
        void calculate(List<ActivityRuleDTO> activityRuleDTOList, ActivityParamsDTO params, ActivityResponseParamsDTO activityResponseParamsDTO);
    }

    private MJYHtrategyEnumsCalculate MJYHEnum() {
        return (activityRuleList, params, activityResponseParams) -> {
            boolean enumsFlag = activityResponseParams.getPaTemplateId().equals(StrategyGroupEnum.MJS_G.getId());
            if (!enumsFlag) {
                return;
            }
            if (activityRuleList == null) {
                return;
            }
            String strategyType = activityRuleList.get(0).getStrategyType();
            BigDecimal discounts = null;
            if (MJYHStrategyType.JEMJ.getId().equals(strategyType)) {
                discounts = getJEMJDiscounts(activityRuleList, activityResponseParams);
            } else if (MJYHStrategyType.WMK.getId().equals(strategyType)) {
                discounts = getWMKDiscounts(activityRuleList);
            }

            if (Objects.isNull(discounts) || discounts.equals(new BigDecimal("0"))) {
                super.getActivityResponseParamsDTO().getActivityCommodityDTOList().forEach(val -> val.setStockType(ActivityInventory.SALES_INVENTORY.getId()));
                return;
            }
            // 金额分摊
            super.amountOfShareCalculate(activityResponseParams.getActivityCommodityDTOList(), discounts.doubleValue());
            // 优惠信息汇总
            super.collectDiscounts(activityResponseParams);
        };
    }

    private BigDecimal getJEMJDiscounts(List<ActivityRuleDTO> activityRuleDTOList, ActivityResponseParamsDTO activityResponseParamsDTO) {
        // 获取所有商品的总价 进行比例的计算需要用到
        double allPrice = super.getAllPrice(activityResponseParamsDTO.getActivityCommodityDTOList(), DETAIL_PRICE_ALL);
        // 规则排序
        if (activityRuleDTOList.get(0).getSort() == null) {
            activityRuleDTOList.forEach(val -> {
                val.setSort(val.getCondition().get(0).getValue());
            });
        }
        activityRuleDTOList.sort(Comparator.comparing((ActivityRuleDTO val) -> Double.valueOf(val.getSort())).reversed());

        for (ActivityRuleDTO activityRuleDTO : activityRuleDTOList) {
            String value = activityRuleDTO.getCondition()
                    .stream()
                    .filter(val -> MJYHConditionEnum.JE.getId().equals(val.getId()))
                    .findFirst()
                    .map(BaseActivityDTO::getValue)
                    .orElse("0");
            double conditionValue = Double.parseDouble(value);
            if (allPrice < conditionValue) {
                continue;
            }

            List<ActivityRuleDTO> tempList = new ArrayList<>();
            tempList.add(activityRuleDTO);
            return getWMKDiscounts(tempList);
        }
        return new BigDecimal(0);
    }

    /**
     * 获取无门槛的优惠条件
     *
     * @param activityRuleDTOList 优惠规则组合
     * @return 优惠的金额
     */
    private BigDecimal getWMKDiscounts(List<ActivityRuleDTO> activityRuleDTOList) {
        String value = activityRuleDTOList.get(0)
                .getOperation()
                .stream()
                .filter(val -> MJYHOperationEnum.JE.getId().equals(val.getId()))
                .findFirst()
                .map(BaseActivityDTO::getValue)
                .orElse(null);
        return null == value ? BigDecimal.ZERO : new BigDecimal(value);
    }

    private void init() {
        calculateHelper.add(MJYHEnum());
    }

    @Override
    public Boolean calculate() {
        // 获取活动的策略
        List<ActivityRuleDTO> activityStrategiesList = super.getActivityConfigDTO().getActivityRuleDTOList();
        // 获取活动的参数
        ActivityParamsDTO params = super.getParams();
        // 活动返回的参数
        ActivityResponseParamsDTO activityResponseParamsDTO = super.getActivityResponseParamsDTO();
        init();
        calculate(activityStrategiesList, params, activityResponseParamsDTO);
        return true;
    }

    private void calculate(List<ActivityRuleDTO> activityStrategiesList, ActivityParamsDTO params, ActivityResponseParamsDTO activityResponseParamsDTO) {
        for (MJYHtrategyEnumsCalculate calculate : calculateHelper) {
            calculate.calculate(activityStrategiesList, params, activityResponseParamsDTO);
        }
    }
}
