package com.deepexi.dxp.marketing.service.specify;

import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityParticipationQuery;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityParticipationDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityParticipationGroupResponseDTO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityParticipationDO;

import java.util.List;

/**
 * 项目参与活动中间表service
 */
public interface ActivityParticipationService {

    /**
     * 获取活动下面的所有项目
     * @param activityId
     * @return
     */
    List<ActivityParticipationGroupResponseDTO> projectGroupList(Long activityId);

    /**
     * 获取活动下面的所有项目
     * @param activityId
     * @return
     */
    List<ActivityParticipationGroupResponseDTO> getProjectGroupList(Long activityId);

    /**
     * 获取用户下面的所有项目
     * @param userId
     * @return
     */
    List<ActivityParticipationGroupResponseDTO> projectGroupListByUserId(String userId);


    List<ActivityParticipationDO> listByActivityId(Long id);

    /**
     * 开始异步执行项目是否跳转批量更新任务
     * @param activityId
     */
    void updateBatchProjectIsJumpable(Long activityId);


    /**
     * 通过活动信息获取下拉选项
     * @param activityParticipationQuery
     * @return
     */
    List<ActivityParticipationDTO> getSelectActivity(ActivityParticipationQuery activityParticipationQuery);

}
