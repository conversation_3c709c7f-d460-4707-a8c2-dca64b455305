package com.deepexi.dxp.marketing.controller.specify.adminapi;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.UserLotteryRecordQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.VerifyInfoQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.*;
import com.deepexi.dxp.marketing.domain.marketing.response.*;
import com.deepexi.dxp.marketing.service.specify.LuckyDrawService;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 抽奖活动
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin-api/v1/luckyDraw")
@Api(value = "抽奖活动",tags = {"抽奖活动接口"})
public class LuckyDrawController {

    @Resource
    private LuckyDrawService luckyDrawService;

    @PostMapping("/save")
    @ApiOperation(value = "抽奖活动保存", notes = "抽奖活动信息保存")
    public Data<Boolean> save(@RequestBody @Valid LuckyDrawRequestDTO requestDTO) {
        if (Objects.isNull(requestDTO)){
            throw new ApplicationException("入参对象不能为空");
        }
        return new Data<>(luckyDrawService.save(requestDTO));
    }

    @ApiOperation(value = "修改活动")
    @PostMapping("/update")
    public Data<Boolean> update(@RequestParam Long id, @RequestBody @Valid LuckyDrawRequestDTO dto) {
        if (Objects.isNull(dto)){
            throw new ApplicationException("入参对象不能为空");
        }
        return new Data<>(luckyDrawService.updateActivityById(id, dto));
    }

    @PostMapping("/detail")
    @ApiOperation(value="活动详情", notes = "活动详情")
    public Data<LuckyDrawResponseDTO> detail(@RequestParam Long id) {

        return new Data<>(luckyDrawService.detail(id));
    }

    @PostMapping("/userLotteryRecord")
    @ApiOperation(value="活动详情-用户抽奖记录", notes = "活动详情-用户抽奖记录")
    public Data<PageBean<ActivityPartakeLogResponseDTO>> userLotteryRecord(@RequestBody @Valid UserLotteryRecordQuery query) {
        return new Data<>(luckyDrawService.userLotteryRecord(query));
    }

    @PostMapping("/prizeResultList")
    @ApiOperation(value="活动详情-中奖结果列表", notes = "活动详情-中奖结果列表")
    public Data<Set<String>> prizeResultList(@RequestParam Long id) {
        return new Data<>(luckyDrawService.prizeResultList(id));
    }

    @PostMapping("/verifyInfoList")
    @ApiOperation(value="活动详情-奖品领取/核销明细", notes = "活动详情-奖品领取/核销明细")
    public Data<List<VerifyInfoResponseDTO>> verifyInfoList(@RequestBody @Valid VerifyInfoQuery query) {
        return new Data<>(luckyDrawService.verifyInfoList(query));
    }
}
