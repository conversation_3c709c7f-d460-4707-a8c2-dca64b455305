package com.deepexi.dxp.marketing.controller.specify.middleapi;


import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.constant.RedisConstants;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityFissionLogQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.PartakeLogQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.PromotionActivityUpdateStatusRequest;
import com.deepexi.dxp.marketing.domain.marketing.response.*;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.PromotionActivityVO;
import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityQuery;
import com.deepexi.dxp.marketing.domain.promotion.request.PromotionActivityCreateRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.PromotionActivityDeleteRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.PromotionActivityUpdateRequest;
import com.deepexi.dxp.marketing.enums.status.ActivityStatus;
import com.deepexi.dxp.marketing.service.specify.ActivityParticipationService;
import com.deepexi.dxp.marketing.service.specify.PromotionActivityService;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;


@RestController
@Slf4j
@RequestMapping("/middle-api/v1/promotion-activity")
@Api(value = "middle-活动管理", tags = "middle-活动管理")
public class MiddlePromotionActivityController {

    @Autowired
    private PromotionActivityService promotionActivityService;

    @Autowired
    private ActivityParticipationService activityParticipationService;

    /**
     * Feign调用方法
     * @param query
     * @return
     */
    @PostMapping("/list")
    @ApiOperation(value="活动列表", notes = "活动列表")
    public Data<PageBean<PromotionActivityVO>> list(@RequestBody PromotionActivityQuery query) {
        return new Data<>(promotionActivityService.findPage(query));
    }

    /**
     * 创建活动
     *
     * @param vo
     * @return
     */
    @PostMapping("/create")
    @ApiOperation(value = "创建活动")
    public Data<Long> create(@RequestBody PromotionActivityCreateRequest vo) {
        return new Data<>(promotionActivityService.create(vo));
    }

    @DeleteMapping("/delete")
    @ApiOperation(value="删除活动", notes = "删除活动")
    public Data<Boolean> delete(@RequestBody PromotionActivityDeleteRequest vo) {
        return new Data<>(promotionActivityService.delete(vo.getIds()));
    }

    /**
     * 修改活动
     *
     * @param vo
     * @return
     */
    @PutMapping("/update")
    @ApiOperation(value = "修改活动")
    public Data<Boolean> update(@RequestBody PromotionActivityUpdateRequest vo) {
        if (vo.getId() == null){
            throw new ApplicationException("活动id不能为空！");
        }
        return new Data<>(promotionActivityService.updateActivityById(vo));
    }

    @ApiOperation(value = "更改活动状态，仅适用于秒杀活动")
    @PostMapping("/seckillUpdateStatus")
    public Data<Boolean> seckillUpdateStatus(@Valid @RequestBody PromotionActivityUpdateStatusRequest requestVo) {
        return new Data<>(promotionActivityService.seckillUpdateStatus(requestVo));
    }

    @ApiOperation(value = "终止活动")
    @PutMapping("/stop")
    public Data<Boolean> stop(@RequestParam Long id) {
        return new Data<>(promotionActivityService.updateStatus(id, ActivityStatus.STOP.getId()));
    }

    @ApiOperation(value = "开始活动")
    @PutMapping("/start")
    public Data<Boolean> start(@RequestParam Long id) {
        return new Data<>(promotionActivityService.updateStatus(id, ActivityStatus.IN_PROGRESS.getId()));
    }

    /**
     * 根据id查询活动
     *
     * @param id
     * @return
     */
    @GetMapping("/getById")
    @ApiOperation(value = "根据id查询活动")
    public Data<PromotionActivityDetailDTO> findActivityById(@RequestParam Long id) {
        return new Data<>(promotionActivityService.getActivityById(id));
    }


    @PostMapping("/partakeLogList")
    @ApiOperation(value="活动参与明细", notes = "活动参与明细")
    public Data<PageBean<ActivityPartakeLogResponseDTO>> partakeLogList(@RequestBody @Valid PartakeLogQuery query) {
        return new Data<>(promotionActivityService.actPartakeLogList(query));
    }

    @ApiOperation(value="获取参与明细导出数据", notes = "获取参与明细导出数据")
    @PostMapping("/getExportPartakeLogExcelData")
    public ExcelExportResponseDTO getExportPartakeLogExcelData(@RequestBody PartakeLogQuery query){
        return promotionActivityService.getExportPartakeLogExcelData(query);
    }


    @GetMapping("/projectListByActivityId")
    @ApiOperation(value="项目列表", notes = "项目列表")
    public Data<List<ActivityParticipationGroupResponseDTO>> getProjectList(@RequestParam Long id) {
        return new Data<>(activityParticipationService.projectGroupList(id));
    }

    @PostMapping("/rankList")
    @ApiOperation(value="排行榜", notes = "排行榜")
    public Data<FissionRankAdminResponseDTO> rankList(@RequestParam Long activityId) {
        FissionRankAdminResponseDTO fissionRankAdminResponseDTO = new FissionRankAdminResponseDTO();
        List<FissionRankResponseDTO> fissionRankResponseDTOS = promotionActivityService.rankList(activityId);
        if(CollectionUtil.isNotEmpty(fissionRankResponseDTOS)){
            List<String> nickNameList = fissionRankResponseDTOS.stream().map(FissionRankResponseDTO::getNickName).collect(Collectors.toList());
            List<Integer> cntList = fissionRankResponseDTOS.stream().map(FissionRankResponseDTO::getCnt).collect(Collectors.toList());
            fissionRankAdminResponseDTO.setNickNameList(nickNameList);
            fissionRankAdminResponseDTO.setCntList(cntList);
        }
        return new Data<>(fissionRankAdminResponseDTO);
    }

    @GetMapping("/getUUID")
    @ApiOperation(value = "从后端获取uuid")
    public  Data<String> getUUID(@RequestParam String createdBy){
        return new Data<>(promotionActivityService.getUUID(String.format(RedisConstants.CACHE_PREV_KEY_ACT_CREATE_INFO_REPEAT,createdBy)));
    }

    @PostMapping("/fissionFriendsList")
    @ApiOperation(value="裂变好友列表")
    public Data<PageBean<ActivityFissionLogResponseDTO>> fissionFriendsList(@RequestBody ActivityFissionLogQuery query) {
        return new Data<>(promotionActivityService.fissionFriendsList(query));
    }

    @ApiOperation(value = "活动下架")
    @PutMapping("/offShelf")
    public Data<Boolean> offShelf(@RequestParam Long id) {
        return new Data<>(promotionActivityService.updateStatus(id, ActivityStatus.OFF_SHELF.getId()));
    }
}
