package com.deepexi.dxp.marketing.engine.task;

import com.deepexi.dxp.middle.marketing.domain.dto.TaskSendDataDTO;
import com.deepexi.util.exception.ApplicationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 任务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-16 11:50
 */
@Component
@Slf4j
public abstract class Task {


    /**
     * 执行任务
     *
     * @param taskSendDataDto 发送内容体
     * @return
     * @throws ApplicationException
     */
    public abstract boolean execute(TaskSendDataDTO taskSendDataDto) throws ApplicationException;


}
