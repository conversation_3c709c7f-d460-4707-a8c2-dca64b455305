package com.deepexi.dxp.marketing.domain.marketing.vo;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * 会员列表Query
 *
 * <AUTHOR>
 * @Date 2020/3/20
 */
@Data
@ApiModel
public class MemberListQuery extends SuperQuery {

    @ApiModelProperty("会员ID")
    private Long id;

    @ApiModelProperty("公众号ID")
    private String mediaAppId;

    @ApiModelProperty("会员ID集合")
    private List<Long> ids;

    @ApiModelProperty("状态 1：启用，0：禁用")
    private Integer status;

    @ApiModelProperty("会员手机号")
    private String phone;

    @ApiModelProperty("会员手机号模糊查询")
    private String phoneLike;

    @ApiModelProperty("注册时间开始")
    private Long registerTimeStart;
    @ApiModelProperty("开始时间")
    private Long startTime;

    @ApiModelProperty("注册时间结束")
    private Long registerTimeEnd;
    @ApiModelProperty("注册时间")
    private Long endTime;
    @ApiModelProperty("时间类型 0：注册时间，1：最近关注时间")
    private Integer timeType;

    @ApiModelProperty("会员编码")
    private String code;
    @ApiModelProperty("设备号")
    private String deviceNumber;
    @ApiModelProperty("邮箱")
    private String email;
    @ApiModelProperty("邀请人")
    private Long inviterId;
    @ApiModelProperty("来源")
    private String origin;
    @ApiModelProperty("注册地址")
    private String registerAddress;
    @ApiModelProperty("用户类型：1：会员 2、会员，粉丝 3：粉丝")
    private Integer type;
    @ApiModelProperty("类型，0：个人，1：企业")
    private Integer accountType;
    @ApiModelProperty("会员账户")
    private String username;
    @ApiModelProperty("会员账户模糊查询")
    private String usernameLike;

    @ApiModelProperty("会员名称")
    private String name;

    @ApiModelProperty("会员名称模糊")
    private String nameLike;


    @ApiModelProperty("age")
    private Integer age;
    @ApiModelProperty("appId")
    private Long appId;
    @ApiModelProperty("avatarUrl")
    private String avatarUrl;
    @ApiModelProperty("birthday")
    private String birthday;
    @ApiModelProperty("businessId")
    private Long businessId;
    @ApiModelProperty("constellation")
    private String constellation;
    @ApiModelProperty("contactAddress")
    private String contactAddress;
    @ApiModelProperty("detailAddressLike")
    private String detailAddressLike;
    @ApiModelProperty("education")
    private String education;
    @ApiModelProperty("emailLike")
    private String emailLike;
    @ApiModelProperty("emails")
    private List<String> emails;
    @ApiModelProperty("hobbies")
    private String hobbies;
    @ApiModelProperty("idList")
    private List<Long> idList;
    @ApiModelProperty("inArea")
    private String inArea;
    @ApiModelProperty("job")
    private String job;
    @ApiModelProperty("maritalStatus")
    private String maritalStatus;
    @ApiModelProperty("nationality")
    private String nationality;
    @ApiModelProperty("nickName")
    private String nickName;
    @ApiModelProperty("registerAt")
    private String registerAt;
    @ApiModelProperty("registerFrom")
    private String registerFrom;
    @ApiModelProperty("registerStore")
    private String registerStore;
    @ApiModelProperty("registerTo")
    private String registerTo;
    @ApiModelProperty("sex")
    private Integer sex;
    @ApiModelProperty("storeId")
    private Long storeId;

}
