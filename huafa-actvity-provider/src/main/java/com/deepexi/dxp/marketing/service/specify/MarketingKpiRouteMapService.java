package com.deepexi.dxp.marketing.service.specify;

import com.deepexi.dxp.marketing.domain.marketing.query.specify.MarketingKpiRouteMapQuery;
import com.deepexi.dxp.middle.marketing.domain.dto.MarketingKpiRouteMapDTO;
import com.deepexi.util.pageHelper.PageBean;

import java.util.List;

/**
 * 指标路径图Service
 *
 * @Author: HuangBo.
 * @Date: 2020/6/18 14:22
 */
public interface MarketingKpiRouteMapService {

    Boolean save(MarketingKpiRouteMapDTO dtoEntity);

    MarketingKpiRouteMapDTO queryById(Long id);


    /**
     * 根据查询条件获取集合
     */
    List<MarketingKpiRouteMapDTO> queryList(MarketingKpiRouteMapQuery query);

    /**
     * 分页查询列表
     */
    PageBean<MarketingKpiRouteMapDTO> pageList(MarketingKpiRouteMapQuery query);


    /**
     * 启用/禁用
     *
     * @return
     */
    Boolean processStatus(Long id, Integer status);


    /**
     * 通过任务名称判断是否存在任务
     */
    Boolean existByName(String tenantId, String name, String oldName);

    Boolean deleteByRoute(Long id);

}
