package com.deepexi.dxp.marketing.domain.marketing.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * 自动任务资源执行dto
 * <AUTHOR>
 * @version 1.0
 * @date 2020-05-29 11:09
 */
@Data
public class MarketingResourceDataDTO {

    /**
     * 任务id
     */
    private Long taskId;
    /**
     * 资源id
     */
    private String resourceId;

    /**
     * 资源编码
     */
    private String resourceCode;

    /**
     * 资源渠道
     */
    private String resourceChannel;

    /**
     * 资源名称
     */
    private String resourceName;

    /**
     * 资源信息
     */
    private String resourceInfo;

    /**
     * 资源类型编码
     */
    private String resourceTypeCode;

    /**
     * 资源类型名称
     */
    private String resourceTypeName;

    /**
     * 资源类别编码
     */
    private String resourceCategoryCode;

    /**
     * 资源类别名称
     */
    private String resourceCategoryName;

    /**
     * 资源url地址
     */
    private String resourceUrl;

    /**
     * 开始时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date endTime;

    /**
     * 租户信息
     */
    private String tenantId;

    /**
     * 应用信息
     */
    private Long appId;

    /**
     * 活动扩展信息
     */
    private Map<String, Object> extContent;
}
