package com.deepexi.dxp.marketing.service.specify;

import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityFissionLogQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.*;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityFissionLogResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.CardCollectingHomePageInfoResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.CardCollectingHomeUserInfoResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.CardCollectingPrizeResponseDTO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityLimitDO;
import com.deepexi.util.pageHelper.PageBean;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/27
 */
public interface CardCollectingService {
    /**
     * 活动首页页面信息
     */
    CardCollectingHomePageInfoResponseDTO homePageInfo(ActivityPartakeRequest query);

    /**
     * 活动首页用户信息
     */
    CardCollectingHomeUserInfoResponseDTO homeUserInfo(ActivityPartakeRequest query);

    LotteryResourceDTO lottery(Long partakeLogId, Long resourceId);
    LotteryResourceDTO compose(Long partakeLogId);
    Boolean receiveNow(ReceiveNowDTO dto);

    List<ActivityFissionLogResponseDTO> fissionFriendsList(ActivityFissionLogQuery query);

    List<CardCollectingPrizeResponseDTO> myPrizeList(ActivityFissionLogQuery query);
    /**
     * 参加活动赠送卡片
     */
    List<CardGiveDTO> giveCard(ActivityPartakeRequest requestVo, List<PromotionActivityLimitDO> promotionActivityLimitList);

    /**
     * 助力赠送卡片
     */
    AssistResultDTO assist(ActivityPartakeRequest requestVo);

}
