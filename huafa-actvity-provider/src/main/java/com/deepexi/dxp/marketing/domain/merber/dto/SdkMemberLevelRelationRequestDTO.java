package com.deepexi.dxp.marketing.domain.merber.dto;

import com.deepexi.util.domain.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * SDK-会员等级关系请求DTO
 *
 * <AUTHOR>
 * @since 2020年08月21日 16:29
 */
@Data
public class SdkMemberLevelRelationRequestDTO extends BaseDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 会员等级组ID
     */
    private Long levelGroupId;

    /**
     * 会员等级ID
     */
    private Long levelId;

    @ApiModelProperty("会员id列表")
    private List<Long> memberIdList;
}
