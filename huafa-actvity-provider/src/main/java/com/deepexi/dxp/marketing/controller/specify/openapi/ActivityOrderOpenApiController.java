package com.deepexi.dxp.marketing.controller.specify.openapi;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.constant.PayConstant;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityOrderQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.CancelOrderRequest;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityOrderRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.CouponPayCallBackRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityOrderResponseVO;
import com.deepexi.dxp.marketing.domain.marketing.response.PayOrderQueryResponseDTO;
import com.deepexi.dxp.marketing.enums.specify.OrderStatusEnum;
import com.deepexi.dxp.marketing.extension.HuafaRuntimeEnv;
import com.deepexi.dxp.marketing.service.specify.ActivityOrderService;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.exception.CommonExceptionCode;
import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;


@RestController
@RequestMapping("/open-api/v1/open/ActivityOrder")
@Api(value = "活动订单表", description = "活动订单表接口", tags = {"活动订单表接口"})
public class ActivityOrderOpenApiController {

	@Autowired
	private ActivityOrderService activityOrderService;


	@GetMapping("/pageList")
	@ApiOperation(value = "查询列表", notes = "查询信息列表")
	public Data<PageBean<ActivityOrderResponseVO>> pageList(ActivityOrderQuery query) {
		//如果有传电话，则查询该用户的活动列表
		if (StrUtil.isNotBlank(HuafaRuntimeEnv.getPhone())) {
			query.setPhone(HuafaRuntimeEnv.getPhone());
		}
		return new Data<>(activityOrderService.pageList(query));

	}

	@PostMapping("/pageListPost")
	@ApiOperation(value = "查询列表", notes = "查询信息列表")
	public Data<PageBean<ActivityOrderResponseVO>> pageListPost(@RequestBody ActivityOrderQuery query) {

		return new Data<>(activityOrderService.pageList(query));

	}

	@PostMapping("/save")
	@ApiOperation(value = "新建保存", notes = "新建保存")
	public Data<Long> save(@RequestBody ActivityOrderRequestDTO dto) {
		return new Data<>(activityOrderService.save(dto));
	}


	@GetMapping("/detail")
	@ApiOperation(value="用户订单详情", notes = "用户订单详情")
	public Data<ActivityOrderResponseVO> detail(@RequestParam Long resourceId,@RequestParam  String phone,@RequestParam  Long activityId){
		return new Data<>(activityOrderService.getOrderDetail(resourceId,phone,activityId));
	}
	@GetMapping("/get")
	@ApiOperation(value="用户订单详情", notes = "用户订单详情")
	public Data<ActivityOrderResponseVO> detail(@RequestParam String code){
		return new Data<>(activityOrderService.getOrderDetail(code));
	}

	@ApiOperation(value = "取消订单(未支付)")
	@PutMapping("/closeOrder")
	public Data<Boolean> closeOrder(@RequestParam Long orderId) {
		ActivityOrderResponseVO orderResponseVO = activityOrderService.getDetail(orderId);
		CancelOrderRequest dto = new CancelOrderRequest(orderResponseVO.getCode());
		return closeOrderByCode(dto);
	}

	@ApiOperation(value = "取消订单(未支付)")
	@PostMapping("/closeOrder")
	public Data<Boolean> closeOrderByCode(@Valid @RequestBody CancelOrderRequest dto) {
		return new Data<>(activityOrderService.closeOrder(dto));
	}

	@ApiOperation(value = "取消订单(已支付)")
	@PostMapping("/cancelOrder")
	public Data<Boolean> cancelOrder(@Valid @RequestBody CancelOrderRequest dto) {
		return new Data<>(activityOrderService.cancelOrder(dto));
	}

	@ApiOperation(value = "过期取消订单")
	@PutMapping("/overdueCloseOrder")
	public Data<Boolean> overdueCloseOrder(@RequestParam Long orderId) {
		ActivityOrderResponseVO orderResponseVO = activityOrderService.getDetail(orderId);
		if(Objects.isNull(orderResponseVO)){
			throw new ApplicationException("订单不存在!");
		}
		if(!OrderStatusEnum.UNPAY.getId().equals(orderResponseVO.getStatus())){
			throw new ApplicationException("该订单状态不可以取消!");
		}
		DateTime now = DateTime.now();
		Calendar nowTime = Calendar.getInstance();
		nowTime.setTime(orderResponseVO.getCreatedTime());
		nowTime.add(Calendar.MINUTE, PayConstant.EXPIREMINUTES);
		Date afterTime = nowTime.getTime();
		if(now.after(afterTime)){
			return activityOrderService.closeOrder(orderResponseVO.getWxOrderNo(),orderResponseVO.getId());
		}
		return new Data<>(CommonExceptionCode.ERROR_CODE,"订单未到时间不能取消");
	}

	@GetMapping("/getOrderPayResult")
	@ApiOperation(value="通过订单ID查询订单支付结果", notes = "订单支付结果")
	public PayOrderQueryResponseDTO getOrderPayResult(@RequestParam Long orderId){
		return activityOrderService.getOrderPayResult(orderId);
	}
	@GetMapping("/getLocalOrderPayResult")
	@ApiOperation(value="通过订单ID查询订单支付结果", notes = "订单支付结果")
	public Data<CouponPayCallBackRequestDTO> getLocalOrderPayResult(@RequestParam Long orderId){
		return new Data<>(activityOrderService.getLocalOrderPayResult(orderId));
	}
}