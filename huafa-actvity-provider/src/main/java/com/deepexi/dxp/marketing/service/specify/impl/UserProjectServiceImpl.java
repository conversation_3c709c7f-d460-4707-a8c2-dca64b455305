package com.deepexi.dxp.marketing.service.specify.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cnhuafas.common.service.GatewayService;
import com.deepexi.dxp.marketing.common.base.utils.CommonExceptionCode;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.config.HuafaConstantConfig;
import com.deepexi.dxp.marketing.constant.MkPermitApiConstant;
import com.deepexi.dxp.marketing.constant.RedisConstants;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.UserProjectQuery;
import com.deepexi.dxp.marketing.domain.marketing.response.UserProjectResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityPageQuery;
import com.deepexi.dxp.marketing.enums.specify.CommunityPositionEnum;
import com.deepexi.dxp.marketing.enums.specify.UserProjectsSearchTypeEnum;
import com.deepexi.dxp.marketing.extension.HuafaRuntimeEnv;
import com.deepexi.dxp.marketing.service.specify.UserProjectService;
import com.deepexi.dxp.marketing.utils.HTTPClientUtils;
import com.deepexi.dxp.marketing.utils.HuaFaHmacAuthUtil;
import com.deepexi.dxp.middle.promotion.dao.specify.ActivityParticipationDAO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityParticipationDO;
import com.deepexi.dxp.middle.promotion.util.PageUtil;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.StringUtil;
import com.deepexi.util.pageHelper.PageBean;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserProjectServiceImpl implements UserProjectService {

    @Autowired
    private HuaFaHmacAuthUtil huaFaHmacAuthUtil;

    @Autowired
    private ActivityParticipationDAO activityParticipationDAO;

    @Autowired
    private HuafaConstantConfig huafaConstantConfig;

    @Autowired
    private RedissonClient redissonClient;

    @Resource
    private GatewayService gatewayService;

    @Override
    public PageBean<UserProjectResponseDTO> getUserProjectNoLogin(UserProjectQuery query) {
        return getUserProject(query,false);
    }

    private PageBean<UserProjectResponseDTO> getUserProject(UserProjectQuery query,boolean useV2) {
        try{
            Map<String,Object> map = new HashMap<>(5);
            map.put("pageNumber", query.getPageNumber());
            map.put("pageSize", query.getPageSize());
            map.put("userId", query.getUserId());
            map.put("projectId", query.getProjectId());
            map.put("areaName", query.getRealAreaName());
            map.put("cityName", query.getRealCityName());
            if(UserProjectsSearchTypeEnum.ORG_NAME.getId().equals(query.getSearchType())){
                map.put("orgName", query.getProjectName());
            }else{
                map.put("projectName", query.getProjectName());
            }

            String userProjectNoLoginUrl = MkPermitApiConstant.USER_PROJECT_NO_LOGIN_URL;
            if(useV2){
                userProjectNoLoginUrl = MkPermitApiConstant.USER_PROJECT_NO_LOGIN_URL_V2;
            }
            String result = huaFaHmacAuthUtil.request(0,1, userProjectNoLoginUrl,map);

            log.info("项目分页查询结果:{}",result);
            JSONObject response = JSONUtil.parseObj(result);
            String code = response.getStr("code");

            if(CommonExceptionCode.SUCCESS.equals(code)){
                JSONObject data = JSONUtil.parseObj(response.getStr("data"));
                Integer total = data.getInt("total");
                List<UserProjectResponseDTO> userProjectResponseDTOS = JSONArray.parseArray(data.getStr("projectInfoList"), UserProjectResponseDTO.class);
                PageUtil<UserProjectResponseDTO> responsePageUtil = new PageUtil<>();
                PageBean<UserProjectResponseDTO> page = responsePageUtil.getPage(query.getPageNumber(), query.getPageSize(),total, userProjectResponseDTOS);
                return page;
            }
        }catch(Exception e){
            log.error("获取项目列表异常",e);
        }
        return new PageBean<>();
    }

    public PageBean<UserProjectResponseDTO> getUserProjectNoLoginV2(UserProjectQuery query){
        return getUserProject(query,true);
    }

    @Override
    public List<UserProjectResponseDTO> getCanCheckList(UserProjectQuery query) {
        if(query == null || query.getActivityId() == null){
            return Lists.newArrayList();
        }
        query.setPageSize(999999);//查询用户所有项目不由前端指定数量
        PageBean<UserProjectResponseDTO> userProjectNoLogin = getUserProjectNoLogin(query);
        if(userProjectNoLogin != null && CollectionUtil.isNotEmpty(userProjectNoLogin.getContent())){
            List<UserProjectResponseDTO> userProjectList = userProjectNoLogin.getContent();
            if(CollectionUtil.isEmpty(userProjectList)){
                return Lists.newArrayList();
            }

            QueryWrapper<ActivityParticipationDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(ActivityParticipationDO::getActivityId,query.getActivityId());
            List<ActivityParticipationDO> activityParticipationList = activityParticipationDAO.list(queryWrapper);

            if(CollectionUtil.isEmpty(activityParticipationList)){
                return Lists.newArrayList();
            }

            return userProjectList.stream()
                    .filter(item -> activityParticipationList.stream().map(e -> e.getProjectId()).collect(Collectors.toList())
                            .contains(item.getProjectId())).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    @Override
    public Boolean getProjectPageByHfProjectId(String projectId) {
        if(StringUtil.isEmpty(projectId)){
            return false;
        }
        RMapCache<String, Boolean> rMap = redissonClient.getMapCache(RedisConstants.CACHE_JUMPABLE_PROJECT_INFO);
        //如果有缓存，则直接返回
        if(rMap.containsKey(projectId)){
            return rMap.get(projectId);
        }
        try{
            String result = HTTPClientUtils.get(huafaConstantConfig.ZYT_BASE_URL+MkPermitApiConstant.GET_PROJECT_PAGE_BY_HF_PROJECT_ID + projectId);
            log.info("项目详情查询结果:{}",result);
            JSONObject response = JSONUtil.parseObj(result);
            int code = response.getInt("code");
            boolean b = code == 0;
            rMap.put(projectId,b,2,TimeUnit.HOURS);
            return b;
        }catch(Exception e){
            log.error("获取项目详情异常",e);
        }
        return false;
    }

    @Override
    public Integer getProjectPageId(String projectId) {
        if(StringUtil.isEmpty(projectId)){
            return null;
        }
        try{
            String result = HTTPClientUtils.get(huafaConstantConfig.ZYT_BASE_URL+MkPermitApiConstant.GET_PROJECT_PAGE_BY_HF_PROJECT_ID + projectId);
            log.info("信息推送项目详情查询结果:{}",result);
            JSONObject response = JSONUtil.parseObj(result);
            JSONObject data = response.getJSONObject("data");
            return data.getInt("pageId");
        }catch(Exception e){
            log.error("信息推送获取项目详情异常",e);
        }
        return null;
    }


    @Override
    public List<UserProjectResponseDTO> getUserArea(UserProjectQuery query) {
        try{
            Map<String,Object> map = new HashMap<>(2);
            map.put("userId",query.getUserId());
            map.put("realAreaName",query.getRealAreaName());

            String result = huaFaHmacAuthUtil.request(0,1,MkPermitApiConstant.GET_USER_AREA_URL,map);
            log.info("项目用户区域查询结果:{}",result);
            JSONObject response = JSONUtil.parseObj(result);
            String code = response.getStr("code");
            if(CommonExceptionCode.SUCCESS.equals(code)){
                List<UserProjectResponseDTO> data = JSONArray.parseArray(response.getStr("data"), UserProjectResponseDTO.class);
                if(CollectionUtil.isNotEmpty(data)){
                    data.stream().forEach(item ->{
                        item.setRealAreaId(item.getAreaId());
                        item.setRealAreaName(item.getAreaName());
                        item.setAreaId(null);
                        item.setAreaName(null);
                    });
                }
                return data;
            }
        }catch(Exception e){
            log.error("获取项目用户区域异常",e);
        }
        return Lists.newArrayList();
    }

    @Override
    public List<UserProjectResponseDTO> getUserRealCityByAreaId(UserProjectQuery query) {
        try{
            Map<String,Object> map = new HashMap<>(2);
            map.put("userId",query.getUserId());
            //map.put("realAreaId",query.getRealAreaId());
            map.put("areaId",query.getRealAreaId());
            map.put("realCityName",query.getRealCityName());

            String result = huaFaHmacAuthUtil.request(0,1,MkPermitApiConstant.GET_USER_REAL_CITY_BY_AREA_ID_URL,map);
            log.info("项目用户城市查询结果:{}",result);
            JSONObject response = JSONUtil.parseObj(result);
            String code = response.getStr("code");
            if(CommonExceptionCode.SUCCESS.equals(code)){
                List<UserProjectResponseDTO> data = JSONArray.parseArray(response.getStr("data"), UserProjectResponseDTO.class);
                if(CollectionUtil.isNotEmpty(data)){
                    data.stream().forEach(item ->{
                        item.setRealCityId(item.getCityId());
                        item.setRealCityName(item.getCityName());
                        item.setCityId(null);
                        item.setCityName(null);
                    });
                }
                return data;
            }
        }catch(Exception e){
            log.error("获取项目用户城市异常",e);
        }
        return Lists.newArrayList();
    }

    @Override
    public List<UserProjectResponseDTO> saveToRedis(String userId) {
        try{
            UserProjectQuery query = new UserProjectQuery();
            query.setPageSize(999999);
            query.setUserId(userId);
            query.setPageNumber(1);
            PageBean<UserProjectResponseDTO> userProjectNoLogin = this.getUserProjectNoLogin(query);
            List<UserProjectResponseDTO> content = userProjectNoLogin.getContent();
            if(CollectionUtil.isNotEmpty(content)){
                //缓存用户项目信息
                RBucket bucket = redissonClient.getBucket(String.format(RedisConstants.CACHE_PREV_KEY_USER_PROJECT_INFO,userId));
                bucket.getAndSet(JSON.toJSONString(content));
                bucket.expire(30, TimeUnit.MINUTES);
                return content;
            }
        }catch (Exception e){
            log.error("用户项项目信息缓存异常userid:{}",userId);
        }
        return Lists.newArrayList();
    }


    private List<UserProjectResponseDTO> getUserAreaV2(String userId, String positionId) {
        Map<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        params.put("positionId", positionId);
        Data<List<UserProjectResponseDTO>> resultData = gatewayService.getPermitServiceClient().doGet("/api/v2/getUserArea", params, new TypeReference<Data<List<UserProjectResponseDTO>>>() {
        });
        return resultData.getdata();
    }
    private List<UserProjectResponseDTO> getUserCityByAreaIdV2(String userId, String positionId) {
        Map<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        params.put("positionId", positionId);
        Data<List<UserProjectResponseDTO>> resultData = gatewayService.getPermitServiceClient().doGet("/api/v2/getUserCityByAreaId", params, new TypeReference<Data<List<UserProjectResponseDTO>>>() {
        });
        return resultData.getdata();
    }
    private List<UserProjectResponseDTO> getUserProjectV2(String userId, String positionId) {
        Map<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        params.put("positionId", positionId);
        Data<List<UserProjectResponseDTO>> resultData = gatewayService.getPermitServiceClient().doPost("/api/user/permission/v2/getUserProject", JSONUtil.toJsonStr(params), new TypeReference<Data<List<UserProjectResponseDTO>>>() {
        });
        return resultData.getdata();
    }

    public void initCommunityDataPermission(PromotionActivityPageQuery query) {
        Assert.notBlank(HuafaRuntimeEnv.getPositionId(),"岗位不能为空");
        Assert.notBlank(HuafaRuntimeEnv.getUserId(),"用户Id不能为空");
        CommunityPositionEnum communityPositionEnum = CommunityPositionEnum.getByPositionId(HuafaRuntimeEnv.getPositionId());
        query.setLineType(communityPositionEnum.getLineType());
        query.setAreaIdList(null);
        query.setCityIdList(null);
        query.setProjectIdList(null);
        //根据岗位设置区域，城市，项目
        if (communityPositionEnum.getPermissionType() == 1) {
            query.setAreaIdList(getUserAreaV2(HuafaRuntimeEnv.getUserId(), HuafaRuntimeEnv.getPositionId()).stream().map(UserProjectResponseDTO::getAreaId).collect(Collectors.toList()));
            Assert.notEmpty(query.getAreaIdList(),"用户未分配区域权限");
        } else if (communityPositionEnum.getPermissionType() == 2) {
            query.setCityIdList(getUserCityByAreaIdV2(HuafaRuntimeEnv.getUserId(), HuafaRuntimeEnv.getPositionId()).stream().map(UserProjectResponseDTO::getCityId).collect(Collectors.toList()));
            Assert.notEmpty(query.getCityIdList(),"用户未分配城市权限");
        } else if (communityPositionEnum.getPermissionType() == 3) {
            UserProjectQuery dto = new UserProjectQuery();
            dto.setPageNumber(1);
            dto.setPageSize(999999);
            dto.setUserId(HuafaRuntimeEnv.getUserId());
            query.setProjectIdList(getUserProjectNoLoginV2(dto).getContent().stream().map(UserProjectResponseDTO::getProjectId).collect(Collectors.toList()));
            Assert.notEmpty(query.getProjectIdList(),"用户未分配项目权限");
        }
    }
}
