package com.deepexi.dxp.marketing.domain.marketing.vo;


import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 主动营销任务目的指标值VO
 * @Author: HuangBo.
 * @Date: 2020/3/10 16:50
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MarketingActivePurposeValVO extends SuperVO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 任务指标Kpi配置项编码
     */
    @ApiModelProperty(value = "任务指标Kpi配置项编码")
    @NotNull(message = "任务指标Kpi配置项编码")
    private String code;

    /**
     * 任务指标类型(1:增加；2:减少)
     */
    @ApiModelProperty(value = "任务指标类型(1:营收；2:商品；3:用户)")
    @NotNull(message = "任务目的指标类型不能为空")
    private int type;

    /**
     * 任务指标Kpi配置项ID
     */
    @ApiModelProperty(value = "任务指标Kpi配置项ID")
    @NotNull(message = "任务指标Kpi配置项ID")
    private Long kpiItemId;

    /**
     * 任务指标Kpi配置项父ID
     */
    @ApiModelProperty(value = "任务指标Kpi配置项父ID")
    private Long kpiItemPid;
    /**
     * 任务指标名称
     */
    @ApiModelProperty(value = "任务目的指标名称")
    @NotNull(message = "任务目的指标名称不能为空")
    private String name;

    /**
     * 任务指标值
     */
    @ApiModelProperty(value = "任务目的指标指")
    private String value;

    /**
     * 任务指标单位名称
     */
    @ApiModelProperty(value = "任务指标单位名称")
    private String unitName;

    /**
     * 主动营销任务
     */
    private Long activeId;

    /**
     * 主动营销任务映射ID
     */
    private Long taskMappingId;

    /**
     * 主动营销任务目的
     */
    private Long purposeId;

    /**
     * 是否核心指标
     */
    private boolean coreKpi;


}
