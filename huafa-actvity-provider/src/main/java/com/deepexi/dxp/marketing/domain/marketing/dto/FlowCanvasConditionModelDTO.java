package com.deepexi.dxp.marketing.domain.marketing.dto;

import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import lombok.Data;

/**
 * 流程画布-条件节点—模型配置
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-07 16:03
 */
@Data
public class FlowCanvasConditionModelDTO extends SuperDTO {

    /**
     * 画布ID
     */
    private Long flowId;

    /**
     * 画布节点ID
     */
    private String nodeId;

    /**
     * 判断条件名称
     */
    private String name;

    /**
     * 模型ID
     */
    private Long modelId;

    /**
     * 计算符号
     */
    private String symbols;

    /**
     * 扩展计算符号(前/区间/后)
     */
    private String symbolsExt;

    /**
     * 比对值
     */
    private String value;

    /**
     * 对比值-结束
     */
    private String valueEnd;
}
