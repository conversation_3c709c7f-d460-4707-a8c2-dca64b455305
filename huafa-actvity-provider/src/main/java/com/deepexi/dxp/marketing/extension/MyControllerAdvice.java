package com.deepexi.dxp.marketing.extension;

import com.deepexi.dxp.marketing.common.base.utils.CommonExceptionCode;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.ResponseBean;
import com.deepexi.util.exception.ApplicationException;
import feign.codec.DecodeException;
import lombok.extern.log4j.Log4j2;
import org.apache.http.auth.AuthenticationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.UnexpectedTypeException;
import java.util.Iterator;

/**
 * Created by donh on 2018/11/6.
 * 全局异常统一处理
 */
@Log4j2
@RestControllerAdvice
public class MyControllerAdvice {

    /**
     * 全局异常捕捉处理
     *
     * @param ex
     * @return
     */
    @ExceptionHandler(value = Exception.class)
    @ResponseStatus(code = HttpStatus.OK)
    public Data<String> errorHandler(Exception ex) {
        log.error("Exception异常：" + ex.getMessage(), ex);
        return new Data<>(null, "500", ex.getMessage());
    }

    /**
     * 拦截捕捉自定义异常 MyException.class
     *
     * @param ex
     * @return
     */
    @ExceptionHandler(value = ApplicationException.class)
    @ResponseStatus(code = HttpStatus.OK)
    public Data<String> myErrorHandler(ApplicationException ex) {
        log.error("applicationException异常：" + ex.getMessage(), ex);
        return new Data<>(null, ex.getCode(), ex.getMessage());
    }

    @ExceptionHandler(value = IllegalArgumentException.class)
    @ResponseStatus(code = HttpStatus.OK)
    public Data<String> myErrorHandler(IllegalArgumentException ex) {
        log.error("applicationException异常：" + ex.getMessage(), ex);
        return new Data<>(null, CommonExceptionCode.INVALIDATION, ex.getMessage());
    }

    /**
     * 拦截捕获 @RequestBody 参数校验异常
     *
     * @param ex
     * @return
     */
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    @ResponseStatus(code = HttpStatus.OK)
    public Data<String> validExceptionHandler(MethodArgumentNotValidException ex) {
        String message = ex.getBindingResult().getAllErrors().get(0).getDefaultMessage();
        return new Data<>(null, "400", message);
    }

    /**
     * 拦截捕获数据绑定时异常
     *
     * @param ex
     * @return
     */
    @ExceptionHandler(value = BindException.class)
    @ResponseStatus(code = HttpStatus.OK)
    public Data<String> validExceptionHandler(BindException ex) {
        String message = ex.getBindingResult().getFieldError().getDefaultMessage();
        return new Data<String>(null, "400", message);
    }

    /**
     * 拦截捕获 @RequestParam 参数校验异常
     *
     * @param ex
     * @return
     */
    @ExceptionHandler(value = ConstraintViolationException.class)
    @ResponseStatus(code = HttpStatus.OK)
    public Data<String> validExceptionHandler(ConstraintViolationException ex) {
        Iterator<ConstraintViolation<?>> it = ex.getConstraintViolations().iterator();
        String message = "";
        if (it.hasNext()) {
            message = it.next().getMessageTemplate();
        }
        return new Data<>(null, "400", message);
    }

    /**
     * 拦截捕获 @RequestBody required=true 绑定请求参数异常
     *
     * @param ex
     * @return
     */
    @ExceptionHandler(value = HttpMessageNotReadableException.class)
    @ResponseStatus(code = HttpStatus.OK)
    public Data<String> validExceptionHandler(HttpMessageNotReadableException ex) {
        return new Data<String>(null, "400", "没有请求体");
    }

    /**
     * 拦截捕获绑定请求参数异常
     *
     * @param ex
     * @return
     */
    @ExceptionHandler(value = UnexpectedTypeException.class)
    @ResponseStatus(code = HttpStatus.OK)
    public Data<String> validExceptionHandler(UnexpectedTypeException ex) {
        return new Data<>(null, "400", "参数类型不对");
    }

    /**
     * 认证异常处理
     * @param ex
     * @return
     */
    @ExceptionHandler(value = AuthenticationException.class)
    @ResponseStatus(code = HttpStatus.OK)
    public ResponseBean authenticationErrorHandler(Exception ex) {
        return ResponseBean.error("签名校验失败");
    }

    @ExceptionHandler(value = DecodeException.class)
    @ResponseStatus(code = HttpStatus.OK)
    public Data<String> decodeExceptionHandler(DecodeException ex) {
        log.error("报异常：" + ex.getMessage(), ex);
        return new Data<>(null, "500", ex.getMessage());
    }

    @ExceptionHandler(value = HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(code = HttpStatus.OK)
    public Data<String> httpRequestMethodNotSupportedExceptionHandler(DecodeException ex) {
        log.error("报异常：" + ex.getMessage(), ex);
        return new Data<>(null, "500", ex.getMessage());
    }

}