package com.deepexi.dxp.marketing.domain.marketing.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 活动优惠券Query
 *
 * <AUTHOR>
 * @Date 2020/3/19
 */
@Data
@ApiModel
public class PromotionActivityCouponQuery implements Serializable {

    @ApiModelProperty(value = "租户ID", required = true)
    private String tenantId;

    @ApiModelProperty(value = "活动ID")
    private Long activityId;

}
