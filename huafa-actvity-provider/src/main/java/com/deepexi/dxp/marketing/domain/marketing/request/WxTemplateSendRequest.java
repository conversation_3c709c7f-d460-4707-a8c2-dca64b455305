package com.deepexi.dxp.marketing.domain.marketing.request;

import com.deepexi.dxp.marketing.common.base.request.SuperRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description：微信模板发送消息请求对象
 * @author：<PERSON><PERSON><PERSON><PERSON>
 * @version：1.0.0
 * @date：2021-03-31 2:15 下午
 */
@Data
@ApiModel
public class WxTemplateSendRequest extends SuperRequest {

    /**
     * 接收者openid
     */
    @ApiModelProperty("接收者openid")
    private String touser;

    /**
     * 模板ID
     */
    @ApiModelProperty("接收者openid")
    private String template_id;

    /**
     * 模板跳转链接,非必须
     */
    @ApiModelProperty("接收者openid")
    private String url;

    /**
     * 模板数据
     */
    @ApiModelProperty("模板数据")
    private List<WxTemplateSendDataRequest> data;

    /**
     * 微信appId
     */
    @ApiModelProperty("微信appId")
    private String wxAppId;


}
