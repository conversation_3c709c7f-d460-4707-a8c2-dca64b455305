package com.deepexi.dxp.marketing.domain.marketing.dto;

import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import lombok.Data;


/**
 * 流程画布-圈人配置-模型
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-07 16:03
 */
@Data
public class FlowCanvasTargetUserModelDTO extends SuperDTO {

    /**
     * 流程画布圈选用户配置表ID
     */
    private Long targetUserId;

    /**
     * 模型筛选方式 1：按排名
     */
    private Integer modelType;

    /**
     * 模型ID
     */
    private Long modelId;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 模型精选人数
     */
    private Integer memberNums;

    /**
     * 操作 1:top；2:bottom
     */
    private String operationType;
}
