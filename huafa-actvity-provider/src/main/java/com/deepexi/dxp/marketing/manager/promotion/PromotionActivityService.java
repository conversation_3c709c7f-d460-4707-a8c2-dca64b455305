//package com.deepexi.dxp.marketing.manager.promotion;
//
//import com.deepexi.dxp.marketing.domain.marketing.vo.CouponActivityListVO;
//import com.deepexi.dxp.marketing.domain.promotion.dto.PromotionActivityDetailDTO;
//import com.deepexi.dxp.marketing.domain.promotion.dto.activity.PromotionActivityListPostVO;
//import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityQuery;
//import com.deepexi.dxp.marketing.domain.promotion.request.PromotionActivityCreateRequest;
//import com.deepexi.dxp.marketing.domain.promotion.request.PromotionActivityStatusUpdateRequest;
//import com.deepexi.dxp.marketing.domain.promotion.request.PromotionActivityUpdateRequest;
//import com.deepexi.dxp.middle.marketing.domain.query.CouponActivityQuery;
//import com.deepexi.util.pageHelper.PageBean;
//
//import java.util.List;
//
///**
// * 活动管理
// *
// * <AUTHOR>
// * @version 1.0
// * @date 2020-08-21 14:39
// */
//public interface PromotionActivityService {
//    /**
//     * 分页查询
//     *
//     * @param dto dto
//     * @return List
//     */
//    PageBean<PromotionActivityListPostVO> findPage(PromotionActivityQuery dto);
//
//    /**
//     * 创建促销活动
//     *
//     * @param dto 入参dto 需要转换成po
//     * @return 添加是否成功
//     */
//    Long create(PromotionActivityCreateRequest dto);
//
//    /**
//     * 根据id修改促销活动
//     *
//     * @param id  活动id
//     * @param dto 活动dto
//     * @return 是非修改成功
//     */
//    Boolean updateActivityById(Long id, PromotionActivityUpdateRequest dto);
//
//    /**
//     * 删除活动
//     *
//     * @param ids deletedList
//     * @return 是非删除成功
//     */
//    Boolean delete(List<Long> ids);
//
//    /**
//     * @param id 活动id
//     * @return 改活动的具体信息
//     */
//    PromotionActivityDetailDTO getActivityById(Long id);
//
//    /**
//     * 修改活动状态
//     *
//     * @param dto 入参
//     * @return 出错
//     */
//    Long updateActivityStatus(PromotionActivityStatusUpdateRequest dto);
//
//}
