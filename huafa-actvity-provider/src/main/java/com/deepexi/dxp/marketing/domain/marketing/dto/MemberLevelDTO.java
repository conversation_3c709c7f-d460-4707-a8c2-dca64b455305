package com.deepexi.dxp.marketing.domain.marketing.dto;

import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 会员等级
 * <AUTHOR>
 * @version 1.0
 * @date 2020-11-10 16:18
 */
@Data
public class MemberLevelDTO extends SuperDTO {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID")
    private String tenantId;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createdTime;
    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updatedTime;
    /**
     * 应用ID
     */
    @ApiModelProperty(value = "应用ID")
    private Long appId;
    /**
     * 等级组ID
     */
    @ApiModelProperty(value = "等级组ID")
    private Long levelGroupId;
    /**
     * 等级名称
     */
    @ApiModelProperty(value = "等级名称")
    private String levelName;
    /**
     * 等级编码
     */
    @ApiModelProperty(value = "等级编码")
    private String levelCode;
    /**
     * 等级描述
     */
    @ApiModelProperty(value = "等级描述")
    private String description;
    /**
     * 图片
     */
    @ApiModelProperty(value = "图片")
    private String iconUrl;
    /**
     * 有效类型
     */
    @ApiModelProperty(value = "有效类型")
    private Integer effectType;
    /**
     * 有效期
     */
    @ApiModelProperty(value = "有效期")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date effectTime;

    /**
     * sort排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 表达式
     */
    @ApiModelProperty(value = "表达式")
    private String express;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private Integer type;
}
