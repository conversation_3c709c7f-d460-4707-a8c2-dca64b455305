package com.deepexi.dxp.marketing.controller.specify.middleapi;

import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityTargetQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityTargetRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityTargetResponseDTO;
import com.deepexi.dxp.marketing.service.specify.ActivityTargetService;
import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/middle-api/v1/activity-target")
@Slf4j
@Api(value = "middle-目标管理接口", tags = "middle-目标管理接口")
public class MiddleActivityTargetApiController {

	@Autowired
	private ActivityTargetService activityTargetService;

	@PostMapping("/save")
	public Data<Boolean> save(ActivityTargetRequestDTO dto) {

		Boolean result = activityTargetService.create(dto);

		return new Data<>(result);
	}

	@PostMapping("/getPage")
	public Data<PageBean<ActivityTargetResponseDTO>> getPage(@RequestBody ActivityTargetQuery query) {
		return new Data<>(activityTargetService.getPage(query));
	}
}
