package com.deepexi.dxp.marketing.service.specify;

import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityAnalysisQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityTrendsQuery;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityAnalysisDetailResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityAnalysisResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityTrendResponseDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.IndexPathResponseDTO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.util.pageHelper.PageBean;

import java.util.List;

public interface ActivityAnalysisService {

    /**
     * 活动分析列表
     * @param query
     * @return
     */
    PageBean<ActivityAnalysisResponseDTO> analysisList(ActivityAnalysisQuery query) throws Exception;

    /**
     * 活动分析详情
     * @param query
     * @return
     */
    ActivityAnalysisDetailResponseDTO analysisDetail(ActivityAnalysisQuery query);

    /**
     * 获取活动期间成交和到访数据
     * @return
     */
    ActivityAnalysisDetailResponseDTO getAccessAndDealInfoByActivityId(ActivityAnalysisQuery query);

    /**
     * 获取活动参与人数
     * @param activityId
     * @return
     */
    Long getActPartTotalNumber(Long activityId);



    /**
     * 活动趋势查询
     * @param query
     * @return
     */
    List<ActivityTrendResponseDTO> findActivityTrends(ActivityTrendsQuery query);

    /**
     * 指标概览自定义查询
     * @param query
     * @return
     */
    List<IndexPathResponseDTO> findOverviewOfIndicators(ActivityAnalysisQuery query);

    /**
     * 海报分享统计
     * @param id
     * @return
     */
    PageBean<ActivityAnalysisResponseDTO> posterChannelStatistics(ActivityAnalysisQuery query);
}
