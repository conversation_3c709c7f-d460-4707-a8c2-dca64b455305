package com.deepexi.dxp.marketing.manager.promotion.impl.limit;

import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityCommodityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityConfigDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.TemplateLimitDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityParamsDTO;
import com.deepexi.dxp.marketing.enums.activity.limit.PATemplateShopEnum;
import com.deepexi.dxp.marketing.manager.promotion.impl.BaseLimit;
import com.deepexi.dxp.middle.promotion.util.CollectionsUtil;
import com.deepexi.util.CollectionUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> xinjian.yao
 * @date 2020/3/9 17:40
 */
public class CalculateShopLimit extends BaseLimit {

    private List<ShopLimitEnumsCalculate> calculateHelper = new ArrayList<>();

    public CalculateShopLimit(TemplateLimitDTO templateLimitDTO, ActivityConfigDTO activity, ActivityParamsDTO params) {
        super(templateLimitDTO, activity, params);
    }

    @Override
    public Boolean calculate() {
        // 活动存储的值
        List<BaseActivityDTO> activityTenantLimit = super.getActivity().getShopLimit();
        // 需求比较的值
        ActivityParamsDTO params = super.getParams();

        // 初始化计算信息
        init();
        return calculateshopLimit(activityTenantLimit, params);
    }

    private Boolean calculateshopLimit(List<BaseActivityDTO> activityTenantLimit, ActivityParamsDTO params) {
        return shopCalculate(activityTenantLimit, params);
    }

    /**
     * @param baseActivityDTO 渠道限制类型的活动信息
     * @param params          当前渠道获得的值
     * @return 计算的入口
     */
    private Boolean shopCalculate(List<BaseActivityDTO> baseActivityDTO, ActivityParamsDTO params) {

        for (ShopLimitEnumsCalculate shopLimitEnumsCalculate : calculateHelper) {
            Boolean result = shopLimitEnumsCalculate.calculate(baseActivityDTO, params);
            boolean existFlag = Optional.ofNullable(result)
                    .isPresent();
            if (existFlag) {
                return result;
            }
        }
        return Boolean.FALSE;
    }

    private interface ShopLimitEnumsCalculate {
        /**
         * 计算接口定义
         *
         * @param baseActivityDTO 设计活动时 配置存储h5商城的信息
         * @param params          传过来的参数
         * @return 不同类型是非成功
         */
        Boolean calculate(List<BaseActivityDTO> baseActivityDTO, ActivityParamsDTO params);
    }

    /**
     * 枚举类每添加一种类型，都需要再这里初始化这张类型的处理结果，不然活动选择那种类型 会报错
     */
    private void init() {
        calculateHelper.add(shopEnum());
        calculateHelper.add(allShopEnum());
    }

    private ShopLimitEnumsCalculate shopEnum() {
        return (baseActivity, params) -> {
            List<BaseActivityDTO> channel = baseActivity.stream()
                    .filter(val -> PATemplateShopEnum.CHANNEL.getId().equals(val.getId()))
                    .collect(Collectors.toList());

            if (CollectionUtil.isEmpty(channel)) {
                return null;
            }
            List<String> propertyList = CollectionsUtil.getPropertyList(channel, BaseActivityDTO::getValue);
            ActivityCommodityDTO commoditie = params.getCommoditie();
            String oneInList = CollectionsUtil.findOneInList(propertyList, val -> val.equals(String.valueOf(commoditie.getShopId())));
            return Objects.nonNull(oneInList);
        };
    }

    private ShopLimitEnumsCalculate allShopEnum() {
        return (baseActivity, params) -> {
            BaseActivityDTO isExitAllShop = CollectionsUtil.findOneInList(baseActivity, val -> PATemplateShopEnum.ALL_SHOP.getId().equals(val.getId()));

            return Objects.nonNull(isExitAllShop) ? Boolean.TRUE : null;
        };
    }

}
