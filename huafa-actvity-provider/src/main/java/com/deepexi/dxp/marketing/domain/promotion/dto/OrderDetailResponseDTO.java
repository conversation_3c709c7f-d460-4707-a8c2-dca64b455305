package com.deepexi.dxp.marketing.domain.promotion.dto;

import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityOrderDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityVerifyDO;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2024/12/16 16:10
 */
@Data
public class OrderDetailResponseDTO {
    private ActivityVerifyDO verifyDO;
    private ActivityOrderDO orderDO;
    private PromotionActivityDO activityDO;
    private ActivityOrderDO refundOrder;

    public OrderDetailResponseDTO(ActivityVerifyDO verifyDO, ActivityOrderDO orderDO, ActivityOrderDO refundOrder, PromotionActivityDO activityDO) {
        this.verifyDO = verifyDO;
        this.orderDO = orderDO;
        this.refundOrder = refundOrder;
        this.activityDO = activityDO;
    }
}
