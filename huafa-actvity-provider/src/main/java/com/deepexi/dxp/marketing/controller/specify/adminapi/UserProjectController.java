package com.deepexi.dxp.marketing.controller.specify.adminapi;

import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.UserProjectQuery;
import com.deepexi.dxp.marketing.domain.marketing.response.UserProjectResponseDTO;
import com.deepexi.dxp.marketing.service.specify.UserProjectService;
import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 手机号码登录注册controller
 *
 * <AUTHOR>
 * @date  2019/10/24 14:54
 * @see [相关类/方法]
 * @since 1.0
 */
@RestController
@RequestMapping("/admin-api/v1/project")
@Api(value = "用户项目模块-",tags = {"用户项目模块"})
public class UserProjectController {

    @Resource
    private UserProjectService userProjectService;
    /**
     * 用户项目列表
     * phone参数是AES加密串
     * @param query
     */
    @ApiOperation("用户项目列表")
    @PostMapping("pageList")
    public Data<PageBean<UserProjectResponseDTO>> pageList(@RequestBody @Valid UserProjectQuery query) {
        return new Data<>(userProjectService.getUserProjectNoLogin(query));
    }

    @ApiOperation("用户可选项目列表")
    @PostMapping("canCheckList")
    public Data<List<UserProjectResponseDTO>> canCheckList(@RequestBody @Valid UserProjectQuery query) {
        return new Data<>(userProjectService.getCanCheckList(query));
    }

    /**
     * 用户区域
     * phone参数是AES加密串
     * @param query
     */
    @ApiOperation("用户区域")
    @PostMapping("getUserArea")
    public Data<List<UserProjectResponseDTO>> getUserArea(@RequestBody @Valid UserProjectQuery query) {
        return new Data<>(userProjectService.getUserArea(query));
    }

    /**
     * 用户城市
     * phone参数是AES加密串
     * @param query
     */
    @ApiOperation("用户城市")
    @PostMapping("getUserRealCityByAreaId")
    public Data<List<UserProjectResponseDTO>> getUserRealCityByAreaId(@RequestBody @Valid UserProjectQuery query) {
        return new Data<>(userProjectService.getUserRealCityByAreaId(query));
    }
}
