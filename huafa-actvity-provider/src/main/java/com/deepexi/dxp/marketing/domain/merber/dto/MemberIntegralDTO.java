package com.deepexi.dxp.marketing.domain.merber.dto;

import com.deepexi.util.pojo.AbstractObject;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Class: MemberIntegralDTO
 * @Description:
 * @Author: zht
 * @Date: 2020/6/9
 */
@Data
public class MemberIntegralDTO extends AbstractObject {
    /**
     * 积分体系id
     */
    private Long virtualCurrencyId;

    /**
     * 虚拟币名称
     */
    private String virtualCurrencyName;

    /**
     * 虚拟币代码
     */
    private String virtualCurrencyCode;

    /**
     * 发放积分
     */
    private BigDecimal totalAmt;

    /**
     * 可用积分
     */
    private BigDecimal balanceAmt;

    /**
     * 已用积分
     */
    private BigDecimal usedAmt;

    /**
     * 失效积分
     */
    private BigDecimal wasteAmt;

    /**
     * 冻结积分
     */
    private BigDecimal frozenAmt;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 应用id
     */
    private Long appId;

    /**
     * 积分账号
     */
    private String accountCode;

}
