//package com.deepexi.dxp.marketing.engine.processor;
//
//import com.deepexi.dxp.marketing.engine.MarketingTaskCallback;
//import com.deepexi.dxp.marketing.engine.event.MarketingTaskEvent;
//import com.deepexi.dxp.marketing.engine.listener.MarketingTaskListener;
//import com.deepexi.dxp.marketing.engine.task.MetaDataTask;
//import com.deepexi.dxp.marketing.enums.marketing.MarketingTaskActiveStatusEnum;
//import com.deepexi.dxp.marketing.service.marketing.MarketingActiveService;
//import com.deepexi.dxp.middle.marketing.domain.dto.MarketingActiveDTO;
//import lombok.extern.slf4j.Slf4j;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
///**
// * 营销任务暂停处理事件源
// *
// * <AUTHOR>
// * @date 2020/3/18 18:54
// */
//@Slf4j
//public class MarketingTaskSuspendProcessor extends AbstractTaskProcessor {
//
//    private final Logger logger = LoggerFactory.getLogger(MarketingTaskSuspendProcessor.class);
//
//    public MarketingTaskSuspendProcessor(MetaDataTask metaDataTask) {
//        super(metaDataTask);
//    }
//
//    public MarketingTaskSuspendProcessor(MetaDataTask metaDataTask, MarketingTaskCallback callback) {
//        super(metaDataTask, callback);
//    }
//
//    @Override
//    protected void doProcess(MarketingTaskListener listener, MarketingTaskEvent event) {
//        try {
//            log.info("task-suspend-start-营销任务暂停执行开始");
//            listener.suspend(event);
//        } finally {
//            event.semaphoreDown();
//            log.info("task-suspend-start-营销任务暂停执行结束");
//        }
//    }
//
//    @Override
//    public void callBack() {
//        logger.info("监听器已执行完暂停动作,即将回调处理任务.");
//        // 更新本任务状态，改为暂停状态
//        MarketingActiveDTO activeDTO = (MarketingActiveDTO) this.getMetaDataTask().getMarketingTaskDTO();
//        activeDTO.setStatus(MarketingTaskActiveStatusEnum.SUSPEND.getValue());
//        ((MarketingActiveService) this.getMetaDataTask().getTaskService()).updateStatus(activeDTO);
//        logger.info("监听器已执行完暂停动作,回调将任务状态改为暂停成功.");
//    }
//}
