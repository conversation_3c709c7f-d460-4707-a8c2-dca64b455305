package com.deepexi.dxp.marketing.converter;

import com.alibaba.fastjson.JSON;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityExtVO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.EnrollmentInfoVO;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 活动扩展字段
 *
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date 2021/05/24 14:38
 */
public class ActivityExtConverter {

    private ActivityExtConverter() {
    }

    /**
     * @param ext 扩展字段
     * @return 活动扩展VO
     */
    public static ActivityExtVO converter(Map<String,Object> ext) {
        if(ext ==  null){
            return null;
        }
        //json示例：{"publishTime":"2021-05-30 02:21:19","publishType":1,"resourcesAttribute":1,"isPopular":1,"activityGoal":1,"kpiPath":11,"participants":1}
        ActivityExtVO activityExtVO = new ActivityExtVO();
        Integer activityGoal = Optional.ofNullable(ext.get("activityGoal")).map(c->Integer.parseInt(c.toString())).orElse(null);
        activityExtVO.setActivityGoal(activityGoal);
        Integer activityType = Optional.ofNullable(ext.get("activityType")).map(c->Integer.parseInt(c.toString())).orElse(null);
        activityExtVO.setActivityType(activityType);
        Integer isPopular = Optional.ofNullable(ext.get("isPopular")).map(c->Integer.parseInt(c.toString())).orElse(null);
        activityExtVO.setIsPopular(isPopular);
        Integer kpiPath = Optional.ofNullable(ext.get("kpiPath")).map(c->Integer.parseInt(c.toString())).orElse(null);
        activityExtVO.setKpiPath(kpiPath);
        String orgId = Optional.ofNullable(ext.get("orgId")).map(c->c.toString()).orElse(null);
        activityExtVO.setOrgId(orgId);
        Integer participants = Optional.ofNullable(ext.get("participants")).map(c->Integer.parseInt(c.toString())).orElse(null);
        activityExtVO.setParticipants(participants);
        String payNo = Optional.ofNullable(ext.get("payNo")).map(c->c.toString()).orElse(null);
        activityExtVO.setPayNo(payNo);
//        String tempPublishTime = Optional.ofNullable(ext.get("publishTime")).map(c->c.toString()).orElse(null);
//        if(StringUtils.isNotBlank(tempPublishTime)){
//            Date publishTime = DateUtils.getDate(tempPublishTime,"yyyy-MM-dd HH:mm:ss");
//            activityExtVO.setPublishTime(publishTime);
//        }
        Integer publishType = Optional.ofNullable(ext.get("publishType")).map(c->Integer.parseInt(c.toString())).orElse(null);
        activityExtVO.setPublishType(publishType);
        Integer resourcesAttribute = Optional.ofNullable(ext.get("resourcesAttribute")).map(c->Integer.parseInt(c.toString())).orElse(null);
        activityExtVO.setResourcesAttribute(resourcesAttribute);

        Integer submitFormAward = Optional.ofNullable(ext.get("submitFormAward")).map(c->Integer.parseInt(c.toString())).orElse(null);
        activityExtVO.setSubmitFormAward(submitFormAward);

        String feedbackInfo = Optional.ofNullable(ext.get("feedbackInfo")).map(JSON::toJSONString).orElse(null);
        if(Objects.nonNull(feedbackInfo)){
            activityExtVO.setFeedbackInfo(JSON.parseArray(feedbackInfo, EnrollmentInfoVO.class));
        }

        Integer projectType = Optional.ofNullable(ext.get("projectType")).map(c->Integer.parseInt(c.toString())).orElse(null);
        activityExtVO.setProjectType(projectType);

        return activityExtVO;

    }

}
