//package com.deepexi.dxp.marketing.service.promotion.impl;
//
//import com.alibaba.fastjson.JSON;
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.deepexi.dxp.marketing.config.PromotionProperties;
//import com.deepexi.dxp.marketing.domain.promotion.dto.activity.*;
//import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.*;
//import com.deepexi.dxp.marketing.domain.promotion.dto.coupon.PromotionCouponLoggerListPostResponseDTO;
//import com.deepexi.dxp.marketing.domain.promotion.request.activity.CommodityActivityRequest;
//import com.deepexi.dxp.marketing.domain.promotion.request.calculate.*;
//import com.deepexi.dxp.marketing.enums.activity.limit.PATemplateBaseEnum;
//import com.deepexi.dxp.marketing.enums.activity.limit.PATemplateCouponEnum;
//import com.deepexi.dxp.marketing.enums.activity.limit.PATemplateNumberEnum;
//import com.deepexi.dxp.marketing.enums.activity.result.NoActivityMsgEnum;
//import com.deepexi.dxp.marketing.enums.activity.strategy.StrategyGroupEnum;
//import com.deepexi.dxp.marketing.enums.activity.strategy.condition.CouponConditionEnum;
//import com.deepexi.dxp.marketing.enums.activity.strategy.condition.JFDHConditionEnum;
//import com.deepexi.dxp.marketing.enums.activity.strategy.condition.TimeConditionEnum;
//import com.deepexi.dxp.marketing.enums.activity.strategy.operation.TimeOperationEnum;
//import com.deepexi.dxp.marketing.enums.coupon.CouponReleaseTypeEnum;
//import com.deepexi.dxp.marketing.enums.coupon.GetCouponResponseEnum;
//import com.deepexi.dxp.marketing.enums.coupon.PromotionCouponLoggerStatusEnums;
//import com.deepexi.dxp.marketing.enums.coupon.PromotionCouponStatusEnums;
//import com.deepexi.dxp.marketing.enums.status.ActivityStatus;
//import com.deepexi.dxp.marketing.manager.promotion.ActivityCacheManager;
//import com.deepexi.dxp.marketing.manager.promotion.ActivityCalculateManager;
//import com.deepexi.dxp.marketing.manager.promotion.Context;
//import com.deepexi.dxp.marketing.manager.promotion.PromotionActivityManager;
//import com.deepexi.dxp.marketing.manager.promotion.impl.limit.CalculateUserLimit;
//import com.deepexi.dxp.middle.promotion.common.base.SuperEntity;
//import com.deepexi.dxp.middle.promotion.converter.ActivityConfigConverter;
//import com.deepexi.dxp.middle.promotion.converter.ActivityParamsConverter;
//import com.deepexi.dxp.middle.promotion.converter.ActivityResponseParamsConverter;
//import com.deepexi.dxp.middle.promotion.dao.PromotionActivityLoggerDAO;
//import com.deepexi.dxp.middle.promotion.dao.PromotionCouponLoggerDAO;
//import com.deepexi.dxp.middle.promotion.domain.entity.*;
//import com.deepexi.dxp.middle.promotion.mapper.*;
//import com.deepexi.dxp.middle.promotion.util.Arith;
//import com.deepexi.dxp.middle.promotion.util.CollectionsUtil;
//import com.deepexi.dxp.middle.promotion.util.DistributeIdUtil;
//import com.deepexi.dxp.middle.promotion.util.PageUtil;
//import com.deepexi.dxp.marketing.service.promotion.ActivityCalculateMiddleService;
//import com.deepexi.util.BeanPowerHelper;
//import com.deepexi.util.CollectionUtil;
//import com.deepexi.util.DateUtils;
//import com.deepexi.util.StringUtil;
//import com.deepexi.util.exception.ApplicationException;
//import com.deepexi.util.pageHelper.PageBean;
//import com.deepexi.util.pojo.CloneDirection;
//import com.google.common.collect.ImmutableList;
//import com.google.common.collect.Lists;
//import com.google.common.collect.Maps;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.math.BigDecimal;
//import java.util.*;
//import java.util.concurrent.atomic.AtomicInteger;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR> xinjian.yao
// * @date 2019/11/28 11:38
// */
//@Service
//@Slf4j
//public class ActivityCalculateMiddleServiceImpl implements ActivityCalculateMiddleService {
//
//    @Autowired
//    private PromotionActivityMapper promotionActivityMapper;
//    @Autowired
//    private PromotionStrategyMapper promotionStrategyMapper;
//    @Autowired
//    private PromotionActivityLimitMapper promotionActivityLimitMapper;
//    @Autowired
//    PromotionActivityLoggerDAO promotionActivityLoggerDao;
//    @Autowired
//    private PromotionActivityLoggerMapper promotionActivityLoggerMapper;
//    @Autowired
//    PromotionCouponLoggerDAO promotionCouponLoggerDao;
//    @Autowired
//    private PromotionCouponLoggerMapper promotionCouponLoggerMapper;
//    @Autowired
//    private PromotionCouponMapper promotionCouponMapper;
//
//    @Autowired
//    ActivityCalculateManager activityCalculateManager;
//    @Autowired
//    ActivityCacheManager activityCacheManager;
//    @Autowired
//    PromotionActivityManager promotionActivityManager;
//
//    private static final String ACTIVITY_ID = "activity_id";
//
//    @Autowired(required = false)
//    private PromotionProperties promotionProperties;
//
//
//    @Override
//    public ShoppingCartVO calculate(ActivityParamsRequest activityParamsRequest) {
//        // 返回的DTO
//        ShoppingCartVO resultDTO = new ShoppingCartVO();
//        // 商品没有可用活动的DTO
//        List<ActivityCommodityDTO> notActivityCommodityDTO = new ArrayList<>();
//        CommodityListActivityRequest filterActivity = new CommodityListActivityRequest();
//        filterActivity.setType(false);
//        filterActivity.setAppId(activityParamsRequest.getAppId());
//        filterActivity.setTenantId(activityParamsRequest.getTenantId());
//        // 获取所有的可用活动
//        List<PromotionActivityDO> promotionActivityDOList = getAllPromotionActivityList(filterActivity);
//        // 获取活动下的策略信息并以活动分组
//        Map<Long, List<PromotionStrategyDO>> activityStrategyMap = getAllPromotionStrategyMap(promotionActivityDOList);
//        // 获取活动下的限制信息以活动分组
//        Map<Long, List<PromotionActivityLimitDO>> activityLimitMap = getAllPromotionActivityLimitList(promotionActivityDOList);
//        // 获取活动下该用户下的日志信息以活动分组
//        Map<Long, List<PromotionActivityLoggerDO>> activityLoggerMap = getAllPromotionActivityLoggerList(activityParamsRequest.getUserId(),
//                activityParamsRequest.getUserType(),
//                promotionActivityDOList);
//        // 获取购物商品
//        List<ActivityCommodityDTO> commoditiesList = activityParamsRequest.getCommodities();
//        // 一个商品可以用多少个活动  K -> skuCode value -> List<PromotionActivityDO>
//        Map<Long, List<PromotionActivityDO>> ckuActivityMap = new HashMap<>();
//        // 缓存一个活动的配置类
//        Map<Long, ActivityConfigDTO> activityConfigCache = new HashMap<>();
//        // 遍历商品
//        for (ActivityCommodityDTO commodity : commoditiesList) {
//            List<PromotionActivityDO> activityFlagList = new ArrayList<>();
//            if (Objects.isNull(commodity.getActivityId()) && commodity.isNonuseActivity()) {
//                //没传活动id,且不给默认活动，直接跳过，下面算原价格(产品/交易组新需求，可以不使用活动，用原价)
//                notActivityCommodityDTO.add(commodity);
//                continue;
//            }
//            for (PromotionActivityDO promotionActivityDo : promotionActivityDOList) {
//                ActivityConfigDTO activity = getActivityConfig(promotionActivityDo, activityStrategyMap, activityLimitMap);
//                ActivityParamsDTO params = getActivityParams(commodity, activityParamsRequest, promotionActivityDo, activityLoggerMap);
//                activityConfigCache.put(promotionActivityDo.getId(), activity);
//                boolean result = activityCalculateManager.limitCalculate(activity, params, activityParamsRequest.getLimitList());
//                if (!result) {
//                    log.info("购物车计算--商品{} 在{}活动{} limit校验不通过", commodity.getUpId(), StrategyGroupEnum.getValueById(promotionActivityDo.getPaTemplateId().toString()), promotionActivityDo.getId());
//                    continue;
//                }
//                activityFlagList.add(promotionActivityDo);
//            }
//            //1.没活动列表的处理逻辑（如果没有活动可以用 就加到没有活动的商品列表里面）
//            if (activityFlagList.isEmpty()) {
//                if (Objects.nonNull(commodity.getActivityId())) {
//                    //指定了活动id没找到，增加返回code给交易域做判断
//                    commodity.setNoActivityCode(NoActivityMsgEnum.LIMIT_ERROR.getId());
//                    commodity.setNoActivityMsg(NoActivityMsgEnum.LIMIT_ERROR.getMsg());
//                }
//                notActivityCommodityDTO.add(commodity);
//                continue;
//            }
//
//            //2.有活动列表逻辑
//            if (Objects.nonNull(commodity.getActivityId())) {
//                //商品指定活动是否在商品可参与活动列表中（商品指定了活动id能找到）
//                PromotionActivityDO promotionActivityDo = CollectionsUtil.findOneInList(activityFlagList, val -> val.getId().equals(commodity.getActivityId()));
//                if (promotionActivityDo != null) {
//                    ckuActivityMap.put(commodity.getUpId(), activityFlagList);
//                }
//                else {
//                    //指定了活动id没找到，增加返回code给交易域
//                    commodity.setNoActivityCode(NoActivityMsgEnum.NO_MATCH.getId());
//                    commodity.setNoActivityMsg(NoActivityMsgEnum.NO_MATCH.getMsg());
//                    notActivityCommodityDTO.add(commodity);
//                }
//                continue;
//            }
//            //3.有活动列表但没指定活动id,会在initResponseList中选1个默认活动返回（此处是与下单接口最大的区别）
//            ckuActivityMap.put(commodity.getUpId(), activityFlagList);
//        }
//        // 构造多个活动计算的入参数
//        List<ActivityResponseParamsDTO> activityResponseParamsDTOList = initResponseList(commoditiesList, ckuActivityMap);
//        List<ActivityResponseParamsDTO> deletedFlags = new ArrayList<>();
//        // 活动策略计算
//        for (ActivityResponseParamsDTO activityResponseParamsDTO : activityResponseParamsDTOList) {
//            Long activityId = activityResponseParamsDTO.getActivityId();
//            ActivityConfigDTO activityConfigDTO = activityConfigCache.get(activityId);
//            activityCalculateManager.strategyCalculate(activityConfigDTO, activityResponseParamsDTO);
//
//
//            if (!activityResponseParamsDTO.getPaTemplateId().equals(StrategyGroupEnum.ZJRX_G.getId())) {
//                continue;
//            }
//            // nn活动有拆分 需要把没有活动的商品拿出来
//            List<ActivityCommodityDTO> deletedFlag = new ArrayList<>();
//            activityResponseParamsDTO.getActivityCommodityDTOList().forEach(val -> {
//                if (val.getSubtractPriceAll() == null || val.getSubtractPriceAll().equals(BigDecimal.ZERO)) {
//                    notActivityCommodityDTO.add(val);
//                    deletedFlag.add(val);
//                }
//            });
//            activityResponseParamsDTO.getActivityCommodityDTOList().removeAll(deletedFlag);
//
//            activityResponseParamsDTO.getActivityCommodityDTOList().removeAll(deletedFlag);
//            if (CollectionUtil.isEmpty(activityResponseParamsDTO.getActivityCommodityDTOList())) {
//                deletedFlags.add(activityResponseParamsDTO);
//            }
//        }
//        activityResponseParamsDTOList.removeAll(deletedFlags);
//        OrderEditResponseDTO orderEditResponseDTO = new OrderEditResponseDTO();
//        orderEditResponseDTO.setNoActivityCommodityDTOList(notActivityCommodityDTO);
//        orderEditResponseDTO.setActivityList(activityResponseParamsDTOList);
//        activityCalculateManager.orderActivityCommodityMoneyGroup(orderEditResponseDTO);
//        activityCalculateManager.orderMoneyGroup(orderEditResponseDTO);
//
//        // 汇总信息
//        resultDTO.setActivityList(activityResponseParamsDTOList);
//        resultDTO.setActivityCommodityDTOList(notActivityCommodityDTO);
//        resultDTO.setOrderDetailPrice(orderEditResponseDTO.getOrderDetailPrice());
//        resultDTO.setOrderDiscountsPrice(orderEditResponseDTO.getOrderDiscountsPrice());
//        return resultDTO;
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public ActivityResponseParamsDTO calculateJFDH(ActivityParamsRequest activityRequestParams) {
//        PromotionActivityDO promotionActivityDo = promotionActivityMapper.selectById(activityRequestParams.getActivityId());
//        if (null == promotionActivityDo) {
//            return null;
//        }
//        List<PromotionActivityLimitDO> promotionActivityLimitDoList = promotionActivityLimitMapper.selectByActivityId(activityRequestParams.getActivityId());
//        List<PromotionStrategyDO> promotionStrategyDoList = promotionStrategyMapper.selectByActivityId(activityRequestParams.getActivityId());
//        QueryWrapper<PromotionActivityLoggerDO> queryWrapper = new QueryWrapper<>();
//        queryWrapper.lambda().eq(PromotionActivityLoggerDO::getActivityId, activityRequestParams.getActivityId()).eq(PromotionActivityLoggerDO::getUserId, activityRequestParams.getUserId());
//        List<PromotionActivityLoggerDO> promotionActivityLoggerDoList = promotionActivityLoggerMapper.selectList(queryWrapper);
//
//        // 获取当前活动的限制
//        ActivityConfigDTO activity = ActivityConfigConverter.converter(promotionActivityDo, promotionStrategyDoList, promotionActivityLimitDoList);
//        List<ActivityRuleDTO> numberLimit = activity.getActivityRuleDTOList();
//        List<BaseActivityDTO> baseActivityDTOS = numberLimit.get(0).getCondition();
//        int limitNum = 0;
//        for (BaseActivityDTO baseActivity : baseActivityDTOS) {
//            if (JFDHConditionEnum.NL.getId().equals(baseActivity.getId())) {
//                limitNum = Integer.parseInt(baseActivity.getValue());
//            }
//        }
//        PromotionActivityLoggerDO loggerDo = new PromotionActivityLoggerDO();
//        loggerDo.setActivityId(activityRequestParams.getActivityId());
//        QueryWrapper<PromotionActivityLoggerDO> var1 = new QueryWrapper<PromotionActivityLoggerDO>();
//        var1.setEntity(loggerDo);
//        Integer totalNum = promotionActivityLoggerMapper.selectCount(var1);
//        if (limitNum < totalNum) {
//            String msg = String.format("活动限定单人最多兑换%s件商品", limitNum);
//            throw new ApplicationException(msg);
//        }
//        // 获取商品
//        List<SkuCodeBaseDTO> skuCodeBases = activity.getCommodityList().getSkuList();
//        // 如果存在商品 获取第一个的商品
//        SkuCodeBaseDTO skuCodeBase = skuCodeBases.get(0);
//        // 新建一个活动商品实体
//        ActivityCommodityDTO activityCommodity = new ActivityCommodityDTO();
//
//        activityCommodity.setUpId(Long.valueOf(skuCodeBase.getUpId()));
//        activityCommodity.setShopId(activityRequestParams.getShopId());
//        // 组装活动入参
//        ActivityParamsConverter.converter(activityCommodity, activityRequestParams, promotionActivityDo, promotionActivityLoggerDoList);
//
//        // 组装活动返回
//        ActivityResponseParamsDTO activityResponseParams = ActivityResponseParamsConverter.converter(promotionActivityDo);
//        activityResponseParams.getActivityCommodityDTOList().add(activityCommodity);
//        Context context = new Context(activity, activityResponseParams);
//        // 限制类型初始化 并且计算
//        context.initJFDHStrategyCalcalate()
//                .calculateStrategy();
//        return activityResponseParams;
//
//    }
//
//    @Override
//    public PageBean<ActivityResponseParamsDTO> showJFDH(ActivityParamsRequest activityParamsRequest, Integer page, Integer size) {
//        long t1 = System.currentTimeMillis();
//        // 获取所有的可用活动
//        List<PromotionActivityDO> promotionActivityDOList = activityCacheManager.getAllPromotionActivityList(activityParamsRequest.getTenantId(), activityParamsRequest.getAppId());
//        List<PromotionActivityDO> jfdhActivityList = promotionActivityDOList.stream().filter(p->StrategyGroupEnum.JFDH_G.getId().equals(String.valueOf(p.getPaTemplateId()))).collect(Collectors.toList());
//        // 获取活动下的限制信息以活动分组
//        Map<Long, List<PromotionActivityLimitDO>> activityLimitMap = getAllPromotionActivityLimitList(jfdhActivityList);
//        // 获取活动下的策略信息并以活动分组
//        Map<Long, List<PromotionStrategyDO>> activityStrategyMap = getAllPromotionStrategyMap(jfdhActivityList);
//        // 获取活动下的日志信息以活动分组
//        Map<Long, List<PromotionActivityLoggerDO>> activityLoggerMap = getAllPromotionActivityLoggerList(activityParamsRequest.getUserId(), activityParamsRequest.getUserType(), promotionActivityDOList);
//
//        long t2 = System.currentTimeMillis();
//        log.info("查询sql耗时->{}", t2 - t1);
//        // 缓存一个活动的配置类
//        Map<Long, ActivityConfigDTO> activityConfigCache = new HashMap<>();
//        // 接受返回活动
//        List<ActivityResponseParamsDTO> activityResponseParamsDTOList = new ArrayList<>();
//        // 遍历活动
//        for (PromotionActivityDO promotionActivityDo : jfdhActivityList) {
//            // 获取当前活动的限制
//            ActivityConfigDTO activity = getActivityConfig(promotionActivityDo, activityStrategyMap, activityLimitMap);
//            // 获取商品
//            List<SkuCodeBaseDTO> skuCodeBaseDTOS = activity.getCommodityList().getSkuList();
//            // 如果存在商品 获取第一个的商品
//            if (CollectionUtil.isEmpty(skuCodeBaseDTOS)) {
//                continue;
//            }
//            SkuCodeBaseDTO skuCodeBaseDTO = skuCodeBaseDTOS.get(0);
//            // 将活动配置加入缓存容器
//            activityConfigCache.put(promotionActivityDo.getId(), activity);
//            // 新建一个活动商品实体
//            ActivityCommodityDTO activityCommodityDTO = new ActivityCommodityDTO();
//            if (StringUtil.isNotBlank(skuCodeBaseDTO.getShopId())) {
//                activityCommodityDTO.setShopId(Long.valueOf(skuCodeBaseDTO.getShopId()));
//            }
//            activityCommodityDTO.setUpId(Long.valueOf(skuCodeBaseDTO.getUpId()));
//            // 组装活动入参
//            ActivityParamsDTO params = getActivityParams(activityCommodityDTO, activityParamsRequest, promotionActivityDo, activityLoggerMap);
//            Context context = new Context(activity, params);
//            // 限制类型初始化 并且计算
//            boolean result = context.initJFDHCalculate()
//                    .calculateLimit();
//            if (!result) {
//                continue;
//            }
//            Date now = new Date();
//            if (now.before(promotionActivityDo.getStartTime()) || now.after(promotionActivityDo.getEndTime())) {
//                continue;
//            }
//            // 组装活动返回
//            ActivityResponseParamsDTO activityResponseParamsDTO = ActivityResponseParamsConverter.converter(promotionActivityDo);
//            activityResponseParamsDTO.getActivityCommodityDTOList().add(activityCommodityDTO);
//            activityResponseParamsDTOList.add(activityResponseParamsDTO);
//        }
//
//        long t3 = System.currentTimeMillis();
//        log.info("通过限制耗时->{}", t3 - t2);
//        // 遍历活动返回  使用策略
//        for (ActivityResponseParamsDTO activityResponseParamsDTO : activityResponseParamsDTOList) {
//            Long activityId = activityResponseParamsDTO.getActivityId();
//            ActivityConfigDTO activityConfigDTO = activityConfigCache.get(activityId);
//            Context context = new Context(activityConfigDTO, activityResponseParamsDTO);
//            context.initJFDHStrategyCalcalate()
//                    .calculateStrategy();
//        }
//
//        long t4 = System.currentTimeMillis();
//        log.info("通过计算耗时->{}", t4 - t3);
//        PageUtil<ActivityResponseParamsDTO> pageUtil = new PageUtil<>();
//        PageBean<ActivityResponseParamsDTO> pageBean = pageUtil.pageUtil(page, size, activityResponseParamsDTOList);
//
//        long t5 = System.currentTimeMillis();
//        log.info("通过限制耗时->{}", t5 - t4);
//        return pageBean;
//    }
//
//
//    /**
//     * 领券 活动展示
//     *
//     * @param activityParamsRequest 领券活动入参数
//     * @return 领券活动出参数
//     */
//    @Override
//    public PageBean<CouponResponseDTO> couponList(ActivityParamsRequest activityParamsRequest, Integer page, Integer size) {
//        // 获取所有的可用活动
//        List<PromotionActivityDO> promotionActivityDOList =
//                promotionActivityMapper.selectAllCanUserActivity(Integer.parseInt(ActivityStatus.IN_PROGRESS.getId()),Collections.singletonList(Integer.valueOf(StrategyGroupEnum.LQHD_G.getId())), new Date(), activityParamsRequest.getAppId(), activityParamsRequest.getTenantId());
//        if (CollectionUtil.isEmpty(promotionActivityDOList)) {
//            return new PageBean<>(new ArrayList<>());
//        }
//        // 获取活动下的限制信息以活动分组
//        Map<Long, List<PromotionActivityLimitDO>> activityLimitMap = getAllPromotionActivityLimitList(promotionActivityDOList);
////         获取活动下的策略信息并以活动分组
//        Map<Long, List<PromotionStrategyDO>> activityStrategyMap = getAllPromotionStrategyMap(promotionActivityDOList);
//        // 获取活动下的日志信息以活动分组
//        Map<Long, List<PromotionActivityLoggerDO>> activityLoggerMap = getAllPromotionActivityLoggerList(activityParamsRequest.getUserId(), activityParamsRequest.getUserType(), promotionActivityDOList);
//        // 用户每天参与活动次数
//        Map<Long, Integer> dayNumCountMap = getDayNumCount(activityParamsRequest.getUserId(), promotionActivityDOList);
//        // 用户参与活动总次数
//        Map<Long, Integer> totalNumCountMap = getTotalNumCount(activityParamsRequest.getUserId(), promotionActivityDOList);
//
//        List<CouponResponseDTO> couponResponseDTOList = new ArrayList<>();
//        for (PromotionActivityDO promotionActivityDo : promotionActivityDOList) {
//            // 自动领券的时候
//            boolean autoFlag = Boolean.FALSE;
//            // 获取当前活动的限制
//            ActivityConfigDTO activity = getActivityConfig(promotionActivityDo, activityStrategyMap, activityLimitMap);
//            CouponResponseDTO couponResponseDTO = transform(activity);
//            //获取关联的优惠券
//            List<BaseActivityDTO> couponLimit = activity.getCouponLimit();
//            long couponId = 0L;
//            int couponNumber = 0;
//            for (BaseActivityDTO baseActivityDTO : couponLimit) {
//                if (PATemplateCouponEnum.COUPON_ID.getId().equals(baseActivityDTO.getId())) {
//                    try {
//                        couponId = Long.parseLong(baseActivityDTO.getValue());
//                    } catch (Exception e) {
//                        log.info("错误类型:" + promotionActivityDo.getId());
//                    }
//                }
//                if (PATemplateCouponEnum.COUPON_NUMBER.getId().equals(baseActivityDTO.getId())) {
//                    couponNumber = Integer.parseInt(baseActivityDTO.getValue());
//                }
//            }
//            //目前已发放数量
//            Integer totalSend = promotionCouponLoggerMapper.countTodayCreateNumber(null, promotionActivityDo.getId(), null, 0);
//            int others = couponNumber - totalSend;
//            if (0 >= others) {
//                continue;
//            }
//            PromotionCouponDO couponDo = promotionCouponMapper.selectById(couponId);
//            if (null == couponDo) {
//                continue;
//            }
//            if (PromotionCouponStatusEnums.FORBIDDEN.getId().equals(couponDo.getStatus())) {
//                continue;
//            }
//            ActivityRuleDTO activityRuleDTO = activity.getActivityRuleDTOList().get(0);
//            List<BaseActivityDTO> conditions = activityRuleDTO.getCondition();
//            if (CollectionUtil.isNotEmpty(conditions)) {
//                for (BaseActivityDTO condition : conditions) {
//                    if (CouponConditionEnum.AUTO.getId().equals(condition.getId())) {
//                        autoFlag = Boolean.TRUE;
//                        break;
//                    }
//                    String obtainCondition = CouponConditionEnum.getValueById(condition.getId());
//                    if (StringUtil.isNotBlank(obtainCondition)) {
//                        couponResponseDTO.setObtainCondition(obtainCondition);
//                    }
//                }
//            }
//            int dayNum = 0;
//            int totalNum = 0;
//
//            for (BaseActivityDTO baseActivityDTO : activity.getNumberLimit()) {
//                if (PATemplateNumberEnum.DRCS.getId().equals(baseActivityDTO.getId())) {
//                    dayNum = StringUtil.isBlank(baseActivityDTO.getValue()) ? 0 : Integer.parseInt(baseActivityDTO.getValue());
//                }
//                if (PATemplateNumberEnum.ZCS.getId().equals(baseActivityDTO.getId())) {
//                    totalNum = StringUtil.isBlank(baseActivityDTO.getValue()) ? 0 : Integer.parseInt(baseActivityDTO.getValue());
//                }
//            }
//            //如果自动领券  下一个
//            int dayCount;
//            int totalCount;
//            dayCount = dayNumCountMap.get(activity.getActivityId());
//            totalCount = totalNumCountMap.get(activity.getActivityId());
//            //TODO：目前优惠券系统发放，是用户点击我的—优惠券列表-发放优惠券（系统发放），是耦合进列表查询，建议拆成两个接口调用。
//            if (autoFlag) {
//                ActivityParamsDTO params =
//                        activityParamsRequest.clone(ActivityParamsDTO.class, CloneDirection.FORWARD);
//                CalculateUserLimit calculateUserLimit = new CalculateUserLimit(null, activity, params);
//                boolean userLimitResult = calculateUserLimit.calculate();
//                if (userLimitResult) {
//                    if (totalNum > totalCount) {
//                        if (dayNum > dayCount) {
//                            //设置用户优惠券
//                            PromotionCouponLoggerDO loggerDO = new PromotionCouponLoggerDO();
//                            loggerDO.setReleaseType(CouponReleaseTypeEnum.ACTIVITY.getId());
//                            loggerDO.setUserType(0);
//                            loggerDO.setStatus(PromotionCouponLoggerStatusEnums.UN_USED.getId());
//                            loggerDO.setCouponId(couponId);
//                            loggerDO.setReleaseId(promotionActivityDo.getId());
//                            loggerDO.setUserId(activityParamsRequest.getUserId());
//                            loggerDO.setAmount(1);
//                            loggerDO.setAppId(activityParamsRequest.getAppId());
//                            loggerDO.setTenantId(activityParamsRequest.getTenantId());
//                            CouponLimitDTO couponLimitDTO = JSON.parseObject(couponDo.getLimits(), CouponLimitDTO.class, CloneDirection.OPPOSITE);
//                            setTime(loggerDO, couponLimitDTO.getTimeLimit());
//                            Date now = new Date();
//                            loggerDO.setCreatedTime(now);
//                            loggerDO.setReceiveTime(now);
//                            loggerDO.setUpdatedTime(now);
//                            String code = DistributeIdUtil.generateId();
//                            loggerDO.setCode(code);
//
//                            //设置活动日志、
//                            PromotionActivityLoggerDO loggerDo1 = new PromotionActivityLoggerDO();
//                            loggerDo1.setUserType(0);
//                            loggerDo1.setUserId(activityParamsRequest.getUserId());
//                            loggerDo1.setAppId(activityParamsRequest.getAppId());
//                            loggerDo1.setTenantId(activityParamsRequest.getTenantId());
//                            loggerDo1.setActivityId(activity.getActivityId());
//                            loggerDo1.setCreatedTime(now);
//                            loggerDo1.setOrderDetail("{}");
//                            loggerDo1.setStatus(1);
//                            loggerDo1.setUpdatedTime(now);
//                            int amount = dayNum - dayCount;
//                            List<PromotionCouponLoggerDO> loggerDOS = new ArrayList<>();
//                            List<PromotionActivityLoggerDO> loggerDOS1 = new ArrayList<>();
//                            if (amount >= others) {
//                                for (int i = 0; i < others; i++) {
//                                    loggerDOS.add(loggerDO);
//                                    loggerDOS1.add(loggerDo1);
//                                }
//                            } else {
//                                for (int i = 0; i < amount; i++) {
//                                    loggerDOS.add(loggerDO);
//                                    loggerDOS1.add(loggerDo1);
//                                }
//                            }
//                            promotionCouponLoggerDao.saveOrUpdateBatch(loggerDOS);
//                            promotionActivityLoggerDao.saveOrUpdateBatch(loggerDOS1);
//                        }
//                    }
//                }
//
//                continue;
//            }
//            couponResponseDTO.setCouponNumber(couponNumber);
//            setCouponResponseDTO(couponResponseDTO, couponDo);
//            // 组装活动入参
//            ActivityParamsDTO params = getActivityParams(null, activityParamsRequest, promotionActivityDo, activityLoggerMap);
//
//            params.setDayNum(dayCount);
//            params.setTotalNum(totalCount);
//            Context context = new Context(couponResponseDTO, params);
//            // 限制类型初始化 并且计算
//            boolean result = context.initLJHDCalculate()
//                    .calculateLimit();
//            if (!result) {
//                continue;
//            }
//            Date now = new Date();
//            if (now.before(promotionActivityDo.getStartTime()) || now.after(promotionActivityDo.getEndTime())) {
//                continue;
//            }
//            couponResponseDTOList.add(couponResponseDTO);
//        }
//        PageUtil<CouponResponseDTO> pageUtil = new PageUtil<>();
//        long start = System.currentTimeMillis();
//        PageBean<CouponResponseDTO> pageBean = pageUtil.pageUtil(page, size, couponResponseDTOList);
//        long end = System.currentTimeMillis();
//        log.info("转化耗时——>{}", end - start);
//        return pageBean;
//    }
//
//    private void setCouponResponseDTO(CouponResponseDTO couponResponseDTO, PromotionCouponDO promotionCouponDo) {
//        couponResponseDTO.setCondition(promotionCouponDo.getCondition());
//        couponResponseDTO.setCouponId(promotionCouponDo.getId());
//        couponResponseDTO.setCouponName(promotionCouponDo.getCouponName());
//        couponResponseDTO.setCouponType(promotionCouponDo.getCouponType());
//        couponResponseDTO.setCouponValue(promotionCouponDo.getCouponValue());
//    }
//
//    private CouponResponseDTO transform(ActivityConfigDTO activityConfigDTO) {
//        CouponResponseDTO couponResponseDTO = new CouponResponseDTO();
//        couponResponseDTO.setActivityId(activityConfigDTO.getActivityId());
//        couponResponseDTO.setActivityName(activityConfigDTO.getActivityName());
//        couponResponseDTO.setDescription(activityConfigDTO.getDescription());
//        couponResponseDTO.setStartTime(activityConfigDTO.getStartTime());
//        couponResponseDTO.setEndTime(activityConfigDTO.getEndTime());
//        couponResponseDTO.setCommodityList(activityConfigDTO.getCommodityList());
//        couponResponseDTO.setActivityRuleDTOList(activityConfigDTO.getActivityRuleDTOList());
//        couponResponseDTO.setTenantLimit(activityConfigDTO.getTenantLimit());
//        couponResponseDTO.setUserLimit(activityConfigDTO.getUserLimit());
//        couponResponseDTO.setNumberLimit(activityConfigDTO.getNumberLimit());
//        couponResponseDTO.setCouponLimit(activityConfigDTO.getCouponLimit());
//        return couponResponseDTO;
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public BaseActivityDTO getCoupon(CouponActivityParamsRequest activityRequestParams) {
//        if (CouponReleaseTypeEnum.ACTIVITY.getId().equals(activityRequestParams.getReleaseType())) {
//            return getActivityCoupon(activityRequestParams);
//        } else if (CouponReleaseTypeEnum.MEMBER.getId().equals(activityRequestParams.getReleaseType())
//                || CouponReleaseTypeEnum.THIRD_PARTY.getId().equals(activityRequestParams.getReleaseType())) {
//            getMemberCoupon(activityRequestParams);
//            return null;
//        } else {
//            throw new ApplicationException("关联类型未传！");
//        }
//    }
//
//    /**
//     * 获取会员权益优惠券、第三方权益优惠券，但因为是特殊类型，详情无法展示，所以均返回null
//     */
//    private Boolean getMemberCoupon(CouponActivityParamsRequest activityRequestParams) {
//        PromotionCouponDO couponDo = promotionCouponMapper.selectById(activityRequestParams.getCouponId());
//        CouponLimitDTO couponLimitDTO = JSON.parseObject(couponDo.getLimits(), CouponLimitDTO.class);
//        ActivityRuleDTO activityRuleDTO = couponLimitDTO.getTimeLimit();
//        if (null != activityRequestParams.getAmount() && 0 < activityRequestParams.getAmount()) {
//            return createCouponLogger(activityRequestParams, activityRuleDTO);
//        } else {
//            return false;
//        }
//
//    }
//
//    /**
//     * 会员权益优惠券，活动优惠券，第三方优惠券，均在此创建
//     */
//    public boolean createCouponLogger(CouponActivityParamsRequest activityRequestParamsDTO, ActivityRuleDTO activityRuleDTO) {
//        int amount = null == activityRequestParamsDTO.getAmount() ? 1 : activityRequestParamsDTO.getAmount();
//        if (CouponReleaseTypeEnum.ACTIVITY.getId().equals(activityRequestParamsDTO.getReleaseType())) {
//
//            QueryWrapper<PromotionActivityLimitDO> queryWrapper1 = new QueryWrapper<>();
//            queryWrapper1.lambda().eq(PromotionActivityLimitDO::getActivityId, activityRequestParamsDTO.getReleaseId());
//            //取出所有限制
//            List<PromotionActivityLimitDO> promotionActivityLimitDOS = promotionActivityLimitMapper.selectList(queryWrapper1);
//            checkLimitCondition(promotionActivityLimitDOS, amount, activityRequestParamsDTO);
//        }
//
//        boolean is;
//        //设置活动日志、
//        PromotionActivityLoggerDO loggerDO1 = new PromotionActivityLoggerDO();
//        loggerDO1.setUserType(0);
//        loggerDO1.setUserId(activityRequestParamsDTO.getUserId());
//        loggerDO1.setAppId(activityRequestParamsDTO.getAppId());
//        loggerDO1.setTenantId(activityRequestParamsDTO.getTenantId());
//        loggerDO1.setActivityId(activityRequestParamsDTO.getReleaseId());
//        Date now = new Date();
//        loggerDO1.setStatus(1);
//        loggerDO1.setUpdatedTime(now);
//        loggerDO1.setCreatedTime(now);
//        loggerDO1.setOrderDetail("{}");
//        loggerDO1.setCode(DistributeIdUtil.generateId());
//        List<PromotionCouponLoggerDO> loggerDOS = new ArrayList<>();
//        PromotionCouponLoggerDO promotionCouponLoggerDo = setPromotionCouponLoggerDo(activityRequestParamsDTO);
//        //设置时间
//        setTime(promotionCouponLoggerDo, activityRuleDTO);
//        List<PromotionActivityLoggerDO> loggerDOS1 = new ArrayList<>();
//        for (int i = 0; i < amount; i++) {
//            loggerDOS.add(promotionCouponLoggerDo);
//            loggerDOS1.add(loggerDO1);
//        }
//        is = promotionCouponLoggerDao.saveOrUpdateBatch(loggerDOS);
//        is = is && promotionActivityLoggerDao.saveOrUpdateBatch(loggerDOS1);
//        return is;
//    }
//
//    private void checkLimitCondition(List<PromotionActivityLimitDO> promotionActivityLimitDOS, Integer amount, CouponActivityParamsRequest detail) {
//        promotionActivityLimitDOS.forEach(promotionActivityLimitDO -> {
//            //初始化数据
//            Long activityId = detail.getReleaseId();
//            Integer activityType = detail.getReleaseType();
//            Long userId = detail.getUserId();
//
//            //查询所有领券记录
//            QueryWrapper<PromotionCouponLoggerDO> queryWrapper = new QueryWrapper<>();
//            queryWrapper.eq("release_id", activityId);
//            queryWrapper.eq("release_type", activityType);
//            List<PromotionCouponLoggerDO> loggerDOS = promotionCouponLoggerMapper.selectList(queryWrapper);
//
//            //2、优惠券限制
//            if (promotionActivityLimitDO.getType().equals(PATemplateBaseEnum.COUPON.getId()) &&
//                    StringUtils.isNotBlank(promotionActivityLimitDO.getLimits())) {
//
//                int existCouponLogger = loggerDOS.size();
//                List<BaseActivityDTO> couponLimit = JSON.parseArray(promotionActivityLimitDO.getLimits(), BaseActivityDTO.class);
//                int amountLimit = 0;
//                for (BaseActivityDTO limit : couponLimit) {
//                    if (PATemplateCouponEnum.COUPON_NUMBER.getId().equals(limit.getId())) {
//                        amountLimit = Integer.parseInt(limit.getValue());
//                        break;
//                    }
//                }
//                //判断是否超出数量限制
//                if (amount > amountLimit - existCouponLogger) {
//                    throw new ApplicationException("优惠券已被领完");
//                }
//            }
//            //3、数量限制
//            if (PATemplateBaseEnum.NUMBER.getId().equals(promotionActivityLimitDO.getType()) &&
//                    StringUtils.isNotBlank(promotionActivityLimitDO.getLimits())) {
//                AtomicInteger dayNum = new AtomicInteger(0);
//                AtomicInteger totalNum = new AtomicInteger(0);
//
//                loggerDOS.forEach(promotionCouponLoggerDO -> {
//
//                    if (promotionCouponLoggerDO.getUserId().equals(userId)) {
//                        // 总下单量
//                        totalNum.addAndGet(1);
//                        Date orderTime = promotionCouponLoggerDO.getCreatedTime();
//                        // 当天下单量
//                        if (DateUtils.getCurrenDate().equals(DateUtils.format(orderTime, DateUtils.DATE_FORMAT))) {
//                            dayNum.addAndGet(1);
//                        }
//                    }
//
//                });
//
//                List<BaseActivityDTO> jsonArray = JSON.parseArray(promotionActivityLimitDO.getLimits(), BaseActivityDTO.class);
//                for (int i = 0; i < jsonArray.size(); i++) {
//                    BaseActivityDTO jsonObject = jsonArray.get(i);
//                    if ((jsonObject.getId().equals(PATemplateNumberEnum.DRCS.getId()) &&
//                            dayNum.get() >= Integer.parseInt(jsonObject.getValue())) ||
//                            (jsonObject.getId().equals(PATemplateNumberEnum.ZCS.getId()) &&
//                                    totalNum.get() >= Integer.parseInt(jsonObject.getValue()))) {
//                        throw new ApplicationException("您已达到活动参与次数上限.");
//                    }
//                }
//            }
//
//        });
//    }
//
//    /**
//     * 正常活动，创建优惠券并返回
//     */
//    private BaseActivityDTO getActivityCoupon(CouponActivityParamsRequest activityRequestParams) {
//        List<PromotionStrategyDO> strategyDoList = promotionStrategyMapper.selectByActivityId(activityRequestParams.getReleaseId());
//        PromotionStrategyDO strategyDo = strategyDoList.get(0);
//        List<ActivityRuleDTO> activityRuleDTOS = JSON.parseArray(strategyDo.getRules(), ActivityRuleDTO.class);
//        List<BaseActivityDTO> conditions = activityRuleDTOS.get(0).getCondition();
//        BaseActivityDTO condition = conditions.get(0);
//        PromotionCouponDO couponDo = promotionCouponMapper.selectById(activityRequestParams.getCouponId());
//        CouponLimitDTO couponLimitDTO = JSON.parseObject(couponDo.getLimits(), CouponLimitDTO.class);
//        ActivityRuleDTO activityRuleDTO = couponLimitDTO.getTimeLimit();
//        BaseActivityDTO baseActivityDTO = new BaseActivityDTO();
//        if (CouponConditionEnum.AUTO.getId().equals(condition.getId())) {
//            log.info("condition.getId() equals {}", CouponConditionEnum.AUTO.getId());
//        } else if (CouponConditionEnum.INVITE.getId().equals(condition.getId())) {
//            baseActivityDTO.setId(CouponConditionEnum.INVITE.getId());
//            if (ifCanGetCoupon(activityRequestParams)) {
//                if (createCouponLogger(activityRequestParams, activityRuleDTO)) {
//                    baseActivityDTO.setId(GetCouponResponseEnum.SUCCESS.getId());
//                    baseActivityDTO.setValue(GetCouponResponseEnum.SUCCESS.getValue());
//                } else {
//                    baseActivityDTO.setId(GetCouponResponseEnum.FAILED.getId());
//                    baseActivityDTO.setValue(GetCouponResponseEnum.FAILED.getValue());
//                }
//            } else {
//                baseActivityDTO.setId((GetCouponResponseEnum.INVITE.getId()));
//                baseActivityDTO.setValue(GetCouponResponseEnum.INVITE.getValue());
//            }
//        } else if (CouponConditionEnum.HANDLE.getId().equals(condition.getId())) {
//            if (createCouponLogger(activityRequestParams, activityRuleDTO)) {
//                baseActivityDTO.setId(GetCouponResponseEnum.SUCCESS.getId());
//                baseActivityDTO.setValue(GetCouponResponseEnum.SUCCESS.getValue());
//            } else {
//                baseActivityDTO.setId(GetCouponResponseEnum.FAILED.getId());
//                baseActivityDTO.setValue(GetCouponResponseEnum.FAILED.getValue());
//            }
//        } else {
//            throw new ApplicationException("活动失效，无条件");
//        }
//        return baseActivityDTO;
//    }
//
//    /**
//     * 判断通过邀请领取的用户优惠券的次数是不是少于邀请人数
//     */
//    private boolean ifCanGetCoupon(CouponActivityParamsRequest dto) {
//        //获取邀请人数
//        int invited = Integer.parseInt(dto.getIsInvited());
//        //获取邀请领取领券活动id列表
//        QueryWrapper<PromotionStrategyDO> queryWrapper = new QueryWrapper<>();
//        String sql = "JSON_CONTAINS( `rules`, JSON_OBJECT(\"condition\",JSON_ARRAY(JSON_OBJECT(\"id\",\"invite\"))))";
//        queryWrapper.apply(sql);
//        queryWrapper.select(ACTIVITY_ID);
//        List<PromotionStrategyDO> doList = promotionStrategyMapper.selectList(queryWrapper);
//        int gets = 0;
//        if (CollectionUtil.isNotEmpty(doList)) {
//            List<Long> activityIds = doList.stream()
//                    .map(PromotionStrategyDO::getActivityId)
//                    .collect(Collectors.toList());
//            QueryWrapper<PromotionCouponLoggerDO> queryWrapper1 = new QueryWrapper<>();
//            queryWrapper1.in("release_id", activityIds);
//            queryWrapper1.eq("release_type", CouponReleaseTypeEnum.ACTIVITY.getId());
//            queryWrapper1.eq("user_id", dto.getUserId());
//            queryWrapper1.eq("user_type", dto.getUserType());
//            queryWrapper1.eq("app_id", dto.getAppId());
//            queryWrapper1.eq("tenant_id", dto.getTenantId());
//            List<PromotionCouponLoggerDO> doList1 = promotionCouponLoggerMapper.selectList(queryWrapper1);
//            if (CollectionUtil.isNotEmpty(doList1)) {
//                gets = doList1.size();
//            }
//        }
//        return invited > gets;
//    }
//
//    /**
//     * 根据优惠券时间限制设置用户优惠券时间
//     */
//    private void setTime(PromotionCouponLoggerDO promotionCouponLoggerDo, ActivityRuleDTO activityRuleDTO) {
//        Date startTime = null;
//        Date endTime = null;
//        List<BaseActivityDTO> conditions = activityRuleDTO.getCondition();
//        List<UpIdDTO> operations = activityRuleDTO.getOperation();
//        if (TimeConditionEnum.ORDER_TIME.getId().equals(conditions.get(0).getId())) {
//            for (BaseActivityDTO activity : operations) {
//                if (TimeOperationEnum.START_TIME.getId().equals(activity.getId())) {
//                    startTime = DateUtils.parse(activity.getValue(), "yyyy-MM-dd HH:mm:ss");
//                }
//                if (TimeOperationEnum.END_TIME.getId().equals(activity.getId())) {
//                    endTime = DateUtils.parse(activity.getValue(), "yyyy-MM-dd HH:mm:ss");
//                }
//            }
//        } else if (TimeConditionEnum.SINCE_GET.getId().equals(conditions.get(0).getId())) {
//            startTime = new Date();
//            int days = Integer.parseInt(operations.get(0).getValue());
//            endTime = DateUtils.addDay(startTime, days);
//        } else if (TimeConditionEnum.WXZ.getId().equals(conditions.get(0).getId())) {
//            log.info("TimeConditionEnum.WXZ.getId().equals(conditions.get(0).getId()):{}", conditions.get(0).getId());
//        } else {
//            throw new ApplicationException("优惠券异常！时间条件错误！");
//        }
//        if ((null == startTime && null != endTime) || (null != startTime && null == endTime)) {
//            throw new ApplicationException("日期设置错误");
//        }
//        promotionCouponLoggerDo.setStartTime(startTime);
//        promotionCouponLoggerDo.setEndTime(endTime);
//    }
//
//    @Override
//    public OrderEditResponseDTO orderCalculate(ActivityOrderParamsRequest activityRequestParams) {
//
//        return null;
//    }
//
//    @Override
//    public CommodityActivityVO commodityActivity(CommodityActivityRequest commodityActivityRequest) {
//
//        CommodityActivityVO resultDTO = new CommodityActivityVO();
//        ActivityCommodityDTO commodityDTO = commodityActivityRequest.getCommodityDTO();
//        resultDTO.setUpId(String.valueOf(commodityDTO.getUpId()));
//        resultDTO.setSkuId(String.valueOf(commodityDTO.getSkuId()));
//
//        // 获取所有的可用活动
//        List<PromotionActivityDO> promotionActivityDOList = promotionActivityManager.findForeshowActivityList(commodityActivityRequest.getTenantId()
//                , commodityActivityRequest.getAppId(), Lists.newArrayList(Integer.parseInt(StrategyGroupEnum.SECKILL.getId())), promotionProperties.getForeshowHour());
//        // 获取活动下的策略信息并以活动分组
//        Map<Long, List<PromotionStrategyDO>> activityStrategyMap = getAllPromotionStrategyMap(promotionActivityDOList);
//        // 获取活动下的限制信息以活动分组
//        Map<Long, List<PromotionActivityLimitDO>> activityLimitMap = getAllPromotionActivityLimitList(promotionActivityDOList);
//        // 获取活动下的日志信息以活动分组
//        Map<Long, List<PromotionActivityLoggerDO>> activityLoggerMap = getAllPromotionActivityLoggerList(commodityActivityRequest.getUserId(),
//                commodityActivityRequest.getUserType(),
//                promotionActivityDOList);
//
//        // 获取一个商品的可用活动
//        commodityActivityList(promotionActivityDOList,
//                activityStrategyMap,
//                activityLimitMap,
//                activityLoggerMap,
//                commodityDTO,
//                resultDTO,
//                commodityActivityRequest.clone(CommodityListActivityRequest.class));
//        return resultDTO;
//    }
//
//    /**
//     * 获取一个商品的可用活动
//     *
//     * @param promotionActivityDOList         活动doList
//     * @param activityStrategyMap             策略 以活动分组
//     * @param activityLimitMap                限制 以活动分组
//     * @param activityLoggerMap               日志 以获得分组
//     * @param commodityDTO                    商品
//     * @param resultDTO                       商品下的可用活动
//     * @param commodityListActivityRequest 用户信息dto
//     */
//    private void commodityActivityList(List<PromotionActivityDO> promotionActivityDOList,
//                                       Map<Long, List<PromotionStrategyDO>> activityStrategyMap,
//                                       Map<Long, List<PromotionActivityLimitDO>> activityLimitMap,
//                                       Map<Long, List<PromotionActivityLoggerDO>> activityLoggerMap,
//                                       ActivityCommodityDTO commodityDTO,
//                                       CommodityActivityVO resultDTO,
//                                       CommodityListActivityRequest commodityListActivityRequest) {
//        List<PromotionActivityListPostVO> activityList = new ArrayList<>();
//
//        for (PromotionActivityDO promotionActivityDo : promotionActivityDOList) {
//            ActivityConfigDTO activityConfig = getActivityConfig(promotionActivityDo, activityStrategyMap, activityLimitMap);
//            ActivityParamsDTO params = getActivityParams(commodityDTO,
//                    commodityListActivityRequest.clone(CommodityActivityRequest.class),
//                    promotionActivityDo, activityLoggerMap);
//            boolean result = activityCalculateManager.limitCalculate(activityConfig, params, commodityListActivityRequest.getLimitList());
//            if (!result) {
//                log.info("商品可参加活动计算--商品{} 在{}活动{} limit校验不通过", commodityDTO.getUpId(), StrategyGroupEnum.getValueById(promotionActivityDo.getPaTemplateId().toString()), promotionActivityDo.getId());
//                continue;
//            }
//            if (activityConfig.getPaTemplateId() != Integer.parseInt(StrategyGroupEnum.SECKILL.getId())) {
//                //秒杀活动可以提前预告。因此不校验时间
//                Date now = new Date();
//                if (now.before(promotionActivityDo.getStartTime()) || now.after(promotionActivityDo.getEndTime())) {
//                    continue;
//                }
//            }
//
//            PromotionActivityListPostVO clone = promotionActivityDo.clone(PromotionActivityListPostVO.class);
//            clone.setActivityId(promotionActivityDo.getId());
//            clone.setActivityName(promotionActivityDo.getName());
//            clone.setPaTemplateId(promotionActivityDo.getPaTemplateId());
//            clone.setPaTemplateName(StrategyGroupEnum.getValueById(String.valueOf(clone.getPaTemplateId())));
//            clone.setActivityRuleDTOList(activityConfig.getActivityRuleDTOList());
//            clone.setCouponLimit(activityConfig.getCouponLimit());
//            clone.setNumberLimit(activityConfig.getNumberLimit());
//            clone.setCommodity(activityConfig.getCommodityList());
//            activityList.add(clone);
//        }
//        resultDTO.setPromotionActivityListPostResponseDTOList(activityList);
//    }
//
//    @Override
//    public List<CommodityActivityVO> commodityListActivity(CommodityListActivityRequest commodityListActivityRequest) {
//        List<CommodityActivityVO> resultList = new ArrayList<>();
//
//        // 获取所有的可用活动(含未开始预告活动)
//        List<PromotionActivityDO> promotionActivityDOList = promotionActivityManager.findForeshowActivityList(commodityListActivityRequest.getTenantId()
//                , commodityListActivityRequest.getAppId(), Lists.newArrayList(Integer.parseInt(StrategyGroupEnum.SECKILL.getId())), promotionProperties.getForeshowHour());
//        log.info("可用活动的信息->{}", CollectionsUtil.getPropertyList(promotionActivityDOList, SuperEntity::getId));
//        // 获取活动下的策略信息并以活动分组
//        Map<Long, List<PromotionStrategyDO>> activityStrategyMap = getAllPromotionStrategyMap(promotionActivityDOList);
//        // 获取活动下的限制信息以活动分组
//        Map<Long, List<PromotionActivityLimitDO>> activityLimitMap = getAllPromotionActivityLimitList(promotionActivityDOList);
//        // 获取活动下的日志信息以活动分组
//        Map<Long, List<PromotionActivityLoggerDO>> activityLoggerMap = getAllPromotionActivityLoggerList(commodityListActivityRequest.getUserId(),
//                commodityListActivityRequest.getUserType(),
//                promotionActivityDOList);
//
//        List<ActivityCommodityDTO> commodityDTOList = commodityListActivityRequest.getCommodityDTO();
//        commodityDTOList.forEach(val -> {
//
//            CommodityActivityVO commodityActivityVO = new CommodityActivityVO();
//
//            // 获取一个商品的可用活动
//            commodityActivityList(promotionActivityDOList,
//                    activityStrategyMap,
//                    activityLimitMap,
//                    activityLoggerMap,
//                    val,
//                    commodityActivityVO,
//                    commodityListActivityRequest);
//            commodityActivityVO.setUpId(val.getUpId().toString());
//            commodityActivityVO.setSkuId(val.getSkuId().toString());
//            resultList.add(commodityActivityVO);
//
//        });
//        return resultList;
//
//
//    }
//
//    private List<PromotionActivityDO> getAllPromotionActivityList(CommodityListActivityRequest commodityListActivityRequest) {
//        HashSet<Integer> typeList = new HashSet<>();
//        typeList.add(Integer.valueOf(StrategyGroupEnum.MJS_G.getId()));
//        typeList.add(Integer.valueOf(StrategyGroupEnum.ZJRX_G.getId()));
//        typeList.add(Integer.valueOf(StrategyGroupEnum.DZCX_G.getId()));
//        typeList.add(Integer.valueOf(StrategyGroupEnum.MZ_G.getId()));
//        typeList.add(Integer.valueOf(StrategyGroupEnum.YS_G.getId()));
//        if (Objects.nonNull(commodityListActivityRequest.getType()) && commodityListActivityRequest.getType().equals(Boolean.TRUE)) {
//            typeList.add(Integer.valueOf(StrategyGroupEnum.JFDH_G.getId()));
//            typeList.add(Integer.valueOf(StrategyGroupEnum.PTHD_G.getId()));
//        }
//        typeList.add(Integer.valueOf(StrategyGroupEnum.SECKILL.getId()));
//        List<PromotionActivityDO> allPromotionActivityList = promotionActivityManager.findForeshowActivityList(commodityListActivityRequest.getTenantId()
//                , commodityListActivityRequest.getAppId(), Lists.newArrayList(Integer.valueOf(StrategyGroupEnum.SECKILL.getId())), promotionProperties.getForeshowHour());
//        return allPromotionActivityList.stream().filter(p -> typeList.contains(p.getPaTemplateId())).collect(Collectors.toList());
//    }
//
//    @Override
//    public List<ActivityConfigDTO> getCommodityByActivityId(List<Long> activityIds) {
//        List<ActivityConfigDTO> resultList = new ArrayList<>();
//        List<PromotionActivityDO> promotionActivityDOList = promotionActivityMapper.selectBatchIds(activityIds);
//        // 获取活动下的限制信息以活动分组
//        Map<Long, List<PromotionActivityLimitDO>> activityLimitMap = getAllPromotionActivityLimitList(promotionActivityDOList);
//        // 获取活动下的策略信息并以活动分组
//        Map<Long, List<PromotionStrategyDO>> activityStrategyMap = getAllPromotionStrategyMap(promotionActivityDOList);
//        promotionActivityDOList.forEach(activity -> {
//            ActivityConfigDTO activityConfig = getActivityConfig(activity, activityStrategyMap, activityLimitMap);
//            resultList.add(activityConfig);
//        });
//        return resultList;
//
//    }
//
//    private static final String COUPON_FLAG = "true";
//
//    @Override
//    public PageBean<PromotionCouponLoggerListPostResponseDTO> usableCouponList(ActivityParamsRequest activityParamsRequest) {
//        //获取商品列表
//        List<ActivityCommodityDTO> activityCommodityDTOS = activityParamsRequest.getCommodities();
//        //如果没有则不使用优惠券
//        if (CollectionUtil.isEmpty(activityCommodityDTOS)) {
//            PageBean<PromotionCouponLoggerListPostResponseDTO> res = new PageBean<>(new ArrayList<>());
//            res.setSize(activityParamsRequest.getSize());
//            return res;
//        }
//        //根据活动分配商品
//        Map<Long, List<ActivityCommodityDTO>> activitySkuMap = activityCommodityDTOS.stream()
//                .filter(e -> null != e.getActivityId())
//                .collect(Collectors.groupingBy(ActivityCommodityDTO::getActivityId));
//        //判断活动是否可以使用优惠券
//        List<PromotionActivityDO> activityDOS = activityCommodityDTOS.stream().map(ActivityParamsConverter::convert).collect(Collectors.toList());
//        List<Long> collect = activityDOS.stream()
//                .map(SuperEntity::getId)
//                .distinct()
//                .collect(Collectors.toList());
//        List<PromotionActivityLimitDO> limitDOList = Lists.newArrayList();
//        if(CollectionUtil.isNotEmpty(collect)){
//            limitDOList = promotionActivityLimitMapper.selectAllByActivityIdList(collect);
//        }
//        Map<Long, List<PromotionActivityLimitDO>> listMap = limitDOList.stream().collect(Collectors.groupingBy(PromotionActivityLimitDO::getActivityId));
//        activitySkuMap.forEach((k, v) -> {
//            List<PromotionActivityLimitDO> limitDOS = listMap.get(k);
//            //活动中的优惠券限制
//            if (limitDOS!=null){
//                for (PromotionActivityLimitDO limitDO : limitDOS) {
//                    if (PATemplateBaseEnum.COUPON.getId().equals(limitDO.getType())) {
//                        List<BaseActivityDTO> limit = JSON.parseArray(limitDO.getLimits(), BaseActivityDTO.class);
//                        for (BaseActivityDTO baseActivityDTO : limit) {
//                            if (PATemplateCouponEnum.COUPONFLAG.getId().equals(baseActivityDTO.getId())) {
//                                String value = baseActivityDTO.getValue();
//                                if (!COUPON_FLAG.equals(value)) {
//                                    activityCommodityDTOS.removeAll(v);
//                                }
//                            }
//                        }
//                    }
//                }
//            }
//        });
//        //查询用户优惠券
//        PromotionCouponLoggerDO couponLoggerDO = new PromotionCouponLoggerDO();
//        couponLoggerDO.setStatus(PromotionCouponLoggerStatusEnums.UN_USED.getId());
//        couponLoggerDO.setUserId(activityParamsRequest.getUserId());
//        List<PromotionCouponLoggerDO> promotionCouponLoggerDOS = promotionCouponLoggerMapper.findAll(couponLoggerDO);
//        //无则返回空
//        if (CollectionUtil.isEmpty(promotionCouponLoggerDOS)) {
//            PageBean<PromotionCouponLoggerListPostResponseDTO> res = new PageBean<>(new ArrayList<>());
//            res.setSize(activityParamsRequest.getSize());
//            return res;
//        }
//        //优惠券id
//        List<Long> couponIdList = promotionCouponLoggerDOS.stream()
//                .map(PromotionCouponLoggerDO::getCouponId)
//                .collect(Collectors.toList());
//        Map<Long, List<PromotionCouponLoggerDO>> longListMap = promotionCouponLoggerDOS.stream().collect(Collectors.groupingBy(PromotionCouponLoggerDO::getCouponId));
//        //根据id查寻优惠券，无则返回null
//        List<PromotionCouponDO> couponDoList = promotionCouponMapper.selectBatchIds(couponIdList);
//
//        if (CollectionUtil.isEmpty(couponDoList)) {
//            PageBean<PromotionCouponLoggerListPostResponseDTO> res = new PageBean<>(new ArrayList<>());
//            res.setSize(activityParamsRequest.getSize());
//            return res;
//        }
//        //创建优惠券对应商品的缓存
//        Map<PromotionCouponDO, List<ActivityCommodityDTO>> cacheMap = new HashMap<>();
//        couponDoList.forEach(coupon -> {
//            //获取商品限制
//            CouponLimitDTO couponLimitDTO = JSON.parseObject(coupon.getLimits(), CouponLimitDTO.class);
//            ComdityLimitDTO comdityLimitDTO = couponLimitDTO.getCommodity();
//            ActivityConfigDTO activityConfigDTO = new ActivityConfigDTO();
//            activityConfigDTO.setCommodityList(comdityLimitDTO);
//
//            List<ActivityCommodityDTO> list = new ArrayList<>();
//            for (ActivityCommodityDTO sku : activityCommodityDTOS) {
//                ActivityParamsDTO activityParamsDTO = new ActivityParamsDTO();
//                activityParamsDTO.setCommoditie(sku);
//                //判断商品限制，被限制则剔除商品
//                Context context = new Context(activityConfigDTO, activityParamsDTO);
//                boolean result = context.initCouponCommodityLimit().calculateLimit();
//                if (!result) {
//                    continue;
//                }
//                //未被限制的加入集合
//                list.add(sku);
//            }
//
//            //优惠券这边也做一次移除
//            boolean couponLimitFlag = true;
//            //[{"id": "couponFlag", "flag": "==", "value": "false"}]
//            List<BaseActivityDTO> couponLimit = Optional.ofNullable(couponLimitDTO.getCouponLimit()).orElse(ImmutableList.of());
//            for (BaseActivityDTO baseActivityDTO : couponLimit) {
//                if (PATemplateCouponEnum.COUPONFLAG.getId().equals(baseActivityDTO.getId())) {
//                    String value = baseActivityDTO.getValue();
//                    if (!COUPON_FLAG.equals(value)) {
//                        couponLimitFlag = Boolean.FALSE;
//                    }
//                }
//            }
//
//            if (CollectionUtil.isNotEmpty(list) && couponLimitFlag) {
//                cacheMap.put(coupon, list);
//            }
//        });
//        //根据优惠券条件筛选金额
//        Map<Long, PromotionCouponDO> couponMap = new HashMap<>(cacheMap.size());
//        List<PromotionCouponDO> list = new ArrayList<>();
//        cacheMap.forEach((k, v) -> {
//            BigDecimal totalMoney = v.stream().map(this::calculateTotalMoney).reduce(new BigDecimal("0"), BigDecimal::add);
//            if (totalMoney.compareTo(k.getCondition()) >= 0) {
//                list.add(k);
//                couponMap.put(k.getId(), k);
//            }
//        });
//        if (CollectionUtil.isEmpty(list)) {
//            PageBean<PromotionCouponLoggerListPostResponseDTO> res = new PageBean<>(new ArrayList<>());
//            res.setSize(activityParamsRequest.getSize());
//            return res;
//        }
//        List<PromotionCouponLoggerListPostResponseDTO> dtoList = new ArrayList<>();
//        list.forEach(e -> {
//            List<PromotionCouponLoggerDO> dos = longListMap.get(e.getId());
//            dos.forEach(po -> {
//                PromotionCouponLoggerListPostResponseDTO dto = new PromotionCouponLoggerListPostResponseDTO();
//                BeanPowerHelper.mapPartOverrider(po, dto);
//                dto.setCouponDto(couponMap.get(dto.getCouponId()).clone(CouponResponseDTO.class));
//                dtoList.add(dto);
//            });
//        });
//        List<PromotionCouponLoggerListPostResponseDTO> dtos = dtoList.stream()
//                .filter(e -> {
//                    if (null == e.getStartTime() && null == e.getEndTime()) {
//                        return true;
//                    } else {
//                        Date now = new Date();
//                        return now.after(e.getStartTime()) && now.before(e.getEndTime());
//                    }
//                })
//                .sorted(Comparator.comparing(PromotionCouponLoggerListPostResponseDTO::getUpdatedTime).reversed())
//                .collect(Collectors.toList());
//        PageUtil<PromotionCouponLoggerListPostResponseDTO> pageUtil = new PageUtil<>();
//        return pageUtil.pageUtil(activityParamsRequest.getPage(), activityParamsRequest.getSize(), dtos);
//    }
//
//
//    private BigDecimal calculateTotalMoney(ActivityCommodityDTO sku) {
//        return Arith.transformToBigDecimal(sku.getDetailPrice().multiply(new BigDecimal(sku.getSkuAmount())), 2);
//    }
//
//    private ActivityParamsDTO getActivityParams(ActivityCommodityDTO commodityDTO, CommodityActivityRequest commodityActivityRequest, PromotionActivityDO promotionActivityDo, Map<Long, List<PromotionActivityLoggerDO>> activityLoggerMap) {
//        List<PromotionActivityLoggerDO> loggerList = activityLoggerMap.get(promotionActivityDo.getId());
//        return ActivityParamsConverter.converter(commodityDTO, commodityActivityRequest, promotionActivityDo, loggerList);
//    }
//
//
//    private PromotionCouponLoggerDO setPromotionCouponLoggerDo(CouponActivityParamsRequest activityRequestParams) {
//        PromotionCouponLoggerDO promotionCouponLoggerDo = new PromotionCouponLoggerDO();
//        promotionCouponLoggerDo.setCouponId(activityRequestParams.getCouponId());
//        promotionCouponLoggerDo.setReleaseId(activityRequestParams.getReleaseId());
//        promotionCouponLoggerDo.setReleaseType(activityRequestParams.getReleaseType());
//        promotionCouponLoggerDo.setAmount(1);
//        promotionCouponLoggerDo.setUserId(activityRequestParams.getUserId());
//        promotionCouponLoggerDo.setStatus(PromotionCouponLoggerStatusEnums.UN_USED.getId());
//        promotionCouponLoggerDo.setUserType(Integer.valueOf(activityRequestParams.getUserType()));
//        promotionCouponLoggerDo.setTenantId(activityRequestParams.getTenantId());
//        promotionCouponLoggerDo.setAppId(activityRequestParams.getAppId());
//        promotionCouponLoggerDo.setCode(DistributeIdUtil.generateId());
//        Date now = new Date();
//        promotionCouponLoggerDo.setCreatedTime(now);
//        promotionCouponLoggerDo.setReceiveTime(now);
//        promotionCouponLoggerDo.setUpdatedTime(now);
//        return promotionCouponLoggerDo;
//    }
//
//
//    private ActivityParamsDTO getActivityParams(ActivityCommodityDTO commodity, ActivityParamsRequest activityParamsRequest,
//                                                PromotionActivityDO promotionActivityDo,
//                                                Map<Long, List<PromotionActivityLoggerDO>> promotionActivityLoggerDoList) {
//        List<PromotionActivityLoggerDO> loggerList = promotionActivityLoggerDoList.get(promotionActivityDo.getId());
////        //活动今日总参与次数
////        long activityId = promotionActivityDo.getId();
//        return ActivityParamsConverter.converter(commodity, activityParamsRequest, promotionActivityDo, loggerList);
//    }
//
//
//    private ActivityConfigDTO getActivityConfig(PromotionActivityDO promotionActivityDo,
//                                                Map<Long, List<PromotionStrategyDO>> activityStrategyMap,
//                                                Map<Long, List<PromotionActivityLimitDO>> activityLimitMap) {
//
//        Long activityId = promotionActivityDo.getId();
//        List<PromotionStrategyDO> strategyList = activityStrategyMap.get(activityId);
//        List<PromotionActivityLimitDO> limitList = activityLimitMap.get(activityId);
//
//
//        return ActivityConfigConverter.converter(promotionActivityDo, strategyList, limitList);
//    }
//
//    private List<ActivityResponseParamsDTO> initResponseList(List<ActivityCommodityDTO> commoditiesList,
//                                                             Map<Long, List<PromotionActivityDO>> ckuActivityMap) {
//        List<ActivityResponseParamsDTO> resultList = new ArrayList<>();
//        ckuActivityMap.forEach((upId, activityDoList) -> {
//            ActivityCommodityDTO activityCommodityDTO = CollectionsUtil
//                    .findOneInList(commoditiesList,
//                            val -> val.getUpId().equals(upId));
//            if (null == activityCommodityDTO) {
//                return;
//            }
//            // 商品对应的活动
//            PromotionActivityDO promotionActivityDo = null;
//            // 如果用户有指定活动 进行匹配
//            if (null != activityCommodityDTO.getActivityId()) {
//                promotionActivityDo = CollectionsUtil
//                        .findOneInList(activityDoList, val -> val.getId().equals(activityCommodityDTO.getActivityId()));
//            }
//            // 匹配不成功 给默认的
//            if (promotionActivityDo == null) {
//                //activityDoList.sort(Comparator.comparing(PromotionActivityDO::getStartTime));
//                //优先秒杀活动，再按开始时间升序
//                activityDoList = activityDoList.stream().sorted(((Comparator<PromotionActivityDO>) (act1, act2) -> {
//                    int sort1 = StrategyGroupEnum.getById(String.valueOf(act1.getPaTemplateId())).getSort();
//                    int sort2 = StrategyGroupEnum.getById(String.valueOf(act2.getPaTemplateId())).getSort();
//                    return sort1 - sort2;
//                }).thenComparing(PromotionActivityDO::getStartTime)).collect(Collectors.toList());
//
////                // 预售活动优先级比其他活动高
////                for (PromotionActivityDO activityDO : activityDoList) {
////                    if (activityDO.getPaTemplateId().equals(Integer.valueOf(StrategyGroupEnum.YS_G.getId()))) {
////                        promotionActivityDo = activityDO;
////                        break;
////                    }
////                }
////                if (Objects.isNull(promotionActivityDo)) {
////                    promotionActivityDo = activityDoList.get(0);
////                }
//                promotionActivityDo = activityDoList.get(0);
//
//            }
//            Long activityDoId = promotionActivityDo.getId();
//            // 判断改活动是否已经创建
//            ActivityResponseParamsDTO activityResponseParamsDTO = CollectionsUtil
//                    .findOneInList(resultList, val -> val.getActivityId().equals(activityDoId));
//            if (null == activityResponseParamsDTO) {
//                ActivityResponseParamsDTO arp = ActivityResponseParamsConverter.converter(promotionActivityDo);
//                arp.getActivityCommodityDTOList().add(activityCommodityDTO);
//                resultList.add(arp);
//            }
//            else {
//                activityResponseParamsDTO.getActivityCommodityDTOList().add(activityCommodityDTO);
//            }
//
//        });
//        return resultList;
//    }
//
//    private Map<Long, List<PromotionActivityLimitDO>> getAllPromotionActivityLimitList(List<PromotionActivityDO> promotionActivityDOList) {
//        List<Long> ids = promotionActivityDOList.stream()
//                .map(SuperEntity::getId)
//                .distinct()
//                .collect(Collectors.toList());
//        List<PromotionActivityLimitDO> doList = activityCacheManager.selectListByActivityId(ids);
//
//        return doList.stream()
//                .collect(Collectors.groupingBy(PromotionActivityLimitDO::getActivityId));
//    }
//
//    private Map<Long, List<PromotionActivityLoggerDO>> getAllPromotionActivityLoggerList(Long userId, String userType,
//                                                                                         List<PromotionActivityDO> promotionActivityDOList) {
//        List<Long> collect = promotionActivityDOList.stream()
//                .map(SuperEntity::getId)
//                .distinct()
//                .collect(Collectors.toList());
//        Integer integer = null;
//        if (Objects.nonNull(userType)) {
//            integer = Integer.valueOf(userType);
//        }
//        List<PromotionActivityLoggerDO> doList = promotionActivityLoggerMapper.selectListByActivityId(collect, userId, integer);
//        return doList.stream()
//                .collect(Collectors.groupingBy(PromotionActivityLoggerDO::getActivityId));
//    }
//
//    private Map<Long, List<PromotionStrategyDO>> getAllPromotionStrategyMap(List<PromotionActivityDO> promotionActivityDOList) {
//        List<Long> ids = promotionActivityDOList.stream()
//                .map(SuperEntity::getId)
//                .distinct()
//                .collect(Collectors.toList());
//        List<PromotionStrategyDO> doList = activityCacheManager.selectStrategyByActivityList(ids);
//        return doList.stream()
//                .collect(Collectors.groupingBy(PromotionStrategyDO::getActivityId));
//
//
//    }
//
//    public Map<Long, Integer> getDayNumCount(Long userId, List<PromotionActivityDO> promotionActivityDOList) {
//        Map<Long, Integer> mapNumCount = Maps.newHashMap();
//        for (PromotionActivityDO activityDo : promotionActivityDOList) {
//            Long activityId = activityDo.getId();
//            PromotionActivityLoggerDO loggerDo = new PromotionActivityLoggerDO();
//            loggerDo.setActivityId(activityId);
//            loggerDo.setUserId(userId);
//            QueryWrapper<PromotionActivityLoggerDO> var1 = new QueryWrapper<PromotionActivityLoggerDO>();
//            var1.setEntity(loggerDo);
//            var1.lt("updated_time", new Date());
//            var1.gt("updated_time", DateUtils.getCurrenDate());
//            Integer dayNum = promotionActivityLoggerMapper.selectCount(var1);
//            mapNumCount.put(activityId, dayNum);
//        }
//        return mapNumCount;
//    }
//
//    public Map<Long, Integer> getTotalNumCount(Long userId, List<PromotionActivityDO> promotionActivityDOList) {
//        Map<Long, Integer> mapNumCount = Maps.newHashMap();
//        for (PromotionActivityDO activityDo : promotionActivityDOList) {
//            Long activityId = activityDo.getId();
//            PromotionActivityLoggerDO loggerDo = new PromotionActivityLoggerDO();
//            loggerDo.setActivityId(activityId);
//            loggerDo.setUserId(userId);
//            QueryWrapper<PromotionActivityLoggerDO> var1 = new QueryWrapper<PromotionActivityLoggerDO>();
//            var1.setEntity(loggerDo);
//            Integer totalNumCount = promotionActivityLoggerMapper.selectCount(var1);
//            mapNumCount.put(activityId, totalNumCount);
//        }
//        return mapNumCount;
//    }
//
//    @Override
//    public ComdityLimitDTO returnCouponCommodityLimit(Long couponId) {
//        PromotionCouponDO promotionCouponDO = promotionCouponMapper.selectById(couponId);
//        if (null == promotionCouponDO || PromotionCouponStatusEnums.FORBIDDEN.getId().equals(promotionCouponDO.getStatus())) {
//            throw new ApplicationException("优惠券不可用或者不存在！");
//        }
//        CouponLimitDTO couponLimitDTO = JSON.parseObject(promotionCouponDO.getLimits(), CouponLimitDTO.class);
//        return couponLimitDTO.getCommodity();
//    }
//
//    @Override
//    public Integer getJFDHTimes(Long appId, String tenantId, Long userId, String userType) {
//        PromotionActivityLoggerDO activityLoggerDO = new PromotionActivityLoggerDO();
//        activityLoggerDO.setAppId(appId);
//        activityLoggerDO.setTenantId(tenantId);
//        activityLoggerDO.setUserId(userId);
//        activityLoggerDO.setUserType(Integer.valueOf(userType));
//        List<PromotionActivityLoggerDO> doList = promotionActivityLoggerMapper.selectByAll(activityLoggerDO);
//        if (CollectionUtil.isEmpty(doList)) {
//            return 0;
//        }
//        List<PromotionActivityLoggerDO> doList1 = doList.stream().filter(e -> {
//            PromotionActivityDO promotionActivityDO = promotionActivityMapper.selectById(e.getActivityId());
//            if (null == promotionActivityDO) {
//                return false;
//            }
//            return StrategyGroupEnum.JFDH_G.getId().equals(promotionActivityDO.getPaTemplateId().toString());
//        }).collect(Collectors.toList());
//        if (CollectionUtil.isEmpty(doList1)) {
//            return 0;
//        }
//        return doList1.size();
//    }
//
//    @Override
//    public ShoppingCartShopAspectVO shoppingCartShopAspect(ActivityParamsRequest activityParamsRequest) {
//
//        //返回数据定义
//        ShoppingCartShopAspectVO responseDTO = new ShoppingCartShopAspectVO();
//        List<ShoppingCartShopAspectVO.ShopAvtivity> shopAvtivityList = new ArrayList<>();
//
//        // 调用旧的购物车计算
//        ShoppingCartVO calculate = this.calculate(activityParamsRequest);
//        // 有活动的商品
//        List<ActivityResponseParamsDTO> activityList = calculate.getActivityList();
//        // 没有活动的商品
//        List<ActivityCommodityDTO> notActivityList = calculate.getActivityCommodityDTOList();
//
//        // 没有活动的商品按店铺分组
//        Map<Long, List<ActivityCommodityDTO>> notActivityShopCommodity =
//                notActivityList
//                        .stream()
//                        .collect(Collectors.groupingBy(ActivityCommodityDTO::getShopId));
//
//        // 没有活动的商品组装进店铺
//        notActivityShopCommodity.forEach((shopId, commodityList) -> {
//            ShoppingCartShopAspectVO.ShopAvtivity shopAvtivity = new ShoppingCartShopAspectVO.ShopAvtivity();
//            shopAvtivity.setNotActivityCommodityDTOList(commodityList);
//            shopAvtivity.setShopId(shopId);
//            shopAvtivity.setActivityList(new ArrayList<>());
//            shopAvtivityList.add(shopAvtivity);
//        });
//
//        // 有活动的商品组装
//        activityList.forEach(activityCommodity -> activityCommodity
//                .getActivityCommodityDTOList()
//                .stream()
//                .collect(Collectors.groupingBy(ActivityCommodityDTO::getShopId))
//                .forEach((shop, commodity) -> {
//                    ActivityResponseParamsDTO tempClone = activityCommodity.clone(ActivityResponseParamsDTO.class, CloneDirection.OPPOSITE);
//                    tempClone.getActivityCommodityDTOList().clear();
//                    tempClone.getActivityCommodityDTOList().addAll(commodity);
//                    // 门店已经存在
//                    if (shopAvtivityList.stream().anyMatch(val -> val.getShopId().equals(shop))) {
//                        shopAvtivityList
//                                .stream()
//                                .filter(val -> val.getShopId().equals(shop))
//                                .forEach(val -> val.getActivityList().add(tempClone));
//
//                    } else {
//                        // 不存在 新增
//                        ShoppingCartShopAspectVO.ShopAvtivity shopAvtivity = new ShoppingCartShopAspectVO.ShopAvtivity();
//                        shopAvtivity.setNotActivityCommodityDTOList(new ArrayList<>());
//                        shopAvtivity.setShopId(shop);
//                        shopAvtivity.setActivityList(new ArrayList<>());
//                        shopAvtivity.getActivityList().add(tempClone);
//                        shopAvtivityList.add(shopAvtivity);
//                    }
//
//                }));
//        responseDTO.setOrderDetailPrice(calculate.getOrderDetailPrice());
//        responseDTO.setOrderDiscountsPrice(calculate.getOrderDiscountsPrice());
//        responseDTO.setShopAvtivityList(shopAvtivityList);
//        return responseDTO;
//    }
//
//}
