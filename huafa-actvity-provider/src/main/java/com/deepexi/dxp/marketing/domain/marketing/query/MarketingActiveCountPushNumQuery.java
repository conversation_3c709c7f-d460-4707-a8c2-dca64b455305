package com.deepexi.dxp.marketing.domain.marketing.query;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description：
 * @author: ch<PERSON><PERSON><PERSON>
 * @version: 1.0.0
 * @date: 2021-03-30 19:52
 */
@Data
public class MarketingActiveCountPushNumQuery extends SuperQuery {

    @ApiModelProperty("营销任务映射表ID列表")
    private List<Long> taskMappingIdList;
}
