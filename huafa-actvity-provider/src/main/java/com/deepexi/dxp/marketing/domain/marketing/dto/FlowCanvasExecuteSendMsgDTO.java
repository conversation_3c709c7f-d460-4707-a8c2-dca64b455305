package com.deepexi.dxp.marketing.domain.marketing.dto;
import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import lombok.Data;


/**
 * 流程画布-执行-发送信息
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-07 16:03
 */
@Data
public class FlowCanvasExecuteSendMsgDTO extends SuperDTO {
    /**
     * 画布ID
     */
    private Long flowId;

    /**
     * 画布节点ID
     */
    private String nodeId;

    /**
     * 发送模板id
     */
    private Long sendTemplateId;

    /**
     * 发送规则渠道
     */
    private Integer sendRuleChannel;

    /**
     * 发送规则类型
     */
    private Integer sendRuleType;

    /**
     * 优先级：越小优先级越高
     */
    private Integer priority;

    /**
     * 是否有资源 ： 1：有 2：无
     */
    private Integer resourceAvailable;

    /**
     * 资源id
     */
    private Long resourceId;

    /**
     * 资源编码
     */
    private String resourceCode;

    /**
     * 资源渠道
     */
    private String resourceChannel;

    /**
     * 资源名称
     */
    private String resourceName;

    /**
     * 资源信息
     */
    private String resourceInfo;

    /**
     * 资源类型编码
     */
    private String resourceTypeCode;

    /**
     * 资源类型名称
     */
    private String resourceTypeName;

    /**
     * 资源类别编码
     */
    private String resourceCategoryCode;

    /**
     * 资源类别名称
     */
    private String resourceCategoryName;

    /**
     * 资源url地址
     */
    private String resourceUrl;
}
