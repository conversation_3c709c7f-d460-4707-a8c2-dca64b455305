package com.deepexi.dxp.marketing.domain.marketing.dto;

import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * KPI指标Druid查询
 *
 * <AUTHOR>
 * @since 2020年05月21日 15:40
 */
@Data
@ApiModel
public class MarketingKpiQueryStatementDTO extends SuperDTO {

    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String code;

    /**
     * 指标名称
     */
    @ApiModelProperty(value = "指标名称")
    private String name;

    /**
     * 数据源类型（1-druid、2-mysql）
     */
    @ApiModelProperty(value = "数据源类型（1-druid、2-mysql）")
    private Integer datasourceType;

    /**
     * 查询语句
     */
    @ApiModelProperty(value = "查询语句")
    private String statement;
}
