package com.deepexi.dxp.marketing.domain.marketing.request;

import com.deepexi.dxp.marketing.common.base.request.SuperRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/30
 */
@Data
@ApiModel
public class HaoWanUserInfoUpdateRequest extends SuperRequest {
    @ApiModelProperty("主键id")
    @NotNull(message = "id不能为空")
    private Long id;
    /**
     * 24好玩的用户appid
     */
    @ApiModelProperty("24好玩的用户appid")
    private String haowanAppid;

    /**
     * 24好玩的用户appsecret
     */
    @ApiModelProperty("24好玩的用户appsecret")
    private String haowanAppsecret;

    /**
     * 24好玩的用户uid
     */
    @ApiModelProperty("24好玩的用户uid")
    private String haowanUid;

    /**
     * 过期时间
     */
    @ApiModelProperty("过期时间")
    private Date expirationTime;

}
