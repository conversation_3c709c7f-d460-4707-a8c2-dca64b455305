package com.deepexi.dxp.marketing.service.specify.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.deepexi.common.extension.AppRuntimeEnv;
import com.deepexi.dxp.marketing.common.base.utils.CommonExceptionCode;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.config.HuafaConstantConfig;
import com.deepexi.dxp.marketing.constant.CommonConstant;
import com.deepexi.dxp.marketing.constant.PromotionActivityConstant;
import com.deepexi.dxp.marketing.constant.RedisConstants;
import com.deepexi.dxp.marketing.converter.ActivityExtConverter;
import com.deepexi.dxp.marketing.converter.ActivityInfoConverter;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityFissionLogQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.CustomerFeedbackQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.PartakeLogQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.UserProjectQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.CancelOrderRequest;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.*;
import com.deepexi.dxp.marketing.domain.marketing.response.*;
import com.deepexi.dxp.marketing.domain.promotion.dto.CommunityActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.CreateSignCodeDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.OneCodeAuthDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.PromotionActivityListPostVO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityConfigDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.ActivityParamsDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.*;
import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityPageQuery;
import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityQuery;
import com.deepexi.dxp.marketing.domain.promotion.query.specify.PromotionHisResourceQuery;
import com.deepexi.dxp.marketing.domain.promotion.request.ActivityAuditRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.CancelActivityRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.PromotionActivityCreateRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.PromotionActivityUpdateRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityCreatePostRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityUpdatePostRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.specify.ActivityPromotionChannelCreateRequestDTO;
import com.deepexi.dxp.marketing.domain.specify.dto.ActivityFormFeedbackDTO;
import com.deepexi.dxp.marketing.enums.activity.limit.PATemplateBaseEnum;
import com.deepexi.dxp.marketing.enums.activity.strategy.DeliveryChannelEnum;
import com.deepexi.dxp.marketing.enums.activity.strategy.FeedbackActivityTypeEnum;
import com.deepexi.dxp.marketing.enums.activity.strategy.StrategyGroupEnum;
import com.deepexi.dxp.marketing.enums.common.AssociationTypeEnum;
import com.deepexi.dxp.marketing.enums.coupon.WhetherEnum;
import com.deepexi.dxp.marketing.enums.resource.ActivityStatusEnum;
import com.deepexi.dxp.marketing.enums.specify.*;
import com.deepexi.dxp.marketing.enums.status.ActivityAuditStatusEnum;
import com.deepexi.dxp.marketing.enums.status.ActivityOnOffShelfStatus;
import com.deepexi.dxp.marketing.extension.HuafaRuntimeEnv;
import com.deepexi.dxp.marketing.manager.promotion.CalculateResult;
import com.deepexi.dxp.marketing.manager.promotion.Context;
import com.deepexi.dxp.marketing.manager.promotion.PromotionActivityManager;
import com.deepexi.dxp.marketing.service.promotion.PromotionActivityMiddleService;
import com.deepexi.dxp.marketing.service.specify.*;
import com.deepexi.dxp.marketing.utils.*;
import com.deepexi.dxp.middle.marketing.common.base.SuperEntity;
import com.deepexi.dxp.middle.marketing.domain.dto.MarketingKpiRouteMapDTO;
import com.deepexi.dxp.middle.promotion.common.base.SuperExtEntity;
import com.deepexi.dxp.middle.promotion.converter.ActivityConfigConverter;
import com.deepexi.dxp.middle.promotion.converter.specify.BargainingConverter;
import com.deepexi.dxp.middle.promotion.converter.specify.LuckyDrawConverter;
import com.deepexi.dxp.middle.promotion.dao.PromotionActivityDAO;
import com.deepexi.dxp.middle.promotion.dao.PromotionActivityLimitDAO;
import com.deepexi.dxp.middle.promotion.dao.impl.specify.PromotionActivityAssociationService;
import com.deepexi.dxp.middle.promotion.dao.specify.*;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityLimitDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.*;
import com.deepexi.util.*;
import com.deepexi.util.domain.entity.BaseEntity;
import com.deepexi.util.domain.entity.BaseExtEntity;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import com.deepexi.util.pojo.CloneDirection;
import com.deepexi.util.pojo.ObjectCloneUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PromotionActivityServiceImpl implements PromotionActivityService {

    private static final Integer RETRY_TIME = 3;

    @Autowired
    private PromotionActivityDAO promotionActivityDAO;

    @Autowired
    private PromotionHisResourceDAO promotionHisResourceDAO;

    @Autowired
    private PromotionResourceDAO promotionResourceDAO;

    @Autowired
    private PromotionActivityLimitDAO promotionActivityLimitDAO;

    @Autowired
    private ActivityPageDAO activityPageDAO;

    @Autowired
    private ActivityPageShareDAO activityPageShareDAO;

    @Resource
    public ActivityVerifyDAO activityVerifyDAO;

    @Resource
    private ActivityEvaluationDAO activityEvaluationDAO;

    @Autowired
    private ActivityParticipationDAO activityParticipationDAO;

    @Autowired
    private PromotionActivityMiddleService promotionActivityMiddleService;

    @Autowired
    private MarketingKpiRouteMapService marketingKpiRouteMapService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private PromotionActivityManager promotionActivityManager;

    @Autowired
    private CustomerFeedbackDAO customerFeedbackDAO;

    @Autowired
    private ActivityPartakeLogDAO activityPartakeLogDAO;

    @Autowired
    private GenerateIdUtil generateIdUtil;

    @Autowired
    private ActivityOrderService activityOrderService;

    @Value("${activity.default-kpi-path}")
    private Integer defaultKpiPath;

    @Autowired
    private WxPayService wxPayService;

    @Autowired
    private ActivityOrderDAO activityOrderDAO;

    @Resource
    public ActivityUserRelatedDAO activityUserRelatedDAO;
    @Resource
    public LuckyDrawService luckyDrawService;
    @Resource
    public AssistActService assistActService;

    @Autowired
    private ActivityFissionLogDAO activityFissionLogDAO;

    @Autowired
    private HuafaConstantConfig huafaConstantConfig;

    @Autowired
    private AuthUtil authUtil;

    @Autowired
    private ActivityFissionAssistResourceDAO activityFissionAssistResourceDAO;

    @Resource
    public AsynchronousService asynchronousService;

    @Autowired
    private ActivityFissionAssistLogDAO activityFissionAssistLogDAO;
    @Resource
    private UserProjectService userProjectService;
    @Resource
    private InteractionCenterService interactionCenterService;
    @Resource
    private PromotionMarathonService promotionMarathonService;
    @Autowired
    private IncentiveService incentiveService;
    @Autowired
    private PhoneService phoneService;

    @Autowired
    private ActivityParticipationService activityParticipationService;

    @Resource
    private MiniProgramService miniProgramService;
    @Resource
    private CardCollectingService cardCollectingService;

    @Resource
    private GoodAnchorService goodAnchorService;
    @Resource
    private SignActService signActService;
    @Resource
    private SignUpService signUpService;
    @Resource
    private LocationSignActService locationSignActService;

    @Resource
    private HuaFaHmacAuthUtil huaFaHmacAuthUtil;
    @Resource
    private PromotionActivityAssociationService associationService;
    @Resource
    private ActivityPromotionChannelService activityPromotionChannelService;

    @Override
    public PageBean<PromotionActivityVO> findPage(PromotionActivityQuery query) {
        //通过用户id获取用户可看活动ids
        List<Long> activityIds = this.getActivityIdsByUserId(query.getUserId());

        if (CollectionUtil.isEmpty(activityIds)) {
            return new PageBean<PromotionActivityVO>();
        }
        query.setIds(activityIds);
        IPage<PromotionActivityResponseDTO> page = promotionActivityDAO.pageListOrderByCreateTime(query);
        PageBean<PromotionActivityResponseDTO> dtoPageBean = new PageBean<>(page);
        List<PromotionActivityResponseDTO> records = page.getRecords();

        List<PromotionActivityVO> list = Lists.newArrayList();

        //扩展字段
        if (CollectionUtil.isNotEmpty(records)) {
            List<Long> activityIdList = records.stream().map(PromotionActivityResponseDTO::getId).distinct().collect(Collectors.toList());
            QueryWrapper<PromotionHisResourceDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(PromotionHisResourceDO::getActivityId, activityIdList);
            List<PromotionHisResourceDO> promotionHisRsourceDOList = promotionHisResourceDAO.list(queryWrapper);

            Map<Long, Set<String>> projectNameMap = activityParticipationDAO.getProjectNamesByActivityId(activityIdList);


            Map<Long, List<PromotionHisResourceDO>> hisResourceMap = promotionHisRsourceDOList.stream()
                    .collect(Collectors.groupingBy(PromotionHisResourceDO::getActivityId));

            //提取裂变类活动ids
            List<Long> fissionActivityIds = this.getFissionActivityIds(records);

            //获取裂变活动的活动规则
            Map<Long, List<PromotionActivityLimitDO>> promotionActivityLimitMap = getActivityLimitList(fissionActivityIds);

            //裂变类活动人数统计
            Map<Long, FissionActivityStatisticsVO> fissionActivityStatisticsMap = getFissionActivityStatistics(fissionActivityIds);

            records.forEach(record -> {
                PromotionActivityVO clone = record.clone(PromotionActivityVO.class);

                record.getExt();
                String sceneCode = MapUtils.getString(record.getExt(), "sceneCode");
                Integer orgType = MapUtils.getInteger(record.getExt(), "orgType");
                clone.setSceneCode(sceneCode);
                clone.setOrgType(orgType);
                if (StringUtil.isNotEmpty(sceneCode) && orgType == null) {
                    clone.setOrgType(MiniOrgTypeEnum.NATIONAL.getId());//默认为全国
                }
                //历史数据没有上下架状态的
                if (record.getUpperStatus() == null) {
                    String deliveryChannel = MapUtils.getString(record.getExt(), "deliveryChannel");
                    clone.setUpperStatus(StringUtil.isNotEmpty(deliveryChannel) ? Integer.valueOf(ActivityOnOffShelfStatus.ON.getId()) : Integer.valueOf(ActivityOnOffShelfStatus.OFF.getId()));
                }

                Integer projectType = Optional.ofNullable(record.getExt().get("projectType")).map(c -> c != null && StringUtil.isNotEmpty(c.toString()) ? Integer.parseInt(c.toString()) : 0).orElse(null);

                if (ResourcesAttributeEnum.NATIONAL.getId().equals(projectType)) {
                    clone.setProjects("全国项目");
                } else {
                    if (projectNameMap != null && projectNameMap.size() > 0 && CollectionUtil.isNotEmpty(projectNameMap.get(clone.getId()))) {
                        Set<String> projectNameList = projectNameMap.get(clone.getId());
                        clone.setProjects(StringUtils.join(projectNameList.stream().distinct().collect(Collectors.toList()), ","));
                    }
                }

                List<PromotionHisResourceDO> promotionHisResourceList = hisResourceMap.get(record.getId());
                if (CollectionUtil.isNotEmpty(promotionHisResourceList)) {
                    List<Integer> receiveModeList = promotionHisResourceList.stream().map(PromotionHisResourceDO::getReceiveMode).distinct().collect(Collectors.toList());
                    Collections.sort(receiveModeList);
                    clone.setReceiveModeList(receiveModeList);

                    //砍价活动列表所需信息
                    PromotionHisResourceDO promotionHisResourceDO = promotionHisResourceList.get(0);
                    clone.setUrl(promotionHisResourceDO.getUrl());
                    clone.setSellingPrice(String.valueOf(promotionHisResourceDO.getPurchasePrice()));
                }

                //裂变类活动数据拼装
                this.FissionActivityConverter(clone, promotionActivityLimitMap, fissionActivityStatisticsMap);

                //表单属性相关
                this.formFeedbackInfo(clone);

                list.add(clone);
            });
        }
        PageBean<PromotionActivityVO> pageBean = new PageBean<PromotionActivityVO>();
        pageBean.setContent(list);
        pageBean.setSize(dtoPageBean.getSize());
        pageBean.setTotalElements(dtoPageBean.getTotalElements());
        pageBean.setNumber(dtoPageBean.getNumber());
        pageBean.setTotalPages(dtoPageBean.getTotalPages());
        pageBean.setNumberOfElements(dtoPageBean.getNumberOfElements());
        return pageBean;
    }

    @Override
    public PageBean<PromotionActivityDTO> findWholePageList(PromotionActivityPageQuery query) {
        //如果是社群小程序，需要根据岗位进行数据权限设置
        List<Long> idList = null;
        if (Objects.equals(query.getDeliveryChannel(),DeliveryChannelEnum.SQ.getId())) {
            idList = getActivityIdsByUserIdV2();
        } else {
            idList = this.getActivityIdsByUserId(HuafaRuntimeEnv.getUserId());
        }
        if (CollectionUtil.isEmpty(idList)) {
            return new PageBean<>();
        }
        //设置查询活动ID
        query.setIdList(idList);
        query.setCreatedBy(HuafaRuntimeEnv.getUserId()); // 设置创建人过滤条件
        //查询活动数据
        IPage<PromotionActivityDTO> page = promotionActivityDAO.selectWholePageList(query);
        PageBean<PromotionActivityDTO> pageBean = new PageBean<>(page);
        List<PromotionActivityDTO> activityDTOList = pageBean.getContent();
        fillActivityCommonInfo(activityDTOList);
        //处理扩展字段
        if(CollectionUtil.isNotEmpty(activityDTOList)){
            for(PromotionActivityDTO activityDTO : activityDTOList){
                Map<String, Object> extMap = activityDTO.getExt();
                //场景码，机构组织类型
                String sceneCode = MapUtils.getString(extMap, "sceneCode");
                Integer orgType = MapUtils.getInteger(extMap, "orgType");
                activityDTO.setSceneCode(sceneCode);
                activityDTO.setOrgType(orgType);
                if (StringUtil.isNotEmpty(sceneCode) && orgType == null) {
                    activityDTO.setOrgType(MiniOrgTypeEnum.NATIONAL.getId());//默认为全国
                }
                //投放渠道
                String deliveryChannel = (String)extMap.get("deliveryChannel");
                activityDTO.setDeliveryChannel(deliveryChannel);
                //历史数据没有上下架状态的
                if (activityDTO.getUpperStatus() == null) {
                    activityDTO.setUpperStatus(StringUtil.isNotEmpty(deliveryChannel) ? Integer.valueOf(ActivityOnOffShelfStatus.ON.getId())
                            : Integer.valueOf(ActivityOnOffShelfStatus.OFF.getId()));
                }
            }
        }
        return pageBean;
    }

    /**
     * 获取表单列表反馈数及最新反馈时间
     */
    private void formFeedbackInfo(PromotionActivityVO promotionActivityVO) {
        if (StrategyGroupEnum.HF_FORM_ACT.getId().equals(promotionActivityVO.getPaTemplateId().toString())) {
            ActivityFormFeedbackDO activityFormFeedbackDO = customerFeedbackDAO.getNewByActivityId(promotionActivityVO.getId());
            Integer count = customerFeedbackDAO.getCountByActivityId(promotionActivityVO.getId());
            promotionActivityVO.setFeedbackNum(count);
            if (Objects.nonNull(activityFormFeedbackDO)) {
                promotionActivityVO.setNewFeedBackTime(activityFormFeedbackDO.getCreatedTime());
            }
        }
    }

    private void FissionActivityConverter(PromotionActivityVO clone, Map<Long, List<PromotionActivityLimitDO>> promotionActivityLimitMap,
                                          Map<Long, FissionActivityStatisticsVO> fissionActivityStatisticsMap) {
        if (clone == null) {
            return;
        }

        //裂变类活动统计
        if (fissionActivityStatisticsMap != null && !fissionActivityStatisticsMap.isEmpty()) {
            FissionActivityStatisticsVO fissionActivityStatisticsVO = fissionActivityStatisticsMap.get(clone.getId());
            if (fissionActivityStatisticsVO != null) {
                clone.setSuccessNumber(fissionActivityStatisticsVO.getSuccessNumber() == null ? 0 : fissionActivityStatisticsVO.getSuccessNumber());
                clone.setInitiatorsNumber(fissionActivityStatisticsVO.getInitiatorsNumber() == null ? 0 : fissionActivityStatisticsVO.getInitiatorsNumber());
            }
        }

        //裂变类活动规则详情
        if (promotionActivityLimitMap != null && !promotionActivityLimitMap.isEmpty()) {
            List<PromotionActivityLimitDO> limitDO = promotionActivityLimitMap.get(clone.getId());
            if (limitDO == null) {
                return;
            }

            //砍价活动规则
            if (Objects.equals(String.valueOf(clone.getPaTemplateId()), StrategyGroupEnum.HF_FISSION_REDUCEPRICE_ACT.getId())) {
                List<BaseActivityDTO> numberList = ActivityConfigConverter.getLimitArray(limitDO, PATemplateBaseEnum.BARGAIN.getId(), BaseActivityDTO.class);
                clone.setValidPeriod(LuckyDrawConverter.elLimitConverter(numberList, ActivityTemplateNumberEnum.VALID_PERIOD.getId()));
                clone.setLowestPrice(LuckyDrawConverter.elLimitConverterToBigDecimal(numberList, ActivityTemplateNumberEnum.LOWEST_PRICE.getId()));
                clone.setHelperNumber(LuckyDrawConverter.elLimitConverter(numberList, ActivityTemplateNumberEnum.HELPER_NUMBER.getId()));
            }

            if (Objects.equals(String.valueOf(clone.getPaTemplateId()), StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId())) {
                List<BaseActivityDTO> numberList = ActivityConfigConverter.getLimitArray(limitDO, PATemplateBaseEnum.ASSIST.getId(), BaseActivityDTO.class);
                clone.setValidPeriod(LuckyDrawConverter.elLimitConverter(numberList, ActivityTemplateNumberEnum.VALID_PERIOD.getId()));
                //所需助力人数
                List<PromotionHisResourceDO> fissonResourceList = promotionHisResourceDAO.findByFissonResourceType(clone.getId(), FissonResourceTypeEnum.POWER_LADDER.getId());
                Integer fissionNum = fissonResourceList.stream().map(PromotionHisResourceDO::getFissonCount).reduce(0, Integer::sum);
                clone.setHelperNumber(fissionNum);
            }
        }
    }

    private Map<Long, FissionActivityStatisticsVO> getFissionActivityStatistics(List<Long> fissionActivityIds) {
        if (CollectionUtil.isEmpty(fissionActivityIds)) {
            return Maps.newHashMap();
        }
        List<FissionActivityStatisticsVO> fissionActivityStatisticsVOS = activityPartakeLogDAO.fissionActivityStatistics(fissionActivityIds);
        if (CollectionUtil.isEmpty(fissionActivityStatisticsVOS)) {
            return Maps.newHashMap();
        }
        return fissionActivityStatisticsVOS.stream().collect(Collectors.toMap(FissionActivityStatisticsVO::getActivityId, statistics -> statistics));
    }

    /**
     * 提取裂变类活动ids
     *
     * @param records
     * @return
     */
    private List<Long> getFissionActivityIds(List<PromotionActivityResponseDTO> records) {
        return records
                .stream()
                .filter(e ->
                        String.valueOf(e.getPaTemplateId()).equals(StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId()) ||
                                String.valueOf(e.getPaTemplateId()).equals(StrategyGroupEnum.HF_FISSION_REDUCEPRICE_ACT.getId()) ||
                                String.valueOf(e.getPaTemplateId()).equals(StrategyGroupEnum.HF_CARD_COLLECTING_ACT.getId())
                )
                .map(PromotionActivityResponseDTO::getId)
                .distinct()
                .collect(Collectors.toList());
    }

    private Map<Long, List<PromotionActivityLimitDO>> getActivityLimitList(List<Long> activityIds) {

        if (CollectionUtil.isEmpty(activityIds)) {
            return Maps.newHashMap();
        }
        List<PromotionActivityLimitDO> promotionActivityLimitList = promotionActivityLimitDAO.selectByActivityIds(activityIds);
        if (CollectionUtil.isNotEmpty(promotionActivityLimitList)) {
            return promotionActivityLimitList.stream().collect(Collectors.groupingBy(PromotionActivityLimitDO::getActivityId));
        }
        return Maps.newHashMap();
    }

    @Override
    public List<PromotionActivityVO> list(PromotionActivityQuery query) {
        return promotionActivityDAO.list(query);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(PromotionActivityCreateRequest dto) {

        PromotionActivityCreatePostRequest requestDto = dto.clone(PromotionActivityCreatePostRequest.class, CloneDirection.OPPOSITE);

        String createdBy = requestDto.getCreatedBy();
        //处理默认值
        if (dto.isFromMini()) {
            processMini(requestDto.getExt(),requestDto, null);
            createdBy = requestDto.getUserId();
        }
        //较验重复提交
        this.checkClickRepeat(String.format(RedisConstants.CACHE_PREV_KEY_ACT_CREATE_INFO_REPEAT, createdBy), dto.getUuId());

        processDefaultValue(requestDto);
        //优惠券活动，抽奖活动较验
        this.couponCheck(requestDto.getPrizeList(), requestDto.getPaTemplateId(), requestDto.getEndTime());

        if (requestDto.getExt().get("projectType") == null || StringUtil.isEmpty(requestDto.getExt().get("projectType").toString())) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "适用项目选项不能为空！");
        }
        //项目较验
        Integer projectType = Optional.ofNullable(requestDto.getExt().get("projectType")).map(c -> StringUtil.isNotEmpty(c.toString()) ? Integer.parseInt(c.toString()) : 0).orElse(null);
        this.projectCheck(requestDto.getProjectIds(), requestDto.getPaTemplateId(), projectType);

        //全国项目类型获取全国项目列表数据
        List<ActivityParticipationVO> nationalProjects = this.getNationalProjects(requestDto.getExt(), requestDto.getUserId());
        if (CollectionUtil.isNotEmpty(nationalProjects)) {
            requestDto.setProjectIds(nationalProjects);
        }
        if (CollectionUtil.isEmpty(requestDto.getProjectIds())) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "活动至少关联一个项目");
        }
        //原生创建活动
        Long activityId = promotionActivityMiddleService.create(requestDto);

        /**
         * *************华发创建活动 开始*************
         */
        PromotionActivityDO promotionActivityDO = promotionActivityDAO.getById(activityId);

        //保存适用
        List<ActivityParticipationDO> activityParticipationList = ActivityInfoConverter.participationConverter(requestDto, promotionActivityDO.getId());
        activityParticipationDAO.saveBatch(activityParticipationList);

        //保存活动奖品配置
        List<HisResourceJsonVO> hisResourceJsonList = ObjectCloneUtils.convertList(requestDto.getPrizeList(), HisResourceJsonVO.class);
        this.saveHisResource(hisResourceJsonList, requestDto.getActivityCardList(), promotionActivityDO.getId(), Long.valueOf(promotionActivityDO.getPaTemplateId()));

        //保存界面活动页配置
        ActivityPageDO activityPageDO = ActivityInfoConverter.activityPageConverter(requestDto);
        activityPageDO.setActivityId(promotionActivityDO.getId());
        activityPageDO.setType(1);
        activityPageDAO.save(activityPageDO);

        //保存界面分享页配置
        ActivityPageShareDO activityPageShareDO = ActivityInfoConverter.activityShareConverter(requestDto);
        activityPageShareDO.setActivityId(promotionActivityDO.getId());
        activityPageShareDO.setType(1);
        activityPageShareDAO.save(activityPageShareDO);

        //生成推广二维码
        //activityPromotionChannelService.createDefaults(promotionActivityDO.getId(),ActivityPromotionTypeEnum.ACTIVITY.getType(), promotionActivityDO.getPaTemplateId());

        /**
         * *************华发创建活动 结束*************
         */
        //好主播活动，放到缓存，因为要提前参与活动
        if (Objects.equals(StrategyGroupEnum.HF_GOOD_ANCHOR_ACT.getId(),requestDto.getPaTemplateId().toString())) {
            promotionActivityManager.forceCacheActInfo(activityId);
        }
        //5秒后异步处理场景码和项目是否跳转批量更新
        ThreadPoolUtils.getScheduledExecutor().schedule(() ->{
            //生成场景码
            List<MiniOrgRequestDTO> orgList = ObjectCloneUtils.convertList(requestDto.getProjectIds(), MiniOrgRequestDTO.class);
            miniProgramService.aSynCreateOneCodeScene(promotionActivityDO,MiniOrgTypeEnum.PROJECT.getId(),orgList, ActivityPromotionTypeEnum.ACTIVITY.getType());
            //项目是否跳转批量更新
            activityParticipationService.updateBatchProjectIsJumpable(promotionActivityDO.getId());
        },5,TimeUnit.SECONDS);

        return activityId;
    }

    //创建时处理
    private void processDefaultValue(PromotionActivityCreatePostRequest dto) {
        //如果没有传指标路径，则使用默认
        dto.getExt().putIfAbsent("kpiPath",defaultKpiPath);
        //如果没有传是否展示，则默认展示
        dto.getExt().putIfAbsent("isDisplay", 1);
        //如果是报名活动
        if (StrategyGroupEnum.HF_SIGN_UP_ACT.getId().equals(dto.getPaTemplateId().toString())) {
            signUpService.processDefaultValue(dto);
        }
    }
    //更新活动时处理
    private void processUpdateValue(PromotionActivityUpdateRequest dto) {
        //如果是报名活动
        if (StrategyGroupEnum.HF_SIGN_UP_ACT.getId().equals(dto.getPaTemplateId().toString())) {
            signUpService.processUpdateValue(dto);
        }
    }

    private void resetAuditStatus(ActivityAuditStatusEnum auditStatus, Integer paTemplateId, Map<String, Object> ext) {
        if (StrategyGroupEnum.HF_SIGN_UP_ACT.getId().equals(paTemplateId.toString())) {
            ext.put("auditStatus", auditStatus.getId());
            ext.put("auditor", null);
            ext.put("auditTime", null);
            ext.put("rejectReason", null);
        }
    }

    private List<ActivityParticipationVO> getNationalProjects(Map<String, Object> ext, String userId) {
        ActivityExtVO activityExtVO = ActivityExtConverter.converter(ext);
        if (activityExtVO.getProjectType() != null && activityExtVO.getProjectType().equals(ResourcesAttributeEnum.NATIONAL.getId())) {
            if (userId == null) {
                throw new ApplicationException(CommonExceptionCode.INVALIDATION, "用户id不能为空！");
            }
            UserProjectQuery query = new UserProjectQuery();
            query.setUserId(userId);
            query.setPageNumber(1);
            query.setPageSize(9999);
            List<UserProjectResponseDTO> content = userProjectService.getUserProjectNoLogin(query).getContent();

            return ObjectCloneUtils.convertList(content, ActivityParticipationVO.class);
        }
        return null;
    }

    private void projectCheck(List<ActivityParticipationVO> projectIds, Integer paTemplateId, Integer projectType) {
        if (!StrategyGroupEnum.HF_COUPON_ACT.getId().equals(paTemplateId.toString()) &&
                !StrategyGroupEnum.HF_SECKILL_ACT.getId().equals(paTemplateId.toString()) &&
                CollectionUtil.isEmpty(projectIds) && ObjectUtil.notEqual(projectType, ResourcesAttributeEnum.NATIONAL.getId())
        ) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "项目信息不能为空！");
        }
    }

    /**
     * 较验优惠券资源结束时间不能小于活动结束时间
     */
    private void couponCheck(List<PrizeConfigVO> prizeList, Integer paTemplateId, Date endTime) {
        if (CollectionUtil.isNotEmpty(prizeList) && (StrategyGroupEnum.HF_COUPON_ACT.getId().equals(paTemplateId.toString()) ||
                StrategyGroupEnum.HF_LUCKYDRAW_ACT.getId().equals(paTemplateId.toString()) ||
                StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId().equals(paTemplateId.toString()) ||
                StrategyGroupEnum.HF_FORM_ACT.getId().equals(paTemplateId.toString()) ||
                StrategyGroupEnum.HF_SIGN_ACT.getId().equals(paTemplateId.toString()) ||
                StrategyGroupEnum.HF_CARD_COLLECTING_ACT.getId().equals(paTemplateId.toString()))) {
            //较验资源结束时时间是否在活动结束时间之前
            List<Long> resourceIdList = prizeList.stream().map(PrizeConfigVO::getResourceId).distinct().collect(Collectors.toList());
            QueryWrapper<PromotionResourceDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(PromotionResourceDO::getId, resourceIdList);
            List<PromotionResourceDO> list = promotionResourceDAO.list(queryWrapper);
            if (CollectionUtil.isNotEmpty(list)) {
                list.forEach(item -> {
                    if (PromotionResourceValidTimeTypeEnum.DESIGNATED_TIME.getId().equals(item.getValidTimeType()) && item.getValidEndTime().before(endTime)) {
                        throw new ApplicationException(CommonExceptionCode.INVALIDATION, "活动资源:" + item.getName() + ",结束时间不能小于活动结束时间!");
                    }
                });
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(List<Long> ids) {
        String userId = HuafaRuntimeEnv.getUserId();
        List<PromotionActivityDO> activityIds = promotionActivityDAO.selectBatchIds(ids);
        if (CollectionUtil.isEmpty(activityIds)) {
            throw new ApplicationException("活动不存在或已被删除!");
        }

        List<PromotionActivityDO> noUserCreateList = activityIds.stream().filter(item -> item.getCreatedPerson() != null && !item.getCreatedPerson().equals(userId)).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(noUserCreateList)
                && !authUtil.isAdminPosition()) {
            throw new ApplicationException("您没有权限删除此活动!");
        }
        boolean isStart = activityIds.stream().filter(item -> Integer.parseInt(ActivityStatusEnum.UN_START.getId()) < item.getStatus()).findAny().isPresent();

        if (isStart) {
            throw new ApplicationException("已开始的活动不可以删除!");
        }


        Boolean activity = promotionActivityDAO.deleteBatchIds(ids);
        Boolean limit = promotionActivityLimitDAO.deleteByActivityIds(ids);
        //Boolean participation = activityParticipationDAO.deletedByActivityIds(ids);
        Boolean hisResource = promotionHisResourceDAO.deletedByActivityIds(ids);
        Boolean activityPage = activityPageDAO.deletedByActivityIds(ids);
        Boolean activityPageShare = activityPageShareDAO.deletedByActivityIds(ids);

        return activity && limit
                && hisResource && activityPage && activityPageShare;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateActivityById(PromotionActivityUpdateRequest dto) {

        PromotionActivityListPostVO activityByIdDetail = promotionActivityMiddleService.getActivityById(dto.getId());
        if (activityByIdDetail == null) {
            throw new ApplicationException("查找不到该活动：" + dto.getId());
        }

        //如果是小程序过来的，只能更新报名活动
        if (dto.isFromMini()) {
            processMini(dto.getExt(),null,dto);
        } else {//PC端的默认审核通过
            resetAuditStatus(ActivityAuditStatusEnum.PASS_AUDIT, dto.getPaTemplateId(),dto.getExt());
        }
        if (StringUtil.isNotEmpty(activityByIdDetail.getCreatedPerson())
                && !activityByIdDetail.getCreatedPerson().equals(dto.getUserId())
                && !authUtil.isAdminPosition()) {
            throw new ApplicationException("您没有权限修改此活动：" + dto.getId());
        }

        //判断活动是否为为开始或者正在进行中
        if (!activityByIdDetail.getStatus().equals(Integer.parseInt(ActivityStatusEnum.UN_START.getId())) &&
                        !activityByIdDetail.getStatus().equals(Integer.parseInt(ActivityStatusEnum.IN_PROGRESS.getId()))
        ) {
            throw new ApplicationException("活动已结束，无法编辑");
        }
        //时间较验：只有未开始才进行较验
        if (activityByIdDetail.getStatus() < Integer.parseInt(ActivityStatusEnum.IN_PROGRESS.getId())) {
            this.couponCheck(dto.getPrizeList(), dto.getPaTemplateId(), dto.getEndTime());
        }
        //如果活动是正在进行中
        if (activityByIdDetail.getStatus().equals(Integer.parseInt(ActivityStatusEnum.IN_PROGRESS.getId())) && StrategyGroupEnum.HF_SECKILL_ACT.getId().equals(dto.getPaTemplateId().toString())) {//除秒杀之外都可以编辑

            //进行中的状态下仅支持增加奖品数量，其它信息不可编辑
            return this.updateResourceIssuedQtyAndRemainQty(dto.getPrizeList());

        }

        //更新活动前处理
        processUpdateValue(dto);

        //剩下继续执行的话，就是未开始的活动 -- UN_START状态
        PromotionActivityUpdatePostRequest cloneUpdateDTO = dto.clone(PromotionActivityUpdatePostRequest.class,
                CloneDirection.FORWARD);
        cloneUpdateDTO.setActivityId(dto.getId());
        //不修改活动创建人
        cloneUpdateDTO.setCreatedBy(null);
        Boolean flag = promotionActivityMiddleService.updateActivityById(dto.getId(), cloneUpdateDTO);

        //更新活动项目及奖品
        updateProjectAndResource(dto, activityByIdDetail);

        //更新场景码
        List<MiniOrgRequestDTO> orgList = ObjectCloneUtils.convertList(dto.getProjectIds(), MiniOrgRequestDTO.class);
        PromotionActivityDO promotionActivityDO = promotionActivityDAO.getById(dto.getId());
        if (Objects.nonNull(promotionActivityDO)) {
            miniProgramService.aSynUpdateOneCodeScene(promotionActivityDO, MiniOrgTypeEnum.PROJECT.getId(), orgList, ActivityPromotionTypeEnum.ACTIVITY.getType());
        }

        //编辑界面活动页配置
        ActivityPageDO activityPageDO = ActivityInfoConverter.activityPageConverter(dto.clone(PromotionActivityCreatePostRequest.class, CloneDirection.FORWARD));
        UpdateWrapper<ActivityPageDO> wrapperPage = new UpdateWrapper<>();
        wrapperPage.lambda().eq(ActivityPageDO::getActivityId, dto.getId());
        wrapperPage.lambda().eq(ActivityPageDO::getType, ActivityTypeEnum.ACTIVITY.getId());
        activityPageDAO.update(activityPageDO, wrapperPage);

        //编辑界面分享页配置
        ActivityPageShareDO activityPageShareDO = ActivityInfoConverter.activityShareConverter(dto.clone(PromotionActivityCreatePostRequest.class, CloneDirection.FORWARD));
        UpdateWrapper<ActivityPageShareDO> wrapperShare = new UpdateWrapper<>();
        wrapperShare.lambda().eq(ActivityPageShareDO::getActivityId, dto.getId());
        wrapperShare.lambda().eq(ActivityPageShareDO::getType, ActivityTypeEnum.ACTIVITY.getId());
        activityPageShareDAO.update(activityPageShareDO, wrapperShare);
        // 更新是否可跳转（不能异步，后面同步缓存的时候要缓存项目信息）
//        asynchronousService.projectIsJumpable(dto.getId());
        activityParticipationService.updateBatchProjectIsJumpable(dto.getId());
        //同步到缓存
        promotionActivityManager.forceCacheActInfo(dto.getId());

        return flag;
    }

    private void updateProjectAndResource(PromotionActivityUpdateRequest dto, PromotionActivityListPostVO activityByIdDetail) {
        //项目较验
        Integer projectType = Optional.ofNullable(dto.getExt().get("projectType")).map(c -> Integer.parseInt(c.toString())).orElse(null);
        this.projectCheck(dto.getProjectIds(), dto.getPaTemplateId(), projectType);
        //活动开始之前
        if (activityByIdDetail.getStatus() < Integer.parseInt(ActivityStatusEnum.IN_PROGRESS.getId())) {
            //全国项目类型获取全国项目列表数据(全国项目没变不需要修改)
            Integer projectTypeOld = Optional.ofNullable(activityByIdDetail.getExt().get("projectType")).map(c -> Integer.parseInt(c.toString())).orElse(null);
            if (ObjectUtil.equal(projectType, ResourcesAttributeEnum.NATIONAL.getId()) && ObjectUtil.notEqual(projectTypeOld, projectType)) {
                List<ActivityParticipationVO> nationalProjects = this.getNationalProjects(dto.getExt(), dto.getUserId());
                if (CollectionUtil.isNotEmpty(nationalProjects)) {
                    dto.setProjectIds(nationalProjects);
                } else {
                    throw new ApplicationException(CommonExceptionCode.INVALIDATION, "活动至少关联一个项目");
                }
            }

            //保存适用项目(如果是全国，且没有变理，则不更新)---->删除旧数据重新添加
            boolean isUpdate = ObjectUtil.equal(projectType, ResourcesAttributeEnum.NATIONAL.getId()) && ObjectUtil.equal(projectTypeOld, projectType);
            if (!isUpdate) {
                UpdateWrapper<ActivityParticipationDO> participationUpdateWrapper = new UpdateWrapper<>();
                participationUpdateWrapper.lambda().eq(ActivityParticipationDO::getActivityId, dto.getId());
                participationUpdateWrapper.lambda().eq(ActivityParticipationDO::getDeleted, 0);
                activityParticipationDAO.remove(participationUpdateWrapper);
                List<ActivityParticipationDO> activityParticipationList = ActivityInfoConverter.participationConverter(dto.clone(PromotionActivityCreatePostRequest.class, CloneDirection.FORWARD), dto.getId());
                activityParticipationDAO.saveBatch(activityParticipationList);
            }
            //保存活动奖品配置----》删除旧数据重新添加（如果是报名活动，在原来的奖品配置上做更新）
            if (!ObjectUtil.equal(dto.getPaTemplateId().toString(), StrategyGroupEnum.HF_SIGN_UP_ACT.getId())) {
                UpdateWrapper<PromotionHisResourceDO> hisResourceUpdateWrapper = new UpdateWrapper<>();
                hisResourceUpdateWrapper.lambda().eq(PromotionHisResourceDO::getActivityId, dto.getId());
                hisResourceUpdateWrapper.lambda().eq(PromotionHisResourceDO::getDeleted, SuperEntity.DR_NORMAL);
                promotionHisResourceDAO.remove(hisResourceUpdateWrapper);
            }
            List<HisResourceJsonVO> hisResourceJsonList = ObjectCloneUtils.convertList(dto.getPrizeList(), HisResourceJsonVO.class);
            this.saveHisResource(hisResourceJsonList, dto.getActivityCardList(), dto.getId(), Long.valueOf(dto.getPaTemplateId()));
            //活动开始之后
        } else {
            //全国项目类型获取全国项目列表数据(全国项目没变不需要修改)--更新后为全国，原来不是全国的情况
            Integer projectTypeOld = Optional.ofNullable(activityByIdDetail.getExt().get("projectType")).map(c -> Integer.parseInt(c.toString())).orElse(null);
            List<ActivityParticipationDO> activityParticipationList = activityParticipationDAO.listByActivityId(dto.getId());
            List<String> projectIdList = activityParticipationList.stream().map(ActivityParticipationDO::getProjectId).distinct().collect(Collectors.toList());

            if (ObjectUtil.equal(projectType, ResourcesAttributeEnum.NATIONAL.getId()) && ObjectUtil.notEqual(projectTypeOld, projectType)) {
                List<ActivityParticipationVO> nationalProjects = this.getNationalProjects(dto.getExt(), dto.getUserId());
                if (CollectionUtil.isNotEmpty(nationalProjects)) {
                    //已存在的不进行更新

                    List<ActivityParticipationVO> diffProjects = nationalProjects.stream().filter(item -> !projectIdList.contains(item.getProjectId())).collect(Collectors.toList());
                    dto.setProjectIds(diffProjects);//需要新增的项目
                } else {
                    throw new ApplicationException(CommonExceptionCode.INVALIDATION, "活动至少关联一个项目");
                }
            } else if (!ObjectUtil.equal(projectType, ResourcesAttributeEnum.NATIONAL.getId()) && ObjectUtil.equal(projectTypeOld, projectType)) {
                //非全国项目更新
                List<ActivityParticipationVO> projectList = dto.getProjectIds().stream().filter(item -> !projectIdList.contains(item.getProjectId())).collect(Collectors.toList());//更新的交集
                dto.setProjectIds(projectList);//需要新增的项目
            }

            //保存适用项目---->删除旧数据重新添加
            boolean isUpdate = ObjectUtil.equal(projectType, ResourcesAttributeEnum.NATIONAL.getId()) && ObjectUtil.equal(projectTypeOld, projectType);//全国项目无改动
            if (!isUpdate && CollectionUtil.isNotEmpty(dto.getProjectIds())) {//不删除，只进行新增
                List<ActivityParticipationDO> activityParticipationDOList = ActivityInfoConverter.participationConverter(dto.clone(PromotionActivityCreatePostRequest.class, CloneDirection.FORWARD), dto.getId());
                activityParticipationDAO.saveBatch(activityParticipationDOList);
            }
            //资源只更新
            List<HisResourceJsonVO> hisResourceJsonList = ObjectCloneUtils.convertList(dto.getPrizeList(), HisResourceJsonVO.class);
            //this.saveHisResource(hisResourceJsonList, dto.getId(), Long.valueOf(dto.getPaTemplateId()));
            //更新资源
            boolean nullFlag = hisResourceJsonList.stream().filter(m -> m.getId() == null).findAny().isPresent();
            if (nullFlag) {
                throw new ApplicationException(CommonExceptionCode.INVALIDATION, "奖品只能更新不能移除新增");
            }
            //抽奖更新库存，每天发放上线，中奖概率
            if (StrategyGroupEnum.HF_LUCKYDRAW_ACT.getId().equals(dto.getPaTemplateId().toString())) {
                hisResourceJsonList.forEach(item -> {
                    PromotionHisResourceDO promotionHisResource = this.promotionHisResourceDAO.getById(item.getId());
                    item.setIssuedQuantity(promotionHisResource.getIssuedQuantity());
                    BeanPowerHelper.mapPartOverrider(item, promotionHisResource);
                    this.promotionHisResourceDAO.updateById(promotionHisResource);
                });
            }
            this.updateResourceIssuedQtyAndRemainQty(dto.getPrizeList());
        }
    }

    private void processMini(Map<String, Object> ext,
                             PromotionActivityCreatePostRequest requestDto,
                             PromotionActivityUpdateRequest updateDto) {
        //手机上只能创建报名活动
        Integer paTemplateId = requestDto != null ? requestDto.getPaTemplateId() : updateDto.getPaTemplateId();
        Assert.isTrue(StrategyGroupEnum.HF_SIGN_UP_ACT.getId().equals(paTemplateId.toString()), "活动类型错误");
        signUpService.processMini(ext,requestDto,updateDto);
        //默认为待审核,清除其它审核信息
        resetAuditStatus(ActivityAuditStatusEnum.PENDING_AUDIT, paTemplateId,ext);
    }

    @Override
    public List<PromotionActivityDO> getListByShouldStart() {
        return promotionActivityDAO.getListByShouldStart();
    }

    @Override
    public Boolean updateStatusByStartTime() {
        return promotionActivityDAO.updateStatusByStartTime();
    }

    @Override
    public List<PromotionActivityDO> getListByShouldFinish() {
        return promotionActivityDAO.getListByShouldFinish();
    }

    @Override
    public Boolean updateStatusByEndTime() {
        return promotionActivityDAO.updateStatusByEndTime();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(Long id, String status) {
        PromotionActivityDO activityDo = promotionActivityDAO.getById(id);

        /*if(Objects.nonNull(activityDo) && Objects.equals(status, ActivityStatusEnum.OFF_SHELF.getId()) && StringUtil.isNotEmpty(activityDo.getCreatedPerson())
                && !activityDo.getCreatedPerson().equals(HuafaRuntimeEnv.getUserId())){
            throw new ApplicationException("当前用户没有下架此活动权限");
        }*/

        if (Objects.nonNull(activityDo) && Objects.equals(status, ActivityStatusEnum.FINISH.getId()) && StringUtil.isNotEmpty(activityDo.getCreatedPerson())
                && !activityDo.getCreatedPerson().equals(HuafaRuntimeEnv.getUserId())
                && !authUtil.isAdminPosition()) {
            throw new ApplicationException("当前用户没有结束此活动权限");
        }

        /*if(!Objects.equals(status, ActivityStatusEnum.IN_PROGRESS.getId())){
            throw new ApplicationException("非进行中活动不可以废弃");
        }*/

        if (Objects.isNull(activityDo.getStartTime()) || Objects.isNull(activityDo.getEndTime())) {
            throw new ApplicationException("当前活动有效期校验失败");
        }
        /*if(Objects.equals(status, ActivityStatusEnum.OFF_SHELF.getId()) && !Objects.equals(String.valueOf(activityDo.getStatus()), ActivityStatusEnum.FINISH.getId())){
            throw new ApplicationException("当前活动状态不能下架！");
        }*/

        activityDo.setStatus(Integer.parseInt(status));
        boolean flag = promotionActivityDAO.updateById(activityDo);

        //如果活动手动开始，则缓存活动数据
        if (flag && status.equals(ActivityStatusEnum.IN_PROGRESS.getId())) {
            promotionActivityManager.forceCacheActInfo(id);
        }

        //如果活动手动结束，则修改缓存活动的状态
        if (flag && status.equals(ActivityStatusEnum.FINISH.getId())) {
            PromotionActivityDetailDTO redisActivityDetailDTO = JSON.parseObject(String.valueOf(redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO + id).get()), PromotionActivityDetailDTO.class);
            if (Objects.nonNull(redisActivityDetailDTO)) {
                redisActivityDetailDTO.setStatus(Integer.parseInt(ActivityStatusEnum.FINISH.getId()));
                redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO + id).set(JSON.toJSONString(redisActivityDetailDTO));
            }
            //助力活动:如果助力活动至少满足一个阶梯则算成功，更新助力活动状态并发放奖品
            this.grantResource(activityDo);
        }

        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean seckillUpdateStatus(PromotionActivityUpdateStatusRequest requestVo) {

        PromotionActivityDO activityDo = promotionActivityDAO.getById(requestVo.getId());

        if (Objects.nonNull(activityDo) && ActivityStatusEnum.IN_PROGRESS.getId().equals(requestVo.getStatus().toString())
                && StringUtil.isNotEmpty(activityDo.getCreatedPerson())
                && !activityDo.getCreatedPerson().equals(HuafaRuntimeEnv.getUserId())
                && !authUtil.isAdminPosition()) {
            throw new ApplicationException("没有操作此活动开始秒杀权限");
        }

        if (Objects.nonNull(activityDo) && ActivityStatusEnum.FINISH.getId().equals(requestVo.getStatus().toString())
                && StringUtil.isNotEmpty(activityDo.getCreatedPerson())
                && !activityDo.getCreatedPerson().equals(HuafaRuntimeEnv.getUserId())
                && !authUtil.isAdminPosition()) {
            throw new ApplicationException("没有操作此活动结束权限");
        }

//        if(Objects.nonNull(activityDo) && ActivityStatusEnum.STOP.getId().equals(requestVo.getStatus().toString())
//                && StringUtil.isNotEmpty(activityDo.getCreatedPerson())
//                && !activityDo.getCreatedPerson().equals(HuafaRuntimeEnv.getUserId())){
//            throw new ApplicationException("没有操作此活动结束秒杀权限");
//        }

        if (
                !ActivityStatusEnum.IN_PROGRESS.getId().equals(requestVo.getStatus().toString()) &&
                        !ActivityStatusEnum.STOP.getId().equals(requestVo.getStatus().toString()) &&
                        !ActivityStatusEnum.FINISH.getId().equals(requestVo.getStatus().toString())
        ) {
            throw new ApplicationException("状态只能修改为开始与结束");
        }

        //获取当前活动信息

        //判断是否可以更改状态
        if (
                ActivityStatusEnum.UN_START.getId().equals(activityDo.getStatus().toString()) &&
                        !ActivityStatusEnum.IN_PROGRESS.getId().equals(requestVo.getStatus().toString())
        ) {
            throw new ApplicationException("当前活动不支持修改状态");
        }
        if (
                ActivityStatusEnum.IN_PROGRESS.getId().equals(activityDo.getStatus().toString()) &&
                        !ActivityStatusEnum.STOP.getId().equals(requestVo.getStatus().toString()) &&
                        !ActivityStatusEnum.FINISH.getId().equals(requestVo.getStatus().toString())
        ) {
            throw new ApplicationException("此状态不允许修改");
        }

        //更改活动状态
        if (ActivityStatusEnum.IN_PROGRESS.getId().equals(requestVo.getStatus().toString())) {
            activityDo.setStartTime(new Date());
            activityDo.setStatus(requestVo.getStatus());
        } else if (ActivityStatusEnum.FINISH.getId().equals(requestVo.getStatus().toString()) && requestVo.getIsSchedule().equals(WhetherEnum.YES.getId())) {
            if (Objects.isNull(requestVo.getEndTime())) {
                throw new ApplicationException("请设置活动结束时间");
            }
            if (requestVo.getEndTime().before(new Date())) {
                throw new ApplicationException("结束时间不能在当前时间之前");
            }

            activityDo.setEndTime(requestVo.getEndTime());
        } else if (ActivityStatusEnum.FINISH.getId().equals(requestVo.getStatus().toString()) && requestVo.getIsSchedule().equals(WhetherEnum.NO.getId())) {
            activityDo.setEndTime(new Date());
            activityDo.setStatus(requestVo.getStatus());
        }
        boolean flag = promotionActivityDAO.updateById(activityDo);

        //如果活动手动开始，则缓存活动数据
        if (flag && ActivityStatusEnum.IN_PROGRESS.getId().equals(requestVo.getStatus().toString())) {
            promotionActivityManager.forceCacheActInfo(requestVo.getId());
        }

        //如果活动手动结束，则修改缓存活动的状态
        if (flag && ActivityStatusEnum.FINISH.getId().equals(requestVo.getStatus().toString()) && requestVo.getIsSchedule().equals(WhetherEnum.NO.getId())) {
            PromotionActivityDetailDTO redisActivityDetailDTO = JSON.parseObject(String.valueOf(redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO + requestVo.getId()).get()), PromotionActivityDetailDTO.class);
            if (Objects.nonNull(redisActivityDetailDTO)) {
                redisActivityDetailDTO.setStatus(Integer.parseInt(ActivityStatusEnum.FINISH.getId()));
                redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO + requestVo.getId()).set(JSON.toJSONString(redisActivityDetailDTO));
            }
        }

        return flag;
    }

    @Override
    public PromotionActivityDetailDTO getActivityById(Long id) {

        if (Objects.isNull(id)) {
            throw new ApplicationException("活动id缺失");
        }

        PromotionActivityDetailDTO result = promotionActivityManager.getCacheActivityById(id);
        if (Objects.nonNull(result)) {
            return result;
        }

        //活动基本数据
        PromotionActivityListPostVO activityDetail = promotionActivityMiddleService.getActivityById(id);
        if (Objects.isNull(activityDetail)) {
            throw new ApplicationException("活动数据不存在");
        }
        result = activityDetail.clone(PromotionActivityDetailDTO.class, CloneDirection.OPPOSITE);

        //======================华发===============================
        ActivityExtVO ext = ActivityExtConverter.converter(result.getExt());

//        ActivityTargetDO activityTargetDO = activityTargetDAO.getById(ext.getActivityGoal());
//
//        //活动目标名称
//        result.setActivityTargetName(Objects.nonNull(activityTargetDO)?activityTargetDO.getTargetName():"");

        //指标路径名称
        MarketingKpiRouteMapDTO marketingKpiRouteMapDTO = marketingKpiRouteMapService.queryById(Long.valueOf(ext.getKpiPath()));
        result.setMarketingKpiRouteMapName(Objects.nonNull(marketingKpiRouteMapDTO) ? marketingKpiRouteMapDTO.getName() : "");


        //获取项目列表
        List<ActivityParticipationDO> activityParticipationList = activityParticipationDAO.listByActivityId(id);
        if (CollectionUtil.isNotEmpty(activityParticipationList)) {
            List<ActivityParticipationVO> activityParticipationVOList = ObjectCloneUtils.convertList(activityParticipationList, ActivityParticipationVO.class);
            result.setProjectInfoList(activityParticipationVOList);
        }
        //获取项目列表，展示给前端
        List<ActivityParticipationGroupResponseDTO> activityParticipationGroupResponseDTOList = activityParticipationService.getProjectGroupList(id);
        if (CollectionUtil.isNotEmpty(activityParticipationGroupResponseDTOList)) {
            result.setFrontProjectList(activityParticipationGroupResponseDTOList);
        }

        //活动信息
        result.setPrizeList(this.getPrizeList(id));

        //活动页
        ActivityPageDO activityPageDO = activityPageDAO.getByActivity(id, ActivityTypeEnum.ACTIVITY.getId());
        ActivityPageVO activityPageVO = Optional.ofNullable(activityPageDO).orElseGet(ActivityPageDO::new).clone(ActivityPageVO.class);
        String bottomBtnType = activityPageDO.getBottomBtnType();
        if (StringUtil.isNotEmpty(bottomBtnType)) {
            String[] split = bottomBtnType.split(",");
            Integer[] types = (Integer[]) ConvertUtils.convert(split, Integer.class);
            activityPageVO.setBottomBtnTypes(Arrays.asList(types));
        }

        result.setActivityPageVO(activityPageVO);

        //活动分享页
        ActivityPageShareDO activityPageShareDO = activityPageShareDAO.getByActivity(id, ActivityTypeEnum.ACTIVITY.getId());
        result.setActivityPageShareVO(Optional.ofNullable(activityPageShareDO).orElseGet(ActivityPageShareDO::new).clone(ActivityPageShareVO.class));

        //活动规则
        List<PromotionActivityLimitDO> limitList = promotionActivityLimitDAO.selectByActivityId(id);
        if (CollectionUtil.isNotEmpty(limitList)) {
            result.setUserLimit(ActivityConfigConverter.getLimitArray(limitList, PATemplateBaseEnum.USER.getId(), BaseActivityDTO.class));
            result.setLuckyDrawLimit(ActivityConfigConverter.getLimitArray(limitList, PATemplateBaseEnum.LUCKYDRAW.getId(), BaseActivityDTO.class));
            result.setCouponLimit(ActivityConfigConverter.getLimitArray(limitList, PATemplateBaseEnum.COUPON.getId(), BaseActivityDTO.class));
            result.setBargainLimit(ActivityConfigConverter.getLimitArray(limitList, PATemplateBaseEnum.BARGAIN.getId(), BaseActivityDTO.class));
            result.setAssistLimit(ActivityConfigConverter.getLimitArray(limitList, PATemplateBaseEnum.ASSIST.getId(), BaseActivityDTO.class));
            result.setFormLimit(ActivityConfigConverter.getLimitArray(limitList, PATemplateBaseEnum.FORM.getId(), BaseActivityDTO.class));
            result.setCardCollectingLimit(ActivityConfigConverter.getLimitArray(limitList, PATemplateBaseEnum.CARD_COLLECTING.getId(), BaseActivityDTO.class));
            result.setNumberLimit(ActivityConfigConverter.getLimitArray(limitList, PATemplateBaseEnum.NUMBER.getId(), BaseActivityDTO.class));
        }

        //处理标签
        List<TagItemDTO> tags = associationService.getAssociationList(id, AssociationTypeEnum.ACTIVITY_TAG).stream().map(associationService::convertToTagItem).collect(Collectors.toList());
        result.setTags(tags);

        //todo 如果是报名活动，则需要查询已报名人数和已签到人数
        //======================华发===============================

        if (
                result.getStatus().equals(Integer.parseInt(ActivityStatusEnum.UN_START.getId())) ||
                        result.getStatus().equals(Integer.parseInt(ActivityStatusEnum.IN_PROGRESS.getId()))
        ) {
            //缓存活动信息
            asynchronousService.asyncCacheNewActInfo(id);
        }


        return result;
    }

    @Override
    public Boolean partakeActCheck(ReceiveCouponRequestDTO dto) {

        //判断redis缓存是否存在，不存在就重新缓存
        promotionActivityManager.cacheActInfo(dto.getActivityId());


        //活动较验
        PromotionActivityDO activity = promotionActivityDAO.getById(dto.getActivityId());
        if (Objects.isNull(activity)) {
            throw new ApplicationException("活动不存在");
        }
        if (activity.getStatus().equals(Integer.parseInt(ActivityStatusEnum.UN_START.getId()))) {
            throw new ApplicationException("活动尚未开始，请耐心等待！");
        } else if (!activity.getStatus().equals(Integer.parseInt(ActivityStatusEnum.IN_PROGRESS.getId()))) {
            throw new ApplicationException("来晚啦，活动已结束！");
        }
        //库存较验
        PromotionHisResourceDO promotionHisResourceDO = null;
        if (Objects.equals(String.valueOf(activity.getPaTemplateId()), StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId())) {
            promotionHisResourceDO = promotionHisResourceDAO.findLadder(dto.getActivityId(), 1);//阶梯一奖品
        } else {
            promotionHisResourceDO = promotionHisResourceDAO.getById(dto.getHisResourceId());
        }

        if (dto.getHisResourceId() != 0) {
            if (Objects.isNull(promotionHisResourceDO)) {
                throw new ApplicationException("参数错误，资源不存在！");
            }

            //取redis库存较验
            long currentCount = redissonClient.getAtomicLong(RedisConstants.CACHE_PREV_KEY_ACT_RESOURCE_INFO_COUNT + promotionHisResourceDO.getId()).get();
            if (currentCount <= 0) {
                log.info("库存[{}]已经为0", promotionHisResourceDO.getId());
                throw new ApplicationException("奖品已经被抢光啦~下次再来吧~");
            }

            //查询相同活动相同优惠券相同用户领取次数
            if (Objects.equals(String.valueOf(activity.getPaTemplateId()), StrategyGroupEnum.HF_COUPON_ACT.getId()) &&
                    promotionHisResourceDO.getLimitTimes() <= activityPartakeLogDAO.getCountByActivity(dto.getActivityId(), dto.getHisResourceId(), dto.getPhone())) {
                throw new ApplicationException(CommonExceptionCode.INVALIDATION, promotionHisResourceDO.getReceiveMode() == 0 ? "领取次数到达上限" : "购买次数到达上限");
            }
        }
        return Boolean.TRUE;
    }


    @Override
    @Transactional
    public Data<Object> partakeAct(ActivityPartakeRequest requestVo) {

        PromotionActivityDetailDTO actDTO = JSON.parseObject(String.valueOf(redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO + requestVo.getActivityId()).get()), PromotionActivityDetailDTO.class);
        if (Objects.isNull(actDTO)) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "活动已下架或不存在");
        }
        //PromotionActivityDO promotionActivityDO = promotionActivityDAO.selectById(requestVo.getActivityId()); //this.getActivityById(requestVo.getActivityId());
        PromotionActivityDO promotionActivityDO = actDTO.clone(PromotionActivityDO.class, CloneDirection.OPPOSITE);
        promotionActivityDO.setName(actDTO.getActivityName());
        promotionActivityDO.setId(actDTO.getActivityId());

        boolean isGoodAnchorJoin = Objects.equals(StrategyGroupEnum.HF_GOOD_ANCHOR_ACT.getId(), promotionActivityDO.getPaTemplateId().toString())
                && Objects.equals(requestVo.getUserJoinType(), UserJoinTypeEnum.JOIN_TYPE_1.getId());
        boolean isSignUpJoin = Objects.equals(StrategyGroupEnum.HF_SIGN_UP_ACT.getId(), promotionActivityDO.getPaTemplateId().toString());
        //活动校验-状态校验-时间校验，好主播/报名活动需要活动前参加，所以要排除
        if (!isGoodAnchorJoin && !isSignUpJoin) {
            if (promotionActivityDO.getStatus().equals(Integer.parseInt(ActivityStatusEnum.UN_START.getId()))) {
                throw new ApplicationException(CommonExceptionCode.INVALIDATION, "活动尚未开始，请耐心等待！");
            }
        }
        if (promotionActivityDO.getStatus().equals(Integer.parseInt(ActivityStatusEnum.FINISH.getId()))) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "来晚啦，活动已结束！");
        }

        if (!Objects.equals(StrategyGroupEnum.HF_SECKILL_ACT.getId(), promotionActivityDO.getPaTemplateId().toString())
                && !Objects.equals(requestVo.getUserJoinType(), UserJoinTypeEnum.JOIN_TYPE_2.getId()) && !isGoodAnchorJoin) {
            this.checkClickRepeat(String.format(RedisConstants.CACHE_PREV_KEY_ACT_RESOURCE_INFO_REPEAT, requestVo.getPhone(), requestVo.getActivityId(), requestVo.getHisResourceId() == null ? 0L : requestVo.getHisResourceId()), requestVo.getUuId());
        }

        //获取指定活动的所有规则
        List<PromotionActivityLimitDO> promotionActivityLimitList = promotionActivityLimitDAO.selectByActivityId(requestVo.getActivityId());

        //活动规则校验
        ActivityUserRelatedDTO activityUserRelated = null;
        if (!Objects.equals(StrategyGroupEnum.HF_SECKILL_ACT.getId(), promotionActivityDO.getPaTemplateId().toString())
                && !Objects.equals(StrategyGroupEnum.HF_CARD_COLLECTING_ACT.getId(), promotionActivityDO.getPaTemplateId().toString())
                && !Objects.equals(StrategyGroupEnum.HF_GOOD_ANCHOR_ACT.getId(), promotionActivityDO.getPaTemplateId().toString())
                && !Objects.equals(StrategyGroupEnum.HF_SIGN_UP_ACT.getId(), promotionActivityDO.getPaTemplateId().toString())
        ) {
            activityUserRelated = this.getActivityUserRelated(requestVo, promotionActivityLimitList, promotionActivityDO.getPaTemplateId().toString());
        }

        if (StrategyGroupEnum.HF_LUCKYDRAW_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString()) ||
                StrategyGroupEnum.HF_FISSION_REDUCEPRICE_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString()) ||
                StrategyGroupEnum.HF_COUPON_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString()) ||
                StrategyGroupEnum.HF_SECKILL_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString()) ||
                StrategyGroupEnum.HF_FORM_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString()) ||
                StrategyGroupEnum.HF_SIGN_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString()) ||
                StrategyGroupEnum.HF_SIGN_UP_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())
        ) {
            this.checkActivityLimits(requestVo, promotionActivityDO, promotionActivityLimitList, activityUserRelated);
        }

        //信息登记较验(抽奖，砍价、表单活动这里不校验)
        if (ObjectUtil.notEqual(promotionActivityDO.getPaTemplateId().toString(), StrategyGroupEnum.HF_LUCKYDRAW_ACT.getId()) &&
                ObjectUtil.notEqual(promotionActivityDO.getPaTemplateId().toString(), StrategyGroupEnum.HF_FISSION_REDUCEPRICE_ACT.getId()) &&
                ObjectUtil.notEqual(promotionActivityDO.getPaTemplateId().toString(), StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId()) &&
                ObjectUtil.notEqual(promotionActivityDO.getPaTemplateId().toString(), StrategyGroupEnum.HF_SECKILL_ACT.getId()) &&
                ObjectUtil.notEqual(promotionActivityDO.getPaTemplateId().toString(), StrategyGroupEnum.HF_FORM_ACT.getId()) &&
                ObjectUtil.notEqual(promotionActivityDO.getPaTemplateId().toString(), StrategyGroupEnum.HF_CARD_COLLECTING_ACT.getId()) &&
                ObjectUtil.notEqual(promotionActivityDO.getPaTemplateId().toString(), StrategyGroupEnum.HF_SIGN_ACT.getId()) &&
                ObjectUtil.notEqual(promotionActivityDO.getPaTemplateId().toString(), StrategyGroupEnum.HF_LOCATION_SIGN_ACT.getId()) &&
                ObjectUtil.notEqual(promotionActivityDO.getPaTemplateId().toString(), StrategyGroupEnum.HF_SIGN_UP_ACT.getId()) &&
                ObjectUtil.notEqual(promotionActivityDO.getPaTemplateId().toString(), StrategyGroupEnum.HF_GOOD_ANCHOR_ACT.getId())
        ) {
            this.checkFeedbackInfo(promotionActivityDO, requestVo.getPhone(), requestVo.getHisResourceId());
        }
        //检查用户是否授权
        checkAuthUser(requestVo);

        if (StrategyGroupEnum.HF_SECKILL_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())) {
            //秒杀活动
            //判断是否有信息登记
            this.checkSeckillFeedbackInfo(promotionActivityDO, requestVo.getPhone());
            //判断是否重复订单
            activityOrderService.checkRepeatOrder(requestVo);
            //资源库存校验、校验领取次数、锁定缓存库存和数据库库存
            PromotionHisResourceDO promotionHisResourceDO = promotionHisResourceDAO.checkResourceQtyAndLimitTimeAndDecrQty(requestVo);

            //判断核销的优惠券是否过期 指定时间
            if (PromotionResourceValidTimeTypeEnum.DESIGNATED_TIME.getId().equals(promotionHisResourceDO.getValidTimeType())
                    && promotionHisResourceDO.getValidEndTime().before(DateTime.now())) {
                throw new ApplicationException(CommonExceptionCode.INVALIDATION, "奖品已经被抢光啦~下次再来吧~");
            }

            try {
                //TODO 扣减缓存库存操作改为在秒杀服务应用层端操作
//                boolean flag = promotionActivityManager.decrRedisQty(promotionHisResourceDO.getId(),promotionActivityDO.getId());
//                if(!flag){
//                    throw new ApplicationException(CommonExceptionCode.INVALIDATION,"库存不足，下单失败");
//                }

                //不限项目将payNo传给projectId进行下单,如为指定项目则取前端传的值----(0-不限项目,1-指定项目)
                Integer resourcesAttribute = (Integer) promotionActivityDO.getExt().get("resourcesAttribute");
                if (ResourcesAttributeEnum.UNLIMITED.getId().equals(resourcesAttribute)) {
                    requestVo.setProjectId((String) promotionActivityDO.getExt().get("payNo"));
                }
                //创建订单
                Data<OrderPayResponseDTO> orderPayResponseDTOData = activityOrderService.placeOrder(requestVo, promotionActivityDO, promotionHisResourceDO);
//                if (!CommonExceptionCode.SUCCESS.equals(orderPayResponseDTOData.getCode())) {
//                    promotionActivityManager.incrRedisQty(promotionHisResourceDO.getId(), promotionActivityDO.getId());
//                }
                return new Data<>(orderPayResponseDTOData.getdata(), orderPayResponseDTOData.getCode(), orderPayResponseDTOData.getMsg());
            } catch (Exception e) {
                log.error("下单失败，报错原因：{}", e);
                //TODO 返还库存操作改为在秒杀服务应用层端操作
//                promotionActivityManager.incrRedisQty(promotionHisResourceDO.getId(),promotionActivityDO.getId());
                return new Data<>(null, "500", e.getMessage());
            }

        } else if (StrategyGroupEnum.HF_COUPON_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())) {

            //优惠券活动
            return this.couponAct(promotionActivityDO, requestVo);
        } else if (StrategyGroupEnum.HF_LUCKYDRAW_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())) {
            // 马拉松活动抽奖，加一个是否完成比赛的校验
            if ("marathon".equals(promotionActivityDO.getRemark())) {
                Integer status = promotionMarathonService.checkStatus(requestVo.getPhone());
                if (Objects.equals(status, 1)) {
                    throw new ApplicationException(CommonExceptionCode.INVALIDATION, "用户未报名");
                } else if (Objects.equals(status, 2)) {
                    throw new ApplicationException(CommonExceptionCode.INVALIDATION, "用户未完成比赛");
                }
                log.info("用户:{}的活动:{}比赛校验通过:{}", requestVo.getPhone(), requestVo.getActivityId(), status);
            }
            //抽奖活动
            return luckyDrawAct(requestVo, activityUserRelated);
        } else if (StrategyGroupEnum.HF_FISSION_REDUCEPRICE_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())) {
            //砍价活动
            return bargainAct(requestVo, promotionActivityLimitList, activityUserRelated);
        } else if (StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())) {
            //助力活动
            return assistActService.partakeAct(requestVo, activityUserRelated, promotionActivityLimitList);
        } else if (StrategyGroupEnum.HF_FORM_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())) {
            // 马拉松活动报名
            if ("marathon".equals(promotionActivityDO.getRemark())) {
                return new Data<>(this.marathonAct(requestVo, promotionActivityDO));
            }
            //表单活动
            return new Data<>(this.formAct(requestVo, promotionActivityDO));
        } else if (StrategyGroupEnum.HF_CARD_COLLECTING_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())) {
            // 集卡活动
            return new Data<>(this.cardCollectingAct(requestVo, promotionActivityLimitList));
        } else if (StrategyGroupEnum.HF_GOOD_ANCHOR_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())) {
            // 好主播活动
            return new Data<>(goodAnchorService.partakeAct(requestVo, promotionActivityLimitList));
        } else if (StrategyGroupEnum.HF_SIGN_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())) {
            // 签到活动
            return new Data<>(signActService.partakeAct(requestVo, promotionActivityLimitList,actDTO.getPrizeList()));
        } else if (StrategyGroupEnum.HF_LOCATION_SIGN_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())) {
            // 签到地点活动
            return new Data<>(locationSignActService.partakeAct(requestVo, promotionActivityLimitList,activityUserRelated));
        } else if (StrategyGroupEnum.HF_SIGN_UP_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())) {
            // 报名活动
            return signUpService.partakeAct(promotionActivityDO,requestVo);
        }
        return new Data<>(Boolean.TRUE);
    }

    private void checkAuthUser(ActivityPartakeRequest requestVo) {
        List<AccountInfo> accountInfos = huaFaHmacAuthUtil.getByUnionId(requestVo.getUnionId());
        if (CollectionUtil.isNotEmpty(accountInfos)) {
            //看号码是否有匹配的
            for (AccountInfo accountInfo : accountInfos) {
                if (accountInfo.getTelephone().equals(requestVo.getPhone())) {
                    return;
                }
            }
        }
        throw new ApplicationException( "参与活动前先请先授权电话号码");
    }

    private Object cardCollectingAct(ActivityPartakeRequest requestVo, List<PromotionActivityLimitDO> promotionActivityLimitList) {
        if (UserJoinTypeEnum.JOIN_TYPE_1.getId().equals(requestVo.getUserJoinType())) {
            return cardCollectingService.giveCard(requestVo, promotionActivityLimitList);
        } else if (UserJoinTypeEnum.JOIN_TYPE_2.getId().equals(requestVo.getUserJoinType())) {
            return cardCollectingService.assist(requestVo);
        }
        return null;
    }

    private Data<Object> bargainAct(ActivityPartakeRequest requestVo, List<PromotionActivityLimitDO> promotionActivityLimitList, ActivityUserRelatedDTO activityUserRelated) {
        try {
            //查询活动关联奖品及奖品规则 砍价活动只有一个礼品
            PromotionHisResourceDO hisResource = luckyDrawService.getPromotionHisResource(requestVo.getActivityId()).get(0);

            //发起者
            if (UserJoinTypeEnum.JOIN_TYPE_1.getId().equals(requestVo.getUserJoinType())) {
                if (hisResource.getRemainingQuantity() != null && 0 >= hisResource.getRemainingQuantity()) {
                    throw new ApplicationException("奖品已经被抢光啦~下次再来吧~");
                }
                checkBargain(requestVo.getPhone(), requestVo.getActivityId());

                //查询用户砍价信息
                ActivityPartakeLogDTO partakeLogDTO = activityPartakeLogDAO.getList(requestVo.getActivityId(), requestVo.getUserId(), FissonStatusEnum.PROCESSING.getId(), FissonTypeEnum.Bargain.getId());
                if (partakeLogDTO != null) {
                    return new Data<>(partakeLogDTO);
                }

                //发起砍价次数校验,
                int launchTimes = 0;
                if (activityUserRelated != null) {
                    launchTimes = (int) activityUserRelated.getLimits().get(ActivityTemplateNumberEnum.LAUNCH_TIMES.getId());

                    if (launchTimes <= 0) {
                        throw new ApplicationException("您当前剩余发起砍价次数为0，不可继续发起砍价！");
                    }
                } else {
                    throw new ApplicationException("用户信息异常！");
                }
                String resourceCode = generateIdUtil.getResourceCode();
                ActivityPartakeLogDO activityPartakeLogDO = BargainingConverter.partakeLogConverter(requestVo, promotionActivityLimitList, PATemplateBaseEnum.BARGAIN.getId(), resourceCode);
                activityPartakeLogDAO.save(activityPartakeLogDO);

                //发起砍价次数 - 1
                ActivityUserRelatedDO activityUserRelatedUpdate = new ActivityUserRelatedDO();
                activityUserRelatedUpdate.setId(activityUserRelated.getId());
                activityUserRelated.getLimits().put(ActivityTemplateNumberEnum.LAUNCH_TIMES.getId(), launchTimes - 1);
                activityUserRelatedUpdate.setLimits(activityUserRelated.getLimits());
                activityUserRelatedDAO.updateById(activityUserRelatedUpdate);

                //扣减缓存库存 发起时先扣减库存，砍价成功扣减数据库库存，失败测返还缓存库存
                boolean flag = promotionActivityManager.decrRedisQty(hisResource.getId(), requestVo.getActivityId());
                if (!flag) {
                    throw new ApplicationException(CommonExceptionCode.INVALIDATION, "奖品已经被抢光啦~下次再来吧~");
                }
                return new Data<>(activityPartakeLogDO.clone(ActivityPartakeLogDTO.class));
            } else {
                //参与者校验
                ActivityPartakeLogDO partakeLog = this.checkParticipant(requestVo, promotionActivityLimitList);

                //计算砍价金额  2021、7、29  添加购买价格做为砍价金额，礼品券获取原价
                BigDecimal bargainAmount = hisResource.getPurchasePrice();
                if (bargainAmount == null || bargainAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    throw new ApplicationException("资源价格异常");
                }

                //（商品价格 - 砍价最低价) = 砍价金额
                BigDecimal amount = bargainAmount.subtract(partakeLog.getPayMoney());
                //当前待砍金额 = (砍价金额 - 当前已砍价格) * 100
                BigDecimal amountInt = amount.subtract(partakeLog.getCurrentFissonPrice());
                int surplusMoney = amountInt.multiply(new BigDecimal(100)).intValue();
                //当前待砍数量 = 所需助力人数 - 当前已助力人数
                int surplusKnife = partakeLog.getNeedFissonCount() - partakeLog.getCurrentFissonCount();
                int bargain = RandomBargainAmountUtils.bargain(surplusMoney, surplusKnife);
                if (0 > bargain) {
                    throw new ApplicationException("《砍价》砍价系统异常");
                }
                //砍掉金额
                double bargainAmountDouble = new BigDecimal(bargain).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).doubleValue();
                log.info("-----------------当前砍掉" + bargainAmountDouble + "元");

                //添加助力者记录
                ActivityFissionLogDO activityFissionLogDO = BargainingConverter.activityFissionLogConverter(requestVo, bargainAmountDouble, partakeLog);
                activityFissionLogDAO.save(activityFissionLogDO);

                ActivityPartakeLogDO activityPartakeLogDO = new ActivityPartakeLogDO();
                activityPartakeLogDO.setId(partakeLog.getId());
                //助力人数 + 1
                activityPartakeLogDO.setCurrentFissonCount(partakeLog.getCurrentFissonCount() + 1);
                //当前已砍价价格 ++
                activityPartakeLogDO.setCurrentFissonPrice(partakeLog.getCurrentFissonPrice().add(new BigDecimal(bargainAmountDouble)));
                activityPartakeLogDAO.updateById(activityPartakeLogDO);

                //帮助砍价结果是否完成
                this.checkFissionStatus(partakeLog, activityPartakeLogDO.getCurrentFissonCount(), hisResource.getName());
                return new Data<>(activityFissionLogDO.clone(ActivityFissionLogDTO.class));
            }
        } catch (Exception e) {
            log.error("砍价失败，报错原因：", e);
            return new Data<>(null, "500", e.getMessage());
        }
    }

    /**
     * 砍价是否存在未领取奖品较验
     *
     * @param phone
     * @param activityId
     */
    private void checkBargain(String phone, Long activityId) {
        QueryWrapper<ActivityPartakeLogDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ActivityPartakeLogDO::getPhone, phone);
        queryWrapper.lambda().eq(ActivityPartakeLogDO::getActivityId, activityId);
        List<ActivityPartakeLogDO> list = activityPartakeLogDAO.list(queryWrapper);
        if (CollectionUtil.isNotEmpty(list)) {
            List<String> codeList = list.stream()
                    .filter(e -> e.getFissonStatus().equals(FissonStatusEnum.SUCCESS.getId()))
                    .map(ActivityPartakeLogDO::getCode).distinct().collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(codeList)) {
                QueryWrapper<ActivityVerifyDO> verifyQueryWrapper = new QueryWrapper<>();
                verifyQueryWrapper.lambda().in(ActivityVerifyDO::getCode, codeList);
                if (activityVerifyDAO.count(verifyQueryWrapper) < codeList.size()) {
                    throw new ApplicationException("请领取奖品后，再发起砍价活动！");
                }
            }
        }
    }

    private Data<Object> luckyDrawAct(ActivityPartakeRequest requestVo, ActivityUserRelatedDTO activityUserRelated) {
        boolean flag = Boolean.FALSE;
        Long hisResourceId = 0L;
        Long activityId = 0L;
        try {

            //查询活动关联奖品及奖品规则
            List<PromotionHisResourceDO> hisResourceList = luckyDrawService.getPromotionHisResource(requestVo.getActivityId());

            //奖品规则校验
            luckyDrawService.prizeConfig(requestVo.getActivityId(), requestVo.getPhone(), hisResourceList);

            //根据概率随机抽取奖品
            List<Double> collect = hisResourceList.stream().map(PromotionHisResourceDO::getOddsOfWinning).collect(Collectors.toList());
            int lottery = LotteryUtil.lottery(collect);
            double sum = collect.stream().mapToDouble(e -> e).sum();
            if (sum <= 0) {
                PromotionHisResourceDO promotionHisResourceDO = hisResourceList.stream().filter(e -> e.getResourceId() == -1).findFirst().get();
                lottery = hisResourceList.indexOf(promotionHisResourceDO);
            }
            PromotionHisResourceDO promotionHisResourceDO = hisResourceList.get(lottery);

            //扣减缓存库存
            if (promotionHisResourceDO.getResourceId() > 0) {//非谢谢参与
                flag = promotionActivityManager.decrRedisQty(promotionHisResourceDO.getId(), promotionHisResourceDO.getActivityId());
                if (!flag) {
                    throw new ApplicationException(CommonExceptionCode.INVALIDATION, "奖品已领完了");
                }
                promotionActivityManager.decrRemainQty(promotionHisResourceDO.getId(), 1L);
                hisResourceId = promotionHisResourceDO.getId();
                activityId = promotionHisResourceDO.getActivityId();
            }

            //保存用户抽奖记录
            String resourceCode = generateIdUtil.getResourceCode();
            //没有选择项目或者完成信息登记，领取不成功，奖品数量不扣减，消耗抽奖次数
            ActivityPartakeLogDTO activityPartakeLogDTO = LuckyDrawConverter.partakeLogConverter(requestVo, promotionHisResourceDO, resourceCode);

            /**
             * 先更新缓存，再新增数据库
             */
            RMap<String, String> totalCountRMap = redissonClient.getMap("pc_activityId_" + activityPartakeLogDTO.getActivityId());
            RMap<String, String> userCountRMap = redissonClient.getMap("pc_activityId_" + activityPartakeLogDTO.getActivityId() + "_phone_" + activityPartakeLogDTO.getPhone());
            String dayCountStr = totalCountRMap.get("dayCount_" + activityPartakeLogDTO.getResourceId());
            String userCountStr = userCountRMap.get("userCount_" + activityPartakeLogDTO.getResourceId());
            totalCountRMap.put("dayCount_" + activityPartakeLogDTO.getResourceId(),StringUtils.isEmpty(dayCountStr) ? "1" : String.valueOf(Integer.parseInt(dayCountStr) + 1));
            userCountRMap.put("userCount_" + activityPartakeLogDTO.getResourceId(),StringUtils.isEmpty(userCountStr) ? "1" : String.valueOf(Integer.parseInt(userCountStr) + 1));

            Long partakeLogId = activityPartakeLogDAO.save(activityPartakeLogDTO);

            LotteryResourceDTO lotteryResourceDTO = promotionHisResourceDO.clone(LotteryResourceDTO.class);
            lotteryResourceDTO.setWinLotteryId(partakeLogId);

            //消耗抽奖次数
            ActivityUserRelatedDO activityUserRelatedDO = new ActivityUserRelatedDO();
            activityUserRelatedDO.setId(activityUserRelated.getId());
            int drawNumber = (int) activityUserRelated.getLimits().get(ActivityTemplateNumberEnum.DRAW_NUMBER.getId());
            activityUserRelated.getLimits().put(ActivityTemplateNumberEnum.DRAW_NUMBER.getId(), drawNumber - 1);
            activityUserRelatedDO.setLimits(activityUserRelated.getLimits());
            activityUserRelatedDAO.updateById(activityUserRelatedDO);
//
//            //发送短信通知
//            if (promotionHisResourceDO.getResourceId() > -1) {
//                this.templateSend(requestVo.getPhone(), requestVo.getUserName(), promotionHisResourceDO.getName(), resourceCode,huafaConstantConfig.WINNING_NOTICE_TEMPLATE_ID);
//            }

            return new Data<>(lotteryResourceDTO);
        } catch (Exception e) {
            log.error("抽奖失败，报错原因：", e);
            if (flag) {
                //返还库存
                promotionActivityManager.incrRedisQty(hisResourceId, activityId);
            }
            return new Data<>(null, "500", e.getMessage());
        }
    }

    /**
     * 优惠券活动参与
     *
     * @param promotionActivityDO
     * @param requestVo
     * @return
     */
    private Data<Object> couponAct(PromotionActivityDO promotionActivityDO, ActivityPartakeRequest requestVo) {
        //优惠券活动
        if (ReceiveModeEnum.PAY.getId().equals(requestVo.getReceiveMode())) {
            //付费购券
            //判断是否重复订单
            activityOrderService.checkRepeatOrder(requestVo);
            //资源库存校验、校验领取次数、锁定缓存库存和数据库库存
            PromotionHisResourceDO promotionHisResourceDO = promotionHisResourceDAO.checkResourceQtyAndLimitTimeAndDecrQty(requestVo);
            try {
                //扣减缓存库存
                boolean flag = promotionActivityManager.decrRedisQty(promotionHisResourceDO.getId(), promotionActivityDO.getId());
                if (!flag) {
                    throw new ApplicationException(CommonExceptionCode.INVALIDATION, "奖品已经被抢光啦~下次再来吧~");
                }
                //不限项目将payNo传给projectId进行下单,如为指定项目则取前端传的值----(0-不限项目,1-指定项目)
                Integer resourcesAttribute = (Integer) promotionActivityDO.getExt().get("resourcesAttribute");
                if (ResourcesAttributeEnum.UNLIMITED.getId().equals(resourcesAttribute)) {
                    requestVo.setProjectId((String) promotionActivityDO.getExt().get("payNo"));
                }
                //创建订单
                Data<OrderPayResponseDTO> orderPayResponseDTOData = activityOrderService.placeOrder(requestVo, promotionActivityDO, promotionHisResourceDO);
                if (!CommonExceptionCode.SUCCESS.equals(orderPayResponseDTOData.getCode())) {
                    promotionActivityManager.incrRedisQty(promotionHisResourceDO.getId(), promotionActivityDO.getId());
                }
                return new Data<>(orderPayResponseDTOData.getdata(), orderPayResponseDTOData.getCode(), orderPayResponseDTOData.getMsg());
            } catch (Exception e) {
                log.error("下单失败，报错原因：{}", e);
                //返还库存
                promotionActivityManager.incrRedisQty(promotionHisResourceDO.getId(), promotionActivityDO.getId());
                return new Data<>(null, "500", e.getMessage());
            }
        } else if (ReceiveModeEnum.FREE.getId().equals(requestVo.getReceiveMode())) {
            //免费领券
            //资源库存校验、校验领取次数、锁定缓存库存和数据库库存
            PromotionHisResourceDO promotionHisResourceDO = promotionHisResourceDAO.checkResourceQtyAndLimitTimeAndDecrQty(requestVo);
            //执行领取逻辑
            this.freeReceiveCoupons(promotionHisResourceDO, requestVo.clone(ReceiveCouponRequestDTO.class));
        }
        return new Data<>(Boolean.TRUE);
    }

    private Object marathonAct(ActivityPartakeRequest requestVo, PromotionActivityDO promotionActivityDO) {
        // 校验验证码
        JSONArray jsonArrayData = JSONArray.parseArray(JSON.toJSONString(requestVo.getDynamicForm()));
        Map<Integer, String> dataMap = jsonArrayData.stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        object -> {
                            JSONObject item = (JSONObject) object;
                            return item.getInteger("formId");
                        },
                        object -> {
                            JSONObject item = (JSONObject) object;
                            return item.getString("inputValue");
                        }
                ));
        phoneService.checkSmsCode(requestVo.getPhone(), dataMap.get(4));

        RSet<String> phoneSet = redissonClient.getSet(String.format(RedisConstants.CACHE_PREV_KEY_ACT_MARATHON_PHONE, requestVo.getActivityId()));
        if (phoneSet.contains(requestVo.getPhone())) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "该手机号已经报名过了:" + requestVo.getPhone());
        }
        // 分配参赛码
        String marathonCode = promotionMarathonService.allocationMarathonCode(requestVo.getActivityId(), requestVo.getPhone(), promotionActivityDO.getEndTime());
        log.info("已经有{}人报名,{}正在报名,分配参赛码:{}", phoneSet.size(), requestVo.getPhone(), marathonCode);
        // 组装参赛码
        JSONObject codeItem = new JSONObject();
        codeItem.put("formId", 5);
        codeItem.put("type", "input");
        codeItem.put("title", "参赛码");
        codeItem.put("inputValue", marathonCode);
        codeItem.put("value", marathonCode);
        jsonArrayData.add(codeItem);
        requestVo.setDynamicForm(jsonArrayData);
        try {
            phoneSet.add(requestVo.getPhone());
            // 表单报名
            log.info("开始记录{}的报名属性:{}", requestVo.getPhone(), JSONObject.toJSONString(jsonArrayData));
            formAct(requestVo, promotionActivityDO);
            log.info("报名成功返回参赛码:{}", marathonCode);
            return marathonCode;
        } catch (Exception e) {
            log.error("马拉松报名失败,异常信息:" + e.getMessage(), e);
            // 回退
            phoneSet.remove(requestVo.getPhone());
        }
        return Boolean.FALSE;
    }

    /**
     * 参与表单活动
     *
     * @param requestVo
     * @param promotionActivityDO
     * @return
     */
    private Object formAct(ActivityPartakeRequest requestVo, PromotionActivityDO promotionActivityDO) {

        //表单活动时段较验
        if (requestVo.getDynamicForm() == null) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "提交内容不能为空!");
        }
        ActivityFormFeedbackDO activityFormFeedbackDO = customerFeedbackDAO.getFormFeedbackByPhone(requestVo.getActivityId(), requestVo.getPhone());

        JSONArray jsonArrayForm = (JSONArray) promotionActivityDO.getExt().get("dynamicForm");
        JSONArray jsonArrayData = JSONArray.parseArray(JSON.toJSONString(requestVo.getDynamicForm()));

        Map<Integer, String> dataMap = jsonArrayData.stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        object -> {
                            JSONObject item = (JSONObject) object;
                            return item.getInteger("formId");
                        },
                        object -> {
                            JSONObject item = (JSONObject) object;
                            return item.getString("inputValue");
                        }
                ));

        //限制人数较验
        for (int i = 0; i < jsonArrayForm.size(); i++) {
            JSONObject formObject = jsonArrayForm.getJSONObject(i);
            if (formObject.get("type").equals("timers")) {
                String value = dataMap.get(formObject.get("formId"));
                JSONArray pickerOptions = formObject.getJSONArray("pickerOptions");
                if (Objects.nonNull(pickerOptions) && pickerOptions.size() > 0) {
                    for (int j = 0; j < pickerOptions.size(); j++) {
                        JSONObject jsonObject = (JSONObject) pickerOptions.get(j);
                        if (jsonObject.get("time1").equals(value)) {//获取时段
                            Integer num = jsonObject.getInteger("num");//限制人数
                            String time1 = jsonObject.getString("time1");
                            //查询数据库
                            Integer cont = customerFeedbackDAO.getLimitCount(requestVo.getActivityId(), "\"inputValue\": \"" + time1 + "\"", requestVo.getPhone());
                            if (cont >= num) {
                                throw new ApplicationException(CommonExceptionCode.INVALIDATION, "当前时段参与人数已达到限制!");
                            }
                        }
                    }
                }
            }
        }

        Map<String, Object> limits = Maps.newHashMap();
        limits.put("dynamicForm", requestVo.getDynamicForm());
        if (Objects.nonNull(activityFormFeedbackDO)) {
            //已存在参与记录只进行信息更新
            activityFormFeedbackDO.setLimits(limits);
            activityFormFeedbackDO.setUpdatedTime(DateTime.now());
            customerFeedbackDAO.updateById(activityFormFeedbackDO);
        } else {//更新可不用选择项目,仅改表单内容
            activityFormFeedbackDO = requestVo.clone(ActivityFormFeedbackDO.class);
            activityFormFeedbackDO.setAppId(AppRuntimeEnv.getAppId());
            activityFormFeedbackDO.setTenantId(AppRuntimeEnv.getTenantId());
            activityFormFeedbackDO.setActivityType(FeedbackActivityTypeEnum.FORM.getId());
            activityFormFeedbackDO.setLimits(limits);
            customerFeedbackDAO.save(activityFormFeedbackDO);
//            if(flag){
//                promotionActivityManager.incrUserFeedBackCount(requestVo.getPhone(),requestVo.getActivityId(),requestVo.getHisResourceId());
//            }
            if ("marathon".equals(promotionActivityDO.getRemark())) {
                // 马拉松活动上报潜客池
                String pageIdStr = dataMap.get(2);
                if (NumberUtil.isNumber(pageIdStr)) {
                    String url = huafaConstantConfig.RS_CENTE_BASE_URL + CommonConstant.AD_OPT_USER_LOG_URL;
                    SysOptUserLogRequestDTO userInfo = new SysOptUserLogRequestDTO();
                    userInfo.setActId(activityFormFeedbackDO.getActivityId().intValue());
                    userInfo.setActName(promotionActivityDO.getName());
                    userInfo.setAppId(requestVo.getWxAppId());
                    userInfo.setHeadImgUrl(requestVo.getAvatar());
                    userInfo.setMobile(requestVo.getPhone());
                    userInfo.setNickname(requestVo.getNickName());
                    userInfo.setPageId(Integer.valueOf(dataMap.get(2)));
                    userInfo.setUnionId(requestVo.getUnionId());
                    userInfo.setSource(2);
                    userInfo.setSourceType(8);
                    try {
                        String paramStr = JSONUtil.toJsonStr(userInfo);
                        log.info("开始上传潜客池{}:{}", url, paramStr);
                        String mes = HttpUtil.post(url, paramStr);
                        log.info("上传潜客池结果:{}", mes);
                    } catch (Exception e) {
                        log.error("上传潜客池异常" + e.getMessage(), e);
                    }
                } else {
                    log.warn("用户{}选择的当前城市不上报潜客池:{}", requestVo.getPhone(), requestVo.getDynamicForm());
                }
            }
            //参与记录
            ActivityFormFeedbackDTO activityFormFeedbackDTO = activityFormFeedbackDO.clone(ActivityFormFeedbackDTO.class);
            //是否提交表单获奖-发放礼品
            PromotionHisResourceDO promotionHisResourceDO = new PromotionHisResourceDO();
            String resourceCode = generateIdUtil.getResourceCode();
            ActivityOrderDO activityOrderDO = new ActivityOrderDO();
            activityOrderDO.setType(requestVo.getType());
            activityOrderDO.setActivityId(requestVo.getActivityId());
            activityOrderDO.setUserId(requestVo.getUserId());
            activityOrderDO.setId(0L);
            activityOrderDO.setPhone(requestVo.getPhone());

            //是否发放奖品
            Integer submitFormAward = (Integer) promotionActivityDO.getExt().get("submitFormAward");
            boolean flag = false;
            if (WhetherEnum.YES.getId().equals(submitFormAward)) {
                List<PromotionHisResourceDO> hisResourceList = promotionHisResourceDAO.findByActivityId(requestVo.getActivityId());
                if (CollectionUtil.isNotEmpty(hisResourceList)) {
                    promotionHisResourceDO = hisResourceList.get(0);
                    //红包需要领取，话费不需要通过接口进行发放,需排除红包,红包进行手动领取
                    //卡券类
                    if (promotionHisResourceDO.getRemainingQuantity() > 0) {
                        if (PromotionResourceTypeEnum.COUPON.getId().equals(promotionHisResourceDO.getType())) {
                            flag = this.formGrantResource(promotionHisResourceDO, requestVo, activityFormFeedbackDTO, activityOrderDO, resourceCode);
                        } else if (PromotionResourceTypeEnum.THIRD_PARTY.getId().equals(promotionHisResourceDO.getType())) {
                            //第三方资源
                            boolean qtyFlag = promotionActivityManager.decrRedisQty(promotionHisResourceDO.getId(), requestVo.getActivityId());
                            try {
                                if (qtyFlag) {
                                    //取资源项目ID
                                    requestVo.setProjectName(promotionHisResourceDO.getProjectName());
                                    requestVo.setProjectId(promotionHisResourceDO.getProjectId());
                                    incentiveService.thirdVerify(requestVo, promotionHisResourceDO, activityFormFeedbackDTO, promotionActivityDO.getName(), resourceCode, SendTemplateNewsRequestDTO.MP_FROM_ZYT);
                                    promotionActivityManager.decrRemainQty(promotionHisResourceDO.getId(), 1L);
                                    flag = true;
                                }
                            } catch (Exception e) {
                                promotionActivityManager.incrRedisQty(promotionHisResourceDO.getId(), requestVo.getActivityId());
                                throw e;
                            }
                        }
                    } else {
                        log.info("=================表单活动数据库存不足=================>activityId:{}", requestVo.getActivityId());
                    }

                }
            }
            //添加参与记录
            ActivityPartakeLogDTO activityPartakeLogDTO = ActivityInfoConverter.partakeLogConverter(activityOrderDO, activityFormFeedbackDTO, promotionHisResourceDO, resourceCode);
            activityPartakeLogDAO.save(activityPartakeLogDTO);
            //信息上报
            miniProgramService.adoptUserLog(activityFormFeedbackDO, null, null);
            return flag ? promotionHisResourceDO : null;
        }
        return null;
    }

    /**
     * 表单活动资源领取核销记录
     *
     * @param promotionHisResourceDO
     * @param requestVo
     * @param activityFormFeedbackDTO
     * @param activityOrderDO
     * @return
     */
    private Boolean formGrantResource(PromotionHisResourceDO promotionHisResourceDO, ActivityPartakeRequest requestVo, ActivityFormFeedbackDTO activityFormFeedbackDTO, ActivityOrderDO activityOrderDO, String resourceCode) {
        //扣减缓存库存
        boolean flag = promotionActivityManager.decrRedisQty(promotionHisResourceDO.getId(), requestVo.getActivityId());
        if (!flag) {
            log.info("=================表单活动redis库存不足=================>activityId:{}", requestVo.getActivityId());
            return Boolean.FALSE;//库存不足
        }
        try {
            //数据库-更新剩余资源数量
            promotionActivityManager.decrRemainQty(promotionHisResourceDO.getId(), 1L);
            //添加核销记录
            Map<String, Object> projectInfo = new HashMap<>();
            if (StringUtil.isNotEmpty(requestVo.getProjectId()) && StringUtil.isNotEmpty(requestVo.getProjectName())) {
                projectInfo.put("projectId", requestVo.getProjectId());
                projectInfo.put("projectName", requestVo.getProjectName());
            }
            activityOrderDO.setExt(projectInfo);
            activityOrderDO.setPayTime(DateTime.now());
            ActivityVerifyDO activityVerifyDO = ActivityInfoConverter.verifyConverter(activityOrderDO, activityFormFeedbackDTO, promotionHisResourceDO, resourceCode, VerifyStatusEnum.NO_VERIFY.getId(), WhetherEnum.NO.getId());
            activityVerifyDAO.save(activityVerifyDO);

            //发送模板消息
            this.templateSend(activityVerifyDO.getPhone(), activityVerifyDO.getUserName(), promotionHisResourceDO.getName(), resourceCode, huafaConstantConfig.SUBMIT_FORM_WIN_TEMPLATE_ID);

            //小程序订阅消息
            SendTemplateNewsRequestDTO dto = new SendTemplateNewsRequestDTO();
            int[] timeByCalendar = DateUtils.getTimeByCalendar(activityVerifyDO.getCreatedTime());
            Map<String, Object> templateParam = Maps.newHashMap();
            templateParam.put("thing1", promotionHisResourceDO.getName());
            templateParam.put("time2", " " + timeByCalendar[0] + "年" + timeByCalendar[1] + "月" + timeByCalendar[2] + "日 " + timeByCalendar[3] + ":" + timeByCalendar[4]);
            templateParam.put("thing3", "请到我的礼品栏目查看详情");
            dto.setTemplateParam(JSON.toJSONString(templateParam));
            dto.setTemplateId(huafaConstantConfig.MINI_SUBMIT_FORM_WIN_TEMPLATE_ID);
            dto.setUserOpenId(activityVerifyDO.getUserId());
            dto.setMpFrom(SendTemplateNewsRequestDTO.MP_FROM_ZYT);
            dto.setCreateId(activityVerifyDO.getUserName());
            interactionCenterService.miniTemplateNews(dto);

            return Boolean.TRUE;
        } catch (Exception e) {
            promotionActivityManager.incrRedisQty(promotionHisResourceDO.getId(), requestVo.getActivityId());
            throw e;
        }
    }



    /**
     * 帮助砍价人数已完成，活动砍价成功
     *
     * @param partakeLog
     * @param currentFissionCount
     */
    private void checkFissionStatus(ActivityPartakeLogDO partakeLog, Integer currentFissionCount, String name) {
        if (currentFissionCount != null && currentFissionCount.equals(partakeLog.getNeedFissonCount())) {
            updateActivityPartakeLog(partakeLog.getId(), FissonStatusEnum.SUCCESS.getId(), partakeLog.clone(ActivityPartakeLogDTO.class), name);
        }
    }

    private Boolean updateActivityPartakeLog(Long id, Integer fissionStatus, ActivityPartakeLogDTO partakeLog, String coupon) {
        ActivityPartakeLogDO activityPartakeLogDO = new ActivityPartakeLogDO();
        activityPartakeLogDO.setId(id);
        activityPartakeLogDO.setFissonStatus(fissionStatus);
        boolean b = activityPartakeLogDAO.updateById(activityPartakeLogDO);
        if (b && FissonStatusEnum.SUCCESS.getId().equals(fissionStatus)) {
            //砍价成功人数神策埋点
            asynchronousService.sensorsBuriedPointFission(id, SensorsEventEnum.BARGAIN_SUCCESS.getCode());

//            //发送砍价成功通知
//            this.templateSend(partakeLog.getPhone(),partakeLog.getUserName(),coupon,partakeLog.getCode(),huafaConstantConfig.BARGAIN_SUCCESS_TEMPLATE_ID);
        }
        return b;
    }




    private ActivityPartakeLogDO checkParticipant(ActivityPartakeRequest requestVo, List<PromotionActivityLimitDO> promotionActivityLimitList) {
        if (requestVo.getSponsorId() == null) {
            throw new ApplicationException("发起人信息id不能为空");
        }

        //查询用户砍价信息
        ActivityPartakeLogDO partakeLog = activityPartakeLogDAO.getById(requestVo.getSponsorId());
        if (partakeLog == null) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "砍价信息不存在");
        } else if (ObjectUtil.notEqual(partakeLog.getFissonStatus(), FissonStatusEnum.PROCESSING.getId())) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "砍价活动已完成");
        } else if (partakeLog.getCurrentFissonCount() >= partakeLog.getNeedFissonCount()) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "活动邀请砍价人数已满");
        } else if (partakeLog.getPhone().equals(requestVo.getPhone())) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "不能为自己砍价");
        } else if (partakeLog.getFissonEndTime().before(DateUtils.now())) {
            //结束有效期，校验活动是否成功
            PromotionHisResourceDO byId = promotionHisResourceDAO.getById(requestVo.getHisResourceId());
            if (byId == null) {
                throw new ApplicationException(CommonExceptionCode.INVALIDATION, "资源信息丢失");
            }
            this.checkFissionEndTime(partakeLog.clone(ActivityPartakeLogDTO.class), byId.getName());
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "活动已结束");
        }

        List<BaseActivityDTO> numberList = BargainingConverter.getPromotionActivityLimit(promotionActivityLimitList, PATemplateBaseEnum.BARGAIN.getId());

        //用户帮砍次数
        Integer helpTimes = BargainingConverter.elLimitConverter(numberList, ActivityTemplateNumberEnum.HELP_TIMES.getId());
        if (helpTimes <= 0) {
            helpTimes = 1;
        }
        //可帮参与当前砍价活动的好友砍价的总次数,单个砍价活动默认砍一刀
        //查询此活动用户帮助次数
//        int count = activityFissionLogDAO.countByUserIdPartakeLogId(partakeLog.getId(),requestVo.getPhone(),null);
        Boolean count = helpTimesCheck(partakeLog.getId(), requestVo.getUserId(), partakeLog.getActivityId(), helpTimes);
        if (!count) {
            throw new ApplicationException("您已帮该好友砍价过");
        }

        return partakeLog;
    }

    @Override
    public Boolean helpTimesCheck(Long partakeLogId, String userId, Long activityId, Integer helpTimes) {
        //可帮参与当前砍价活动的好友砍价的总次数,单个砍价活动默认砍一刀
        //查询此活动用户帮助次数
        int count = activityFissionLogDAO.countByUserIdPartakeLogId(partakeLogId, userId, null);

        if (count > 0) {
            return false;
        }
        count = activityFissionLogDAO.countByUserIdPartakeLogId(null, userId, activityId);
        if (count >= helpTimes) {
            return false;
        }
        return true;
    }

    @Override
    public PageBean<PromotionActivityVO> getTiktokActivityList(PromotionActivityQuery query) {
        //进行中且是在抖音小程序中展示的
        query.setStatus(Integer.valueOf(ActivityStatusEnum.IN_PROGRESS.getId()));
        query.setDeliveryChannel(2);
        return this.getAllRunningActivityList(query);
    }

    /**
     * 活动已过有效期，帮助砍价人数已完成 则活动砍价成功，否则砍价失败
     *
     * @param partakeLog
     */
    @Override
    public Integer checkFissionEndTime(ActivityPartakeLogDTO partakeLog, String name) {
        if (partakeLog.getCurrentFissonCount() >= partakeLog.getNeedFissonCount()) {
            Boolean aBoolean = updateActivityPartakeLog(partakeLog.getId(), FissonStatusEnum.SUCCESS.getId(), partakeLog, name);
            if (aBoolean) {
                return BargainActTypeEnum.SUCCESSFUL.getId();
            }
        } else {
            Boolean aBoolean = updateActivityPartakeLog(partakeLog.getId(), FissonStatusEnum.FAILURE.getId(), partakeLog, name);
            if (aBoolean) {
                return BargainActTypeEnum.FAILED.getId();
            }
        }
        return null;
    }

    /**
     * 较验重复点击
     *
     * @param key
     * @param requestUUId
     */
    private void checkClickRepeat(String key, String requestUUId) {
        RBucket<Object> bucket = redissonClient.getBucket(key);
        String uuId = (String) bucket.get();
        if (StringUtil.isEmpty(uuId) || StringUtil.isEmpty(requestUUId) || !requestUUId.equals(uuId)) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "请勿频繁提交");
        }
        bucket.delete();
    }

    /**
     * 免费领取
     *
     * @param promotionHisResourceDO
     * @param dto
     * @return
     */
    private Boolean freeReceiveCoupons(PromotionHisResourceDO promotionHisResourceDO, ReceiveCouponRequestDTO dto) {

        //扣减缓存库存
        boolean flag = promotionActivityManager.decrRedisQty(promotionHisResourceDO.getId(), dto.getActivityId());
        if (!flag) {
            throw new ApplicationException("奖品已经被抢光啦~下次再来吧~");
        }
        try {
            String resourceCode = generateIdUtil.getResourceCode();

            //数据库-更新剩余资源数量
            promotionActivityManager.decrRemainQty(promotionHisResourceDO.getId(), 1L);

            //获取信息登记
            CustomerFeedbackQuery query = new CustomerFeedbackQuery();
            query.setActivityId(dto.getActivityId());
            //query.setUserId(dto.getUserId());
            query.setPhone(dto.getPhone());
            query.setResourceId(promotionHisResourceDO.getId());
            List<ActivityFormFeedbackDO> formFeedbackList = customerFeedbackDAO.pageList(query).getContent();
            ActivityFormFeedbackDTO activityFormFeedbackDTO = null;
            if (formFeedbackList.size() > 0) {
                activityFormFeedbackDTO = formFeedbackList.get(0).clone(ActivityFormFeedbackDTO.class, CloneDirection.FORWARD);
            }

            ActivityOrderDO activityOrderDO = new ActivityOrderDO();
            activityOrderDO.setType(dto.getType());
            activityOrderDO.setActivityId(dto.getActivityId());
            activityOrderDO.setUserId(dto.getUserId());
            activityOrderDO.setPhone(dto.getPhone());//add by 0713
            activityOrderDO.setId(0L);
            //添加参与记录
            ActivityPartakeLogDTO activityPartakeLogDTO = ActivityInfoConverter.partakeLogConverter(activityOrderDO, activityFormFeedbackDTO, promotionHisResourceDO, resourceCode);
            activityPartakeLogDTO.setProjectId(dto.getProjectId());
            activityPartakeLogDTO.setProjectName(dto.getProjectName());
            activityPartakeLogDAO.save(activityPartakeLogDTO);


            //添加核销记录
            activityOrderDO.setPayTime(DateTime.now());
            Map<String, Object> projectInfo = new HashMap<>();
            if (StringUtil.isNotEmpty(dto.getProjectId()) && StringUtil.isNotEmpty(dto.getProjectName())) {
                projectInfo.put("projectId", dto.getProjectId());
                projectInfo.put("projectName", dto.getProjectName());
            }
            activityOrderDO.setExt(projectInfo);
            ActivityVerifyDO activityVerifyDO = ActivityInfoConverter.verifyConverter(activityOrderDO, activityFormFeedbackDTO, promotionHisResourceDO, resourceCode, VerifyStatusEnum.NO_VERIFY.getId(), WhetherEnum.NO.getId());
            activityVerifyDAO.save(activityVerifyDO);

            //发送短信通知
            this.templateSend(activityVerifyDO.getPhone(), activityVerifyDO.getUserName(), promotionHisResourceDO.getName(), resourceCode, huafaConstantConfig.COUPON_ARRIVES_TEMPLATE_ID);


            //小程序订阅消息
            SendTemplateNewsRequestDTO sendTemplateNews = new SendTemplateNewsRequestDTO();
            Map<String, Object> templateParam = Maps.newHashMap();
            templateParam.put("thing1", promotionHisResourceDO.getName());
            templateParam.put("time2", DateUtils.format(activityVerifyDO.getCreatedTime()));
            templateParam.put("thing3", "请到我的礼品栏目查看详情");
            sendTemplateNews.setTemplateParam(JSON.toJSONString(templateParam));
            sendTemplateNews.setTemplateId(huafaConstantConfig.MINI_COUPON_ARRIVES_TEMPLATE_ID);
            sendTemplateNews.setUserOpenId(activityVerifyDO.getUserId());
            sendTemplateNews.setMpFrom(SendTemplateNewsRequestDTO.MP_FROM_ZYT);
            sendTemplateNews.setCreateId(activityVerifyDO.getUserName());
            interactionCenterService.miniTemplateNews(sendTemplateNews);

            return Boolean.TRUE;
        } catch (Exception e) {
            promotionActivityManager.incrRedisQty(promotionHisResourceDO.getId(), dto.getActivityId());
            throw e;
        }
    }

    @Override
    public PageBean<ActivityPartakeLogResponseDTO> actPartakeLogList(PartakeLogQuery query) {
        query.setUserId(null);
        if (StringUtil.isNotBlank(query.getPrizeResult()) && query.getPrizeResult().equals(PromotionActivityConstant.NOT_WINNING)) {
            query.setPrizeResult(PromotionActivityConstant.THANK_PARTICIPATION);
        }
        PageBean<ActivityPartakeLogResponseDTO> page;
        if (StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId().equals(query.getPaTemplateId().toString())
                || StrategyGroupEnum.HF_FISSION_REDUCEPRICE_ACT.getId().equals(query.getPaTemplateId().toString())
                || StrategyGroupEnum.HF_SIGN_ACT.getId().equals(query.getPaTemplateId().toString())) {
            page = activityPartakeLogDAO.findPageSerial(query);
        } else {
            page = activityPartakeLogDAO.findPage(query);
        }
        this.getCustomerFeedbackList(page.getContent(), query);
        return page;

    }

    private List<EnrollmentInfoVO> getFeedbackInfoByActivityId(Long activityId) {
        PromotionActivityDO promotionActivityDO = this.promotionActivityDAO.getById(activityId);
        ActivityExtVO ext = ActivityExtConverter.converter(promotionActivityDO.getExt());
        if (Objects.isNull(ext)) {
            return Lists.newArrayList();
        }
        return ext.getFeedbackInfo();
    }

    /**
     * 拼装用户登记明细和 礼品券发放方式
     *
     * @param content
     * @param query
     */
    private void getCustomerFeedbackList(List<ActivityPartakeLogResponseDTO> content, PartakeLogQuery query) {
        if (CollectionUtil.isEmpty(content)) {
            return;
        }
        //提取用户id
        Set<String> phoneList = content.stream().map(ActivityPartakeLogResponseDTO::getPhone).collect(Collectors.toSet());

        //提取资源id
        Set<Long> resourceIds = content.stream().filter(item -> item.getResourceId() != null && item.getResourceId() > 0)
                .map(ActivityPartakeLogResponseDTO::getResourceId).collect(Collectors.toSet());

        CustomerFeedbackQuery customerFeedbackQuery = new CustomerFeedbackQuery();
        customerFeedbackQuery.setActivityId(query.getActivityId());
        customerFeedbackQuery.setPhoneList(new ArrayList<>(phoneList));
        customerFeedbackQuery.setSize(9999);
        customerFeedbackQuery.setPage(1);
        List<ActivityFormFeedbackDO> list = customerFeedbackDAO.pageList(customerFeedbackQuery).getContent();

        Map<String, ActivityFormFeedbackDO> formFeedbackMap = Maps.newHashMap();
        Map<Long, PromotionHisResourceDO> resourceMap = Maps.newHashMap();
        PromotionActivityDO promotionActivityDO = promotionActivityDAO.getById(query.getActivityId());

        if (CollectionUtil.isNotEmpty(list)) {
            if (promotionActivityDO.getPaTemplateId().toString().equals(StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId())
                    || promotionActivityDO.getPaTemplateId().toString().equals(StrategyGroupEnum.HF_SECKILL_ACT.getId())
                    || promotionActivityDO.getPaTemplateId().toString().equals(StrategyGroupEnum.HF_SIGN_ACT.getId())) {
                formFeedbackMap = list.stream().collect(Collectors.toMap(formFeedbackDO -> formFeedbackDO.getPhone() + "_" + formFeedbackDO.getActivityId(), formFeedbackDO -> formFeedbackDO, (formFeedback1, formFeedback2) -> formFeedback1));
            } else {
                formFeedbackMap = list.stream().collect(Collectors.toMap(formFeedbackDO -> formFeedbackDO.getPhone() + "_" + formFeedbackDO.getResourceId(), formFeedbackDO -> formFeedbackDO, (formFeedback1, formFeedback2) -> formFeedback1));
            }
        }
        if (CollectionUtil.isNotEmpty(resourceIds)) {
            QueryWrapper<PromotionHisResourceDO> queryWrapperResource = new QueryWrapper<>();
            queryWrapperResource.lambda().in(PromotionHisResourceDO::getId, resourceIds);
            List<PromotionHisResourceDO> resourceList = promotionHisResourceDAO.list(queryWrapperResource);
            if (CollectionUtil.isNotEmpty(resourceList)) {
                resourceMap = resourceList.stream().collect(Collectors.toMap(PromotionHisResourceDO::getId, Function.identity()));
            }
        }

        Map<Long, PromotionHisResourceDO> finalResourceMap = resourceMap;
        Map<String, ActivityFormFeedbackDO> finalFormFeedbackMap = formFeedbackMap;
        try {
            content.forEach(log -> {
                ActivityFormFeedbackDO activityFormFeedbackDO;
                if (promotionActivityDO.getPaTemplateId().toString().equals(StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId())
                        || promotionActivityDO.getPaTemplateId().toString().equals(StrategyGroupEnum.HF_SECKILL_ACT.getId())
                        || promotionActivityDO.getPaTemplateId().toString().equals(StrategyGroupEnum.HF_SIGN_ACT.getId())) {
                    activityFormFeedbackDO = finalFormFeedbackMap.get(log.getPhone() + "_" + log.getActivityId());
                } else {
                    activityFormFeedbackDO = finalFormFeedbackMap.get(log.getPhone() + "_" + log.getResourceId());
                }
                if (activityFormFeedbackDO != null) {
                    ActivityFormFeedbackDTO clone = activityFormFeedbackDO.clone(ActivityFormFeedbackDTO.class, CloneDirection.OPPOSITE);
                    log.setFeedback(PhoneEncryUtils.feeBackDesensitization(clone));
                }

                //资源信息
                PromotionHisResourceDO promotionHisResourceDO = finalResourceMap.get(log.getResourceId());
                if (promotionHisResourceDO != null) {
                    log.setResourceHisDetailResponseDTO(promotionHisResourceDO.clone(ResourceHisDetailResponseDTO.class));
                    if (promotionHisResourceDO.getResourceId() != -1) {
                        log.setGrantWay(String.valueOf(promotionHisResourceDO.getGrantWay()));
                    }
                }
                //助力活动
                if (StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())
                        || StrategyGroupEnum.HF_CARD_COLLECTING_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())
                        || StrategyGroupEnum.HF_SIGN_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())) {
                    //当前助力活动可领取的奖品
                    List<ActivityFissionAssistResourceDO> assistResourceList = activityFissionAssistResourceDAO.getListByPartakeLogId(log.getId(), 1);
                    if (CollectionUtil.isNotEmpty(assistResourceList)) {
                        List<Long> hisResourceIds = assistResourceList.stream().map(ActivityFissionAssistResourceDO::getResourceId).distinct().collect(Collectors.toList());
                        List<PromotionHisResourceDO> promotionHisResourceDOS = promotionHisResourceDAO.listByIds(hisResourceIds);
                        Map<Long, PromotionHisResourceDO> promotionHisResourceDOMap = promotionHisResourceDOS.stream().collect(Collectors.toMap(PromotionHisResourceDO::getId, item -> item));
                        String names = assistResourceList.stream().map(item -> {
                            PromotionHisResourceDO item2 = promotionHisResourceDOMap.get(item.getResourceId());
                            if (item2 != null && item2.getResourceId() > 0) {
                                return item2;
                            } else {
                                return null;
                            }
                        }).filter(Objects::nonNull).map(PromotionHisResourceDO::getName).collect(Collectors.joining(","));
                        log.setResourceNames(names);
                    }
                }
                String fissonEndTime = DateTimeUtils.calculateIntervalTime(DateTime.now(), log.getFissonEndTime());
                log.setSurplusTimeFission(StringUtil.isNotEmpty(fissonEndTime) ? fissonEndTime : "0小时0分0秒");
                if (Objects.equals(StrategyGroupEnum.HF_SIGN_UP_ACT.getId(),query.getPaTemplateId().toString())) {
                    log.setFullPhone(log.getPhone());
                }
                //手机号脱敏
                log.setPhone(StringUtil.isNotEmpty(log.getPhone()) ? PhoneEncryUtils.encode(log.getPhone()) : "");
            });
        } catch (Exception e) {
            log.error("获取资源信息异常!", e);
        }
    }

    /**
     * 拼装用户登记明细和 礼品券发放方式----导出
     *
     * @param content
     * @param activityId
     */
    private void getCustomerFeedbackListExport(List<ActivityPartakeLogResponseDTO> content, Long activityId) {
        if (CollectionUtil.isEmpty(content)) {
            return;
        }
        //提取用户id
        Set<String> phoneList = content.stream().map(ActivityPartakeLogResponseDTO::getPhone).collect(Collectors.toSet());

        //提取资源id
        Set<Long> resourceIds = content.stream().map(ActivityPartakeLogResponseDTO::getResourceId).collect(Collectors.toSet());

        CustomerFeedbackQuery customerFeedbackQuery = new CustomerFeedbackQuery();
        customerFeedbackQuery.setActivityId(activityId);
        customerFeedbackQuery.setPhoneList(new ArrayList<>(phoneList));
        customerFeedbackQuery.setSize(9999);
        customerFeedbackQuery.setPage(1);
        List<ActivityFormFeedbackDO> list = customerFeedbackDAO.pageList(customerFeedbackQuery).getContent();

        Map<String, ActivityFormFeedbackDO> formFeedbackMap = Maps.newHashMap();
        Map<Long, PromotionHisResourceDO> resourceMap = Maps.newHashMap();
        Map<String, ActivityAnalysisResponseDTO> analysisMap = Maps.newHashMap();
        PromotionActivityDO promotionActivityDO = promotionActivityDAO.getById(activityId);

        if (CollectionUtil.isNotEmpty(list)) {
            if (promotionActivityDO.getPaTemplateId().toString().equals(StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId())
                    || promotionActivityDO.getPaTemplateId().toString().equals(StrategyGroupEnum.HF_SECKILL_ACT.getId())
                    || promotionActivityDO.getPaTemplateId().toString().equals(StrategyGroupEnum.HF_SIGN_ACT.getId())) {
                formFeedbackMap = list.stream().collect(Collectors.toMap(formFeedbackDO -> formFeedbackDO.getPhone() + "_" + formFeedbackDO.getActivityId(), formFeedbackDO -> formFeedbackDO, (formFeedback1, formFeedback2) -> formFeedback1));
            } else {
                formFeedbackMap = list.stream().collect(Collectors.toMap(formFeedbackDO -> formFeedbackDO.getPhone() + "_" + formFeedbackDO.getResourceId(), formFeedbackDO -> formFeedbackDO, (formFeedback1, formFeedback2) -> formFeedback1));
            }
        }

        QueryWrapper<PromotionHisResourceDO> queryWrapperResource = new QueryWrapper<>();
        queryWrapperResource.lambda().in(PromotionHisResourceDO::getId, resourceIds);
        List<PromotionHisResourceDO> resourceList = promotionHisResourceDAO.list(queryWrapperResource);
        if (CollectionUtil.isNotEmpty(resourceList)) {
            resourceMap = resourceList.stream().collect(Collectors.toMap(PromotionHisResourceDO::getId, Function.identity()));
        }

        Map<Long, PromotionHisResourceDO> finalResourceMap = resourceMap;
        Map<String, ActivityFormFeedbackDO> finalFormFeedbackMap = formFeedbackMap;
        try {
            content.forEach(log -> {
                ActivityFormFeedbackDO activityFormFeedbackDO = null;
                if (promotionActivityDO.getPaTemplateId().toString().equals(StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId())
                        || promotionActivityDO.getPaTemplateId().toString().equals(StrategyGroupEnum.HF_SECKILL_ACT.getId())
                        || promotionActivityDO.getPaTemplateId().toString().equals(StrategyGroupEnum.HF_SIGN_ACT.getId())) {
                    activityFormFeedbackDO = finalFormFeedbackMap.get(log.getPhone() + "_" + log.getActivityId());
                } else {
                    activityFormFeedbackDO = finalFormFeedbackMap.get(log.getPhone() + "_" + log.getResourceId());
                }
                if (activityFormFeedbackDO != null) {

                    ActivityFormFeedbackDTO clone = activityFormFeedbackDO.clone(ActivityFormFeedbackDTO.class, CloneDirection.OPPOSITE);
                    log.setFeedback(clone);
                }
                //资源信息
                PromotionHisResourceDO promotionHisResourceDO = finalResourceMap.get(log.getResourceId());
                if (promotionHisResourceDO != null) {
                    log.setResourceHisDetailResponseDTO(promotionHisResourceDO.clone(ResourceHisDetailResponseDTO.class));
                    if (promotionHisResourceDO.getResourceId() != -1) {
                        log.setGrantWay(String.valueOf(promotionHisResourceDO.getGrantWay()));
                    }
                }
                //助力活动
                if (StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())
                    || StrategyGroupEnum.HF_SIGN_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())) {
                    //当前助力活动可领取的奖品
                    List<ActivityFissionAssistResourceDO> assistResourceList = activityFissionAssistResourceDAO.getListByPartakeLogId(log.getId(), 1);
                    if (CollectionUtil.isNotEmpty(assistResourceList)) {
                        List<Long> hisResourceIds = assistResourceList.stream().map(ActivityFissionAssistResourceDO::getResourceId).distinct().collect(Collectors.toList());
                        List<PromotionHisResourceDO> promotionHisResourceDOS = promotionHisResourceDAO.listByIds(hisResourceIds);
                        Map<Long, PromotionHisResourceDO> promotionHisResourceDOMap = promotionHisResourceDOS.stream().collect(Collectors.toMap(PromotionHisResourceDO::getId, item -> item));
                        String names = assistResourceList.stream().map(item -> {
                            PromotionHisResourceDO item2 = promotionHisResourceDOMap.get(item.getResourceId());
                            if (item2 != null && item2.getResourceId() > 0) {
                                return item2;
                            } else {
                                return null;
                            }
                        }).filter(Objects::nonNull).map(PromotionHisResourceDO::getName).collect(Collectors.joining(","));
                        log.setResourceNames(names);
                    }
                }
                String fissonEndTime = DateTimeUtils.calculateIntervalTime(DateTime.now(), log.getFissonEndTime());
                log.setSurplusTimeFission(StringUtil.isNotEmpty(fissonEndTime) ? fissonEndTime : "0小时0分0秒");
            });
        } catch (Exception e) {
            log.error("获取资源信息异常!", e);
        }
    }

    private List<ResourceHisDetailResponseDTO> getPrizeList(Long activityId) {
        QueryWrapper<PromotionHisResourceDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PromotionHisResourceDO::getActivityId, activityId);
        queryWrapper.lambda().eq(PromotionHisResourceDO::getDeleted, SuperEntity.DR_NORMAL);
        List<PromotionHisResourceDO> list = promotionHisResourceDAO.list(queryWrapper);
        List<ResourceHisDetailResponseDTO> resourceHisDetailResponseDTOS = ObjectCloneUtils.convertList(list, ResourceHisDetailResponseDTO.class);
        return Optional.ofNullable(resourceHisDetailResponseDTOS).orElse(org.apache.commons.compress.utils.Lists.newArrayList());
    }

    @Override
    public PageBean<PromotionActivityMiniVO> findMyActivityList(PromotionActivityQuery query) {
        PageBean<PromotionActivityDO> myActivityList = null;
        //如果有传电话，则查询该用户的活动列表
        if (StrUtil.isNotBlank(HuafaRuntimeEnv.getPhone())) {
            query.setPhone(HuafaRuntimeEnv.getPhone());
        }
        if (query.getQueryType() == 1 || query.getQueryType() == 3) {
            myActivityList = promotionActivityDAO.findMyActivityList(query);
        } else {
            Assert.notNull(query.getDeliveryChannel(),"发布渠道不能为空");
            myActivityList = promotionActivityDAO.findMyPublishActivityList(query);
        }
        PageBean<PromotionActivityMiniVO> pageBean = ObjectCloneUtils.convertPageBean(myActivityList, PromotionActivityMiniVO.class);
        List<PromotionActivityMiniVO> content = pageBean.getContent();
        if (CollectionUtil.isNotEmpty(content)) {
            List<Long> ids = content.stream().map(PromotionActivityMiniVO::getId).collect(Collectors.toList());
            List<ActivityPageDO> pageList = activityPageDAO.getByActivity(ids, ActivityTypeEnum.ACTIVITY.getId());
            Map<Long, ActivityPageDO> pageMap = pageList.stream().collect(Collectors.toMap(ActivityPageDO::getActivityId, item -> item));

            //处理奖品列表
            List<PromotionHisResourceDO> resourceDOList = promotionHisResourceDAO.findByActivityIds(ids);
            List<ResourceHisDetailResponseDTO> resourceHisDetailResponseDTOS = ObjectCloneUtils.convertList(resourceDOList, ResourceHisDetailResponseDTO.class);
            Map<Long,List<ResourceHisDetailResponseDTO>> resourceMap = resourceHisDetailResponseDTOS.stream().collect(Collectors.groupingBy(ResourceHisDetailResponseDTO::getActivityId));

            //处理标签,参与人数,只社群小程序才查
            Map<Long,List<TagItemDTO>> tagsMap = new HashMap<>();
            Map<Long,Long> partakeCountMap = new HashMap<>();
            if (query.getDeliveryChannel() != null && Objects.equals(DeliveryChannelEnum.SQ.getId(),query.getDeliveryChannel().toString())) {
                tagsMap = associationService.getAssociationListBatch(ids, AssociationTypeEnum.ACTIVITY_TAG).stream().collect(Collectors.groupingBy(PromotionActivityAssociation::getMainId,Collectors.mapping(associationService::convertToTagItem, Collectors.toList())));
                partakeCountMap = activityPartakeLogDAO.partakeCount(ids);
            }
            for (PromotionActivityMiniVO act : content) {
                if (pageMap.get(act.getId()) != null) {
                    act.setActivityIconUrl(pageMap.get(act.getId()).getActivityIconUrl());
                    act.setActivityPageDTO(pageMap.get(act.getId()).clone(ActivityPageDTO.class));
                }
                act.setPrizeList(resourceMap.get(act.getId()));
                act.setTags(tagsMap.get(act.getId()));
                act.setTotalCnt(partakeCountMap.getOrDefault(act.getId(),0L));
            }
        }
        return pageBean;
    }

    @Override
    public PageBean<PromotionActivityVO> getAllRunningActivityList(PromotionActivityQuery query) {
//        PromotionActivityQuery query = new PromotionActivityQuery();

        //TODO 该接口暂时只能查询正在进行的优惠券活动
//        query.setPaTemplateId(Integer.valueOf(StrategyGroupEnum.HF_COUPON_ACT.getId()));
//        query.setStatus(Integer.valueOf(ActivityStatusEnum.IN_PROGRESS.getId()));
        String key = DigestUtil.md5Hex(JsonUtil.bean2JsonString(query));
        String finalKey = RedisConstants.CACHE_PREV_KEY_RUNNINGACT_INFO + key;
        RBucket<PageBean<PromotionActivityVO>> bucket = redissonClient.getBucket(finalKey);
        PageBean<PromotionActivityVO> pageBean = bucket.get();
        if (pageBean != null) {
            return pageBean;
        }

        PageBean<PromotionActivityDO> myActivityList = promotionActivityDAO.findAllPublishActivityList(query);
        pageBean = ObjectCloneUtils.convertPageBean(myActivityList, PromotionActivityVO.class);
        List<PromotionActivityVO> content = pageBean.getContent();
        if (CollectionUtil.isNotEmpty(content)) {
            //处理活动页
            List<Long> ids = content.stream().map(PromotionActivityVO::getId).collect(Collectors.toList());
            List<ActivityPageDO> pageList = activityPageDAO.getByActivity(ids, ActivityTypeEnum.ACTIVITY.getId());
            Map<Long, ActivityPageDO> pageMap = pageList.stream().collect(Collectors.toMap(ActivityPageDO::getActivityId, item -> item));

            //处理奖品列表
            List<PromotionHisResourceDO> resourceDOList = promotionHisResourceDAO.findByActivityIds(ids);
            List<ResourceHisDetailResponseDTO> resourceHisDetailResponseDTOS = ObjectCloneUtils.convertList(resourceDOList, ResourceHisDetailResponseDTO.class);
            Map<Long,List<ResourceHisDetailResponseDTO>> resourceMap = resourceHisDetailResponseDTOS.stream().collect(Collectors.groupingBy(ResourceHisDetailResponseDTO::getActivityId));

            //处理标签,参与人数,只社群小程序才查
            Map<Long,List<TagItemDTO>> tagsMap = new HashMap<>();
            Map<Long,Long> partakeCountMap = new HashMap<>();
            if (query.getDeliveryChannel() != null && Objects.equals(DeliveryChannelEnum.SQ.getId(),query.getDeliveryChannel().toString())) {
                tagsMap = associationService.getAssociationListBatch(ids, AssociationTypeEnum.ACTIVITY_TAG).stream().collect(Collectors.groupingBy(PromotionActivityAssociation::getMainId,Collectors.mapping(associationService::convertToTagItem, Collectors.toList())));
                partakeCountMap = activityPartakeLogDAO.partakeCount(ids);

            }

            for (PromotionActivityVO act : content) {
                ActivityPageDO page = pageMap.get(act.getId());
                //act.setActivityIconUrl(page.getActivityIconUrl());
                act.setActivityPageDTO(page.clone(ActivityPageDTO.class, CloneDirection.OPPOSITE));
                act.setPrizeList(resourceMap.get(act.getId()));
                act.setTags(tagsMap.get(act.getId()));
                act.setTotalCnt(partakeCountMap.getOrDefault(act.getId(),0L));
            }
        }
        bucket.set(pageBean,10, TimeUnit.MINUTES);//30分钟
        redissonClient.getSet(RedisConstants.CACHE_PREV_KEY_RUNNINGACT_INFO_KEYS).add(finalKey);
        return pageBean;
    }

    //活动规则 校验
    private void checkActivityLimits(ActivityPartakeRequest requestVo, PromotionActivityDO promotionActivityDO, List<PromotionActivityLimitDO> promotionActivityLimitList, ActivityUserRelatedDTO activityUserRelated) {

        Integer projectType = MapUtils.getInteger(promotionActivityDO.getExt(), "projectType");
        List<ActivityParticipationDO> projectList;
        if (ResourcesAttributeEnum.APPOINT.getId().equals(projectType)) {
            projectList = activityParticipationDAO.listByActivityId(promotionActivityDO.getId());
        } else {
            projectList = new ArrayList<>();
        }
        //获取所有规则类型
        List<String> limitLists = Arrays.stream(PATemplateBaseEnum.values()).map(PATemplateBaseEnum::name).collect(Collectors.toList());
        //获取活动基本数据，活动规则
        ActivityConfigDTO activityConfigDTO = ActivityConfigConverter.converter(promotionActivityDO, projectList, promotionActivityLimitList, activityUserRelated);
        //传递活动基本数据
        ActivityParamsDTO activityParamsDTO = new ActivityParamsDTO();
        //activityParamsDTO.setUserId(requestVo.getUserId());
        activityParamsDTO.setPhone(requestVo.getPhone());
        activityParamsDTO.setUnionId(requestVo.getUnionId());
        activityParamsDTO.setUserType(requestVo.getUserType());
        activityParamsDTO.setProjectId(requestVo.getProjectId());
        activityParamsDTO.setUserJoinType(requestVo.getUserJoinType());

        //提取当前活动规则信息
        limitLists = this.checkLimitLists(promotionActivityDO.getPaTemplateId().toString(), limitLists);

        // 限制类型初始化 并且计算
        Context context = new Context(activityConfigDTO, activityParamsDTO);
        CalculateResult limitFlag = context
                .initLimitCalculate(limitLists)
                .calculateLimitAndReturn();

        if (!limitFlag.getResult()) {
            if (StringUtil.isNotBlank(limitFlag.getMsg())) {
                throw new ApplicationException(limitFlag.getMsg());
            } else {
                throw new ApplicationException("用户不符合活动参加条件");
            }
        }

    }

    private List<String> checkLimitLists(String paTemplateId, List<String> limitLists) {
        if (StrategyGroupEnum.HF_LUCKYDRAW_ACT.getId().equals(paTemplateId)) {
            return limitLists.stream().filter(e -> e.equals(PATemplateBaseEnum.LUCKYDRAW.name())
                    || e.equals(PATemplateBaseEnum.USER.name())).collect(Collectors.toList());
        }
        if (StrategyGroupEnum.HF_FISSION_REDUCEPRICE_ACT.getId().equals(paTemplateId)) {
            return limitLists.stream().filter(e -> e.equals(PATemplateBaseEnum.BARGAIN.name())).collect(Collectors.toList());
        }
        if (StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId().equals(paTemplateId)) {
            return limitLists.stream().filter(e -> e.equals(PATemplateBaseEnum.ASSIST.name())).collect(Collectors.toList());
        }

        if (StrategyGroupEnum.HF_COUPON_ACT.getId().equals(paTemplateId)) {
            return limitLists.stream().filter(e -> e.equals(PATemplateBaseEnum.USER.name())).collect(Collectors.toList());
        }
        if (StrategyGroupEnum.HF_SECKILL_ACT.getId().equals(paTemplateId)) {
            return limitLists.stream().filter(e -> e.equals(PATemplateBaseEnum.USER.name())).collect(Collectors.toList());
        }
        if (StrategyGroupEnum.HF_FORM_ACT.getId().equals(paTemplateId)) {
            return limitLists.stream().filter(e -> e.equals(PATemplateBaseEnum.USER.name())).collect(Collectors.toList());
        }
        if (StrategyGroupEnum.HF_SIGN_ACT.getId().equals(paTemplateId)) {
            return limitLists.stream().filter(e -> e.equals(PATemplateBaseEnum.USER.name())).collect(Collectors.toList());
        }
        if (StrategyGroupEnum.HF_SIGN_UP_ACT.getId().equals(paTemplateId)) {
            return limitLists.stream().filter(e -> e.equals(PATemplateBaseEnum.USER.name())).collect(Collectors.toList());
        }
        return limitLists;
    }

    //信息登记 校验
    private void checkFeedbackInfo(PromotionActivityDO promotionActivityDO, String phone, Long resourceId) {

        //判断是否活动前填写活动反馈信息
        ActivityExtVO activityExtVO = ActivityExtConverter.converter(promotionActivityDO.getExt());
        //if(FeedbackInfoWriteTimeEnum.ACT_BEFORE.getId().equals(activityExtVO.getFeedbackInfoWriteTime())){

        CustomerFeedbackQuery query = new CustomerFeedbackQuery();
        query.setActivityId(promotionActivityDO.getId());
        query.setPhone(phone);
        query.setResourceId(resourceId);
        List<ActivityFormFeedbackDO> formFeedbackList = customerFeedbackDAO.pageList(query).getContent();
        if (CollectionUtil.isEmpty(formFeedbackList)) {
            log.info("未填写信息登记，req={}", JsonUtil.bean2JsonString(query));
            throw new ApplicationException("请填写信息登记");
        }

        //}
    }

    //信息登记 校验
    private void checkSeckillFeedbackInfo(PromotionActivityDO promotionActivityDO, String phone) {

        CustomerFeedbackQuery query = new CustomerFeedbackQuery();
        query.setActivityId(promotionActivityDO.getId());
        query.setPhone(phone);
        List<ActivityFormFeedbackDO> formFeedbackList = customerFeedbackDAO.pageList(query).getContent();
        if (CollectionUtil.isEmpty(formFeedbackList)) {
            log.info("未填写信息登记，req={}", JsonUtil.bean2JsonString(query));
            throw new ApplicationException("请填写信息登记");
        }

    }

    //创建参与记录
    private void createPartakeLog(ActivityPartakeRequest requestVo, PromotionActivityDO promotionActivityDO, PromotionHisResourceDO promotionHisResourceDO, ActivityOrderRequestDTO activityOrderRequestDTO) {
        ActivityPartakeLogDTO activityPartakeLog = new ActivityPartakeLogDTO();
        activityPartakeLog.setActivityId(requestVo.getActivityId());
        activityPartakeLog.setCode(promotionHisResourceDO.getCode());
        ActivityExtVO activityExtVO = ActivityExtConverter.converter(promotionActivityDO.getExt());

        //TODO 从信息登记中，获取地区和详细地址
//        CustomerFeedbackQuery query = new CustomerFeedbackQuery();
//        query.setActivityId(promotionActivityDO.getId());
//        query.setUserId(requestVo.getUserId());
//        List<ActivityFormFeedbackDO> formFeedbackList = customerFeedbackDAO.pageList(query).getContent();
//        if(CollectionUtil.isNotEmpty(formFeedbackList)){
//            JSONObject jsonObject = JSON.parseObject(formFeedbackList.get(0).getLimits());
//            activityPartakeLog.setArea(jsonObject.getString(""));
//            activityPartakeLog.setDetaAddress();
//        }

        activityPartakeLog.setPhone(requestVo.getPhone());
        activityPartakeLog.setPrizeResult(promotionHisResourceDO.getName());
        activityPartakeLog.setResourceId(promotionHisResourceDO.getId());
        activityPartakeLog.setAppId(AppRuntimeEnv.getAppId());
        activityPartakeLog.setTenantId(AppRuntimeEnv.getTenantId());
        activityPartakeLog.setActivityId(promotionHisResourceDO.getActivityId());
        activityPartakeLog.setUserId(requestVo.getUserId());
        activityPartakeLog.setUserName(requestVo.getUserName());
        activityPartakeLog.setPhone(requestVo.getPhone());
        activityPartakeLog.setNickName(requestVo.getNickName());
        activityPartakeLog.setPrizeResult(promotionHisResourceDO.getName());
        activityPartakeLog.setResourceId(promotionHisResourceDO.getId());
        activityPartakeLog.setType(requestVo.getType());
        activityPartakeLog.setCreatedTime(DateTime.now());
        activityPartakeLog.setUnionId(requestVo.getUnionId());

        if (Objects.nonNull(activityOrderRequestDTO)) {
            activityPartakeLog.setOrderId(activityOrderRequestDTO.getId());
            activityPartakeLog.setPayMoney(activityOrderRequestDTO.getPayMoney());
        }
        activityPartakeLogDAO.save(activityPartakeLog);
    }

    /**
     * @param prizeList
     * @return
     */
    public Boolean updateResourceIssuedQtyAndRemainQty(List<PrizeConfigVO> prizeList) {

        if (CollectionUtil.isEmpty(prizeList)) {
            return Boolean.TRUE;
        }

        //提取id List
        List<Long> hisResourceIds = prizeList.stream().filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getId())).map(PrizeConfigVO::getId).collect(Collectors.toList());

        //如果没有资源修改，返回true
        if (CollectionUtil.isEmpty(hisResourceIds)) {
            return Boolean.TRUE;
        }

        //提取id和总量  Map
        Map<Long, Integer> collect = prizeList.stream().filter(e -> Objects.nonNull(e.getId())).collect(Collectors.toMap(PrizeConfigVO::getId, PrizeConfigVO::getIssuedQuantity));

        //获取对应资源的原数量
        PromotionHisResourceQuery promotionHisResourceQuery = new PromotionHisResourceQuery();
        promotionHisResourceQuery.setIds(hisResourceIds);
        promotionHisResourceQuery.setPage(1L);
        promotionHisResourceQuery.setSize(999999L);
        List<PromotionHisResourceDO> promotionHisResourceDOList = promotionHisResourceDAO.findPage(promotionHisResourceQuery).getContent();

        Long count = 0L;
        for (PromotionHisResourceDO promotionHisResourceDO : promotionHisResourceDOList) {
            count = collect.get(promotionHisResourceDO.getId()).longValue() - promotionHisResourceDO.getIssuedQuantity().longValue();
            if (count.compareTo(0L) <= 0) {
                continue;
            }
            promotionActivityManager.incrIssuedQty(promotionHisResourceDO.getId(), count);
        }

        return Boolean.TRUE;
    }


    public void saveHisResource(List<HisResourceJsonVO> hisResourceList, List<ActivityCardVO> cardList, Long activtityId, Long paTemplateId) {
        //表单活动，好主播活动资源可为空
        if ((StrategyGroupEnum.HF_FORM_ACT.getId().equals(paTemplateId.toString())
                || StrategyGroupEnum.HF_GOOD_ANCHOR_ACT.getId().equals(paTemplateId.toString()))
                || StrategyGroupEnum.HF_LOCATION_SIGN_ACT.getId().equals(paTemplateId.toString())
                && CollectionUtil.isEmpty(hisResourceList)) {
            return;
        }
        if (CollectionUtil.isEmpty(hisResourceList)) {
            log.info("资源列表为空");
            throw new ApplicationException("资源列表不能为空");
        }
        List<Long> resourceIds = hisResourceList.stream().map(HisResourceJsonVO::getResourceId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(resourceIds)) {
            log.info("资源列表id为空");
            throw new ApplicationException("资源id不能为空");
        }
        QueryWrapper<PromotionResourceDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(PromotionResourceDO::getId, resourceIds);
        List<PromotionResourceDO> list = promotionResourceDAO.list(queryWrapper);
        if (CollectionUtil.isEmpty(list)) {
            log.info("资源信息不存在");
            throw new ApplicationException("资源信息不存在");
        }

        List<PromotionHisResourceDO> promotionHisResourceList = Lists.newArrayList();
        Map<Long, PromotionResourceDO> resourceMap = list.stream().collect(Collectors.toMap(PromotionResourceDO::getId, v -> v));


        if (StrategyGroupEnum.HF_LUCKYDRAW_ACT.getId().equals(String.valueOf(paTemplateId))
                || StrategyGroupEnum.HF_SIGN_ACT.getId().equals(String.valueOf(paTemplateId))) {
            //计算谢谢参与概率
            boolean checkOddsOfWinning = hisResourceList.stream().filter(h -> h.getOddsOfWinning() == null).findAny().isPresent();
            if (checkOddsOfWinning) {
                throw new ApplicationException("中奖率不能为空!");
            }

            if (StrategyGroupEnum.HF_SIGN_ACT.getId().equals(String.valueOf(paTemplateId))) {
                //新增一个谢谢参与的奖品，中奖比例到时根据奖品进行动态设置
                HisResourceJsonVO hisResourceJsonVO = new HisResourceJsonVO();
                hisResourceJsonVO.setResourceId(-1L);
                hisResourceList.add(hisResourceJsonVO);
            } else {
                this.getHisResourceJsonList(hisResourceList);
            }

            hisResourceList.forEach(his -> {
                PromotionResourceDO resource = resourceMap.get(his.getResourceId());

                PromotionHisResourceDO promotionHisResource = null;
                if (Objects.isNull(resource)) {
                    promotionHisResource = new PromotionHisResourceDO();
                    promotionHisResource.setResourceId(his.getResourceId());
                    promotionHisResource.setAppId(AppRuntimeEnv.getAppId());
                    promotionHisResource.setTenantId(AppRuntimeEnv.getTenantId());
                    promotionHisResource.setName(PromotionActivityConstant.THANK_PARTICIPATION);
                } else {
                    promotionHisResource = resource.clone(PromotionHisResourceDO.class);
                    promotionHisResource.setResourceId(resource.getId());
                }

                fillPromotionHisResource(activtityId, promotionHisResourceList, his, promotionHisResource);
            });
        } else if (StrategyGroupEnum.HF_CARD_COLLECTING_ACT.getId().equals(String.valueOf(paTemplateId))) {
            //计算谢谢参与概率
            boolean checkOddsOfWinning = hisResourceList.stream().anyMatch(h -> h.getOddsOfWinning() == null);
            if (checkOddsOfWinning) {
                throw new ApplicationException("中奖率不能为空!");
            }
            // 补充卡片奖品
            fillCardPrize(hisResourceList, cardList, paTemplateId);
            hisResourceList.forEach(his -> {
                PromotionHisResourceDO promotionHisResource = null;
                if (his.getResourceId() > 0) {
                    PromotionResourceDO resource = resourceMap.get(his.getResourceId());
                    if (Objects.isNull(resource)) {
                        throw new ApplicationException("资源信息不存在");
                    } else {
                        promotionHisResource = resource.clone(PromotionHisResourceDO.class);
                        promotionHisResource.setResourceId(resource.getId());
                    }
                }
                if (Objects.isNull(promotionHisResource)) {
                    promotionHisResource = new PromotionHisResourceDO();
                    promotionHisResource.setResourceId(his.getResourceId());
                    promotionHisResource.setAppId(AppRuntimeEnv.getAppId());
                    promotionHisResource.setTenantId(AppRuntimeEnv.getTenantId());
                    promotionHisResource.setName(his.getName());
                    promotionHisResource.setUrl(his.getUrl());
                }
                fillPromotionHisResource(activtityId, promotionHisResourceList, his, promotionHisResource);
            });
        } else {
            hisResourceList.forEach(his -> {
                PromotionResourceDO resource = resourceMap.get(his.getResourceId());

                if (Objects.isNull(resource)) {
                    throw new ApplicationException("资源信息不存在");
                }
                PromotionHisResourceDO promotionHisResource = resource.clone(PromotionHisResourceDO.class);
                promotionHisResource.setResourceId(resource.getId());
                fillPromotionHisResource(activtityId, promotionHisResourceList, his, promotionHisResource);
                if (StrategyGroupEnum.HF_SIGN_UP_ACT.getId().equals(String.valueOf(paTemplateId))) {
                    //如果是报名活动，复用原记录
                    promotionHisResource.setId(his.getId());
                }
            });
        }
        promotionHisResourceDAO.saveOrUpdateBatch(promotionHisResourceList);

    }

    /**
     * 填充资源属性
     * @param activtityId
     * @param promotionHisResourceList
     * @param his
     * @param promotionHisResource
     */
    private void fillPromotionHisResource(Long activtityId, List<PromotionHisResourceDO> promotionHisResourceList, HisResourceJsonVO his, PromotionHisResourceDO promotionHisResource) {
        promotionHisResource.setActivityId(activtityId);
        promotionHisResource.setIssuedQuantity(his.getIssuedQuantity());
        promotionHisResource.setIssuanceCap(his.getIssuanceCap());
        promotionHisResource.setOddsOfWinning(his.getOddsOfWinning());
        promotionHisResource.setLimitType(his.getLimitType());
        promotionHisResource.setRemainingQuantity((his.getIssuedQuantity() == null ? 0 : his.getIssuedQuantity()) - his.getIssuedUsedQuantity());
        //判断剩余数量要大于0
        Assert.isTrue(promotionHisResource.getRemainingQuantity() >= 0, "剩余数量不能小于0");
        promotionHisResource.setLimitTimes(his.getLimitTimes());
        promotionHisResource.setReceiveMode(his.getReceiveMode());
        promotionHisResource.setPurchasePrice(his.getPurchasePrice());
        promotionHisResource.setCreatedTime(new Date());
        promotionHisResource.setUpdatedTime(new Date());
        //助力相关属性
        promotionHisResource.setFissonResourceType(his.getFissonResourceType());
        promotionHisResource.setLadderSort(his.getLadderSort());
        promotionHisResource.setFissonCount(his.getFissonCount());

        //红包、话费资源需要的核销项目属性
        promotionHisResource.setProjectId(his.getProjectId());
        promotionHisResource.setProjectName(his.getProjectName());
        promotionHisResource.setSysType(his.getSysType());

        promotionHisResourceList.add(promotionHisResource);
    }

    private void fillCardPrize(List<HisResourceJsonVO> hisResourceList, List<ActivityCardVO> cardList, Long paTemplateId) {
        // 集卡活动补充卡片奖品
        if (!StrategyGroupEnum.HF_CARD_COLLECTING_ACT.getId().equals(paTemplateId.toString())) {
            return;
        }
        //奖品概率之和
        double oddsOfWinningSum = 0D;
        HisResourceJsonVO mergePrize = null;
        for (HisResourceJsonVO item : hisResourceList) {
            if (item.getResourceId() <= 0) {
                throw new ApplicationException("资源信息不存在");
            }
            if (!Objects.equals(item.getFissonResourceType(), FissonResourceTypeEnum.POWER_LADDER.getId())) {
                throw new ApplicationException("奖品类型错误");
            }
            if (item.getLadderSort() == 2) {
                // 1卡片必中奖，2卡片随机抽奖，3卡片合成大奖，计算随机抽奖概率
                oddsOfWinningSum = oddsOfWinningSum + item.getOddsOfWinning();
            } else if (item.getLadderSort() == 3) {
                mergePrize = item;
            }
        }
        if (mergePrize == null) {
            throw new ApplicationException("合成奖品不能为空");
        } else if (mergePrize.getOddsOfWinning() >= 100D) {
            // 合成大奖概率即稀有卡概率，不能等于100
            mergePrize.setOddsOfWinning(0.01D);
        }
        if (oddsOfWinningSum > PromotionActivityConstant.ODDS_OF_WINNING) {
            throw new ApplicationException("抽奖奖品概率不能超过" + PromotionActivityConstant.ODDS_OF_WINNING);
        }
        //补充剩余概率谢谢参与
        BigDecimal residual = new BigDecimal(PromotionActivityConstant.ODDS_OF_WINNING - oddsOfWinningSum);
        HisResourceJsonVO hisResourceJsonVO = new HisResourceJsonVO();
        hisResourceJsonVO.setResourceId(-1L);
        hisResourceJsonVO.setName("谢谢参与");
        hisResourceJsonVO.setUrl("https://osstest.huafagroup.com/promotion-pre/笑脸@2x_1624343321461.png");
        hisResourceJsonVO.setOddsOfWinning(residual.doubleValue());
        hisResourceJsonVO.setLadderSort(0);
        hisResourceList.add(hisResourceJsonVO);

        int cardSize;
        if (CollectionUtil.isEmpty(cardList) || (cardSize = cardList.size()) < 3) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "卡片不能低于3种！");
        }
        for (int i = 0; i < cardList.size(); i++) {
            ActivityCardVO card = cardList.get(i);
            HisResourceJsonVO hisResource = new HisResourceJsonVO();
            hisResource.setResourceId(-1L);
            hisResource.setName(card.getName());
            hisResource.setUrl(card.getImg());
            hisResource.setLadderSort(i + 1);
            hisResource.setFissonResourceType(FissonResourceTypeEnum.HELP_WIN_AWARDS.getId());
            if (i == 0) {
                // 第一张卡，每10个人助力得一张
                hisResource.setFissonCount(10);
            } else if (i == cardSize - 1) {
                // 最后一张卡，稀有卡，控制合成大奖数量，每天限量，每人只能得一张
                hisResource.setOddsOfWinning(mergePrize.getOddsOfWinning());
                hisResource.setIssuanceCap(mergePrize.getIssuanceCap());
                hisResource.setIssuedQuantity(mergePrize.getIssuedQuantity());
                hisResource.setLimitType(1);
            } else {
                // 普通卡概率
                hisResource.setOddsOfWinning((100D - mergePrize.getOddsOfWinning()) / (cardSize - 2));
            }
            hisResourceList.add(hisResource);
        }
    }

    /**
     * 助力资源较验
     *
     * @param paTemplateId
     * @param hisResourceList
     */
    private void fissionAssistCheck(Long paTemplateId, List<HisResourceJsonVO> hisResourceList) {
        if (StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId().toString().equals(paTemplateId.toString())) {
            List<HisResourceJsonVO> powerLadderList = hisResourceList.stream().filter(item -> item.getFissonResourceType() != null && FissonResourceTypeEnum.POWER_LADDER.getId().equals(item.getFissonResourceType())).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(powerLadderList)) {
                log.info("助力阶梯资源为空");
                throw new ApplicationException("助力阶梯资源不能为空");
            }
        }
    }


    private void getHisResourceJsonList(List<HisResourceJsonVO> hisResourceJsonList) {
        //奖品数量
        int size = hisResourceJsonList.size();

        //最大奖品数量（7奖品 + 谢谢参与）
        int prizeSize = PromotionActivityConstant.DZP_PRIZE_SIZE - 1;
        if (size > prizeSize) {
            return;
        }

        //奖品概率之和
        double OddsOfWinningSum = hisResourceJsonList.stream().mapToDouble(HisResourceJsonVO::getOddsOfWinning).sum();

        //剩余概率
        BigDecimal residual = new BigDecimal(PromotionActivityConstant.ODDS_OF_WINNING - OddsOfWinningSum);

        //平摊剩余概率给谢谢参与
        BigDecimal divide = residual.divide(new BigDecimal(PromotionActivityConstant.DZP_PRIZE_SIZE - size), 2, BigDecimal.ROUND_HALF_UP);

        for (int i = 0; i < PromotionActivityConstant.DZP_PRIZE_SIZE; i++) {
            if (i >= size) {
                HisResourceJsonVO hisResourceJsonVO = new HisResourceJsonVO();
                hisResourceJsonVO.setResourceId(-1L);
                if (i < PromotionActivityConstant.DZP_PRIZE_SIZE - 1) {
                    residual = residual.subtract(divide);
                    hisResourceJsonVO.setOddsOfWinning(divide.doubleValue());
                } else {
                    hisResourceJsonVO.setOddsOfWinning(residual.doubleValue());
                }
                hisResourceJsonList.add(hisResourceJsonVO);
            }
        }
    }


    /**
     * 查询活动用户信息
     *
     * @param query
     * @return
     */
    @Override
    public ActivityUserRelatedDTO getActivityUserRelated(ActivityPartakeRequest query, List<PromotionActivityLimitDO> promotionActivityLimitList, String paTemplateId) {
        ActivityUserRelatedDTO activityUserRelatedDTO = new ActivityUserRelatedDTO();
        activityUserRelatedDTO.setActivityId(query.getActivityId());
        activityUserRelatedDTO.setUserId(query.getUserId());
        activityUserRelatedDTO.setPhone(query.getPhone());
        ActivityUserRelatedDTO activityUserRelated = activityUserRelatedDAO.getActivityUserRelated(activityUserRelatedDTO);
        if (activityUserRelated == null) {
            ActivityUserRelatedDO relatedDO = LuckyDrawConverter.activityUserConverter(query, promotionActivityLimitList, paTemplateId);
            if (relatedDO == null) {
                return null;
            }
            activityUserRelatedDAO.save(relatedDO);
            activityUserRelated = relatedDO.clone(ActivityUserRelatedDTO.class);
            log.info("活动用户信息补全信息：" + JsonUtil.bean2JsonString(activityUserRelated));
        }
        return activityUserRelated;
    }

    @Override
    public String getUUID(String key) {
        String uuId = RandomUtils.getRandomStr();
        RBucket<Object> bucket = redissonClient.getBucket(key);
        bucket.set(uuId);
        bucket.expire(1800, TimeUnit.SECONDS);//30分钟
        return uuId;
    }

    @Override
    public List<FissionRankResponseDTO> rankList(Long activityId) {
        return activityFissionLogDAO.rankList(activityId);
    }

    @Override
    public PageBean<ActivityFissionLogResponseDTO> fissionFriendsList(ActivityFissionLogQuery query) {
        PageBean<ActivityFissionLogResponseDTO> page = activityFissionLogDAO.pageList(query);
        List<ActivityFissionLogResponseDTO> content = page.getContent();
        if (CollectionUtil.isNotEmpty(content)) {
            PromotionActivityDetailDTO promotionActivityDO = JSON.parseObject(String.valueOf(redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO + content.get(0).getActivityId()).get()), PromotionActivityDetailDTO.class);
            Map<Long, ResourceHisDetailResponseDTO> hisResourceMap = new HashMap<>();
            if (promotionActivityDO.getPrizeList() != null) {
                hisResourceMap = promotionActivityDO.getPrizeList().stream().collect(Collectors.toMap(ResourceHisDetailResponseDTO::getId, item -> item));
            }

            Integer helpNumSet = 0;
            String assistNumDefinition = null;
            if (Objects.equals(String.valueOf(promotionActivityDO.getPaTemplateId()), StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId())) {
                List<PromotionActivityLimitDO> limitDO = promotionActivityLimitDAO.selectByActivityId(content.get(0).getActivityId());
                List<BaseActivityDTO> numberList = ActivityConfigConverter.getLimitArray(limitDO, PATemplateBaseEnum.ASSIST.getId(), BaseActivityDTO.class);
                helpNumSet = LuckyDrawConverter.elLimitConverter(numberList, ActivityTemplateNumberEnum.HELP_NUM_SET.getId());
                assistNumDefinition = LuckyDrawConverter.elLimitConverterToStr(numberList, ActivityTemplateNumberEnum.HELP_ASSIST_NUM_DEFINITION.getId());
            }
            for (ActivityFissionLogResponseDTO activityFissionLogResponseDTO : content) {
                activityFissionLogResponseDTO.setPhone(PhoneEncryUtils.encode(activityFissionLogResponseDTO.getPhone()));
                activityFissionLogResponseDTO.setHelpNumSet(helpNumSet);
                activityFissionLogResponseDTO.setAssistNumDefinition(assistNumDefinition);
                ResourceHisDetailResponseDTO hisDetailResponseDTO = hisResourceMap.get(activityFissionLogResponseDTO.getAssistResourceId());
                if (hisDetailResponseDTO != null) {
                    activityFissionLogResponseDTO.setAssistResourceName(hisDetailResponseDTO.getName());
                }
            }
        }
        return page;
    }



    /**
     * 活动领奖,返回核销码
     */
    public String assistReceive(ActivityFissionAssistResourceDO activityFissionAssistResourceDO,
                                PromotionHisResourceDO promotionHisResourceDO,
                                ActivityPartakeLogDO activityPartakeLogDO,
                                String activityName, ActivityFormFeedbackDTO activityFormFeedbackDTO) {
        ActivityPartakeRequest requestVo = activityFissionAssistResourceDO.clone(ActivityPartakeRequest.class);
        requestVo.setSponsorId(activityFissionAssistResourceDO.getPartakeLogId());

        String resourceCode = generateIdUtil.getResourceCode();

        if (PromotionResourceTypeEnum.THIRD_PARTY.getId().equals(promotionHisResourceDO.getType())) {
            //第三方核销
            requestVo.setProjectId(promotionHisResourceDO.getProjectId());
            requestVo.setProjectName(promotionHisResourceDO.getProjectName());
            incentiveService.thirdVerify(requestVo, promotionHisResourceDO, activityFormFeedbackDTO, activityName, resourceCode, SendTemplateNewsRequestDTO.MP_FROM_USH);
        } else {
            ActivityOrderDO activityOrderDO = new ActivityOrderDO();
            activityOrderDO.setType(requestVo.getType());
            activityOrderDO.setActivityId(requestVo.getActivityId());
            activityOrderDO.setUserId(requestVo.getUserId());
            activityOrderDO.setPhone(requestVo.getPhone());//add by 0713
            activityOrderDO.setId(0L);
            //添加参与记录-分享
            ShareLuckyDrawRequestDTO shareLuckyDrawRequestDTO = activityFissionAssistResourceDO.clone(ShareLuckyDrawRequestDTO.class);
            ActivityFissionAssistLogDO activityFissionAssistLogDO = BargainingConverter.ActivityFissionAssistLogConverter(promotionHisResourceDO, shareLuckyDrawRequestDTO, null);
            //驳回时可取回原来的项目信息
            activityFissionAssistLogDO.setCode(resourceCode);
            activityFissionAssistLogDO.setProjectId(activityPartakeLogDO.getProjectId());
            activityFissionAssistLogDO.setProjectName(activityPartakeLogDO.getProjectName());
            activityFissionAssistLogDAO.save(activityFissionAssistLogDO);

            //添加核销记录
            activityOrderDO.setPayTime(DateTime.now());
            Map<String, Object> projectInfo = new HashMap<>();
            if (StringUtil.isNotEmpty(activityPartakeLogDO.getProjectId()) && StringUtil.isNotEmpty(activityPartakeLogDO.getProjectName())) {
                projectInfo.put("projectId", activityPartakeLogDO.getProjectId());
                projectInfo.put("projectName", activityPartakeLogDO.getProjectName());
            }
            activityOrderDO.setExt(projectInfo);
            //分享没有项目信息
            ActivityVerifyDO activityVerifyDO = ActivityInfoConverter.verifyConverter(activityOrderDO, activityFormFeedbackDTO, promotionHisResourceDO, resourceCode, VerifyStatusEnum.NO_VERIFY.getId(), WhetherEnum.NO.getId());
            if (activityVerifyDO.getAppId() == null) {
                activityVerifyDO.setAppId(promotionHisResourceDO.getAppId());
            }
            if (StringUtil.isEmpty(activityVerifyDO.getTenantId())) {
                activityVerifyDO.setTenantId(promotionHisResourceDO.getTenantId());
            }
            activityVerifyDAO.save(activityVerifyDO);
        }
        //更新为已领取
        activityFissionAssistResourceDO.setReceived(1);
        activityFissionAssistResourceDAO.updateById(activityFissionAssistResourceDO);
        return resourceCode;
    }

    @Override
    public void grantResource(PromotionActivityDO promotionActivityDO) {


        String lockKey = RedisConstants.CACHE_PREV_KEY_FISSON_ACT_RESOURCE_LOCK + promotionActivityDO.getId();
        RLock lock = redissonClient.getLock(lockKey);
        int retry = 0;
        Boolean flag = false;
        try {
            /**
             * 获取分布式锁重试次数
             */
            int RETRY = 2;
            while (retry < RETRY) {
                log.info("获取分布式锁重试次数:" + retry);
                if (lock.tryLock(100, 10 * 1000, TimeUnit.MILLISECONDS)) {
                    log.info("----------------------获取分布式锁------------------");

                    //主要逻辑
                    try {
                        if (StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())) {
                            QueryWrapper<ActivityPartakeLogDO> query = new QueryWrapper();
                            query.lambda().eq(ActivityPartakeLogDO::getActivityId, promotionActivityDO.getId());
                            List<ActivityPartakeLogDO> list = activityPartakeLogDAO.list(query);

                            if (CollectionUtil.isNotEmpty(list)) {
                                for (ActivityPartakeLogDO activityPartakeLogDO :
                                        list) {
                                    flag = false;//助力成功
                                    if (FissonStatusEnum.SUCCESS.getId().equals(activityPartakeLogDO.getFissonStatus())) {//助力成功
                                        flag = true;
                                    }
                                    //活动结束,助力活动未结束
                                    if (FissonStatusEnum.PROCESSING.getId().equals(activityPartakeLogDO.getFissonStatus())) {
                                        PromotionHisResourceDO promotionHisResourceDO = promotionHisResourceDAO.findLadder(activityPartakeLogDO.getActivityId(), 1);
                                        if (activityPartakeLogDO.getCurrentFissonCount() >= promotionHisResourceDO.getFissonCount()) {
                                            flag = true;
                                            activityPartakeLogDO.setFissonStatus(FissonStatusEnum.SUCCESS.getId());

                                            //裂变活动成功人数神策埋点
                                            asynchronousService.sensorsBuriedPointFission(activityPartakeLogDO.getId(), SensorsEventEnum.BOOST_SUCCESS.getCode());
                                        } else {
                                            activityPartakeLogDO.setFissonStatus(FissonStatusEnum.FAILURE.getId());
                                        }
                                        activityPartakeLogDAO.updateById(activityPartakeLogDO);
                                    }
                                    if (flag) {
                                        //发起者获奖
                                        List<ActivityFissionAssistResourceDO> assistResourceList = activityFissionAssistResourceDAO.getListByPartakeLogId(activityPartakeLogDO.getId(), 0);
                                        if (CollectionUtil.isNotEmpty(assistResourceList)) {
                                            List<Long> resourceIds = assistResourceList.stream().map(ActivityFissionAssistResourceDO::getResourceId).distinct().collect(Collectors.toList());
                                            List<PromotionHisResourceDO> promotionHisResourceList = promotionHisResourceDAO.listByIds(resourceIds);
                                            Map<Long, PromotionHisResourceDO> hisResourceMap = promotionHisResourceList.stream().collect(Collectors.toMap(PromotionHisResourceDO::getId, Function.identity()));
                                            StringBuilder sb = new StringBuilder();
                                            for (ActivityFissionAssistResourceDO activityFissionAssistResourceDO :
                                                    assistResourceList) {
                                                assistActService.initiatorFissionAssistRecive(activityFissionAssistResourceDO, hisResourceMap.get(activityFissionAssistResourceDO.getResourceId()), activityPartakeLogDO.getProjectId(), activityPartakeLogDO.getProjectName(), promotionActivityDO.getName(), sb);
                                            }

                                            //发送模板消息
                                            this.templateSend(activityPartakeLogDO.getPhone(), activityPartakeLogDO.getUserName(), assistResourceList.stream().map(e -> hisResourceMap.get(e.getResourceId()).getName()).distinct()
                                                    .collect(Collectors.joining(",")), sb.toString(), huafaConstantConfig.BOOST_SUCCESS_TEMPLATE_ID);

                                            //小程序订阅消息
                                            this.miniTemplateNews(promotionActivityDO, activityPartakeLogDO);
                                        }
                                    }
                                }
                            }
                        } else if (StrategyGroupEnum.HF_FISSION_REDUCEPRICE_ACT.getId().equals(promotionActivityDO.getPaTemplateId().toString())) {
                            this.checkBargainEndTime(promotionActivityDO);
                        }
                    } catch (Exception e) {
                        log.error("助力奖品发放异常", e);
                    }

                    //手动释放锁
                    if (lock.isHeldByCurrentThread()) {
                        log.info("----------------------手动释放锁------------------");
                        lock.unlock();
                        break;
                    }

                } else {
                    //获取锁失败,次数加1
                    retry++;
                }
            }
        } catch (InterruptedException e) {
            log.error("redis分布式锁异常,key:{},异常信息:{}", lockKey, e.getMessage());
            throw new ApplicationException("锁出现异常");
        }

        if (retry == RETRY_TIME) {
            lock.unlock();
            throw new ApplicationException("grantResource() 裂变活动自动发放奖品接口锁获取失败");
        }


//        CompletableFuture.runAsync(() -> {
//
//        });
    }

    @Override
    public ExcelExportResponseDTO getExportPartakeLogExcelData(PartakeLogQuery query) {
        if (Objects.isNull(query.getActivityId()) || Objects.isNull(query.getPaTemplateId())) {
            throw new ApplicationException("活动id或者活动类型缺失");
        }
        query.setPage(-1);
        query.setSize(99999);
        query.setUserId(null);
        List<EnrollmentInfoVO> feedbackInfo = getFeedbackInfoByActivityId(query.getActivityId());
        List<ActivityPartakeLogResponseDTO> content;
        if (StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId().equals(query.getPaTemplateId().toString())
                || StrategyGroupEnum.HF_FISSION_REDUCEPRICE_ACT.getId().equals(query.getPaTemplateId().toString())
                || StrategyGroupEnum.HF_SIGN_ACT.getId().equals(query.getPaTemplateId().toString())) {
            content = activityPartakeLogDAO.findPageSerial(query).getContent();
        } else {
            content = activityPartakeLogDAO.findPage(query).getContent();
        }
        //添加用户登记明细
        this.getCustomerFeedbackListExport(content, query.getActivityId());

        if (CollectionUtil.isEmpty(content)) {
            throw new ApplicationException("数据列表为空，无法下载");
        }

        ExcelExportResponseDTO responseDTO = null;
        try {
            GetExcelExportResultUtil util = GetExcelExportResultUtil.getInstance();

            if (StrategyGroupEnum.HF_FORM_ACT.getId().equals(String.valueOf(query.getPaTemplateId()))) {
                //表单活动

            } else if (StrategyGroupEnum.HF_LUCKYDRAW_ACT.getId().equals(String.valueOf(query.getPaTemplateId()))) {
                //抽奖活动
                responseDTO = util.exportLuckyDrawActInfo(content, query.getPaTemplateId(), feedbackInfo);

            } else if (StrategyGroupEnum.HF_COUPON_ACT.getId().equals(String.valueOf(query.getPaTemplateId()))) {
                //优惠券活动
                responseDTO = util.exportCouponActInfo(content, query.getPaTemplateId(), feedbackInfo);

            } else if (StrategyGroupEnum.HF_SECKILL_ACT.getId().equals(String.valueOf(query.getPaTemplateId()))) {
                //秒杀活动

                responseDTO = util.exportSeckillActInfo(content, query.getPaTemplateId(), feedbackInfo);

            } else if (StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId().equals(String.valueOf(query.getPaTemplateId().toString()))) {
                //助力活动
                responseDTO = util.exportAssistActInfo(content, query.getPaTemplateId(), feedbackInfo);

            } else if (StrategyGroupEnum.HF_FISSION_REDUCEPRICE_ACT.getId().equals(String.valueOf(query.getPaTemplateId()))) {
                //砍价活动
                responseDTO = util.exportReducePriceActInfo(content, query.getPaTemplateId(), feedbackInfo);
            } else if (StrategyGroupEnum.HF_CARD_COLLECTING_ACT.getId().equals(String.valueOf(query.getPaTemplateId()))) {
                //集卡活动
                responseDTO = util.exportcardCollectingActInfo(content, query.getPaTemplateId(), feedbackInfo);
            } else if (StrategyGroupEnum.HF_SIGN_ACT.getId().equals(String.valueOf(query.getPaTemplateId()))) {
                //签到抽奖活动
                responseDTO = util.exportSignActInfo(content, query.getPaTemplateId(), feedbackInfo);
            } else if (StrategyGroupEnum.HF_LOCATION_SIGN_ACT.getId().equals(String.valueOf(query.getPaTemplateId()))) {
                //地点签到活动
                //获取打卡明细
                List<ActivityFissionLogDO> fissionLogDOS = activityFissionLogDAO.list(Wrappers.lambdaQuery(ActivityFissionLogDO.class).eq(ActivityFissionLogDO::getActivityId, query.getActivityId()));
                responseDTO = util.exportLocationSignActInfo(content, query.getPaTemplateId(), feedbackInfo,fissionLogDOS);
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ApplicationException("导出失败");
        }
        return responseDTO;
    }


    /**
     * 短信模板通知
     * 【华发股份】XXXX你好！恭喜你抽中"XXXXX"，核销码为(023uc)，使用规则请到小程序-我的礼品查看。
     *
     * @param mobile       发送手机
     * @param userName     中奖人
     * @param coupon       优惠券名称
     * @param code         核销码
     * @param templateCode 短信模板编码
     */
    @Override
    public void templateSend(String mobile, String userName, String coupon, String code, String templateCode) {
        log.info("准备发送短信模板通知参数，手机={},中奖人={},优惠券名称={},核销码={},模板={}", mobile, userName, coupon, code, templateCode);
        if (StringUtil.isNotBlank(mobile) && StringUtil.isNotBlank(userName) && StringUtil.isNotBlank(coupon) && StringUtil.isNotBlank(code)) {
            TemplateSendRequestDTO.Param param = new TemplateSendRequestDTO.Param();
            param.setUsername(userName + ",");
            param.setResource(coupon);
            param.setCode(code);

            TemplateSendRequestDTO requestDTO = new TemplateSendRequestDTO();
            requestDTO.setTemplateCode(templateCode);
            requestDTO.setMobile(mobile);
            requestDTO.setTemplateParam(JsonUtil.bean2JsonString(param));
            requestDTO.setCreateId(userName);
            interactionCenterService.templateSend(requestDTO);
        }
    }

    @Override
    public void minSubscribeNews(String templateParam, String userOpenId, String userName, String templateId) {
        SendTemplateNewsRequestDTO dto = new SendTemplateNewsRequestDTO();
        dto.setTemplateId(templateId);
        dto.setUserOpenId(userOpenId);
        dto.setTemplateParam(templateParam);
        dto.setMpFrom(SendTemplateNewsRequestDTO.MP_FROM_ZYT);
        dto.setCreateId(userName);
        interactionCenterService.miniTemplateNews(dto);
    }

    @Override
    public void checkBargainEndTime(PromotionActivityDO query) {
        if (!Objects.equals(String.valueOf(query.getPaTemplateId()), StrategyGroupEnum.HF_FISSION_REDUCEPRICE_ACT.getId())) {
            return;
        }
        List<ActivityPartakeLogDO> activityPartakeLogs = activityPartakeLogDAO.getListByFissionStatus(query.getId(), FissonStatusEnum.PROCESSING.getId(), FissonTypeEnum.Bargain.getId());
        if (CollectionUtil.isNotEmpty(activityPartakeLogs)) {
            List<Long> resourceIds = activityPartakeLogs.stream().map(ActivityPartakeLogDO::getResourceId).distinct().collect(Collectors.toList());
            List<PromotionHisResourceDO> promotionHisResourceList = promotionHisResourceDAO.listByIds(resourceIds);
            Map<Long, PromotionHisResourceDO> collect = new HashMap<>(16);
            if (CollectionUtil.isNotEmpty(promotionHisResourceList)) {
                collect = promotionHisResourceList.stream().collect(Collectors.toMap(PromotionHisResourceDO::getId, Function.identity()));
            }
            Map<Long, PromotionHisResourceDO> finalCollect = collect;
            activityPartakeLogs.parallelStream().forEach(activityPartakeLog -> {
                PromotionHisResourceDO resourceDO = finalCollect.get(activityPartakeLog.getResourceId());
                Integer integer = checkFissionEndTime(activityPartakeLog.clone(ActivityPartakeLogDTO.class), resourceDO.getName());

                //2021-8-3 砍价活动；砍价成功未领取奖品，这时候整个砍价活动已结束或者已终止；系统统一自动发放奖品至对应用户处
                if (integer != null && integer.equals(BargainActTypeEnum.SUCCESSFUL.getId())) {
                    bargainActivityVerify(activityPartakeLog, query, resourceDO);
                }
            });
        }

    }

    private void bargainActivityVerify(ActivityPartakeLogDO activityPartakeLogDO, PromotionActivityDO activity, PromotionHisResourceDO promotionHisResourceDO) {
        if (!Objects.equals(activityPartakeLogDO.getFissonType(), FissonTypeEnum.Bargain.getId())) {
            return;
        }

        CustomerFeedbackQuery query = new CustomerFeedbackQuery();
        query.setActivityId(activity.getId());
        query.setPhone(activityPartakeLogDO.getPhone());
        query.setResourceId(activityPartakeLogDO.getResourceId());
        List<ActivityFormFeedbackDO> formFeedbackList = customerFeedbackDAO.pageList(query).getContent();

        FissionReceiveNowDTO dto = new FissionReceiveNowDTO();
        dto.setType(activityPartakeLogDO.getType());
        dto.setActivityId(activityPartakeLogDO.getActivityId());
        dto.setUserId(activityPartakeLogDO.getUserId());
        promotionActivityManager.bargainActivityVerify(dto, activity, formFeedbackList, activityPartakeLogDO, promotionHisResourceDO);
    }
    @Override
    public void miniTemplateNews(PromotionActivityDO promotionActivityDO, ActivityPartakeLogDO partakeLogById) {
        try {
            SendTemplateNewsRequestDTO sendTemplateNews = new SendTemplateNewsRequestDTO();
            int[] timeByCalendar = DateUtils.getTimeByCalendar(partakeLogById.getCreatedTime());
            int[] timeByCalendar2 = DateUtils.getTimeByCalendar(partakeLogById.getFissonEndTime());
            Map<String, Object> templateParam = Maps.newHashMap();
            //活动名称
            templateParam.put("thing1", promotionActivityDO.getName());
            //发起助力时间
            templateParam.put("time2", timeByCalendar[0] + "年" + timeByCalendar[1] + "月" + timeByCalendar[2] + "日 " + timeByCalendar[3] + ":" + timeByCalendar[4] + ":" + timeByCalendar[5]);
            //助力结束时间
            templateParam.put("time3", timeByCalendar2[0] + "年" + timeByCalendar2[1] + "月" + timeByCalendar2[2] + "日 " + timeByCalendar2[3] + ":" + timeByCalendar2[4] + ":" + timeByCalendar2[5]);
            templateParam.put("thing4", "恭喜你！助力成功！");
            templateParam.put("thing5", "请到我的礼品栏目查看详情");
            templateParam.put("thing3", "请到我的礼品栏目查看详情");
            sendTemplateNews.setTemplateParam(JSON.toJSONString(templateParam));
            sendTemplateNews.setTemplateId(huafaConstantConfig.MINI_BOOST_SUCCESS_TEMPLATE_ID);
            sendTemplateNews.setUserOpenId(partakeLogById.getUserId());
            sendTemplateNews.setMpFrom(SendTemplateNewsRequestDTO.MP_FROM_USH);
            sendTemplateNews.setCreateId(partakeLogById.getUserName());
            interactionCenterService.miniTemplateNews(sendTemplateNews);
        } catch (Exception ex) {
            log.error("小程序订阅消息异常", ex);
        }
    }

    @Override
    public List<Long> getActivityIdsByUserId(String userId) {
        List<Long> activityIdList = Lists.newArrayList();
        try {
            List<UserProjectResponseDTO> userProjectResponseDTOS = JSON.parseArray(String.valueOf(redissonClient.getBucket(String.format(RedisConstants.CACHE_PREV_KEY_USER_PROJECT_INFO, userId)).get()), UserProjectResponseDTO.class);
            if (CollectionUtil.isEmpty(userProjectResponseDTOS)) {
                userProjectResponseDTOS = userProjectService.saveToRedis(userId);
            }

            if (CollectionUtil.isNotEmpty(userProjectResponseDTOS)) {
                //查询数据库
                List<String> projectIdList = userProjectResponseDTOS.stream().map(UserProjectResponseDTO::getProjectId).collect(Collectors.toList());

                List<ActivityParticipationDO> activityParticipationDOS = activityParticipationDAO.listByProjectIds(projectIdList);
                if (CollectionUtil.isEmpty(activityParticipationDOS)) {
                    return activityIdList;
                }
                return activityParticipationDOS.stream().map(ActivityParticipationDO::getActivityId).distinct().collect(Collectors.toList());
            }

        } catch (Exception e) {
            log.error("获取用户关联活动异常", e);
        }
        return activityIdList;
    }

    /**
     * 获取当前用户的活动权限，新岗位权限
     * @return
     */
    public List<Long> getActivityIdsByUserIdV2() {
        PromotionActivityPageQuery query = new PromotionActivityPageQuery();
        userProjectService.initCommunityDataPermission(query);
        query.setDeliveryChannel(DeliveryChannelEnum.SQ.getId());
        return activityParticipationDAO.listActivityId(query);
    }
    @Override
    public boolean updateOnOffStatus(Long id, String status, String deliveryChannel) {
        PromotionActivityDO promotionActivityDO = this.promotionActivityDAO.getById(id);
        if (Objects.isNull(promotionActivityDO)) {
            throw new ApplicationException("活动不存在!");
        }
        if (Objects.equals(status, ActivityOnOffShelfStatus.OFF.getId()) && StringUtil.isNotEmpty(promotionActivityDO.getCreatedPerson())
                && !promotionActivityDO.getCreatedPerson().equals(HuafaRuntimeEnv.getUserId())
                && !authUtil.isAdminPosition()) {
            throw new ApplicationException("当前用户没有下架此活动权限");
        }
        if (Objects.equals(status, ActivityOnOffShelfStatus.ON.getId()) && StringUtil.isNotEmpty(promotionActivityDO.getCreatedPerson())
                && !promotionActivityDO.getCreatedPerson().equals(HuafaRuntimeEnv.getUserId())
                && !authUtil.isAdminPosition()) {
            throw new ApplicationException("当前用户没有上架此活动权限");
        }
        if (Objects.equals(status, ActivityOnOffShelfStatus.OFF.getId()) && Objects.equals(promotionActivityDO.getUpperStatus(), ActivityOnOffShelfStatus.OFF.getId())) {
            throw new ApplicationException("当前活动已是下架状态");
        }
        if (Objects.equals(status, ActivityOnOffShelfStatus.ON.getId()) && Objects.equals(promotionActivityDO.getUpperStatus(), ActivityOnOffShelfStatus.ON.getId())) {
            throw new ApplicationException("当前活动已是上架状态");
        }
        if (Objects.equals(status, ActivityOnOffShelfStatus.ON.getId())) {
            Map<String, Object> ext = promotionActivityDO.getExt();
            ext.put("deliveryChannel", deliveryChannel);
            promotionActivityDO.setExt(ext);
        }
        promotionActivityDO.setUpperStatus(Integer.valueOf(status));
        boolean result = promotionActivityDAO.updateById(promotionActivityDO);
        if (result) {
            promotionActivityManager.forceCacheActInfo(promotionActivityDO.getId());
            //清空列表缓存
            promotionActivityManager.clearAllRunningListCache();
        }
        return result;
    }

    @Override
    @Transactional
    public Boolean receiveNow(FissionReceiveNowDTO dto) {
        Assert.notNull(dto.getResourceId(),"待领取的资源id不能为空");
        PromotionActivityDO promotionActivityDO = promotionActivityDAO.getById(dto.getActivityId());
        CustomerFeedbackQuery query = new CustomerFeedbackQuery();
        query.setActivityId(dto.getActivityId());
        query.setPhone(dto.getPhone());
        query.setResourceId(dto.getResourceId());
        ActivityFormFeedbackDTO activityFormFeedbackDTO = customerFeedbackDAO.getActivityFormFeedUserId(dto.getActivityId(), dto.getPhone(), dto.getResourceId());
        Assert.notNull(activityFormFeedbackDTO,"请填写信息登记");
        ActivityPartakeLogDO partakeLog = activityPartakeLogDAO.getPartakeLogById(dto.getSponsorId());
        Assert.notNull(partakeLog,"发起人信息丢失");

        ActivityFissionAssistResourceDO fissionAssistResourceDO = activityFissionAssistResourceDAO.getByLadderSort(null,partakeLog.getId(),dto.getResourceId());
        Assert.isTrue(fissionAssistResourceDO != null && fissionAssistResourceDO.getReceived() == 0,"没有可领取奖品");

        PromotionHisResourceDO hisResourceDO = promotionHisResourceDAO.getById(dto.getResourceId());

        //记录奖品领取信息
        assistReceive(fissionAssistResourceDO, hisResourceDO, partakeLog,
                promotionActivityDO.getName(),activityFormFeedbackDTO);
        return true;
    }

    /**
     * 设置参与活动人员白名单
     * @return
     */
    public Boolean whiteList(WhiteListRequestDto dto) {
        PromotionActivityDetailDTO promotionActivity = getActivityById(dto.getActivityId());
        Assert.isTrue(promotionActivity.getStatus() != 3 ,"活动已结束");

        RMapCache<String, Object> mapCache = redissonClient.getMapCache(RedisConstants.CACHE_PREV_KEY_WHITELIST + promotionActivity.getId());
        if (dto.getOptionType() == null || dto.getOptionType() == 1) { // 添加
            if (dto.getTtl() != null && dto.getTtl() > 0) {
                mapCache.put(dto.getUnionId(), 1, dto.getTtl(), TimeUnit.SECONDS);
            } else {
                mapCache.put(dto.getUnionId(), 1,promotionActivity.getEndTime().getTime() - System.currentTimeMillis(), TimeUnit.MILLISECONDS);
            }
        } else if (dto.getOptionType() == 2) { // 删除
            mapCache.remove(dto.getUnionId());
        } else {
            throw new ApplicationException("操作类型错误");
        }
        return true;
    }

    /**
     * 分享活动的操作，不同的活动逻辑可能不一样
     * @param dto
     * @return
     */
    @Override
    @Transactional
    public Object share(ShareLuckyDrawRequestDTO dto) {
        PromotionActivityDetailDTO activity = getActivityById(dto.getActivityId());
        if (StrategyGroupEnum.HF_FISSION_ASSIST_ACT.getId().equals(activity.getPaTemplateId().toString())) {
            // 助力活动
            return assistActService.share(dto, activity);
        }
        //查询活动关联规则
        List<PromotionActivityLimitDO> promotionActivityLimits = promotionActivityLimitDAO.selectByActivityId(dto.getActivityId());
        Assert.notEmpty(promotionActivityLimits,"活动规则未配置");
        //找出用户的参与情况
        ActivityPartakeRequest query = dto.clone(ActivityPartakeRequest.class);
        ActivityUserRelatedDTO activityUserRelated = getActivityUserRelated(query,promotionActivityLimits,activity.getPaTemplateId().toString());
        if (activityUserRelated == null) {
            return false;
        }
        Integer shareNumber = (Integer) activityUserRelated.getLimits().get(ActivityTemplateNumberEnum.SHARE_NUMBER.getId());
        //看分享次数是否已经用完
        Assert.isTrue(shareNumber!= null && shareNumber > 0,"分享次数已用完");
        if (StrategyGroupEnum.HF_LOCATION_SIGN_ACT.getId().equals(activity.getPaTemplateId().toString())) {
            //签到地点活动
            locationSignActService.share(dto, activity,activityUserRelated);
        } else if (StrategyGroupEnum.HF_LUCKYDRAW_ACT.getId().equals(activity.getPaTemplateId().toString())) {
            // 抽奖活动
            luckyDrawService.share(activityUserRelated,promotionActivityLimits);
        }
        //消耗分享次数
        ActivityUserRelatedDO activityUserRelatedDO = new ActivityUserRelatedDO();
        activityUserRelatedDO.setId(activityUserRelated.getId());
        activityUserRelated.getLimits().put(ActivityTemplateNumberEnum.SHARE_NUMBER.getId(),shareNumber - 1);
        activityUserRelatedDO.setLimits(activityUserRelated.getLimits());
        return activityUserRelatedDAO.updateById(activityUserRelatedDO);
    }

    @Override
    public CreateSignCodeDTO createSignCode(Long id) {
        int expired = 30;//有限期30秒
        //重新生成
        PromotionActivityDO activity = promotionActivityDAO.getById(id);
        ActivityPromotionChannelCreateRequestDTO dto = new ActivityPromotionChannelCreateRequestDTO();
        dto.setType(ChannelTypeEnum.WECHAT_APPLET.getId());
        dto.setPromotionType(ActivityPromotionTypeEnum.ACTIVITY.getType());
        dto.setActivityId(id);
        dto.setCreatedBy(activity.getCreatedBy());
        dto.setCodeType(WxCodeTypeEnum.SIGN_CODE.getId().toString());//签到码
        dto.setOrgType(1);//全国
        dto.setCustom(0);//系统生成
        dto.setInvalidTime(DateUtil.offsetSecond(new Date(),expired));//有效时间30秒
        dto.setChannelName(activity.getName() + WxCodeTypeEnum.SIGN_CODE.getValue());

        String signCodeUrl = activityPromotionChannelService.save(dto);
        return new CreateSignCodeDTO(expired,signCodeUrl);
    }
    /**
     * 活动签到
     * 活动结束不能签到，报名前不能签到
     * @param scene
     * @param phone
     * @return
     */
    @Override
    @Transactional
    public Boolean signIn(String scene, String phone) {
        ActivityPromotionChannelDO channelDO = activityPromotionChannelService.getByScene(scene);
        Assert.notNull(channelDO,"签到失败，无效的签到码");
        //检查签到码是否过期
        Long invalidTime = (Long) channelDO.getExt().get("invalidTime");
        Assert.isTrue(invalidTime == null || invalidTime >= System.currentTimeMillis(),"签到失败，码已过期");
        PromotionActivityDO activity = promotionActivityDAO.getById(channelDO.getActivityId());
        Assert.isTrue(activity.getStatus() != 3 ,"签到失败，活动已结束");
        //检查是否已报名
        ActivityPartakeLogDO partakeLog = activityPartakeLogDAO.getByPhone(activity.getId(), phone);
        Assert.notNull(partakeLog,"签到失败，请先报名");
        int count = activityFissionLogDAO.countByUserIdPartakeLogId(partakeLog.getId(), partakeLog.getUserId(), activity.getId());
        Assert.isTrue(count == 0,"签到失败，请勿重复签到");
        ActivityFissionLogDO fissionLogDO = BargainingConverter.activityFissionLogConverter(partakeLog);
        fissionLogDO.setRemark(scene);//记录扫码的scene
        activityFissionLogDAO.save(fissionLogDO);
        //签到成功后的处理
        afterSignIn(activity,partakeLog);
        return true;
    }

    @Override
    public List<PromotionActivityDO> getSignUpActivityCheckList() {
        //未开始的报名活动
        return promotionActivityDAO.lambdaQuery().eq(PromotionActivityDO::getStatus,ActivityStatusEnum.UN_START.getId())
                .eq(PromotionActivityDO::getPaTemplateId,StrategyGroupEnum.HF_SIGN_UP_ACT.getId()).list();
    }

    /**
     * 取消参与记录
     * @param dto
     * @return
     */
    @Override
    public Boolean cancelAct(CancelActivityRequest dto) {
        //找到参与记录，判断activityId，phone是否一致
        ActivityPartakeLogDO partakeLog = activityPartakeLogDAO.getById(dto.getPartakeLogId());
        Assert.isTrue(partakeLog != null && dto.getPhone().equals(partakeLog.getPhone()),"参与记录不存在");
        //有订单的不能取肖
        Assert.isTrue(partakeLog.getOrderId() == null,"此接口不支持订单参与活动");
        PromotionActivityDO activity = promotionActivityDAO.getById(partakeLog.getActivityId());
        //活动结束不能取取消
        Assert.isFalse(activity.getStatus() == 3,"活动已结束，不能取消");

        //检查是否已经签到，已经签到的活动不能取消报名
        if (!dto.isAdmin()) {
            int signInCount = activityFissionLogDAO.countByUserIdPartakeLogId(partakeLog.getId(), partakeLog.getUserId(), activity.getId());
            Assert.isTrue(signInCount == 0, "已经签到的活动不能取消报名");
        }

        //默认为1
        Integer refundQty = 1;
        if (StrategyGroupEnum.HF_SIGN_UP_ACT.getId().equals(activity.getPaTemplateId().toString())) {
            refundQty = cancelSignUp(dto, partakeLog, activity);
            if (refundQty == null) {//不是整个取消
                return true;
            }
        }
        //删除参与记录
        activityPartakeLogDAO.lambdaUpdate()
                .eq(ActivityPartakeLogDO::getId, partakeLog.getId())
                .set(ActivityPartakeLogDO::getDeleted,1)
                .set(HuafaRuntimeEnv.getCreatedBy() != null,ActivityPartakeLogDO::getUpdatedBy,HuafaRuntimeEnv.getCreatedBy())
                .set(ActivityPartakeLogDO::getUpdatedTime,new Date())
                .update();
        //删除签到记录
        activityFissionLogDAO.lambdaUpdate()
                .eq(ActivityFissionLogDO::getPartakeLogId,partakeLog.getId())
                .set(ActivityFissionLogDO::getDeleted,1)
                .set(HuafaRuntimeEnv.getCreatedBy() != null,ActivityFissionLogDO::getUpdatedBy,HuafaRuntimeEnv.getCreatedBy())
                .set(ActivityFissionLogDO::getUpdatedTime,new Date())
                .update();
        //还原库存
        refundStock(partakeLog, refundQty);
        return true;
    }

    private Integer cancelSignUp(CancelActivityRequest dto, ActivityPartakeLogDO partakeLog, PromotionActivityDO activity) {
        //活动开始前24小时前才能取消
        Assert.isTrue(dto.isAdmin() || DateUtil.offsetDay(activity.getStartTime(), -1).isAfter(new Date()),"活动开始前24小时才能取消");
        //取消的人数不能为空
        Assert.notEmpty(dto.getItems(),"取消人不能为空");
        JSONArray items = (JSONArray) partakeLog.getExt().get("items");
        Integer refundQty = 1;
        //统计需要还原的库存数据
        if (CollUtil.isNotEmpty(items)) {
            refundQty = 0;
            List<Integer> cancelIdx = dto.getItems();
            // 删除指定下标的元素
            for (int i = cancelIdx.size() - 1; i >= 0; i--) {
                int idx = cancelIdx.get(i);
                if (idx >= 0 && idx < items.size()) {
                    items.remove(idx);
                    refundQty++;
                }
            }
            //如果items不为空，则更新参与记录的items并退部分库存
            if (items.size() > 0) {
                // 更新参与记录的items
                partakeLog.getExt().put("items", items);
                // 更新总人数
                partakeLog.setNeedFissonCount(items.size());
                partakeLog.setUpdatedTime(new Date());
                partakeLog.setUpdatedBy(HuafaRuntimeEnv.getCreatedBy());
                activityPartakeLogDAO.updateById(partakeLog);
                refundStock(partakeLog, refundQty);
                return null;
            }
        }
        return refundQty;
    }

    private void refundStock(ActivityPartakeLogDO partakeLog, Integer refundQty) {
        //还原部分库存
        if (partakeLog.getResourceId() != null && partakeLog.getResourceId() > 0) {
            promotionActivityManager.incrRemainQty(partakeLog.getResourceId(), refundQty.longValue());
            promotionActivityManager.incrRedisQty(partakeLog.getResourceId(), partakeLog.getActivityId(), refundQty);
        }
    }

    /**
     * 管理员取消活动，如果订单的，则取消订单
     * @param dto
     * @return
     */
    @Override
    public Boolean cancelActByAdmin(CancelActivityRequest dto) {
        //找到参与记录，判断activityId，phone是否一致
        ActivityPartakeLogDO partakeLog = activityPartakeLogDAO.getById(dto.getPartakeLogId());
        Assert.isTrue(partakeLog != null && dto.getPhone().equals(partakeLog.getPhone()),"参与记录不存在");

        //如果是有订单的，则走取消订单，否则走免费取消的
        if (partakeLog.getOrderId() != null && partakeLog.getOrderId() > 0) {
            ActivityOrderDO orderDO = activityOrderDAO.getById(partakeLog.getOrderId());
            //取消订单
            CancelOrderRequest cancelOrderRequest = new CancelOrderRequest();
            cancelOrderRequest.setOrderId(orderDO.getId());
            cancelOrderRequest.setCode(orderDO.getCode());
            cancelOrderRequest.setReason("管理员后台取消");
            activityOrderService.cancelOrder(cancelOrderRequest);
        } else {
            //免费取消
            cancelAct(dto);
        }
        return true;
    }

    @Override
    public void fillActivityCommonInfo(List evaluationDTOList) {
        if (CollectionUtil.isEmpty(evaluationDTOList)) {
            return;
        }
        // 获取指定项目类型的活动ID列表
        List<Long> activityIdList = new ArrayList<>();
        for (Object x : evaluationDTOList) {
            if (x instanceof ActivityCommonInfoDTO) {
                ActivityCommonInfoDTO activityCommonInfoDTO = (ActivityCommonInfoDTO) x;
                if (ResourcesAttributeEnum.APPOINT.getId().equals(activityCommonInfoDTO.getProjectType())) {
                    activityIdList.add(activityCommonInfoDTO.getActivityId());
                }
            }
        }

        // 获取区域、城市、项目名称映射
        List<ActivityParticipationDO> activityParticipationDOList = activityParticipationDAO.getListByActivityList(activityIdList);
        Map<Long, Set<String>> areaNameMap = new HashMap<>();   
        Map<Long, Set<String>> cityNameMap = new HashMap<>();
        Map<Long, Set<String>> projectNameMap = new HashMap<>();
        activityParticipationDOList.forEach(x -> {
            areaNameMap.computeIfAbsent(x.getActivityId(), k -> new HashSet<>()).add(x.getAreaName());
            cityNameMap.computeIfAbsent(x.getActivityId(), k -> new HashSet<>()).add(x.getCityName());
            projectNameMap.computeIfAbsent(x.getActivityId(), k -> new HashSet<>()).add(x.getProjectName());
        });

        for (Object x : evaluationDTOList) {
            ActivityCommonInfoDTO activityCommonInfoDTO = (ActivityCommonInfoDTO) x;
            // 处理扩展字段
            if (StrUtil.isNotBlank(activityCommonInfoDTO.getExtStr())) {
                Map<String, Object> extMap = JSON.parseObject(activityCommonInfoDTO.getExtStr(), Map.class);
                activityCommonInfoDTO.setExtStr(null);
                activityCommonInfoDTO.setExt(extMap);
            }

            // 设置区域、城市、项目信息
            activityCommonInfoDTO.setAreas(areaNameMap.containsKey(activityCommonInfoDTO.getActivityId()) ?
                    String.join(",", areaNameMap.get(activityCommonInfoDTO.getActivityId())) : "全国");
            activityCommonInfoDTO.setCities(cityNameMap.containsKey(activityCommonInfoDTO.getActivityId()) ?
                    String.join(",", cityNameMap.get(activityCommonInfoDTO.getActivityId())) : "全国");
            activityCommonInfoDTO.setProjects(projectNameMap.containsKey(activityCommonInfoDTO.getActivityId()) ?
                    String.join(",", projectNameMap.get(activityCommonInfoDTO.getActivityId())) : "全国");
        }

    }

    private void afterSignIn(PromotionActivityDO activity, ActivityPartakeLogDO partakeLog) {
        //如果是签到活动
        if (StrategyGroupEnum.HF_SIGN_UP_ACT.getId().equals(activity.getPaTemplateId().toString())) {
            signUpService.afterSignIn(activity,partakeLog);
        }
    }

    @Override
    public List<OneCodeAuthDTO> scanList(Long id) {
        //获取扫码记录
        List<ActivityFissionLogDO> fissionLogDOS = activityFissionLogDAO.lambdaQuery().eq(ActivityFissionLogDO::getActivityId, id).orderByDesc(BaseEntity::getId).list();
        List<String> openIds = fissionLogDOS.stream().map(ActivityFissionLogDO::getUserId).collect(Collectors.toList());
        Map<String,AccountQueryVo> accountQueryVoMap;
        if (!openIds.isEmpty()) {
            accountQueryVoMap = huaFaHmacAuthUtil.getByOpenIds(openIds,"SQXCX").stream().collect(Collectors.toMap(AccountQueryVo::getOpenId, Function.identity()));
        } else {
            accountQueryVoMap = new HashMap<>();
        }
        Map<Long,ActivityPartakeLogDO> partakeLogMap = activityPartakeLogDAO.lambdaQuery().select(BaseEntity::getId, BaseExtEntity::getExt).eq(ActivityPartakeLogDO::getActivityId, id).list().stream().collect(Collectors.toMap(ActivityPartakeLogDO::getId, Function.identity()));
        return fissionLogDOS.stream().map(item  -> new OneCodeAuthDTO(item,accountQueryVoMap.get(item.getUserId()),partakeLogMap.get(item.getPartakeLogId()).getExt())).collect(Collectors.toList());
    }

    @Override
    public List<CommunityActivityDTO> getByCommunity(List<Long> ids, boolean fetchDetail) {
        List<PromotionActivityAssociation> list = associationService.getAssociationListBatch(ids, AssociationTypeEnum.COMMUNITY_ACTIVITY);
        //统计每个社群的数量
        Map<Long,List<PromotionActivityAssociation>> communityMap = list.stream().collect(Collectors.groupingBy(PromotionActivityAssociation::getMainId));
        List<CommunityActivityDTO> result = communityMap.entrySet().stream().map(item ->
            new CommunityActivityDTO(item.getKey(),item.getValue().size())
        ).collect(Collectors.toList());
        //如果要获取明细
        if (fetchDetail) {
            List<Long> activityIds = list.stream().map(PromotionActivityAssociation::getSubId).collect(Collectors.toList());
            //活动信息
            Map<Long,PromotionActivityDO> activityMap = promotionActivityDAO.selectBatchIds(activityIds).stream().collect(Collectors.toMap(PromotionActivityDO::getId, Function.identity()));
            //参与人数
            Map<Long,Long> partakeCountMap = activityPartakeLogDAO.partakeCount(activityIds);
            //活动标签
            Map<Long,List<TagItemDTO>> tagsMap = associationService.getAssociationListBatch(activityIds, AssociationTypeEnum.ACTIVITY_TAG).stream().collect(Collectors.groupingBy(PromotionActivityAssociation::getMainId,Collectors.mapping(associationService::convertToTagItem, Collectors.toList())));
            result.forEach(item -> {
                List<PromotionActivityAssociation> associations = communityMap.get(item.getCommunityId());
                List<CommunityActivityDTO.CommunityActivityItem> items = associations.stream().map(association ->
                    new CommunityActivityDTO.CommunityActivityItem(activityMap.get(association.getSubId()),tagsMap.get(association.getSubId()),partakeCountMap.getOrDefault(association.getSubId(),0L))
                ).collect(Collectors.toList());
                item.setItems(items);
            });
        }
        return result;
    }

    @Override
    public List<String> tagList(String deliveryChannel, String cityId) {
        return associationService.activityTagList(deliveryChannel,cityId);
    }

    @Transactional
    @Override
    public Boolean audit(ActivityAuditRequest dto) {
        Assert.notEmpty(HuafaRuntimeEnv.getCreatedBy(),"审核人不能为空");
        Assert.isTrue(Objects.equals(dto.getStatus(),ActivityAuditStatusEnum.PASS_AUDIT.getId()) || StrUtil.isNotBlank(dto.getRejectReason()),"审核不通过，则需要提交不通过原因");
        PromotionActivityDO activityDO = promotionActivityDAO.getById(dto.getId());
        Assert.notNull(activityDO,"活动不存在");
        Assert.isTrue(Objects.equals(activityDO.getExt().get("auditStatus"),ActivityAuditStatusEnum.PENDING_AUDIT.getId()),"活动状态异常，无法审核");
        activityDO.getExt().put("auditStatus",dto.getStatus());
        activityDO.getExt().put("auditTime", DateUtil.now());
        activityDO.getExt().put("auditor",HuafaRuntimeEnv.getCreatedBy());
        //审核通过，直接发布上架
        if (Objects.equals(dto.getStatus(),ActivityAuditStatusEnum.PASS_AUDIT.getId())) {
            activityDO.setUpperStatus(Integer.valueOf(ActivityOnOffShelfStatus.ON.getId()));
            activityDO.getExt().put("rejectReason",null);
        } else {
            activityDO.getExt().put("rejectReason",dto.getRejectReason());
        }
        activityDO.setUpdatedBy(HuafaRuntimeEnv.getCreatedBy());
        activityDO.setUpdatedTime(new Date());
        //同步到缓存
        promotionActivityDAO.updateById(activityDO);
        promotionActivityManager.forceCacheActInfo(dto.getId());
        return true;
    }

    @Override
    public ActivityHomeInfoResponseDTO homeInfo(ActivityHomeInfoRequest query) {
        PromotionActivityDetailDTO actDTO = JSON.parseObject(String.valueOf(redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO + query.getActivityId()).get()), PromotionActivityDetailDTO.class);
        PromotionActivityResponseDTO clone = null;
        List<ResourceHisDetailResponseDTO> prizeList = null;
        ActivityHomeInfoResponseDTO responseDTO = new ActivityHomeInfoResponseDTO();
        if (Objects.nonNull(actDTO)) {
            clone = actDTO.clone(PromotionActivityResponseDTO.class);
            clone.setName(actDTO.getActivityName());
            clone.setId(actDTO.getActivityId());
            prizeList = actDTO.getPrizeList();
            responseDTO.setPrizeList(prizeList);
            responseDTO.setPromotionActivity(clone);
            responseDTO.setActivityPageVO(actDTO.getActivityPageVO());
            responseDTO.setActivityPageShareVO(actDTO.getActivityPageShareVO());
            responseDTO.setFrontProjectList(actDTO.getFrontProjectList());
        } else {
            PromotionActivityDO promotionActivity = promotionActivityDAO.getById(query.getActivityId());
            Assert.notNull(promotionActivity,"活动不存在");

            clone = promotionActivity.clone(PromotionActivityResponseDTO.class);
            //活动页
            ActivityPageDO activityPageDO = activityPageDAO.getByActivity(promotionActivity.getId(), ActivityTypeEnum.ACTIVITY.getId());
            ActivityPageVO activityPageVO = activityPageDO.clone(ActivityPageVO.class);

            String bottomBtnType = activityPageDO.getBottomBtnType();
            if (StringUtil.isNotEmpty(bottomBtnType)) {
                String[] split = bottomBtnType.split(",");
                Integer[] types = (Integer[]) ConvertUtils.convert(split, Integer.class);
                activityPageVO.setBottomBtnTypes(Arrays.asList(types));
            }
            //活动分享页
            ActivityPageShareDO activityPageShareDO = activityPageShareDAO.getByActivity(promotionActivity.getId(), ActivityTypeEnum.ACTIVITY.getId());
            //处理标签
            List<TagItemDTO> tags = associationService.getAssociationList(promotionActivity.getId(), AssociationTypeEnum.ACTIVITY_TAG).stream().map(associationService::convertToTagItem).collect(Collectors.toList());
            clone.setTags(tags);

            prizeList = getPrizeList(promotionActivity.getId());
            responseDTO.setPrizeList(prizeList);
            responseDTO.setPromotionActivity(clone);
            responseDTO.setActivityPageVO(activityPageVO);
            responseDTO.setActivityPageShareVO(activityPageShareDO.clone(ActivityPageShareVO.class));
            //获取项目列表，展示给前端
            List<ActivityParticipationGroupResponseDTO> activityParticipationGroupResponseDTOList = activityParticipationService.getProjectGroupList(promotionActivity.getId());
            responseDTO.setFrontProjectList(activityParticipationGroupResponseDTOList);
        }
        //统计用户参与数量
        Integer count = activityPartakeLogDAO.lambdaQuery().eq(ActivityPartakeLogDO::getActivityId, clone.getId()).count();
        responseDTO.setTotalCnt(count);
        //如果有电话，加上用户参与记录
        ActivityPartakeLogDO activityPartakeLog = null;
        if (StrUtil.isNotBlank(query.getPhone())) {
            activityPartakeLog = activityPartakeLogDAO.getByPhone(clone.getId(),query.getPhone());
        }
        ActivityPartakeLogDTO partakeLogDTO = null;
        if (activityPartakeLog != null) {
            partakeLogDTO = activityPartakeLog.clone(ActivityPartakeLogDTO.class);
            //设置评价id
            ActivityEvaluationDO evaluationDO = activityEvaluationDAO.getByPartakeLogId(activityPartakeLog.getId());
            partakeLogDTO.setEvaluationId(evaluationDO != null ? evaluationDO.getId() : null);
        } else {
            partakeLogDTO = new ActivityPartakeLogDTO();
            partakeLogDTO.setActivityId(clone.getId());
        }
        responseDTO.setActivityPartakeLogDTO(partakeLogDTO);
        if (StrategyGroupEnum.HF_SIGN_ACT.getId().equals(clone.getPaTemplateId().toString())) {// 签到活动
            //计算当天是否已参与，并且是否能抽奖
            signActService.homeInfo(partakeLogDTO);
        }
        if (StrategyGroupEnum.HF_LOCATION_SIGN_ACT.getId().equals(clone.getPaTemplateId().toString())) {// 地点签到活动
            //增加抽奖规则明细
            List<PromotionActivityLimitDO> promotionActivityLimits = promotionActivityLimitDAO.selectByActivityId(query.getActivityId());
            locationSignActService.homeInfo(query,promotionActivityLimits,responseDTO);
        }
        if (StrategyGroupEnum.HF_SIGN_UP_ACT.getId().equals(clone.getPaTemplateId().toString())) {// 地点签到活动
            //增加扫码记录
            List<OneCodeAuthDTO> scanList = scanList(query.getActivityId());
            signUpService.homeInfo(responseDTO,scanList);
        }
        return responseDTO;
    }

    @Override
    public Boolean switchProject(SwitchProjectDTO dto) {
        return activityPartakeLogDAO.lambdaUpdate().set(ActivityPartakeLogDO::getProjectId,dto.getProjectId())
                .set(ActivityPartakeLogDO::getProjectName,dto.getProjectName())
                .eq(ActivityPartakeLogDO::getActivityId,dto.getActivityId())
                .eq(ActivityPartakeLogDO::getPhone,dto.getPhone()).update();
    }
    @Override
    public Boolean toggleTopStatus(Long id, Integer status) {
        Date topTime = status == 1 ? new Date() : null;
        boolean updated = promotionActivityDAO.lambdaUpdate().set(PromotionActivityDO::getTopTime, topTime).eq(PromotionActivityDO::getId, id).update();
        if (updated) {
            //清空列表缓存
            promotionActivityManager.clearAllRunningListCache();
        }
        return updated;
    }

    @Override
    public ExcelExportResponseDTO getExportActivityListExcelData(PromotionActivityPageQuery query) {
        // 设置查询所有数据
        query.setPage(1);
        query.setSize(Integer.MAX_VALUE);

        // 获取活动列表数据
        PageBean<PromotionActivityDTO> pageBean = findWholePageList(query);
        List<PromotionActivityDTO> activityList = pageBean.getContent();

        if (CollectionUtil.isEmpty(activityList)) {
            return ExcelExportResponseDTO.builder()
                    .title("活动列表")
                    .fileName("活动列表导出")
                    .headerKey(new String[]{})
                    .headList(new String[]{})
                    .dataList(Lists.newArrayList())
                    .build();
        }

        try {
            GetExcelExportResultUtil util = GetExcelExportResultUtil.getInstance();
            return util.exportActivityListInfo(activityList, this, associationService);
        } catch (Exception e) {
            log.error("活动列表导出数据处理异常", e);
            throw new ApplicationException("活动列表导出数据处理异常");
        }
    }
}
