package com.deepexi.dxp.marketing.domain.marketing.request;

import lombok.Data;

/**
 * 流程画布执行 信息通用类
 * <AUTHOR>
 * @version 1.0
 * @date 2020-07-16 18:56
 */
@Data
public class FlowCanvasExecuteInfoRequest {
    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 是否异步调用
     */
    private Boolean async;

    /**
     *流程画布实例ID
     */
    private Long execId;

    /**
     * 节点ID
     */
    private String nodeId;

    /**
     * 节点类型
     */
    private String type;

    /**
     * 动作类型
     */
    private String nodeType;

    /**
     * 用户ID
     */
    private Long userId;
}
