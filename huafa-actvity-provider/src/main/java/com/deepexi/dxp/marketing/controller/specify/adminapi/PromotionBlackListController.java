package com.deepexi.dxp.marketing.controller.specify.adminapi;

import com.cnhuafas.common.model.ResultBean;
import com.deepexi.dxp.marketing.domain.promotion.query.specify.PromotionBlackListPageQuery;
import com.deepexi.dxp.marketing.domain.promotion.request.specify.PromotionBlackListCreateRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.specify.PromotionBlackListUpdateRequest;
import com.deepexi.dxp.middle.promotion.dao.impl.specify.PromotionBlackListService;
import com.deepexi.dxp.middle.promotion.domain.dto.PromotionBlackListPageVO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.PromotionBlackListDO;
import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@Api(tags = "参与活动黑名单管理")
@RestController
@RequestMapping("/admin-api/v1/black-list")
public class PromotionBlackListController {

    @Resource
    private PromotionBlackListService promotionBlackListService;

    @ApiOperation("创建记录")
    @PostMapping("/create")
    public ResultBean<Integer> create(@Valid @RequestBody PromotionBlackListCreateRequest dto) {
        Integer id = promotionBlackListService.create(dto);
        return ResultBean.success(id);
    }

    @ApiOperation("更新记录")
    @PostMapping("/update")
    public ResultBean<Boolean> update(@Valid @RequestBody PromotionBlackListUpdateRequest dto) {
        return ResultBean.success(promotionBlackListService.update(dto));
    }

    @ApiOperation("启用/禁用")
    @PostMapping("/enable")
    public ResultBean<Boolean> enable(@RequestParam Integer id, @RequestParam Integer status) {
        Boolean result = promotionBlackListService.enable(id, status);
        return ResultBean.success(result);
    }

    @ApiOperation("根据ID查看详情")
    @GetMapping("/getById")
    public ResultBean<PromotionBlackListDO> getById(@RequestParam Integer id) {
        return ResultBean.success(promotionBlackListService.getById(id));
    }
    @ApiOperation("分页查询数据")
    @PostMapping("/page")
    public ResultBean<PageBean<PromotionBlackListPageVO>> page(@Valid @RequestBody PromotionBlackListPageQuery form) {
        PageBean<PromotionBlackListPageVO> resultPage = promotionBlackListService.pageByCondition(form);
        return ResultBean.success(resultPage);
    }
}
