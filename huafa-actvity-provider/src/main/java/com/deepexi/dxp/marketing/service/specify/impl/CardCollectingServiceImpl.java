package com.deepexi.dxp.marketing.service.specify.impl;

import cn.hutool.core.lang.WeightRandom;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.deepexi.dxp.marketing.common.base.utils.CommonExceptionCode;
import com.deepexi.dxp.marketing.config.HuafaConstantConfig;
import com.deepexi.dxp.marketing.constant.RedisConstants;
import com.deepexi.dxp.marketing.converter.ActivityInfoConverter;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityFissionLogQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.CustomerFeedbackQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.*;
import com.deepexi.dxp.marketing.domain.marketing.response.*;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.PromotionActivityResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.specify.ResourceHisDetailResponseDTO;
import com.deepexi.dxp.marketing.domain.specify.dto.ActivityFormFeedbackDTO;
import com.deepexi.dxp.marketing.enums.activity.limit.PATemplateBaseEnum;
import com.deepexi.dxp.marketing.enums.coupon.WhetherEnum;
import com.deepexi.dxp.marketing.enums.resource.ActivityStatusEnum;
import com.deepexi.dxp.marketing.enums.specify.*;
import com.deepexi.dxp.marketing.manager.promotion.PromotionActivityManager;
import com.deepexi.dxp.marketing.service.specify.AsynchronousService;
import com.deepexi.dxp.marketing.service.specify.CardCollectingService;
import com.deepexi.dxp.marketing.service.specify.IncentiveService;
import com.deepexi.dxp.marketing.service.specify.PromotionActivityService;
import com.deepexi.dxp.marketing.utils.GenerateIdUtil;
import com.deepexi.dxp.middle.promotion.converter.specify.BargainingConverter;
import com.deepexi.dxp.middle.promotion.converter.specify.LuckyDrawConverter;
import com.deepexi.dxp.middle.promotion.dao.PromotionActivityDAO;
import com.deepexi.dxp.middle.promotion.dao.PromotionActivityLimitDAO;
import com.deepexi.dxp.middle.promotion.dao.specify.*;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityLimitDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.*;
import com.deepexi.util.CollectionUtil;
import com.deepexi.util.DateUtils;
import com.deepexi.util.JsonUtil;
import com.deepexi.util.StringUtil;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pojo.ObjectCloneUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.ConvertUtils;
import org.redisson.api.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/12/27
 */
@Slf4j
@Service
public class CardCollectingServiceImpl implements CardCollectingService {

    @Resource
    private PromotionHisResourceDAO promotionHisResourceDAO;
    @Resource
    private ActivityFissionAssistResourceDAO activityFissionAssistResourceDAO;
    @Resource
    private ActivityFissionLogDAO activityFissionLogDAO;
    @Resource
    private ActivityPartakeLogDAO activityPartakeLogDAO;
    @Autowired
    private PromotionActivityLimitDAO promotionActivityLimitDAO;
    @Resource
    private GenerateIdUtil generateIdUtil;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private ActivityPageDAO activityPageDAO;
    @Resource
    private PromotionActivityDAO promotionActivityDAO;
    @Resource
    private ActivityPageShareDAO activityPageShareDAO;
    @Resource
    private PromotionActivityManager promotionActivityManager;
    @Resource
    private IncentiveService incentiveService;
    @Resource
    public CustomerFeedbackDAO customerFeedbackDAO;
    @Resource
    public ActivityVerifyDAO activityVerifyDAO;
    @Resource
    private PromotionActivityService promotionActivityService;
    @Autowired
    private HuafaConstantConfig huafaConstantConfig;
    @Resource
    public AsynchronousService asynchronousService;


    @Override
    public CardCollectingHomePageInfoResponseDTO homePageInfo(ActivityPartakeRequest query) {
        PromotionActivityDetailDTO actDTO = JSON.parseObject(String.valueOf(redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO + query.getActivityId()).get()), PromotionActivityDetailDTO.class);
        if (Objects.isNull(actDTO)) {
            PromotionActivityDO promotionActivity = promotionActivityDAO.getById(query.getActivityId());
            if (promotionActivity == null) {
                throw new ApplicationException("活动不存在");
            }
            actDTO = promotionActivity.clone(PromotionActivityDetailDTO.class);
            actDTO.setActivityName(promotionActivity.getName());
            actDTO.setActivityId(promotionActivity.getId());
            //界面活动页配置
            ActivityPageVO activityPageDTO = Optional.ofNullable(activityPageDAO.getByActivity(query.getActivityId(), ActivityTypeEnum.ACTIVITY.getId()))
                    .map(e -> {
                        ActivityPageVO activityPageVO = e.clone(ActivityPageVO.class);
                        String bottomBtnType = e.getBottomBtnType();
                        if (StringUtil.isNotEmpty(bottomBtnType)) {
                            String[] split = bottomBtnType.split(",");
                            Integer[] types = (Integer[]) ConvertUtils.convert(split, Integer.class);
                            activityPageVO.setBottomBtnTypes(Arrays.asList(types));
                        }
                        return activityPageVO;
                    }).orElse(null);
            actDTO.setActivityPageVO(activityPageDTO);
            //界面分享页配置
            actDTO.setActivityPageShareVO(Optional.ofNullable(activityPageShareDAO.getByActivity(query.getActivityId(), ActivityTypeEnum.ACTIVITY.getId())).map(e -> e.clone(ActivityPageShareVO.class)).orElse(null));
            //奖品列表
            List<PromotionHisResourceDO> promotionHisResourceList = promotionHisResourceDAO.findByActivityId(query.getActivityId());
            actDTO.setPrizeList(ObjectCloneUtils.convertList(promotionHisResourceList, ResourceHisDetailResponseDTO.class));
        }
        CardCollectingHomePageInfoResponseDTO responseDTO = new CardCollectingHomePageInfoResponseDTO();
        PromotionActivityResponseDTO clone = actDTO.clone(PromotionActivityResponseDTO.class);
        clone.setName(actDTO.getActivityName());
        clone.setId(actDTO.getActivityId());
        responseDTO.setPromotionActivity(clone);
        //界面活动页配置
        responseDTO.setActivityPageDTO(actDTO.getActivityPageVO());
        //界面分享页配置
        responseDTO.setActivityPageShareDTO(actDTO.getActivityPageShareVO().clone(ActivityPageShareDTO.class));
        //奖品列表
        responseDTO.setPrizeList(ObjectCloneUtils.convertList(actDTO.getPrizeList().stream()
                        .filter(item -> Objects.equals(item.getFissonResourceType(), FissonResourceTypeEnum.POWER_LADDER.getId())).collect(Collectors.toList()),
                PromotionHisResourceDTO.class));
        // 卡片列表
        responseDTO.setCardList(ObjectCloneUtils.convertList(actDTO.getPrizeList().stream()
                        .filter(item -> Objects.equals(item.getFissonResourceType(), FissonResourceTypeEnum.HELP_WIN_AWARDS.getId()))
                        .sorted(Comparator.comparing(ResourceHisDetailResponseDTO::getLadderSort)).collect(Collectors.toList()),
                PromotionHisResourceDTO.class));
        return responseDTO;
    }

    @Override
    public CardCollectingHomeUserInfoResponseDTO homeUserInfo(ActivityPartakeRequest query) {
        CardCollectingHomeUserInfoResponseDTO responseDTO = new CardCollectingHomeUserInfoResponseDTO();
        ActivityPartakeLogDTO partakeLogDTO = activityPartakeLogDAO.getList(query.getActivityId(), query.getUserId(), null, FissonTypeEnum.CARD_COLLECTING.getId());
        responseDTO.setActivityPartakeLogDTO(partakeLogDTO);
        if (partakeLogDTO != null) {
            PromotionActivityDetailDTO actDTO = JSON.parseObject(String.valueOf(redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO + partakeLogDTO.getActivityId()).get()), PromotionActivityDetailDTO.class);
            List<Long> hisResourceIdList = actDTO.getPrizeList().stream()
                    .filter(item -> Objects.equals(item.getFissonResourceType(), FissonResourceTypeEnum.HELP_WIN_AWARDS.getId()))
                    .map(ResourceHisDetailResponseDTO::getId).collect(Collectors.toList());
            RMap<Long, ActivityFissionAssistResourceDO> rMap = getUserResourceMap(partakeLogDTO.getId(), actDTO.getEndTime());
            responseDTO.setCardList(ObjectCloneUtils.convertList(rMap.readAllValues().stream().filter(item -> hisResourceIdList.contains(item.getResourceId())).collect(Collectors.toList()), ActivityFissionAssistResourceResponseDTO.class));
        }
        return responseDTO;
    }

    @Override
    public LotteryResourceDTO lottery(Long partakeLogId, Long resourceId) {
        log.info("卡片抽奖:{},{}", partakeLogId, resourceId);
        ActivityPartakeLogDO partakeLog = activityPartakeLogDAO.getById(partakeLogId);
        if (partakeLog == null) {
            throw new ApplicationException("参与记录不存在");
        }
        PromotionActivityDetailDTO actDTO = JSON.parseObject(String.valueOf(redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO + partakeLog.getActivityId()).get()), PromotionActivityDetailDTO.class);
        if (Objects.isNull(actDTO)) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "活动已下架或不存在");
        } else if (actDTO.getStatus().equals(Integer.parseInt(ActivityStatusEnum.UN_START.getId()))) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "活动尚未开始，请耐心等待！");
        } else if (!actDTO.getStatus().equals(Integer.parseInt(ActivityStatusEnum.IN_PROGRESS.getId()))) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "来晚啦，活动已结束！");
        }
        // 红包上限配置
        List<PromotionActivityLimitDO> promotionActivityLimitList = promotionActivityLimitDAO.selectByActivityId(partakeLog.getActivityId());
        double moneyLimit = promotionActivityLimitList
                .stream().filter(e -> PATemplateBaseEnum.CARD_COLLECTING.getId().equals(e.getType()))
                .flatMap(item -> JSON.parseArray(item.getLimits(), BaseActivityDTO.class).stream())
                .filter(number -> ActivityTemplateNumberEnum.MONEY_LIMIT.getId().equals(number.getId()))
                .map(e -> Double.valueOf(e.getValue())).findFirst().orElse(0D);
        // 校验卡片是否属于该用户
        RSet<Long> rSet = getUserResourceSet(partakeLogId, actDTO.getEndTime());
        RMap<Long, ActivityFissionAssistResourceDO> rMap = getUserResourceMap(partakeLogId, actDTO.getEndTime());
        RAtomicDouble rAtomicDouble = getUserMoneyLimit(partakeLogId, rMap, actDTO.getPrizeList());
        ActivityFissionAssistResourceDO activityFissionAssistResourceDO = null;
        for (Map.Entry<Long, ActivityFissionAssistResourceDO> entry : rMap.entrySet()) {
            // 校验用户是否拥有卡片，汇总用户红包金额
            ActivityFissionAssistResourceDO item = entry.getValue();
            if (Objects.equals(item.getId(), resourceId)) {
                if (Objects.equals(item.getReceived(), 0)) {
                    item.setReceived(1);
                    rMap.put(item.getId(), item);
                } else {
                    throw new ApplicationException("卡片已抽奖");
                }
                activityFissionAssistResourceDO = item;
            }
        }
        if (activityFissionAssistResourceDO == null) {
            throw new ApplicationException("未找到用户卡片");
        }
        List<ResourceHisDetailResponseDTO> prizeList = new ArrayList<>();
        boolean isCard = false;
        for (ResourceHisDetailResponseDTO item : actDTO.getPrizeList()) {
            if (Objects.equals(FissonResourceTypeEnum.POWER_LADDER.getId(), item.getFissonResourceType())) {
                prizeList.add(item);
            } else if (Objects.equals(FissonResourceTypeEnum.HELP_WIN_AWARDS.getId(), item.getFissonResourceType())) {
                isCard = isCard || Objects.equals(item.getId(), activityFissionAssistResourceDO.getResourceId());
            }
        }
        if (!isCard) {
            throw new ApplicationException("卡片不存在");
        }
        // 0谢谢参与，1必中奖品，2抽奖奖品，3合成奖品
        Map<Integer, List<ResourceHisDetailResponseDTO>> hisResourceMap = prizeList.stream().collect(Collectors.groupingBy(ResourceHisDetailResponseDTO::getLadderSort));
        ResourceHisDetailResponseDTO win = null;
        for (ResourceHisDetailResponseDTO item : hisResourceMap.get(1)) {
            // 必中奖品
            if (!Objects.equals(item.getLimitType(), 1) || rSet.add(item.getId())) {
                // 不限制用户中奖次数或者用户没中过奖则中奖
                boolean success = promotionActivityManager.decrRedisQtyCheckDay(item.getId(), actDTO.getActivityId());
                if (success) {
                    if (isCashBagPrize(item)) { // 红包校验个人库存
                        double amount = item.getCouponValue().doubleValue();
                        if (rAtomicDouble.addAndGet(amount) > moneyLimit) {
                            rAtomicDouble.addAndGet(-amount);
                            promotionActivityManager.incrRedisQty(item.getId(), actDTO.getActivityId());
                            log.info("用户:{} 已中红包金额{}将超过上限{}，取消必中奖品:{}", partakeLogId, rAtomicDouble.get(), moneyLimit, item.getId());
                            continue;
                        }
                    }
                    win = item;
                    log.info("用户:{} 获得必中奖品{}", partakeLogId, win.getId());
                    break;
                }
            }
        }
        if (win == null) {
            // 没有中必中奖品，则随机抽奖池奖品,不限制用户中奖次数或者用户没中过奖则参与抽奖
            WeightRandom<ResourceHisDetailResponseDTO> weightRandom = WeightRandom.create();
            for (ResourceHisDetailResponseDTO item : hisResourceMap.get(2)) {
                weightRandom.add(item, item.getOddsOfWinning());
                log.info("奖品:{}，概率:{}", item.getId(), item.getOddsOfWinning());
            }
            win = weightRandom.next();
            log.info("随机得奖品:{}", win.getId());
            // 扣减库存
            boolean success = promotionActivityManager.decrRedisQtyCheckDay(win.getId(), actDTO.getActivityId());
            if (success) {
                if (!Objects.equals(win.getLimitType(), 1) || rSet.add(win.getId())) {
                    // 不限制用户中奖次数或者用户没中过奖则中奖,校验个人红包限额
                    if (isCashBagPrize(win)) { // 红包校验库存
                        double amount = win.getCouponValue().doubleValue();
                        if (rAtomicDouble.addAndGet(amount) > moneyLimit) {
                            rAtomicDouble.addAndGet(-amount);
                            promotionActivityManager.incrRedisQty(win.getId(), actDTO.getActivityId());
                            log.info("用户:{} 已中红包金额{}将超过上限{}，随机奖品取消", partakeLogId, rAtomicDouble.get(), moneyLimit);
                            win = null;
                        }
                    }
                } else {
                    log.info("奖品{}库存不足，用户:{} 随机奖品取消", win.getId(), partakeLogId);
                    // 用户限制不能中奖则恢复库存
                    promotionActivityManager.incrRedisQty(win.getId(), actDTO.getActivityId());
                    win = null;
                }
            } else {
                // 库存不足
                win = null;
            }
        }
        LotteryResourceDTO lotteryResourceDTO = null;
        if (win != null) {
            boolean decrDb = promotionActivityManager.decrRemainQty(win.getId(), 1L);
            if (decrDb) {
                // 校验红包总数
                // 更新卡片领取状态
                partakeLog.setCode(generateIdUtil.getResourceCode());
                ActivityFissionAssistResourceDO entity = BargainingConverter.activityFissionAssistResourceConverter(partakeLog, win.clone(PromotionHisResourceDO.class));
                if (PromotionResourceTypeEnum.THIRD_PARTY.getId().equals(win.getType())) {
                    if (ThirdCategoryEnum.PHONE_FEE.getId().equals(win.getThirdCategory())) {
                        //话费充值
                        incentiveService.rechargeMobileAsync(LuckyDrawConverter.rechargeMobileConverter(partakeLog, actDTO.getActivityName(), win, entity.getCode()));
                        entity.setReceived(1); // 话费红包自动领取发放
                    } else if (ThirdCategoryEnum.CASH_BAG.getId().equals(win.getThirdCategory())) {
                        //发送红包激励
                        incentiveService.sendIncentiveAsync(LuckyDrawConverter.sendIncentiveConverter(partakeLog, actDTO.getActivityName(), win, entity.getCode(), SendTemplateNewsRequestDTO.MP_FROM_USH));
                        entity.setReceived(1); // 话费红包自动领取发放
                    }
                }
                // 新增中奖记录
                activityFissionAssistResourceDAO.save(entity);
                rMap.put(entity.getId(), entity);
                rSet.add(win.getId());
                lotteryResourceDTO = win.clone(LotteryResourceDTO.class);
                lotteryResourceDTO.setWinLotteryId(entity.getId());
            } else {
                log.info("用户{}中奖{}修改数据库失败,奖品取消", partakeLogId, win.getId());
                win = null;
            }
        }
        if (win == null) {
            // 都没有奖品则谢谢参与
            win = hisResourceMap.get(0).get(0);
            lotteryResourceDTO = win.clone(LotteryResourceDTO.class);
        }
        activityFissionAssistResourceDO.setType(win.getId().intValue());
        rMap.put(activityFissionAssistResourceDO.getId(), activityFissionAssistResourceDO);
        activityFissionAssistResourceDAO.updateById(activityFissionAssistResourceDO);
        return lotteryResourceDTO;
    }

    @Override
    public LotteryResourceDTO compose(Long partakeLogId) {
        log.info("合成卡片:{}", partakeLogId);
        ActivityPartakeLogDO partakeLog = activityPartakeLogDAO.getById(partakeLogId);
        if (partakeLog == null) {
            throw new ApplicationException("参与记录不存在");
        }
        PromotionActivityDetailDTO actDTO = JSON.parseObject(String.valueOf(redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO + partakeLog.getActivityId()).get()), PromotionActivityDetailDTO.class);
        if (Objects.isNull(actDTO)) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "活动已下架或不存在");
        } else if (actDTO.getStatus().equals(Integer.parseInt(ActivityStatusEnum.UN_START.getId()))) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "活动尚未开始，请耐心等待！");
        } else if (!actDTO.getStatus().equals(Integer.parseInt(ActivityStatusEnum.IN_PROGRESS.getId()))) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "来晚啦，活动已结束！");
        }
        RSet<Long> rSet = getUserResourceSet(partakeLog.getId(), actDTO.getEndTime());
        RMap<Long, ActivityFissionAssistResourceDO> rMap = getUserResourceMap(partakeLog.getId(), actDTO.getEndTime());
        List<ActivityFissionAssistResourceDO> cardList = new ArrayList<>();
        ResourceHisDetailResponseDTO prize = null;
        for (ResourceHisDetailResponseDTO item : actDTO.getPrizeList()) {
            if (Objects.equals(FissonResourceTypeEnum.POWER_LADDER.getId(), item.getFissonResourceType())) {
                if (Objects.equals(item.getLadderSort(), 3)) { // 0谢谢参与，1必中奖品，2抽奖奖品，3合成奖品
                    prize = item;
                }
            } else if (Objects.equals(FissonResourceTypeEnum.HELP_WIN_AWARDS.getId(), item.getFissonResourceType())) {
                ActivityFissionAssistResourceDO card = null;
                for (Map.Entry<Long, ActivityFissionAssistResourceDO> entry : rMap.entrySet()) {
                    // 校验用户是否拥有卡片，汇总用户红包金额
                    ActivityFissionAssistResourceDO r = entry.getValue();
                    if (Objects.equals(item.getId(), r.getResourceId()) && !Objects.equals(r.getReceived(), 2)) {
                        card = r;
                        break;
                    }
                }
                if (card == null) {
                    throw new ApplicationException("用户缺" + item.getName() + "卡");
                }
                card.setReceived(2);
                cardList.add(card);
            }
        }
        if (prize == null) {
            throw new ApplicationException("合成奖品未找到");
        }
        // 更新卡片状态
        if (CollectionUtil.isEmpty(cardList)) {
            throw new ApplicationException("卡片配置未找到");
        }
        for (ActivityFissionAssistResourceDO item : cardList) {
            rMap.put(item.getId(), item);
        }
        activityFissionAssistResourceDAO.updateBatchById(cardList);
        // 扣减库存
        boolean success = promotionActivityManager.decrRedisQtyCheckDay(prize.getId(), actDTO.getActivityId());
        if (success) {
            if (!Objects.equals(prize.getLimitType(), 1) || rSet.add(prize.getId())) {
                boolean decrDb = promotionActivityManager.decrRemainQty(prize.getId(), 1L);
                if (!decrDb) {
                    throw new ApplicationException("奖品库存不足");
                }
                // 不限制用户中奖次数或者用户没中过奖则中奖,扣减库存
                log.info("用户合成奖品:{}", JSONUtil.toJsonStr(prize));
            } else {
                // 用户限制不能中奖则恢复库存
                promotionActivityManager.incrRedisQty(prize.getId(), actDTO.getActivityId());
                throw new ApplicationException("用户已合成");
            }
        } else {
            // 库存不足
            throw new ApplicationException("奖品库存不足");
        }
        LotteryResourceDTO lotteryResourceDTO;
        partakeLog.setCode(generateIdUtil.getResourceCode());
        partakeLog.setResourceId(prize.getId());
        partakeLog.setGetTime(DateUtils.getCurrentTime());
        partakeLog.setFissonStatus(FissonStatusEnum.SUCCESS.getId());
        activityPartakeLogDAO.updateActivityPartakeLogById(partakeLog);
        ActivityFissionAssistResourceDO entity = BargainingConverter.activityFissionAssistResourceConverter(partakeLog, prize.clone(PromotionHisResourceDO.class));
        if (PromotionResourceTypeEnum.THIRD_PARTY.getId().equals(prize.getType())) {
            if (ThirdCategoryEnum.PHONE_FEE.getId().equals(prize.getThirdCategory())) {
                //话费充值
                incentiveService.rechargeMobileAsync(LuckyDrawConverter.rechargeMobileConverter(partakeLog, actDTO.getActivityName(), prize, entity.getCode()));
                entity.setReceived(1); // 话费红包自动领取发放
            } else if (ThirdCategoryEnum.CASH_BAG.getId().equals(prize.getThirdCategory())) {
                //发送红包激励
                incentiveService.sendIncentiveAsync(LuckyDrawConverter.sendIncentiveConverter(partakeLog, actDTO.getActivityName(), prize, entity.getCode(), SendTemplateNewsRequestDTO.MP_FROM_USH));
                entity.setReceived(1); // 话费红包自动领取发放
            }
        }
        //合成成功人数神策埋点
        asynchronousService.sensorsBuriedPointFission(partakeLog.getId(), SensorsEventEnum.CARD_COMPOSE_PEOPLE_NUMBER.getCode());
        // 新增中奖记录
        activityFissionAssistResourceDAO.save(entity);
        rMap.put(entity.getId(), entity);
        lotteryResourceDTO = prize.clone(LotteryResourceDTO.class);
        lotteryResourceDTO.setWinLotteryId(entity.getId());
        return lotteryResourceDTO;
    }

    @Override
    public Boolean receiveNow(ReceiveNowDTO dto) {
        if (dto.getResourceId() < 0) {
            throw new ApplicationException("未中奖");
        }
        // 校验卡片是否属于该用户
        PromotionActivityDetailDTO actDTO = JSON.parseObject(String.valueOf(redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO + dto.getActivityId()).get()), PromotionActivityDetailDTO.class);
        if (Objects.isNull(actDTO)) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "活动已下架或不存在");
        } else if (actDTO.getStatus().equals(Integer.parseInt(ActivityStatusEnum.UN_START.getId()))) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "活动尚未开始，请耐心等待！");
        } else if (!actDTO.getStatus().equals(Integer.parseInt(ActivityStatusEnum.IN_PROGRESS.getId()))) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "来晚啦，活动已结束！");
        }
        CustomerFeedbackQuery query = new CustomerFeedbackQuery();
        query.setActivityId(dto.getActivityId());
        //query.setUserId(dto.getUserId());
        query.setPhone(dto.getPhone());
//        query.setResourceId(dto.getId());
        List<ActivityFormFeedbackDO> formFeedbackList = customerFeedbackDAO.pageList(query).getContent();
        if (CollectionUtil.isEmpty(formFeedbackList)) {
            log.info("未填写信息登记，req={}", JsonUtil.bean2JsonString(query));
            throw new ApplicationException(CommonExceptionCode.NOT_ACTIVITY_FORM, "请填写信息登记");
        }
        //查询用户抽奖记录
        ActivityPartakeLogDTO partakeLogDTO = activityPartakeLogDAO.getList(dto.getActivityId(), dto.getUserId(), null, FissonTypeEnum.CARD_COLLECTING.getId());
        if (partakeLogDTO == null) {
            throw new ApplicationException("未找到用户信息");
        }
        RSet<Long> rSet = getUserResourceSet(partakeLogDTO.getId(), actDTO.getEndTime());
        RMap<Long, ActivityFissionAssistResourceDO> rMap = getUserResourceMap(partakeLogDTO.getId(), actDTO.getEndTime());
        ActivityFissionAssistResourceDO fissionAssistResourceDO = null;
        for (Map.Entry<Long, ActivityFissionAssistResourceDO> entry : rMap.entrySet()) {
            // 校验用户是否拥有卡片，汇总用户红包金额
            ActivityFissionAssistResourceDO item = entry.getValue();
            if (Objects.equals(item.getId(), dto.getWinLotteryId())) {
                if (Objects.equals(item.getReceived(), 0)) {
                    item.setReceived(1);
                    rMap.put(item.getId(), item);
                } else {
                    throw new ApplicationException("奖品已领取");
                }
                fissionAssistResourceDO = item;
            }
        }
        if (fissionAssistResourceDO == null) {
            throw new ApplicationException("中奖信息不存在");
        }
        activityFissionAssistResourceDAO.updateById(fissionAssistResourceDO);
        PromotionHisResourceDO hisResource = promotionHisResourceDAO.getById(fissionAssistResourceDO.getResourceId());
        //保存核销信息
        Map<String, Object> projectInfo = new HashMap<>();
        projectInfo.put("projectId", formFeedbackList.get(0).getProjectId());
        projectInfo.put("projectName", formFeedbackList.get(0).getProjectName());
        ActivityOrderDO activityOrderDO = new ActivityOrderDO();
        activityOrderDO.setType(dto.getType());
        activityOrderDO.setActivityId(dto.getActivityId());
        activityOrderDO.setUserId(dto.getUserId());
        activityOrderDO.setId(0L);
        activityOrderDO.setExt(projectInfo);
        activityOrderDO.setPayTime(DateUtils.now());
        ActivityFormFeedbackDTO formFeedback = formFeedbackList.get(0).clone(ActivityFormFeedbackDTO.class);
        ActivityVerifyDO activityVerifyDO = ActivityInfoConverter.verifyConverter(activityOrderDO, formFeedback, hisResource, fissionAssistResourceDO.getCode(), VerifyStatusEnum.NO_VERIFY.getId(), WhetherEnum.NO.getId());
        activityVerifyDAO.save(activityVerifyDO);

        //发送短信通知
        promotionActivityService.templateSend(formFeedbackList.get(0).getPhone(), formFeedbackList.get(0).getUserName(), hisResource.getName(), fissionAssistResourceDO.getCode(), huafaConstantConfig.WINNING_NOTICE_TEMPLATE_ID);

        //小程序订阅消息
        int[] timeByCalendar = DateUtils.getTimeByCalendar(fissionAssistResourceDO.getCreatedTime());
        Map<String, Object> templateParam = Maps.newHashMap();
        templateParam.put("thing1", hisResource.getName());
        templateParam.put("time2", " " + timeByCalendar[0] + "年" + timeByCalendar[1] + "月" + timeByCalendar[2] + "日 " + timeByCalendar[3] + ":" + timeByCalendar[4]);
        templateParam.put("thing3", "请到我的礼品栏目查看详情");
        promotionActivityService.minSubscribeNews(JSON.toJSONString(templateParam), formFeedbackList.get(0).getUserId(), formFeedbackList.get(0).getUserName(), huafaConstantConfig.MINI_WINNING_NOTICE_TEMPLATE_ID);

        return Boolean.TRUE;
    }

    @Override
    public List<ActivityFissionLogResponseDTO> fissionFriendsList(ActivityFissionLogQuery query) {
        QueryWrapper<ActivityFissionLogDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ActivityFissionLogDO::getPartakeLogId, query.getPartakeLogId())
                .orderByDesc(ActivityFissionLogDO::getCreatedTime).last("limit 100");
        List<ActivityFissionLogDO> logList = activityFissionLogDAO.list(wrapper);
        if (CollectionUtil.isEmpty(logList)) {
            return new ArrayList<>();
        }
        PromotionActivityDetailDTO actDTO = JSON.parseObject(String.valueOf(redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO + query.getActivityId()).get()), PromotionActivityDetailDTO.class);
        Map<Long, ResourceHisDetailResponseDTO> hisResourceMap = actDTO.getPrizeList().stream().collect(Collectors.toMap(ResourceHisDetailResponseDTO::getId, item -> item));

        List<ActivityFissionLogResponseDTO> responseList = new ArrayList<>();
        for (ActivityFissionLogDO log : logList) {
            ActivityFissionLogResponseDTO responseDTO = new ActivityFissionLogResponseDTO();
            BeanUtils.copyProperties(log, responseDTO);
            ResourceHisDetailResponseDTO aa = hisResourceMap.get(log.getAssistResourceId());
            if (aa != null) {
                responseDTO.setAssistResourceName(aa.getName());
            }
            responseList.add(responseDTO);
        }
        return responseList;
    }

    @Override
    public List<CardCollectingPrizeResponseDTO> myPrizeList(ActivityFissionLogQuery query) {
        PromotionActivityDetailDTO actDTO = JSON.parseObject(String.valueOf(redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO + query.getActivityId()).get()), PromotionActivityDetailDTO.class);
        Map<Long, ResourceHisDetailResponseDTO> hisResourceMap;
        if (Objects.isNull(actDTO)) {
            //奖品列表
            List<PromotionHisResourceDO> promotionHisResourceList = promotionHisResourceDAO.findByActivityId(query.getActivityId());
            hisResourceMap = promotionHisResourceList.stream()
                    .filter(item -> Objects.equals(item.getFissonResourceType(), FissonResourceTypeEnum.POWER_LADDER.getId()))
                    .collect(Collectors.toMap(PromotionHisResourceDO::getId, item -> item.clone(ResourceHisDetailResponseDTO.class)));
        } else {
            hisResourceMap = actDTO.getPrizeList().stream()
                    .filter(item -> Objects.equals(item.getFissonResourceType(), FissonResourceTypeEnum.POWER_LADDER.getId()))
                    .collect(Collectors.toMap(ResourceHisDetailResponseDTO::getId, item -> item));
        }
        RMap<Long, ActivityFissionAssistResourceDO> rMap = getUserResourceMap(query.getPartakeLogId(), actDTO == null ? null : actDTO.getEndTime());
        List<CardCollectingPrizeResponseDTO> prizeList = new ArrayList<>();
        rMap.readAllValues().forEach(item -> {
            ResourceHisDetailResponseDTO hisResource = hisResourceMap.get(item.getResourceId());
            if (hisResource != null) {
                CardCollectingPrizeResponseDTO prize = new CardCollectingPrizeResponseDTO();
                BeanUtils.copyProperties(hisResource, prize);
                prize.setHisResourceId(hisResource.getId());
                prize.setId(item.getId());
                prize.setReceived(item.getReceived());
                prizeList.add(prize);
            }
        });
        return prizeList;
    }

    @Override
    public List<CardGiveDTO> giveCard(ActivityPartakeRequest requestVo, List<PromotionActivityLimitDO> promotionActivityLimitList) {
        PromotionActivityDetailDTO actDTO = JSON.parseObject(String.valueOf(redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO + requestVo.getActivityId()).get()), PromotionActivityDetailDTO.class);
        //发起者，初次参加赠送卡片
        int size = promotionActivityLimitList
                .stream().filter(e -> PATemplateBaseEnum.CARD_COLLECTING.getId().equals(e.getType()))
                .flatMap(item -> JSON.parseArray(item.getLimits(), BaseActivityDTO.class).stream())
                .filter(number -> ActivityTemplateNumberEnum.INIT_CARD_COUNT.getId().equals(number.getId()))
                .map(e -> Integer.parseInt(e.getValue())).findFirst().orElse(0);
        if (size <= 0) {
            return new ArrayList<>();
        }
        ActivityPartakeLogDO activityPartakeLogDO = null;
        ActivityPartakeLogDTO activityPartakeLogDTO = activityPartakeLogDAO.getList(requestVo.getActivityId(), requestVo.getUserId(), null, FissonTypeEnum.CARD_COLLECTING.getId());
        if (activityPartakeLogDTO == null) {
            activityPartakeLogDO = BargainingConverter.partakeLogConverter(requestVo, promotionActivityLimitList, PATemplateBaseEnum.CARD_COLLECTING.getId(), generateIdUtil.getResourceCode());
            activityPartakeLogDO.setNeedFissonCount(size);
            activityPartakeLogDAO.save(activityPartakeLogDO);
        } else {
            throw new ApplicationException(CommonExceptionCode.PARTICIPATED, "用户已赠送卡片");
        }
        RSet<Long> rSet = getUserResourceSet(activityPartakeLogDO.getId(), actDTO.getEndTime());
        RMap<Long, ActivityFissionAssistResourceDO> rMap = getUserResourceMap(activityPartakeLogDO.getId(), actDTO.getEndTime());
        // 卡片奖品,第一张卡和最后一张卡不参与赠送
        List<ResourceHisDetailResponseDTO> fissionResourceList = actDTO.getPrizeList().stream()
                .filter(item -> Objects.equals(FissonResourceTypeEnum.HELP_WIN_AWARDS.getId(), item.getFissonResourceType())).collect(Collectors.toList());
        int cardSize = fissionResourceList.size();
        fissionResourceList = fissionResourceList.stream()
                .filter(item -> item.getLadderSort() != 1 && item.getLadderSort() != cardSize).collect(Collectors.toList());
        // 参加活动赠送卡片数量
        List<ActivityFissionAssistResourceDO> list = new ArrayList<>();
        Map<Long, ResourceHisDetailResponseDTO> map = new HashMap<>();
        while (list.size() < size) {
            ResourceHisDetailResponseDTO resourceDO;
            if (map.size() < 2) {
                resourceDO = fissionResourceList.get(RandomUtil.randomInt(fissionResourceList.size()));
                map.put(resourceDO.getId(), resourceDO);
            } else {
                // 最多赠送两种卡
                resourceDO = map.get(list.get(RandomUtil.randomInt(list.size())).getResourceId());
            }
            rSet.add(resourceDO.getId());
            if (rSet.size() > size) {
                throw new ApplicationException(CommonExceptionCode.PARTICIPATED, "用户已赠送卡片");
            }
            activityPartakeLogDO.setCode(generateIdUtil.getResourceCode());
            list.add(BargainingConverter.activityFissionAssistResourceConverter(activityPartakeLogDO, resourceDO.clone(PromotionHisResourceDO.class)));
        }
        activityFissionAssistResourceDAO.saveBatch(list);
        rMap.putAll(list.stream().collect(Collectors.toMap(ActivityFissionAssistResourceDO::getId, item -> item)));
        ActivityPartakeLogDO finalActivityPartakeLogDO = activityPartakeLogDO;
        return list.stream().map(item -> {
            ResourceHisDetailResponseDTO hisResourceDO = map.get(item.getResourceId());
            CardGiveDTO dto = new CardGiveDTO();
            dto.setPartakeLogId(finalActivityPartakeLogDO.getId());
            dto.setAssistResourceId(item.getId());
            dto.setResourceId(item.getResourceId());
            dto.setLadderSort(item.getLadderSort());
            dto.setCardName(hisResourceDO.getName());
            dto.setCardImg(hisResourceDO.getUrl());
            return dto;
        }).sorted(Comparator.comparing(CardGiveDTO::getLadderSort)).collect(Collectors.toList());
    }

    @Override
    public AssistResultDTO assist(ActivityPartakeRequest requestVo) {
        PromotionActivityDetailDTO actDTO = JSON.parseObject(String.valueOf(redissonClient.getBucket(RedisConstants.CACHE_PREV_KEY_ACT_INFO + requestVo.getActivityId()).get()), PromotionActivityDetailDTO.class);
        //参与者
        ActivityPartakeLogDO partakeLog = activityPartakeLogDAO.getById(requestVo.getSponsorId());
        if (partakeLog == null) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "助力信息不存在");
        } else if (partakeLog.getPhone().equals(requestVo.getPhone())) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "自己不能参与自己发起的助力活动");
        } else if (ObjectUtil.equal(partakeLog.getFissonStatus(), FissonStatusEnum.FAILURE.getId())) {
            throw new ApplicationException(CommonExceptionCode.INVALIDATION, "助力活动已结束");
        }
        AssistResultDTO resultDTO = new AssistResultDTO();
        resultDTO.setNickName(partakeLog.getNickName());
        resultDTO.setAvatar(partakeLog.getAvatar());
        resultDTO.setUnionId(partakeLog.getUnionId());
        // 校验助力
        ActivityFissionLogDO fissionLogDO = activityFissionLogDAO.getByPhone(partakeLog.getActivityId(), requestVo.getPhone());
        if (fissionLogDO != null) {
            if (Objects.equals(fissionLogDO.getPartakeLogId(), partakeLog.getId())) {
                // 重复助力
                resultDTO.setStatus(2);
                return resultDTO;
            } else {
                // 已经助力其他用户
                resultDTO.setStatus(3);
                return resultDTO;
            }
        }
        // 更新助力次数
        partakeLog.setCurrentFissonCount(partakeLog.getCurrentFissonCount() + 1);
        activityPartakeLogDAO.updateById(partakeLog);

        //助力活动成功的记录参与记录并更新参与人数
        // 赠送卡片
        List<ResourceHisDetailResponseDTO> fissionResourceList = actDTO.getPrizeList().stream()
                .filter(item -> Objects.equals(FissonResourceTypeEnum.HELP_WIN_AWARDS.getId(), item.getFissonResourceType())).collect(Collectors.toList());
        int cardSize = fissionResourceList.stream().max(Comparator.comparing(ResourceHisDetailResponseDTO::getLadderSort)).get().getLadderSort();
        Optional<ResourceHisDetailResponseDTO> promotionHisResourceDO;
        RSet<Long> rSet = getUserResourceSet(partakeLog.getId(), actDTO.getEndTime());
        RMap<Long, ActivityFissionAssistResourceDO> rMap = getUserResourceMap(partakeLog.getId(), actDTO.getEndTime());
        if ((partakeLog.getCurrentFissonCount()) % 10 == 0) {
            // 每第10个人倍数助力得第一张卡
            promotionHisResourceDO = fissionResourceList.stream().filter(item -> item.getLadderSort() == 1).findAny();
        } else {
            // 随机抽取第2345张卡
            fissionResourceList = fissionResourceList.stream().filter(item -> item.getLadderSort() != 1).collect(Collectors.toList());
            WeightRandom<ResourceHisDetailResponseDTO> weightRandom = WeightRandom.create();
            Long tempId = 0L;//稀有卡id，抽奖时先临时添加到用户中奖记录中，防止并发助力得两张稀有卡
            for (ResourceHisDetailResponseDTO hisResourceDO : fissionResourceList) {
                if (hisResourceDO.getLadderSort() == cardSize) {
                    // 最后一张卡(稀有卡)概率
                    RAtomicLong atomicLong = redissonClient.getAtomicLong(RedisConstants.CACHE_PREV_KEY_ACT_RESOURCE_INFO_COUNT + hisResourceDO.getId());
                    if (atomicLong.get() <= 0) {
                        // 库存为0则不发
                        continue;
                    } else {
                        // 用户已中则不发
                        if (!rSet.add(hisResourceDO.getId())) {
                            continue;
                        } else {
                            tempId = hisResourceDO.getId();
                        }
                    }
                }
                weightRandom.add(hisResourceDO, hisResourceDO.getOddsOfWinning());
            }
            promotionHisResourceDO = Optional.of(weightRandom.next());
            if (tempId != 0 && !Objects.equals(promotionHisResourceDO.get().getId(), tempId)) {
                rSet.remove(tempId);
            }
        }
        promotionHisResourceDO.ifPresent(hisResourceDO -> {
            partakeLog.setCode(generateIdUtil.getResourceCode());
            ActivityFissionAssistResourceDO entity = BargainingConverter.activityFissionAssistResourceConverter(partakeLog, hisResourceDO.clone(PromotionHisResourceDO.class));
            activityFissionAssistResourceDAO.save(entity);
            //好友参与记录
            ActivityFissionLogDO activityFissionLogDO = BargainingConverter.activityFissionLogConverter(requestVo, 0, partakeLog);
            activityFissionLogDO.setAssistResourceId(hisResourceDO.getId());
            activityFissionLogDAO.save(activityFissionLogDO);
            rSet.add(hisResourceDO.getId());
            rMap.put(entity.getId(), entity);
        });
        resultDTO.setStatus(1);
        return resultDTO;
    }

    private RSet<Long> getUserResourceSet(Long activityPartakeLogId, Date actEndTime) {
        String setKey = RedisConstants.CACHE_PREV_KEY_USER_RESOURCE_SET + activityPartakeLogId;
        String mapKey = RedisConstants.CACHE_PREV_KEY_USER_RESOURCE_MAP + activityPartakeLogId;
        initUserResource(setKey, mapKey, activityPartakeLogId, actEndTime);
        return redissonClient.getSet(setKey);
    }

    private RMap<Long, ActivityFissionAssistResourceDO> getUserResourceMap(Long activityPartakeLogId, Date actEndTime) {
        String setKey = RedisConstants.CACHE_PREV_KEY_USER_RESOURCE_SET + activityPartakeLogId;
        String mapKey = RedisConstants.CACHE_PREV_KEY_USER_RESOURCE_MAP + activityPartakeLogId;
        initUserResource(setKey, mapKey, activityPartakeLogId, actEndTime);
        return redissonClient.getMap(mapKey);
    }

    private boolean isCashBagPrize(ResourceHisDetailResponseDTO prize) {
        return Objects.equals(FissonResourceTypeEnum.POWER_LADDER.getId(), prize.getFissonResourceType())
                && PromotionResourceTypeEnum.THIRD_PARTY.getId().equals(prize.getType())
                && ThirdCategoryEnum.CASH_BAG.getId().equals(prize.getThirdCategory());
    }

    private RAtomicDouble getUserMoneyLimit(Long activityPartakeLogId, RMap<Long, ActivityFissionAssistResourceDO> rMap, List<ResourceHisDetailResponseDTO> prizeList) {
        String userKey = RedisConstants.CACHE_PREV_KEY_USER_MONEY_LIMIT + activityPartakeLogId;
        RAtomicDouble rAtomicDouble = redissonClient.getAtomicDouble(userKey);
        if (!rAtomicDouble.isExists()) {
            Map<Long, ResourceHisDetailResponseDTO> prizeMap = prizeList.stream().collect(Collectors.toMap(ResourceHisDetailResponseDTO::getId, item -> item));
            BigDecimal total = BigDecimal.ZERO;
            for (Map.Entry<Long, ActivityFissionAssistResourceDO> entry : rMap.entrySet()) {
                // 校验用户是否拥有卡片，汇总用户红包金额
                ActivityFissionAssistResourceDO item = entry.getValue();
                ResourceHisDetailResponseDTO prize = prizeMap.get(item.getResourceId());
                if (prize != null && isCashBagPrize(prize)) {
                    // 汇总红包
                    total = total.add(prize.getCouponValue());
                }
            }
            rAtomicDouble.set(total.doubleValue());
            rAtomicDouble.expire(7, TimeUnit.DAYS);
        }
        return rAtomicDouble;
    }

    private void initUserResource(String setKey, String mapKey, Long activityPartakeLogId, Date actEndTime) {
        RSet<Long> rSet = redissonClient.getSet(setKey);
        RMap<Long, ActivityFissionAssistResourceDO> rMap = redissonClient.getMap(mapKey);
        if (!rSet.isExists() || !rMap.isExists()) {
            // 初始化库存
            //确定缓存过期时间
            long expireTime;
            if (Objects.nonNull(actEndTime)) {
                expireTime = actEndTime.getTime() - System.currentTimeMillis();
                expireTime = Math.max(60, (expireTime / 1000) + RedisConstants.EXTRA_EXPIRE_SECOND);
            } else {
                expireTime = RedisConstants.SECKILL_EXPIRE_SECOND;
            }
            RLock lock = redissonClient.getLock(setKey + "lock");
            int retry = 0;
            int RETRY = 1;
            try {
                while (retry < RETRY) {
                    log.info("获取分布式锁重试次数:" + retry);
                    if (lock.tryLock(100, 10 * 1000, TimeUnit.MILLISECONDS)) {
                        if (rSet.isExists() && rMap.isExists()) {
                            return;
                        }
                        log.info("----------------------获取分布式锁------------------");

                        List<ActivityFissionAssistResourceDO> list = activityFissionAssistResourceDAO.list(activityPartakeLogId);
                        rSet.addAll(list.stream().map(ActivityFissionAssistResourceDO::getResourceId).collect(Collectors.toSet()));
                        rSet.expire(expireTime, TimeUnit.SECONDS);

                        rMap.putAll(list.stream().collect(Collectors.toMap(ActivityFissionAssistResourceDO::getId, item -> item)));
                        rMap.expire(expireTime, TimeUnit.SECONDS);

                        //手动释放锁
                        if (lock.isHeldByCurrentThread()) {
                            log.info("----------------------手动释放锁------------------");
                            lock.unlock();
                            break;
                        }

                    } else {
                        //获取锁失败,次数加1
                        retry++;
                    }
                }
            } catch (Exception e) {
                log.error("初始化用户卡片库存失败", e);
            } finally {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
            if (retry == RETRY) {
                throw new ApplicationException("访问人数过多，请稍后再试");
            }
        }
    }

}
