package com.deepexi.dxp.marketing.domain.marketing.dto;

import lombok.Data;

/**
 * @Class: XxlJobDTO
 * @Description:
 * @Author: zht
 * @Date: 2020/7/23
 */
@Data
public class FlowCanvasXxlJobDTO {
    /**
     * cron表达式
     */
    private String cron;

    /**
     * 多个corn表达式
     */
    private String cronExpression;

    /**
     * 画布id
     */
    private Long taskId;

    /**
     * 画布名称
     */
    private String name;

    /**
     * 执行任务
     */
    private String executeJobName;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 应用id
     */
    private Long appId;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 任务类型（目前只有画布 1）
     */
    private Integer type;
}
