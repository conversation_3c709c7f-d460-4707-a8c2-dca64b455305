//package com.deepexi.dxp.marketing.engine.task;
//
//import com.deepexi.dxp.middle.marketing.domain.dto.SmsSendDTO;
//import com.deepexi.dxp.middle.marketing.domain.dto.TaskSendDataDTO;
//import com.deepexi.dxp.marketing.service.marketing.SmsService;
//import com.deepexi.util.exception.ApplicationException;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
///**
// * 短信任务
// *
// * <AUTHOR>
// * @version 1.0
// * @date 2020-03-16 11:50
// */
//@Component
//@Slf4j
//public class SmsTask extends Task {
//
//    @Autowired
//    private SmsService smsService;
//
//    @Override
//    public boolean execute(TaskSendDataDTO dataDto) throws ApplicationException {
//        log.info("短信任务接收数据：{}", dataDto);
//        if (null == dataDto.getSendTemplateContent().getSmsData().getSmsSendDTO()) {
//            log.info("短信任务接收数据为空");
//            return false;
//        }
//        SmsSendDTO smsSendDTO = dataDto.getSendTemplateContent().getSmsData().getSmsSendDTO();
//        Boolean result;
//        try {
//            result = smsService.sendSMSMessage(smsSendDTO);
//        } catch (Exception e) {
//            result = false;
//        }
//        return result;
//    }
//}
