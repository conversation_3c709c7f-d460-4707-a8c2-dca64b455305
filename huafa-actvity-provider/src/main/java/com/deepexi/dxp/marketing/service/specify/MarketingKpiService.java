package com.deepexi.dxp.marketing.service.specify;

import com.deepexi.dxp.marketing.domain.marketing.query.specify.MarketingKpiItemGroupQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.MarketingKpiQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.KpiOperationDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.MarketingKpiExtDTO;
import com.deepexi.dxp.middle.marketing.domain.dto.MarketingKpiDTO;
import com.deepexi.util.pageHelper.PageBean;

import java.util.List;

/**
 * 主动营销kpi原生指标/派生指标Service
 *
 * @Author: HuangBo.
 * @Date: 2020/5/16 12:02
 */
public interface MarketingKpiService {

    Boolean save(MarketingKpiDTO dtoEntity);


    /**
     * 根据查询条件获取集合
     */
    List<MarketingKpiDTO> queryList(MarketingKpiQuery query,Boolean isFormula);

    /**
     * 分页查询列表
     */
    PageBean<MarketingKpiDTO> pageList(MarketingKpiQuery query);

    /**
     * 通过ID查询
     */
    MarketingKpiDTO queryById(MarketingKpiQuery query);

    /**
     * 查询指定列的所有数据
     */
    List<Object> queryByColumn(MarketingKpiQuery query);

    /**
     * 启用/禁用
     *
     * @return
     */
    Boolean processStatus(Long id, Integer status);



    /**
     * 通过任务名称判断是否存在任务
     */
    Boolean existByName(String tenantId, String name, String oldName);

    /**
     * 根据ID查询运算列表
     *
     * @param id KPI指标ID
     * @return List
     */
    List<KpiOperationDTO> queryOperationList(Long id, String tenantId);

    /**
     * 根据指标组ID获取原生/派生指标集合
     */
    List<MarketingKpiExtDTO> queryByKpiItemGroup(MarketingKpiItemGroupQuery query);

    Boolean deleteByKpi(Long id);
}
