package com.deepexi.dxp.marketing.domain.marketing.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 24好玩
 * <AUTHOR>
 * @version 1.0
 * @date 2020-05-29 11:13
 */
@Data
public class HaoWanGameDTO {
    /**
     * 游戏id
     */
    private String game_id;
    /**
     * 用户id
     */
    private String user_id;
    /**
     * 游戏地址
     */
    private String game_url;
    /**
     * 游戏名称
     */
    private String name;
    /**
     * 描述
     */
    private String describe;
    /**
     * 图片
     */
    private String game_img;
    /**
     * 开始时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date start_time;
    /**
     * 结束时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date end_time;
    /**
     * 更新时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date update_time;
    /**
     * 状态
     */
    private String status;
    /**
     * 浏览次数
     */
    private Integer view_total;
    /**
     * 玩游戏次数
     */
    private Integer play_total;
    /**
     * 参与用户
     */
    private Integer players;
    /**
     * 参与用户浏览
     */
    private Integer view_players;
    /**
     * 分享总数
     */
    private Integer share_total;
    /**
     * 行业标签
     */
    private List<String> industry_label;
    /**
     * 节日标签
     */
    private List<String> festival_label;
    /**
     * 玩法类型标签
     */
    private List<String> tpl_type;
    /**
     * 是否维护中
     */
    private Boolean pause;
    /**
     * 是否可用 true可用 false不可用
     */
    private Boolean available;
}
