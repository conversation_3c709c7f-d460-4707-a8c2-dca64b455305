package com.deepexi.dxp.marketing.service.specify;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.MiniOrgRequestDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.OneCodeAuthDTO;
import com.deepexi.dxp.middle.promotion.domain.entity.PromotionActivityDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityFormFeedbackDO;
import com.deepexi.dxp.middle.promotion.domain.entity.specify.ActivityGroupDO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 小程序
 */
public interface MiniProgramService {

    /**
     * 创建微信小程序码
     * @param name 名称为活动id_渠道名称
     * @param type 小程序码类型
     * @return
     */
    String  createWxCode(String name,Integer type,String path, Map<String, Object> params);

    /**
     * 获取场景码
     * @param createBy 当前系统登录人
     * @param name 场景名称 必填,不能重复
     * @param  orgType 机构类型，1全国，2区域，3城市，4项目，新增时必填
     * @return
     */
    Map<String, String> createOneCodeScene(String createBy, String name,Integer orgType, List<MiniOrgRequestDTO> orgList);

    /**
     * 获取场景码
     * @param  sceneCodeId  场景码id
     * @param  orgType 机构类型，1全国，2区域，3城市，4项目，新增时必填
     * @return
     */
    Map<String, String> updateOneCodeScene(Integer sceneCodeId, Integer orgType, List<MiniOrgRequestDTO> orgList);


    /**
     * 异步生成场景码
     * @param orgType
     * @param orgList
     * @param  promotionType 推广类型，0：活动推广；1：活动专题推广
     */
    void aSynCreateOneCodeScene(Object object,Integer orgType, List<MiniOrgRequestDTO> orgList,Integer promotionType);

    /**
     * 异步修改场景码
     * @param orgType
     * @param orgList
     * @param  promotionType 推广类型，0：活动推广；1：活动专题推广
     */
    void aSynUpdateOneCodeScene(Object object,Integer orgType, List<MiniOrgRequestDTO> orgList,Integer promotionType);


    /**
     * 创建一物一码
     *
     * @param name        名称
     * @param orgList     组织机构
     * @param typeCode    码类型code,码类型列表中的code字段
     * @param sceneCode   场景码
     * @param orgType     机构类型，1全国，2区域，3城市，4项目，新增时必填
     * @param type        类型
     * @param path        小程序页面路径
     * @param createdBy   创建用户
     * @param invalidTime
     * @return
     */
    JSONObject createOneCode(String name, List<MiniOrgRequestDTO> orgList, String typeCode
            , String sceneCode, Integer orgType, Integer type, String path, Map<String, Object> params
            , Integer paTemplateId, Integer appType, String remark, String createdBy, Date invalidTime);

    /**
     * 删除
     * @param codeId
     * @return
     */
    Boolean deleteByCodeId(Integer codeId);

    /**
     * 一物一码更新
     */
    JSONObject updateOneCode(String name, List<MiniOrgRequestDTO> orgList, String typeCode
            , String sceneCode, Integer orgType, Integer type, String path, Map<String, Object> params
            ,Integer paTemplateId,Integer appType,Integer codeId);

    /**
     * 用户信息登记推送
     * @param activityFormFeedbackDO
     * @param avatar
     * @param ip
     */
    void adoptUserLog(ActivityFormFeedbackDO activityFormFeedbackDO, String avatar, String ip);


    /**
     * 活动专题创建场景码
     * @param activityGroupDO
     * @param orgType
     * @return
     */
    String  activityGroupCreateOneCodeScene(ActivityGroupDO activityGroupDO, Integer orgType);

    /**
     * 活动专题更新场景码
     * @param activityGroupDO
     * @param orgType
     * @return
     */
    void  activityGroupUpdateOneCodeScene(ActivityGroupDO activityGroupDO, Integer orgType);


    /**
     * 活动创建场景码
     * @param promotionActivityDO
     * @param orgType
     * @param orgList
     * @return
     */
    String  activityCreateOneCodeScene(PromotionActivityDO promotionActivityDO, Integer orgType, List<MiniOrgRequestDTO> orgList);

    /**
     * 活动更新场景码
     * @param promotionActivityDO
     * @param orgType
     * @param orgList
     * @return
     */
    void  activityUpdateOneCodeScene(PromotionActivityDO promotionActivityDO, Integer orgType, List<MiniOrgRequestDTO> orgList);

    String getAccounts(String projectId);

    String getAreas();

    String page(Map<String,Object> params);
    Page<OneCodeAuthDTO> pageAuth(Integer codeId, Integer pageNo, Integer pageSize);
}
