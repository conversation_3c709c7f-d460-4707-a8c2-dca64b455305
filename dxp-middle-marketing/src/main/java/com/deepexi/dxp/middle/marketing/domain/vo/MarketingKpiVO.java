package com.deepexi.dxp.middle.marketing.domain.vo;

import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 原生指标和派生指标VO
 * @Author: HuangBo.
 * @Date: 2020/5/15 15:51
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MarketingKpiVO extends SuperVO {

    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "指标类型(1:原生指标; 2:派生指标)")
    private int type;

    @ApiModelProperty(value = "指标名称",required = true)
    @NotNull(message = "指标名称不能为空")
    private String name;

    @ApiModelProperty(value = "指标单位名称",required = true)
    @NotNull(message = "指标单位名称不能为空")
    private String unitName;

    @ApiModelProperty(value = "排序")
    private int sort;

    /**
     * 更新频率类型(1:实时，2:间隔)，只针对原生指标
     */
    @ApiModelProperty(value = "更新频率类型(1:实时，2:间隔)，只针对原生指标",required = true)
    @NotNull(message = "更新频率类型不能为空")
    private int frequencyType;

    @ApiModelProperty(value = "更新频率")
    private String frequency;

    @ApiModelProperty(value = "1:启用; 2:禁用",required = true)
    @NotNull(message = "状态不能为空")
    private int status;

    /**
     * 派生指标引用的指标以及连接计算时公式符号集合
     */
    @ApiModelProperty(value = "派生指标公式")
    private List<MarketingKpiFormulaVO> formulaList;

    /**
     * 是否已经被选作核心指标
     */
    @ApiModelProperty(value = "是否已经被选作核心指标")
    private boolean used;

}
