package com.deepexi.dxp.middle.marketing.dao.specify;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepexi.dxp.middle.marketing.domain.dto.MarketingKpiFormulaDTO;
import com.deepexi.dxp.middle.marketing.domain.entity.MarketingKpiFormulaDO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-03-09 17:42
 */
public interface MarketingKpiFormulaDAO extends IService<MarketingKpiFormulaDO> {

    /**
     * 根据派生指标ID查询列表
     */
    List<MarketingKpiFormulaDTO> queryList(Long kpiItemsId);
    /**
     * 根据派生指标ID查询列表
     */
    List<MarketingKpiFormulaDTO> queryListByKpiItemsIds(List<Long> kpiItemsIds);

    /**
     * 根据派生指标ID删除列表
     */
    Boolean deleteByKpiItems(Long kpiItemsId);

}
