package com.deepexi.dxp.middle.marketing.domain.dto.sdk;

import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Class: SdkPromotionCouponListPostResponseDTO
 * @Description:
 * @Author: zht
 * @Date: 2020/9/14
 */
@Data
public class SdkPromotionCouponListPostResponseDTO extends AbstractObject {
    private Long appId;

    private BigDecimal condition;

    private SdkPromotionCouponListPostResponseVOCouponLimitDTO couponLimit;

    private String couponName;

    private String couponType;

    private BigDecimal couponValue;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createdTime;

    private Integer deleted = 0;

    private Long id;

    private String limits;

    private String remark;

    private Integer status;

    private String tenantId;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updatedTime;

    private Long version;
}
