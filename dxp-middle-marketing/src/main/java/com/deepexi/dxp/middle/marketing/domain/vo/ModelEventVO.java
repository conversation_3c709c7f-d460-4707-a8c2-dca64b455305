package com.deepexi.dxp.middle.marketing.domain.vo;


import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
/**
 * @Class: ModelEventVO
 * @Description: 模型事件VO
 * @Author: zht
 * @Date: 2020/3/24
 */
@Data
@ApiModel
public class ModelEventVO extends AbstractObject {
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("事件类型 1：个体评估 2： 人群特征")
    private Integer eventType;

    @ApiModelProperty("事件编码")
    private String eventCode;

    @ApiModelProperty("事件名称")
    private String eventName;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("事件值类型")
    private String valueType;

    @ApiModelProperty("逻辑删除")
    private Integer deleted;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createdTime;

    @ApiModelProperty("定义")
    private String remark;

    @ApiModelProperty("租户id")
    private String tenantId;
}
