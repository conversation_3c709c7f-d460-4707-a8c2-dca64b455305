package com.deepexi.dxp.middle.marketing.domain.vo;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 营销任务触达明细数据分析对象
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-26 11:11
 */
@Data
public class MarketingTaskTouchDetailAnalyzeVO  extends AbstractObject {
    /**
     * 目标触达总人数
     */
    @ApiModelProperty("目标触达总人数")
    private Integer planTouchesQty;

    /**
     * 失败触达次数
     */
    @ApiModelProperty("失败触达次数")
    private Integer failureTouchQty;

    /**
     * 成功触达次数
     */
    @ApiModelProperty("成功触达次数")
    private Integer  successTouchQty;

    /**
     * 触达人数
     */
    @ApiModelProperty("触达人数")
    private Integer touchQty;

    /**
     * 当前转化率
     */
    @ApiModelProperty("当前转化率")
    private Double currentConversionRate;

}
