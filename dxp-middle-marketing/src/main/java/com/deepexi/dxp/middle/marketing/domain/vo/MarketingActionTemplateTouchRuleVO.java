package com.deepexi.dxp.middle.marketing.domain.vo;


import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * <AUTHOR>
 * @desc MarketingActionTemplateTouchRuleVO  行动模板触发规则
 * @Date: Tue Mar 10 11:57:01 CST 2020
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MarketingActionTemplateTouchRuleVO extends AbstractObject {

    /**  */
    @ApiModelProperty("")
    private Long id;
    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer sort;
    /**
     * 发送规则渠道: 1.发送微信  2.发送短信
     */
    @ApiModelProperty("发送规则渠道: 1.发送微信  2.发送短信")
    private Integer sendRuleChannel;
    /**
     * 发送规则类型
     */
    @ApiModelProperty("发送规则类型")
    private Integer sendRuleType;
    /**
     * 是否抽样测试  0：抽样测试 1：不抽样测试
     */
    @ApiModelProperty("是否抽样测试  0：抽样测试 1：不抽样测试")
    private Integer isSampleTest;
    /**
     * 抽样测试数量
     */
    @ApiModelProperty("抽样测试数量")
    private Integer sampleTestQty;
    /**
     * 行动模板id
     */
    @ApiModelProperty("行动模板id")
    private Long actionTemplateId;
    /**
     * 短信或微信模板id
     */
    @ApiModelProperty("短信或微信模板id")
    private Long sendTemplateId;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createdTime;
    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updatedBy;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updatedTime;
    /**
     * 逻辑删除
     */
    @ApiModelProperty("逻辑删除")
    private Integer deleted;
    /**
     * 版本号，乐观锁
     */
    @ApiModelProperty("版本号，乐观锁")
    private Long version;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
    /**  */
    @ApiModelProperty("")
    private String tenantId;
}

