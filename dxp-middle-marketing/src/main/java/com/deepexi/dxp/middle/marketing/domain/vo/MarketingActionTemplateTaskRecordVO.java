package com.deepexi.dxp.middle.marketing.domain.vo;


import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 行动模板引用记录
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-11 15:43
 */
@Data
public class MarketingActionTemplateTaskRecordVO extends SuperVO {
    /**
     * 任务主表ID
     */
    @ApiModelProperty("任务主表ID")
    private Long taskId;

    /**
     * 行动模板ID
     */
    @ApiModelProperty("行动模板ID")
    private Long templateId;

    /**
     * 营销任务名称
     */
    @ApiModelProperty("营销任务名称")
    private String taskName;

    /**
     * 任务类型 1:主动营销 2：自动营销
     */
    @ApiModelProperty("任务类型 1:主动营销 2：自动营销")
    private Integer taskType;

    /**
     * 营销资源
     */
    @ApiModelProperty("营销资源")
    private MarketingActionTemplateResourceVO resource;

    /**
     * 触达规则
     */
    @ApiModelProperty("触达规则")
    private List<MarketingActionTemplateTouchRuleVO> ruleList;
}
