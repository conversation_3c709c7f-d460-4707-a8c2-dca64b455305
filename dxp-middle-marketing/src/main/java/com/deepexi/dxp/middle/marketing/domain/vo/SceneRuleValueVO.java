package com.deepexi.dxp.middle.marketing.domain.vo;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Class: SceneRuleValueVO
 * @Description: 场景规则值VO
 * @Author: zht
 * @Date: 2020/3/24
 */
@Data
@ApiModel
public class SceneRuleValueVO extends AbstractObject {
    private Long id;
    /**
     * 场景id
     */
    @ApiModelProperty("场景id")
    private Long sceneId;

    /**
     * 1:做过 2:没做过
     */
    @ApiModelProperty("1:做过 2:没做过")
    private Integer doType;

    /**
     * 事件
     */
    @ApiModelProperty("事件")
    private String event;

    /**
     * 计算类型 1:单次 2:总次数
     */
    @ApiModelProperty("计算类型 1:单次 2:总次数")
    private Integer countType;

    /**
     * 操作类型0:= 1:> 2:>= 3:< 4:<= 5:区间
     */
    @ApiModelProperty("操作类型0:= 1:> 2:>= 3:< 4:<= 5:区间")
    private Integer operationType;

    /**
     * 区间左值
     */
    @ApiModelProperty("区间左值")
    private Integer firstValue;

    /**
     * 区间右值
     */
    @ApiModelProperty("区间右值")
    private Integer lastValue;
}
