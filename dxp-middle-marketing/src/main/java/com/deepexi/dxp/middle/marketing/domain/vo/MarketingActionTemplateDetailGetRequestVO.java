package com.deepexi.dxp.middle.marketing.domain.vo;


import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import com.deepexi.dxp.middle.marketing.domain.dto.MarketingActionTemplateResourceDTO;
import com.deepexi.dxp.middle.marketing.domain.dto.MarketingActionTemplateTouchRuleDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-09 18:45
 */
@Data
public class MarketingActionTemplateDetailGetRequestVO extends SuperVO {

    /**
     * 行动模板名称
     */
    @ApiModelProperty("行动模板名称")
    private String name;

    /**
     * 行动模板编码
     */
    @ApiModelProperty("行动模板编码")
    private String code;

    /**
     * 状态：0：启用 1：禁用
     */
    @ApiModelProperty("状态：0：启用 1：禁用")
    private Integer status;

    /**
     * 使用次数
     */
    @ApiModelProperty("使用次数")
    private Integer usedQty;

    /**
     * 行动模板营销资源
     */
    @ApiModelProperty("行动模板营销资源")
    private MarketingActionTemplateResourceDTO resource;

    /**
     * 行动模板触达规则列表
     */
    @ApiModelProperty("行动模板触达规则列表")
    private List<MarketingActionTemplateTouchRuleDTO> ruleList;

    /**
     * 发送模板信息
     */
    private List<SendTemplate> sendTemplateList;

    @Data
    public static class SendTemplate{
        /**
         * 发送模板id
         */
        private Long sendTemplateId;

        /**
         * 发送规则渠道
         */
        private Integer sendRuleChannel;

        /**
         * 发送模板名称
         */
        private String sendTemplateName;
    }
}
