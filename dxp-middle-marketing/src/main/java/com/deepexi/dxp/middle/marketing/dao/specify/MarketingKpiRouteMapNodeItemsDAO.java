package com.deepexi.dxp.middle.marketing.dao.specify;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepexi.dxp.middle.marketing.domain.dto.MarketingKpiRouteMapNodeItemsDTO;
import com.deepexi.dxp.middle.marketing.domain.entity.MarketingKpiRouteMapNodeItemsDO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-03-09 17:49
 */

public interface MarketingKpiRouteMapNodeItemsDAO extends IService<MarketingKpiRouteMapNodeItemsDO> {

    /**
     * 根据路径图节点查询绑定的指标信息
     */
    List<MarketingKpiRouteMapNodeItemsDTO> queryByRouteMapNode(Long routeMapNodeId);

    /**
     * 根据路径图查询绑定的指标信息
     */
    List<MarketingKpiRouteMapNodeItemsDTO> queryByRouteMap(Long routeMapId);

    /**
     * 根据路径图删除
     * @param routeMapId
     * @return
     */
    Boolean deleteByRouteMap(Long routeMapId);

    MarketingKpiRouteMapNodeItemsDTO findByKpiId(Long id);
}
