package com.deepexi.dxp.middle.marketing.domain.dto.sdk;

import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020年07月28日 10:55
 */
@Data
public class SdkSmsSendDTO extends AbstractObject {

    
    private Long id = null;
    
    private String appId = null;
    
    private String caseId = null;
    
    private String tenantId = null;
    
    private Long version = null;
    
    private Integer deleted = null;
    
    private String createdBy = null;
    
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createdTime = null;
    
    private String updatedBy = null;
    
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updatedTime = null;
    
    private Long eventId = null;
    
    private String eventCode = null;
    
    private String requestNo = null;
    
    private Long templateId = null;
    
    private Integer serviceProvider = null;
    
    private Integer category = null;
    
    private Integer international = null;
    
    private Integer smsType = null;
    
    private String param = null;
    
    private String templateContent = null;
    
    private String remark = null;
    
    private Integer pushType = null;
    
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date pushTime = null;
    
    private String ext = null;
    
    private List<String> receiverPhones = null;
    
    private List<String> serviceRequestIds = null;
    
    private List<String> serviceReceiptIds = null;
    
    private String templateCode = null;
    
    private Integer commitCount = null;
    
    private Integer sendCount = null;
    
    private String backUrl = null;
    
    private String outId = null;
    
    private String pushCode = null;
    
    private Integer status = null;
}
