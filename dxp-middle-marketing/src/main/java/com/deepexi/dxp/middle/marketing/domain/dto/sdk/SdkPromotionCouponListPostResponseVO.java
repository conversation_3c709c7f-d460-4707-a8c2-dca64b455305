package com.deepexi.dxp.middle.marketing.domain.dto.sdk;

import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * PromotionCouponListPostResponseVO
 */
@Data
public class SdkPromotionCouponListPostResponseVO extends AbstractObject {
  private Long appId = null;

  private BigDecimal condition = null;

  private SdkPromotionCouponListPostResponseVOCouponLimitDTO couponLimit = null;

  private String couponName = null;

  private String couponType = null;

  private BigDecimal couponValue = null;

  @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createdTime = null;

  private Integer deleted = null;

  private Long id = null;

  private String limits = null;

  private String remark = null;

  private Integer status = null;

  private String tenantId = null;

  @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updatedTime = null;

  private Long version = null;
}

