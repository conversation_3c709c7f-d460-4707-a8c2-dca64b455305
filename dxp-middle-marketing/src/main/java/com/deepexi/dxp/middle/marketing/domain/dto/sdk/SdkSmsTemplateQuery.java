package com.deepexi.dxp.middle.marketing.domain.dto.sdk;

import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020年07月28日 11:28
 */
@Data
public class SdkSmsTemplateQuery extends AbstractObject {

    
    private Long id = null;
    
    private String tenantId = null;
    
    private String appId = null;
    
    private String caseId = null;
    
    private String createdBy = null;
    
    private String updatedBy = null;
    
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updatedTimeFrom = null;
    
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updatedTimeTo = null;
    
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createdTimeFrom = null;
    
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createdTimeTo = null;
    
    private Integer page = null;
    
    private Integer size = null;
    
    private Integer international = null;
    
    private Integer serviceProvider = null;
    
    private Integer smsType = null;
    
    private Integer type = null;
    
    private String title = null;
    
    private String code = null;
    
    private Integer status = null;
    
    private String smsKey = null;
    
    private List<String> codeList = null;
    
    private List<String> contentList = null;
}
