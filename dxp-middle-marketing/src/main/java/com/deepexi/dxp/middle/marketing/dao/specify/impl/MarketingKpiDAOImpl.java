package com.deepexi.dxp.middle.marketing.dao.specify.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.MarketingKpiQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.KpiOperationDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.MarketingKpiExtDTO;
import com.deepexi.dxp.marketing.enums.analysis.PurposeTypeEnum;
import com.deepexi.dxp.middle.marketing.dao.specify.MarketingKpiDAO;
import com.deepexi.dxp.middle.marketing.domain.dto.MarketingKpiDTO;
import com.deepexi.dxp.middle.marketing.domain.entity.MarketingKpiDO;
import com.deepexi.dxp.middle.marketing.mapper.specify.MarketingKpiMapper;
import com.deepexi.util.StringUtil;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import com.deepexi.util.pojo.CloneDirection;
import com.deepexi.util.pojo.ObjectCloneUtils;
import com.google.common.base.CaseFormat;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-03-09 17:41
 */
@Repository
public class MarketingKpiDAOImpl extends ServiceImpl<MarketingKpiMapper, MarketingKpiDO> implements MarketingKpiDAO {

    @Override
    public MarketingKpiDTO queryById(Long id) {
        MarketingKpiDO result = this.getById(id);
        return Objects.nonNull(result) ? result.clone(MarketingKpiDTO.class) : null;
    }

    @Override
    public Boolean save(MarketingKpiDTO dtoEntity) {
        MarketingKpiDO kpiDO = dtoEntity.clone(MarketingKpiDO.class);
        boolean r = this.saveOrUpdate(kpiDO);
        dtoEntity.setId(kpiDO.getId());
        return r;
    }


    @Override
    public Boolean saveOriginalKpiItems(MarketingKpiDTO kpiItemsDTO){
        boolean res = false;
        if(Objects.isNull(kpiItemsDTO.getId())){
            MarketingKpiDO kpiDO = kpiItemsDTO.clone(MarketingKpiDO.class);
            res = this.save(kpiDO);
            kpiItemsDTO.setId(kpiDO.getId());
        }else{
            this.baseMapper.updateRemark(kpiItemsDTO.clone(MarketingKpiDO.class));
        }
        return res;
    }

    /**
     * 根据条件查询列表
     */
    @Override
    public List<MarketingKpiDTO> queryList(MarketingKpiQuery query){
        return ObjectCloneUtils.convertList(list(getWrapper(query)), MarketingKpiDTO.class);
    }

    private QueryWrapper<MarketingKpiDO> getWrapper(MarketingKpiQuery query){
        QueryWrapper<MarketingKpiDO> queryWrapper = new QueryWrapper();

        if(Objects.nonNull(query.getAppId())){
            queryWrapper.eq("app_id", query.getAppId());
        }

        if(Objects.nonNull(query.getType())) {
            queryWrapper.eq("type", query.getType());
        }

        // 根据名称模糊查询
        if(StringUtil.isNotBlank(query.getName())){
            queryWrapper.like("name", query.getName());
        }
        // 根据指标定义模糊查询
        if(StringUtil.isNotBlank(query.getRemark())){
            queryWrapper.like("remark", query.getRemark());
        }
        // 根据指标单位精确查询
        if(StringUtil.isNotBlank(query.getUnitName())){
            queryWrapper.like("unit_name", query.getUnitName());
        }
        // 根据指标状态精确查询
        if(Objects.nonNull(query.getStatus())){
            queryWrapper.eq("status", query.getStatus());
        }
        // 当本次查询为原生指标时，根据更新频率模糊查询
        if(MarketingKpiQuery.ORIGINAL_TYPE.equals(query.getType())
                && StringUtil.isNotBlank(query.getFrequency())){
            queryWrapper.like("frequency", query.getFrequency());
        }
        queryWrapper.orderByAsc("sort");
        queryWrapper.orderByDesc("updated_time");
        return queryWrapper;
    }

    /**
     * 获取分页集合列表
     */
    @Override
    public PageBean<MarketingKpiDTO> pageList(MarketingKpiQuery query) {
        IPage<MarketingKpiDO> page = this.page(new Page<>(query.getPage(), query.getSize()), getWrapper(query));
        PageBean<MarketingKpiDO> pageBean = new PageBean<>(page);
        return ObjectCloneUtils.convertPageBean(pageBean, MarketingKpiDTO.class, CloneDirection.OPPOSITE);
    }

    @Override
    public List<Object> queryByColumn(MarketingKpiQuery query) {
        QueryWrapper<MarketingKpiDO> queryWrapper = new QueryWrapper();
        queryWrapper.eq("type", query.getType());
        queryWrapper.select(CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, query.getQueryFieldName()));
        return this.listObjs(queryWrapper);
    }

    /**
     * 启用/禁用
     * @return
     */
    @Override
    public Boolean processStatus(Long id, Integer status) {
        MarketingKpiDO kpiItemsDO = getById(id);
        if(Objects.isNull(kpiItemsDO)){
            throw new ApplicationException("指标不存在.");
        }
        if(MarketingKpiDO.DR_DELETE.equals(kpiItemsDO.getDeleted())){
            throw new ApplicationException("指标已被删除.");
        }
        if(kpiItemsDO.getStatus() == status){
            return true;
        }
        kpiItemsDO.setStatus(status);
        return this.updateById(kpiItemsDO);
    }

    @Override
    public PageBean<MarketingKpiDTO> queryAsCoreKpiItems(MarketingKpiQuery query) {
        if(Objects.isNull(PurposeTypeEnum.getEnumByState(query.getPurposeType()))){
            throw new ApplicationException("活动目标类型不合法.");
        }
        query.setTenantId(query.getTenantId());
        Page page = page(new Page<>(query.getPage(), query.getSize()), getWrapper(query));
        PageBean<MarketingKpiDTO> pageBean = new PageBean<MarketingKpiDTO>(page);
        return pageBean;
    }

    @Override
    public List<MarketingKpiDTO> queryCoreKpiItems(Integer type, Integer status, String tenantId) {
        if(Objects.nonNull(type) && Objects.isNull(PurposeTypeEnum.getEnumByState(type))){
            throw new ApplicationException("活动目标类型不合法.");
        }
        return ObjectCloneUtils.convertList(this.baseMapper.queryCoreKpiItems(type, status,tenantId), MarketingKpiDTO.class);
    }

    @Override
    public List<MarketingKpiExtDTO> queryByKpiItemGroup(Long groupId, Integer status, String tenantId) {
        return this.baseMapper.queryByKpiItemGroup(groupId, status,tenantId);
    }

    @Override
    public List<KpiOperationDTO> queryOperationList(Long id, String tenantId) {
        if (id == null) {
            return null;
        }
        return this.baseMapper.queryOperationList(id, tenantId);
    }

    @Override
    public List<MarketingKpiDTO> queryByName(String tenantId, String name) {
        QueryWrapper<MarketingKpiDO> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("tenant_id" , tenantId);
        queryWrapper.eq("name", StringUtils.trimWhitespace(name));
        return ObjectCloneUtils.convertList(list(queryWrapper), MarketingKpiDTO.class);
    }

    @Override
    public Boolean deleteByKpi(Long id) {
        MarketingKpiDO marketingKpiDO = this.baseMapper.selectById(id);
        if (Objects.isNull(marketingKpiDO)) {
            throw new ApplicationException("派生指标id不存在");
        } else {
            int i = this.baseMapper.deleteById(id);
            return i > 0;
        }
    }


}
