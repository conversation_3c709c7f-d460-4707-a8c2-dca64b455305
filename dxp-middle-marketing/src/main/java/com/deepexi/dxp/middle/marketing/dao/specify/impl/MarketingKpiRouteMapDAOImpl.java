package com.deepexi.dxp.middle.marketing.dao.specify.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.MarketingKpiRouteMapQuery;
import com.deepexi.dxp.middle.marketing.dao.specify.MarketingKpiRouteMapDAO;
import com.deepexi.dxp.middle.marketing.domain.dto.MarketingKpiRouteMapDTO;
import com.deepexi.dxp.middle.marketing.domain.entity.MarketingKpiRouteMapDO;
import com.deepexi.dxp.middle.marketing.mapper.specify.MarketingKpiRouteMapMapper;
import com.deepexi.util.StringUtil;
import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.pageHelper.PageBean;
import com.deepexi.util.pojo.CloneDirection;
import com.deepexi.util.pojo.ObjectCloneUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-03-09 17:47
 */
@Repository
public class MarketingKpiRouteMapDAOImpl extends ServiceImpl<MarketingKpiRouteMapMapper, MarketingKpiRouteMapDO> implements MarketingKpiRouteMapDAO {

    @Override
    public MarketingKpiRouteMapDTO queryById(Long id) {
        MarketingKpiRouteMapDO marketingKpiRouteMapDO = getById(id);
        if(Objects.nonNull(marketingKpiRouteMapDO)){
            MarketingKpiRouteMapDTO routeMapDTO = marketingKpiRouteMapDO.clone(MarketingKpiRouteMapDTO.class);
            check(routeMapDTO);
            return routeMapDTO;
        }
        return null;
    }

    @Override
    public Boolean save(MarketingKpiRouteMapDTO dtoEntity) {
        MarketingKpiRouteMapDO routeMapDO = dtoEntity.clone(MarketingKpiRouteMapDO.class);
        if (saveOrUpdate(routeMapDO)) {
            dtoEntity.setId(routeMapDO.getId());
            return true;
        }
        return false;
    }

    @Override
    public List<MarketingKpiRouteMapDTO> queryList(MarketingKpiRouteMapQuery query) {
        query.setTenantId(query.getTenantId());
        return ObjectCloneUtils.convertList(list(getWrapper(query)), MarketingKpiRouteMapDTO.class);
    }

    private QueryWrapper<MarketingKpiRouteMapDO> getWrapper(MarketingKpiRouteMapQuery query) {
        QueryWrapper<MarketingKpiRouteMapDO> queryWrapper = new QueryWrapper();
//        queryWrapper.eq("tenant_id", query.getTenantId());

        // 根据名称模糊查询
        if (StringUtil.isNotBlank(query.getName())) {
            queryWrapper.like("name", query.getName());
        }

        // 根据指标定义模糊查询
        if (StringUtil.isNotBlank(query.getRemark())) {
            queryWrapper.like("remark", query.getRemark());
        }
        // 根据路径图状态精确查询
        if (Objects.nonNull(query.getStatus())) {
            queryWrapper.eq("status", query.getStatus());
        }
        // 应测试要求将列表排序由更新时间倒序改为创建时间倒序，禅道bug id：22997
        queryWrapper.orderByDesc("created_time");
        return queryWrapper;
    }

    @Override
    public PageBean<MarketingKpiRouteMapDTO> pageList(MarketingKpiRouteMapQuery query) {
        IPage<MarketingKpiRouteMapDO> page = page(new Page<>(query.getPage(), query.getSize()), getWrapper(query));
        PageBean<MarketingKpiRouteMapDO> pageBean = new PageBean<>(page);
        return ObjectCloneUtils.convertPageBean(pageBean, MarketingKpiRouteMapDTO.class, CloneDirection.OPPOSITE);
    }

    @Override
    public Boolean processStatus(Long id, Integer status) {
        MarketingKpiRouteMapDO routeMapDO = getById(id);
        check(routeMapDO.clone(MarketingKpiRouteMapDTO.class));
        if (routeMapDO.getStatus() == status) {
            return true;
        }
        routeMapDO.setStatus(status);
        return this.updateById(routeMapDO);
    }

    @Override
    public List<MarketingKpiRouteMapDTO> queryByName(String tenantId, String name) {
        QueryWrapper<MarketingKpiRouteMapDO> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("tenant_id", tenantId);
        queryWrapper.eq("name", StringUtils.trimWhitespace(name));
        return ObjectCloneUtils.convertList(list(queryWrapper), MarketingKpiRouteMapDTO.class);
    }

    @Override
    public Boolean deleteByRouteId(Long id) {
        Integer result = this.baseMapper.deleteById(id);
        return result > 0;
    }

    protected void check(MarketingKpiRouteMapDTO dtoEntity) {
        if (Objects.isNull(dtoEntity)) {
            throw new ApplicationException("数据不存在.");
        }
        if (MarketingKpiRouteMapDO.DR_DELETE.equals(dtoEntity.getDeleted())) {
            throw new ApplicationException("数据已被删除.");
        }
    }
}
