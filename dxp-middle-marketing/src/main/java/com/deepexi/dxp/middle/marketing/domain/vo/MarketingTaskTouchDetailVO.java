package com.deepexi.dxp.middle.marketing.domain.vo;


import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 触达详情
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-18 17:16
 */
@Data
public class MarketingTaskTouchDetailVO extends SuperVO {
    /**
     * 任务id
     */
    @ApiModelProperty("任务id")
    private Long taskId;

    /**
     * 任务映射id
     */
    @ApiModelProperty("任务映射id")
    private Long mappingId;

    /**
     * 任务类型 1：主动营销 2：自动营销
     */
    @ApiModelProperty("任务类型 1：主动营销 2：自动营销")
    private Integer taskType;

    /**
     * 会员id
     */
    @ApiModelProperty("会员id")
    private Long memberId;

    /**
     * 会员手机号
     */
    @ApiModelProperty("会员手机号")
    private String memberPhone;

    /**
     * 会员姓名
     */
    @ApiModelProperty("会员姓名")
    private String memberName;

    /**
     * 触达类型 ： 1：触达成功 2：触达失败
     */
    @ApiModelProperty("触达类型 ： 1：触达成功 2：触达失败")
    private Integer status;

    /**
     * 发送规则渠道 1.微信 2.短信
     */
    @ApiModelProperty("发送规则渠道")
    private Integer sendRuleChannel;

    /**
     * 发送规则类型
     */
    @ApiModelProperty("发送规则类型")
    private Integer sendRuleType;

    /**
     * 资源类别编码
     */
    @ApiModelProperty("资源类别编码")
    private String resourceCategoryCode;

    /**
     * 转化时间
     */
    @ApiModelProperty("转化时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date conversionTime;

    /**
     * 领取时间
     */
    @ApiModelProperty("领取时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date getTime;

    /**
     * 浏览时间
     */
    @ApiModelProperty("浏览时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date browsingTime;

    /**
     * 首次浏览时间
     */
    @ApiModelProperty("首次浏览时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date firstBrowsingTime;

    /**
     * 资源状态 1.未领取 2:已领取 3.已核销"
     */
    @ApiModelProperty("资源状态 1.未领取 2:已领取 3.已核销")
    private Integer resourceStatus;

    /**
     * 消息模板id
     */
    @ApiModelProperty("消息模板id")
    private Long sendTemplateId;

    /**
     * 资源id
     */
    @ApiModelProperty("资源id")
    private Long resourceId;

    /**
     * 资源渠道
     */
    @ApiModelProperty("资源渠道")
    private String resourceChannel;

    /**
     * 资源名称
     */
    @ApiModelProperty("资源名称")
    private String resourceName;

    /**
     * 记录tasktype_taskid_memberid_sendRuleChannel
     */
    @ApiModelProperty("记录tasktype_taskid_memberid_sendRuleChannel")
    private String record;
}
