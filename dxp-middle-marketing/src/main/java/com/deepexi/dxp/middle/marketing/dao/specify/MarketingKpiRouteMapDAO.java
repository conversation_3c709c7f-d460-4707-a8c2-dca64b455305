package com.deepexi.dxp.middle.marketing.dao.specify;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.MarketingKpiRouteMapQuery;
import com.deepexi.dxp.middle.marketing.domain.dto.MarketingKpiRouteMapDTO;
import com.deepexi.dxp.middle.marketing.domain.entity.MarketingKpiRouteMapDO;
import com.deepexi.util.pageHelper.PageBean;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-03-09 17:47
 */
public interface MarketingKpiRouteMapDAO extends IService<MarketingKpiRouteMapDO> {

    /**
     * 根据ID查询
     */
    MarketingKpiRouteMapDTO queryById(Long id);


    /**
     * 保存
     */
    Boolean save(MarketingKpiRouteMapDTO dtoEntity);


    /**
     * 根据条件查询列表
     */
    List<MarketingKpiRouteMapDTO> queryList(MarketingKpiRouteMapQuery query);

    /**
     * 获取分页集合列表
     */
    PageBean<MarketingKpiRouteMapDTO> pageList(MarketingKpiRouteMapQuery query);

    /**
     * 启用/禁用
     */
    Boolean processStatus(Long id, Integer status);


    /**
     * 通过任务名称获取任务
     */
    List<MarketingKpiRouteMapDTO> queryByName(String tenantId, String name);

    Boolean deleteByRouteId(Long id);
}
