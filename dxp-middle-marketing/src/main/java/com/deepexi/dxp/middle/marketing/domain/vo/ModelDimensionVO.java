package com.deepexi.dxp.middle.marketing.domain.vo;

import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
/**
 * @Class: ModelDimensionVO
 * @Description: 模型维度VO
 * @Author: zht
 * @Date: 2020/3/24
 */
@ApiModel
@Data
public class ModelDimensionVO extends SuperVO {

    /**
     * 模型id
     */
    @ApiModelProperty("模型id")
    private Long modelId;

    /**
     * 维度名称
     */
    @ApiModelProperty("维度名称")
    private String dimensionName;

    /**
     * 维度类型 1：行为评估 2：结果评估
     */
    @ApiModelProperty("维度类型 1：行为评估 2：结果评估")
    private Integer type;

    /**
     * 权重占比
     */
    @ApiModelProperty("权重占比")
    private Integer weightRatio;

    /**
     * 执行周期
     */
    @ApiModelProperty("执行周期")
    private Integer evaluationCycle;

    /**
     * 执行周期单位 1：月份 2：年
     */
    @ApiModelProperty("执行周期单位 1：月份 2：年")
    private Integer unit;

    /**
     * 下次执行时间
     */
    @ApiModelProperty("下次执行时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date nextTime;

    /**
     * 模型规则列表
     */
    @ApiModelProperty("模型规则列表")
    private List<ModelRuleVO> modelRules;
}
