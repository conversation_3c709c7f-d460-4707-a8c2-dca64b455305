package com.deepexi.dxp.middle.marketing.domain.dto.sdk;

import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * PromotionActivityListPostResponseVO
 */
@Data
public class SdkPromotionActivityListPostResponseVO extends AbstractObject {
  private Long activityId = null;

  private String activityName = null;

  private List<SdkPromotionActivityListPostResponseVOActivityRuleDTO> activityRuleDTOList = null;

  private Long appId = null;

  private String code = null;

  private SdkPromotionActivityListPostResponseVOComdityLimitDTO commodity = null;

  private List<SdkPromotionActivityListPostResponseVOBaseActivityDTO> couponLimit = null;

  private String description = null;

  @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date endTime = null;

  private List<SdkPromotionActivityListPostResponseVOBaseActivityDTO> numberLimit = null;

  private Integer paTemplateId = null;

  private List<SdkPromotionActivityListPostResponseVOBaseActivityDTO> shopLimit = null;

  @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date startTime = null;

  private Integer status = null;

  private String tenantId = null;

  private List<SdkPromotionActivityListPostResponseVOBaseActivityDTO> tenantLimit = null;

  private List<SdkPromotionActivityListPostResponseVOBaseActivityDTO> userLimit = null;
}

