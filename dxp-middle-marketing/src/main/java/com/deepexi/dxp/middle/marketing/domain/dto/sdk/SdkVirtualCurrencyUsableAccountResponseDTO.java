package com.deepexi.dxp.middle.marketing.domain.dto.sdk;

import lombok.Data;

import java.math.BigDecimal;

/**
 *
 SdkVirtualCurrencyUsableAccountResponseDTO
 */
@Data
public class SdkVirtualCurrencyUsableAccountResponseDTO {
    private String accountCode;
    private Long appId;
    private BigDecimal balanceAmt;
    private String tenantId;
    private Long userId;
    private Integer userType;
    private Long virtualCurrencyId;
    private String virtualCurrencyName;
}
