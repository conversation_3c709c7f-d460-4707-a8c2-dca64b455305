package com.deepexi.dxp.middle.marketing.domain.vo;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Class: ModelQxConditionVO
 * @Description:
 * @Author: zht
 * @Date: 2020/7/28
 */
@Data
@ApiModel
public class ModelQxConditionVO extends AbstractObject {

    @ApiModelProperty("模型id")
    private Long id;

    @ApiModelProperty("筛选方式  按排名 1/按分值 2")
    private Integer filterType;

    @ApiModelProperty("大于：>,小于：<,等于：=,排名前：1,排名后：2")
    private String symbol;

    @ApiModelProperty("比较值")
    private Integer value;
}
