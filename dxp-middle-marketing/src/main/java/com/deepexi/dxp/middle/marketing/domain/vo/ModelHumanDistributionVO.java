package com.deepexi.dxp.middle.marketing.domain.vo;

import com.deepexi.dxp.middle.marketing.domain.dto.ModelHumanAndScoreDTO;
import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Class: ModelHumanDistributionVO
 * @Description: 模型人群分布
 * @Author: zht
 * @Date: 2020/3/24
 */
@Data
@ApiModel
public class ModelHumanDistributionVO  extends AbstractObject {

    @ApiModelProperty("最多人数的分值")
    private ModelHumanAndScoreDTO maxHuman;

    @ApiModelProperty("最少人数的分值")
    private ModelHumanAndScoreDTO minHuman;

    @ApiModelProperty("最大分数的人数")
    private ModelHumanAndScoreDTO maxScore;

    @ApiModelProperty("最小分数的人数")
    private ModelHumanAndScoreDTO minScore;

    @ApiModelProperty("平均分")
    private ModelHumanAndScoreDTO avgScore;

    @ApiModelProperty("分数人数分布图")
    private List<ModelHumanAndScoreDTO> humanList;

    public ModelHumanDistributionVO(){

    }

    public ModelHumanDistributionVO(ModelHumanAndScoreDTO maxHuman, ModelHumanAndScoreDTO minHuman, ModelHumanAndScoreDTO maxScore, ModelHumanAndScoreDTO minScore, ModelHumanAndScoreDTO avgScore, List<ModelHumanAndScoreDTO> humanList){
        this.maxHuman = maxHuman;
        this.minHuman = minHuman;
        this.maxScore = maxScore;
        this.minScore = minScore;
        this.avgScore = avgScore;
        this.humanList = humanList;
    }
}
