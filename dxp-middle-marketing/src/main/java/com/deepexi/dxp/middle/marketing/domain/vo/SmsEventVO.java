package com.deepexi.dxp.middle.marketing.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Class: SMSEventVO
 * @Description: 短信事件 VO
 * @Author: mou
 * @Date: 2020/3/25
 */
@Data
@ApiModel
public class SmsEventVO {

    /**
     * 事件ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 事件名称
     */
    @ApiModelProperty(value = "事件名称")
    private String name;

    /**
     * 短信模板类型
     */
    @ApiModelProperty(value = "短信模板类型")
    private Integer smsType;

    /**
     * 短信模板内容
     */
    @ApiModelProperty(value = "短信模板内容")
    private String smsContent;

    /**
     * app id
     */
    @ApiModelProperty(value = "应用ID")
    private String appId;

    /**
     * 租户 ID
     */
    @ApiModelProperty(value = "租户ID")
    private String tenantId;
}
