package com.deepexi.dxp.middle.marketing.domain.vo;


import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * <AUTHOR>
 * @desc MarketingActionTemplateResourceVO  行动模板营销资源表
 * @Date: Tue Mar 10 11:57:01 CST 2020
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MarketingActionTemplateResourceVO extends AbstractObject {

    /**  */
    @ApiModelProperty("")
    private Long id;
    /**
     * 行动模板表ID
     */
    @ApiModelProperty("行动模板表ID")
    private Long actionTemplateId;
    /**
     * 资源ID
     */
    @ApiModelProperty("资源ID")
    private Long resourceId;
    /**
     * 资源渠道
     */
    @ApiModelProperty("资源渠道")
    private String resourceChannel;
    /**
     * 资源编码
     */
    @ApiModelProperty("资源编码")
    private String resourceCode;
    /**
     * 资源名称
     */
    @ApiModelProperty("资源名称")
    private String resourceName;
    /**
     * 资源信息
     */
    @ApiModelProperty("资源信息")
    private String resourceInfo;
    /**
     * 资源类型编码(1:赠送优惠券；2:推送H5)
     */
    @ApiModelProperty("资源类型编码(1:赠送优惠券；2:推送H5)")
    private String resourceTypeCode;
    /**
     * 资源类型名称
     */
    @ApiModelProperty("资源类型名称")
    private String resourceTypeName;
    /**
     * 资源类别编码
     */
    @ApiModelProperty("资源类别编码")
    private String resourceCategoryCode;
    /**
     * 资源类别名称
     */
    @ApiModelProperty("资源类别名称")
    private String resourceCategoryName;

    /**
     * 资源url地址
     */
    @ApiModelProperty("资源url地址")
    private String resourceUrl;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createdTime;
    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updatedBy;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updatedTime;
    /**
     * 逻辑删除
     */
    @ApiModelProperty("逻辑删除")
    private Integer deleted;
    /**
     * 版本号，乐观锁
     */
    @ApiModelProperty("版本号，乐观锁")
    private Long version;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
    /**  */
    @ApiModelProperty("")
    private String tenantId;
}

