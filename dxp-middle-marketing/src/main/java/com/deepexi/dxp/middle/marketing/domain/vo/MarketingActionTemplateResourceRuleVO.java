package com.deepexi.dxp.middle.marketing.domain.vo;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-11 14:17
 */
@Data
public class MarketingActionTemplateResourceRuleVO  extends AbstractObject {

    /**
     * 行动模板信息
     */
    @ApiModelProperty("行动模板信息")
    private MarketingActionTemplateInfoVO actionTemplate;

    /**
     * 营销资源信息
     */
    @ApiModelProperty("营销资源信息")
    private MarketingActionTemplateResourceVO resource;

    /**
     * 触达规则信息
     */
    @ApiModelProperty("触达规则信息")
    private List<MarketingActionTemplateTouchRuleVO> ruleList;

    /**
     * 是否可用 true可用 false不可用
     */
    @ApiModelProperty("是否可用")
    private Boolean available;
}
