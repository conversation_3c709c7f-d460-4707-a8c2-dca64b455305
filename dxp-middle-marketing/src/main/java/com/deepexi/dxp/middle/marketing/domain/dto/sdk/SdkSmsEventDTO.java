package com.deepexi.dxp.middle.marketing.domain.dto.sdk;

import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2020年07月28日 10:11
 */
@Data
public class SdkSmsEventDTO extends AbstractObject implements Serializable {

    private Long id = null;
    
    private String appId = null;
    
    private String caseId = null;
    
    private String tenantId = null;
    
    private Long version = null;
    
    private Integer deleted = null;
    
    private String createdBy = null;
    
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createdTime = null;
    
    private String updatedBy = null;
    
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updatedTime = null;
    
    private String code = null;
    
    private String name = null;
    
    private Boolean enable = null;
    
    private Integer category = null;
    
    private Integer serviceProvider = null;
    
    private String smsKey = null;
    
    private Integer international = null;
    
    private Integer smsType = null;
    
    private String sign = null;
    
    private String templateCode = null;
    
    private String eventCode = null;
}
