package com.deepexi.dxp.middle.marketing.domain.dto.sdk;

import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 商品品牌信息DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021-01-14 18:21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SdkBrandDTO extends AbstractObject {

    /**
     * 别名
     */
    private String aliasName;

    /**
     * appId
     */
    private Long appId;

    /**
     *
     */
    private String attachmentBizName;

    /**
     *
     */
    private String attachmentPath;

    /**
     *
     */
    private Integer attachmentType;

    /**
     * 中文名
     */
    private String chineseName;

    /**
     * 品牌code
     */
    private String code;

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createdTime;

    /**
     * 是否启用
     */
    private Integer enabled;

    /**
     * 英文名
     */
    private String englishName;

    /**
     * 编号
     */
    private Long id;

    /**
     * 品牌介绍
     */
    private String introduction;

    /**
     * 品牌所在地
     */
    private String location;

    /**
     * 备注
     */
    private String remark;

    /**
     * 租户
     */
    private String tenantId;

    /**
     * 更新时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updatedTime;
}
