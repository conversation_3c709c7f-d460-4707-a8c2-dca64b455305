package com.deepexi.dxp.middle.marketing.domain.vo;

import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 主动营销任务精选模型
 * @Author: HuangBo.
 * @Date: 2020/3/9 18:39
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MarketingTaskActiveTargetModelVO extends SuperVO {

  @ApiModelProperty(value = "主键ID")
  private Long id;

  /**
   * 模型分类(1:模型单独精选；2:模型综合精选)
   */
  @ApiModelProperty(value = "模型筛选方式(1:按排名；2:按分值)")
  private Integer modelType;

  /**
   * 洞察模型ID
   */
  @ApiModelProperty(value = "洞察模型ID")
  private Long modelId;

  /**
   * 洞察模型名称
   */
  @ApiModelProperty(value = "洞察模型名称")
  private String modelName;

  /**
   * 模型精选人数
   */
  @ApiModelProperty(value = "模型精选人数")
  private Integer memberNums;

  /**
   * 模型实际人数
   */
  @ApiModelProperty(value = "模型实际人数")
  private Integer actualMemberNums;

  /**
   * 圈人规则，取顶部人数或者底部人数 1:top；2:bottom
   */
  @ApiModelProperty(value = "排名Top/Bottom")
  private Integer orderType;

  /**
   * 模型分类值
   */
  @ApiModelProperty(value = "模型分类值,2:个体评估模型；3:数据指标模型")
  private Integer modelTypeVal;

  /**
   * 模型分类名称
   */
  @ApiModelProperty(value = "模型分类名称")
  private String modelTypeName;

}
