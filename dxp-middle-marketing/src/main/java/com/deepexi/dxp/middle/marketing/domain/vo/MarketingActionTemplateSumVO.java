package com.deepexi.dxp.middle.marketing.domain.vo;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020-04-16 11:14
 */
@Data
public class MarketingActionTemplateSumVO  extends AbstractObject {
    /**
     * 模板总数
     */
    @ApiModelProperty("模板总数")
    private Integer totalQty;
    /**
     * 启用总数
     */
    @ApiModelProperty("启用总数")
    private Integer enableQty;
    /**
     * 禁用总数
     */
    @ApiModelProperty("禁用总数")
    private Integer disableQty;
}
