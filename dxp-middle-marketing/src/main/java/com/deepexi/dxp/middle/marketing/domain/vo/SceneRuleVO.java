package com.deepexi.dxp.middle.marketing.domain.vo;


import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * @Class: SceneRuleVO
 * @Description: 场景规则VO
 * @Author: zht
 * @Date: 2020/3/24
 */
@ApiModel
@Data
public class SceneRuleVO extends SuperVO {

    /**
     * 场景id
     */
    @ApiModelProperty("场景id")
    private Long sceneId;

    /**
     * 记录序号
     */
    @ApiModelProperty("记录序号")
    private Integer recordIndex;

    /**
     * 1:做过 2:没做过
     */
    @ApiModelProperty("1:做过 2:没做过")
    private Integer doType;

    /**
     * 事件
     */
    @ApiModelProperty("事件")
    private String event;

    /**
     * 计算类型 1:单次 2:总次数
     */
    @ApiModelProperty("计算类型 1:单次 2:总次数")
    private Integer countType;

    /**
     * 操作类型 1:> 2:>= 3:< 4:<= 5:区间
     */
    @ApiModelProperty("操作类型 1:> 2:>= 3:< 4:<= 5:区间")
    private Integer operationType;

    /**
     * 区间左值
     */
    @ApiModelProperty("区间左值")
    private Integer firstValue;

    /**
     * 区间右值
     */
    @ApiModelProperty("区间右值")
    private Integer lastValue;

    /**
     * 0：并 1：且
     */
    @ApiModelProperty("0：并 1：且")
    private Integer ruleType;

    /**
     * 序列条件值
     */
    @ApiModelProperty("序列条件值")
    private Integer conditionValue;

    /**
     * 条件单位 1:秒 2:分 3:小时 4:天
     */
    @ApiModelProperty("条件单位 1:秒 2:分 3:小时 4:天")
    private Integer conditionUnit;
}
