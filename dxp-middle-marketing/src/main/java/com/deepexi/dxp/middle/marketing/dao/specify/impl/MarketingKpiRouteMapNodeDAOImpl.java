package com.deepexi.dxp.middle.marketing.dao.specify.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.deepexi.dxp.middle.marketing.dao.specify.MarketingKpiRouteMapNodeDAO;
import com.deepexi.dxp.middle.marketing.domain.dto.MarketingKpiRouteMapNodeDTO;
import com.deepexi.dxp.middle.marketing.domain.entity.MarketingKpiRouteMapNodeDO;
import com.deepexi.dxp.middle.marketing.mapper.specify.MarketingKpiRouteMapNodeMapper;
import com.deepexi.util.pojo.ObjectCloneUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-03-09 17:49
 */
@Repository
public class MarketingKpiRouteMapNodeDAOImpl extends ServiceImpl<MarketingKpiRouteMapNodeMapper, MarketingKpiRouteMapNodeDO> implements MarketingKpiRouteMapNodeDAO {

    @Override
    public List<MarketingKpiRouteMapNodeDTO> queryByRouteMap(Long routeMapId, String tenantId) {
        QueryWrapper<MarketingKpiRouteMapNodeDO> wrapper = new QueryWrapper<>();
        wrapper.eq("route_map_id", routeMapId);
//        wrapper.eq("tenant_id", tenantId);
        return ObjectCloneUtils.convertList(this.baseMapper.selectList(wrapper), MarketingKpiRouteMapNodeDTO.class);
    }

    @Override
    public List<MarketingKpiRouteMapNodeDTO> queryByRouteMap(List<Long> routeMapIds) {
        QueryWrapper<MarketingKpiRouteMapNodeDO> wrapper = new QueryWrapper<>();
        wrapper.in("route_map_id", routeMapIds);
        return ObjectCloneUtils.convertList(this.baseMapper.selectList(wrapper), MarketingKpiRouteMapNodeDTO.class);
    }

    @Override
    public Boolean deleteByRouteMap(Long routeMapId, String tenantId) {
        MarketingKpiRouteMapNodeDO nodeDO = new MarketingKpiRouteMapNodeDO();
        nodeDO.setRouteMapId(routeMapId);
//        nodeDO.setTenantId(tenantId);
        return SqlHelper.retBool(this.baseMapper.deleteByRouteMap(nodeDO));
    }

}
