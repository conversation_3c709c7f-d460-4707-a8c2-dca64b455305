package com.deepexi.dxp.middle.marketing.domain.dto.sdk;

import com.deepexi.util.pojo.AbstractObject;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020-09-14 11:49
 */
@Data
public class SdkSendSendDetailByOutIdsQuery extends AbstractObject {
    private String appId = null;
    private String caseId = null;
    private List<String> outIds = null;
    private Integer page = null;
    private Integer size = null;
    private String tenantId = null;
}
