package com.deepexi.dxp.middle.marketing.domain.dto.sdk;

import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020-09-10 11:40
 */
@Data
public class SdkSmsSendDetailDTO extends AbstractObject {
    private String appId ;
    private String caseId ;
    private String content ;
    private String createdBy ;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createdTime ;
    private Integer deleted ;
    private Long eventId ;
    private Long id ;
    private String receiverPhone ;
    private String responseMessage ;
    private Integer result ;
    private Long sendId ;
    private String serviceReceiptId ;
    private String serviceRequestId ;
    private Integer smsType ;
    private String tenantId ;
    private String updatedBy ;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updatedTime ;
    private Long version ;
}
