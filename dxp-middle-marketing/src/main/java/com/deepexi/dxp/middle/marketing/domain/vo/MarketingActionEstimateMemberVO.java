package com.deepexi.dxp.middle.marketing.domain.vo;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 行动模板预估人数
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-31 19:03
 */
@Data
public class MarketingActionEstimateMemberVO  extends AbstractObject {
    /**
     * 会员总数
     */
    @ApiModelProperty("会员总数")
    private Long memberAllQty;

    /**
     * 有openid的会员数
     */
    @ApiModelProperty("有openid的会员数")
    private Long openidMemberQty;

    /**
     * 有手机号的会员数
     */
    @ApiModelProperty("有手机号的会员数")
    private Long phoneMemberQty;
}
