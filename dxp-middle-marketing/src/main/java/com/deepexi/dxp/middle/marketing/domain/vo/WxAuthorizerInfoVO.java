package com.deepexi.dxp.middle.marketing.domain.vo;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Class: WxAuthorizerInfosVO
 * @Description: 微信公众号信息
 * @Author: mou
 * @Date: 2020/4/9
 */
@Data
@ApiModel
public class WxAuthorizerInfoVO extends AbstractObject {

    /**
     * 主键 ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 公众号 appid
     */
    @ApiModelProperty(value = "公众号appid")
    private String authorizerAppId;

    /**
     * 公众号昵称
     */
    @ApiModelProperty(value = "公众号昵称")
    private String alias;

    /**
     * 公众微信号
     */
    @ApiModelProperty(value = "公众微信号")
    private String userName;

    /**
     * 公众号类型 （0 订阅号 1升级后的订阅号 2服务号）json字符串
     */
    @ApiModelProperty(value = "公众号类型 （0 订阅号 1升级后的订阅号 2服务号）json字符串")
    private String serviceTypeInfo;

    /**
     * 主体信息
     */
    @ApiModelProperty(value = "主体信息")
    private String principalName;

    /**
     * 头像 URL
     */
    @ApiModelProperty(value = "头像 URL")
    private String headImg;


    /**
     * 租户 ID
     */
    @ApiModelProperty(value = "租户 ID")
    private String tenantId;

    /**
     * 应用 ID
     */
    @ApiModelProperty(value = "应用 ID")
    private String appId;

    /**
     * 昵称
     */
    @ApiModelProperty(value = "昵称")
    private String nickName;

}
