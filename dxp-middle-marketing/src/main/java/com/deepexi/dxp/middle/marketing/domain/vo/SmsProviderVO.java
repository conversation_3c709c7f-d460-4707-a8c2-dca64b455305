package com.deepexi.dxp.middle.marketing.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class SmsProviderVO {

    /**
     * 主键 ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * app id
     */
    @ApiModelProperty(value = "应用ID")
    private String appId;

    /**
     * 租户 ID
     */
    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    /**
     * 接入名称
     */
    @ApiModelProperty(value = "接入名称")
    private String name;

    /**
     * 服务商  1阿里云, 2腾讯云
     */
    @ApiModelProperty(value = "服务商  1阿里云, 2腾讯云")
    private Integer serviceProvider;

    /**
     * key
     */
    @ApiModelProperty(value = "key")
    private String smsKey;

    /**
     * 状态 false禁用、true启用
     */
    @ApiModelProperty(value = "状态 false禁用、true启用")
    private Boolean enable;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String describtion;

    /**
     * 接入编码
     */
    @ApiModelProperty(value = "接入编码")
    private String code;

    @ApiModelProperty("page")
    private Integer page;

    @ApiModelProperty("size")
    private Integer size;

    /**
     * caseId
     */
    @ApiModelProperty(value = "实例id")
    private String caseId;
}
