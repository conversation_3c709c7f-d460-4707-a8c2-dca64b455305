package com.deepexi.dxp.middle.marketing.domain.dto.sdk;

import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020年07月28日 11:29
 */
@Data
public class SdkSmsTemplateDTO extends AbstractObject {

    
    private Long id = null;
    
    private String appId = null;
    
    private String caseId = null;
    
    private String tenantId = null;
    
    private Long version = null;
    
    private Integer deleted = null;
    
    private String createdBy = null;
    
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createdTime = null;
    
    private String updatedBy = null;
    
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updatedTime = null;
    
    private Integer international = null;
    
    private Integer serviceProvider = null;
    
    private String smsKey = null;
    
    private Integer type = null;
    
    private Integer smsType = null;
    
    private String title = null;
    
    private String content = null;
    
    private String remark = null;
    
    private String code = null;
    
    private Integer status = null;
    
    private String reason = null;
    
    private List<String> picUrls = null;
    
    private Integer signSource = null;
}
