package com.deepexi.dxp.middle.marketing.dao.specify.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.deepexi.dxp.middle.marketing.dao.specify.MarketingKpiRouteMapNodeItemsDAO;
import com.deepexi.dxp.middle.marketing.domain.dto.MarketingKpiRouteMapNodeItemsDTO;
import com.deepexi.dxp.middle.marketing.domain.entity.MarketingKpiRouteMapNodeItemsDO;
import com.deepexi.dxp.middle.marketing.mapper.specify.MarketingKpiRouteMapNodeItemsMapper;
import com.deepexi.util.pojo.ObjectCloneUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-03-09 17:50
 */
@Repository
public class MarketingKpiRouteMapNodeItemsDAOImpl extends ServiceImpl<MarketingKpiRouteMapNodeItemsMapper, MarketingKpiRouteMapNodeItemsDO> implements MarketingKpiRouteMapNodeItemsDAO {


    @Override
    public List<MarketingKpiRouteMapNodeItemsDTO> queryByRouteMapNode(Long routeMapNodeId) {
        QueryWrapper<MarketingKpiRouteMapNodeItemsDO> wrapper = new QueryWrapper<>();
        wrapper.eq("route_map_node_id", routeMapNodeId);
        return ObjectCloneUtils.convertList(baseMapper.selectList(wrapper), MarketingKpiRouteMapNodeItemsDTO.class);
    }

    @Override
    public List<MarketingKpiRouteMapNodeItemsDTO> queryByRouteMap(Long routeMapId) {
        MarketingKpiRouteMapNodeItemsDO nodeItemsDO = new MarketingKpiRouteMapNodeItemsDO();
        nodeItemsDO.setRouteMapId(routeMapId);
        return ObjectCloneUtils.convertList(baseMapper.queryByRouteMap(nodeItemsDO), MarketingKpiRouteMapNodeItemsDTO.class);
    }

    @Override
    public Boolean deleteByRouteMap(Long routeMapId) {
        MarketingKpiRouteMapNodeItemsDO nodeItemsDO = new MarketingKpiRouteMapNodeItemsDO();
        nodeItemsDO.setRouteMapId(routeMapId);
        return SqlHelper.retBool(this.baseMapper.deleteByRouteMap(nodeItemsDO));
    }

    @Override
    public MarketingKpiRouteMapNodeItemsDTO findByKpiId(Long id) {
        return this.baseMapper.findByKpiId(id);
    }

}
