package com.deepexi.dxp.middle.marketing.domain.vo;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Class: WxDmTemplateDetailVO
 * @Description: DM 模板详情 VO
 * @Author: mou
 * @Date: 2020/3/25
 */
@Data
@ApiModel
public class WxDmTemplateDetailVO extends AbstractObject {

    /**
     * 公众号名称
     */
    @ApiModelProperty(value = "公众号名称")
    private String authorizerNickName;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    private String tenantId;

    /**
     * 公众号 appid
     */
    @ApiModelProperty(value = "公众号appid")
    private String authorizerAppId;

    /**
     * 公众号模板ID
     */
    @ApiModelProperty(value = "公众号模板ID")
    private String templateId;

    /**
     * 模板内容
     */
    @ApiModelProperty(value = "模板内容")
    private String content;

    /**
     * 模板实例/详情
     */
    @ApiModelProperty(value = "模板实例/详情")
    private String example;

    /**
     * 模板参数
     */
    @ApiModelProperty(value = "模板参数")
    private String param;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Integer id;

    /**
     * DM模板名称
     */
    @ApiModelProperty(value = "DM模板名称")
    private String dmTitle;

    /**
     * DM模板内容
     */
    @ApiModelProperty(value = "DM模板内容")
    private String dmContent;

    /**
     * 状态
     */
    @ApiModelProperty(value = "DM模板内容")
    private Integer status;

    /**
     * 微信模板名称
     */
    @ApiModelProperty(value = "微信模板名称")
    private String title;

    /**
     * 资源分类
     */
    @ApiModelProperty(value = "资源分类")
    private String resourceActionType;

    /**
     * 资源类别
     */
    @ApiModelProperty(value = "资源类别")
    private String resourceKind;
}
