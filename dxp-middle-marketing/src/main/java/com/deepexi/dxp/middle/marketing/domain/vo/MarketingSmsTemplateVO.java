package com.deepexi.dxp.middle.marketing.domain.vo;

import com.deepexi.dxp.marketing.common.base.dto.SuperExtDTO;
import com.deepexi.dxp.marketing.common.base.vo.SuperExtVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 */
@Data
@ApiModel
@EqualsAndHashCode(callSuper = true)
public class MarketingSmsTemplateVO extends SuperExtVO {



    /**
     * 模板code
     */
    @ApiModelProperty(value = "模板编码")
    private String templateCode;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 自定义内容
     */
    @ApiModelProperty(value = "自定义内容")
    private String contentParam;

    /**
     * 1已启用，0已禁用
     */
    @ApiModelProperty(value = "状态1已启用，0已禁用")
    private Integer status;

    /**
     * 服务商  1阿里云, 2腾讯云
     */
    @ApiModelProperty(value = "服务商  1阿里云, 2腾讯云")
    private Integer serviceProvider;

    /**
     * key
     */
    @ApiModelProperty(value = "key")
    private String smsKey;

    @ApiModelProperty(value = " 0表示国内短信，1表示国际短信，默认为0")
    private Integer international;

    @ApiModelProperty(value = "短信类型    1验证码   2通知短信   3营销短信")
    private Integer smsType;

    @ApiModelProperty(value = "短信签名")
    private String sign;



    @ApiModelProperty(value = "spaas 事件id")
    private Long eventId;


    @ApiModelProperty(value = "审批不通过原因")
    private String reason;

    /**
     * 资源分类
     */
    @ApiModelProperty(value = "资源分类")
    private String resourceActionType;

    /**
     * 资源类别
     */
    @ApiModelProperty(value = "资源类别")
    private String resourceKind;

    /**
     * 短信正文内容
     */
    @ApiModelProperty(value = "短信正文内容")
    private String smsContent;

    private Integer size;

    private Integer page;
}
