package com.deepexi.dxp.middle.marketing.domain.vo;


import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;


/**
 * <AUTHOR>
 * @desc AutoMarketingTaskConditionVO  自动营销触发时机
 * @Date: Mon Mar 09 15:26:44 CST 2020
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AutoMarketingTaskConditionVO extends AbstractObject {

    /**  */
    @ApiModelProperty("")
    private Long id;
    /**
     * 租户ID
     */
    @ApiModelProperty("租户ID")
    private String tenantId;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createdTime;
    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updatedTime;
    /**
     * 创建人id
     */
    @ApiModelProperty("创建人id")
    private Long creatorId;
    /**
     * 修改人id
     */
    @ApiModelProperty("修改人id")
    private Long modifierId;
    /**
     * 创建人名称
     */
    @ApiModelProperty("创建人名称")
    private String createdBy;
    /**
     * 修改人名称
     */
    @ApiModelProperty("修改人名称")
    private String updatedBy;
    /**
     * 删除状态 0无效 1有效
     */
    @ApiModelProperty("删除状态 0无效 1有效")
    private Integer deleted;
    /**
     * 版本号，乐观锁
     */
    @ApiModelProperty("版本号，乐观锁")
    private Long version;
    /**
     * 自动营销ID
     */
    @ApiModelProperty("自动营销ID")
    private Long autoMarketingId;
    /**
     * 触发类型 （0 模型，1 场景 ，2 关联）
     */
    @ApiModelProperty("触发类型 （0 模型，1 场景 ，2 关联）")
    private Integer type;
    /**
     * 模型ID
     */
    @ApiModelProperty("模型ID")
    private Long modelId;
    /**
     * 模型名称
     */
    @ApiModelProperty("模型名称")
    private String modelName;
    /**
     * 模型分类id
     */
    @ApiModelProperty("模型分类id")
    private Long modelTypeId;
    /**
     * 模型分类名称
     */
    @ApiModelProperty("模型分类名称")
    private String modelTypeName;
    /**
     * 触发条件类型（0 分值排序，1排名比重）
     */
    @ApiModelProperty("触发条件类型（0 分值排序，1排名比重）")
    private Long conditionType;
    /**
     * 条件值类型（0 top ，1 bottom）
     */
    @ApiModelProperty("条件值类型（0 top ，1 bottom）")
    private String conditionValueType;
    /**
     * 条件值
     */
    @ApiModelProperty("条件值")
    private BigDecimal conditionVtypeValue;
    /**
     * 场景id
     */
    @ApiModelProperty("场景id")
    private Long sceneId;
    /**
     * 场景类型id
     */
    @ApiModelProperty("场景类型id")
    private Long sceneTypeId;
    /**
     * 场景名称
     */
    @ApiModelProperty("场景名称")
    private String sceneName;
    /**
     * 场景类型名称
     */
    @ApiModelProperty("场景类型名称")
    private String sceneTypeName;
}

