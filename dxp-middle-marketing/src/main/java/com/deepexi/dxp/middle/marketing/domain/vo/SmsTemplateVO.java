package com.deepexi.dxp.middle.marketing.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class SmsTemplateVO {

    /**
     * 主键 ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * app id
     */
    @ApiModelProperty(value = "应用ID")
    private String appId;

    /**
     * 租户 ID
     */
    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    /**
     * 接入名称
     */
    @ApiModelProperty(value = "接入名称")
    private String name;

    /**
     * 服务商  1阿里云, 2腾讯云
     */
    @ApiModelProperty(value = "服务商  1阿里云, 2腾讯云")
    private Integer serviceProvider;

    /**
     * key
     */
    @ApiModelProperty(value = "key")
    private String smsKey;

    /**
     * 状态 false禁用、true启用
     */
    @ApiModelProperty(value = "状态 false禁用、true启用")
    private Boolean enable;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String describtion;

    /**
     * 接入编码
     */
    @ApiModelProperty(value = "接入编码")
    private String code;

    @ApiModelProperty("page")
    private Integer page;

    @ApiModelProperty("size")
    private Integer size;

    /**
     * caseId
     */
    @ApiModelProperty(value = "实例id")
    private String caseId;


    @ApiModelProperty(value = " 0表示国内短信，1表示国际短信，默认为0")
    private Integer international;

    @ApiModelProperty(value = "模板分类: 1短信模板，2短信签名")
    private Integer type;


    @ApiModelProperty(value = "短信类型    1验证码   2通知短信   3营销短信")
    private Integer smsType;

    @ApiModelProperty(value = "模板名称")
    private String title;

    @ApiModelProperty(value = "模板内容")
    private String content;

    @ApiModelProperty(value = "申请说明")
    private String remark;

    @ApiModelProperty(value = "资质证明")
    private List<String> picUrls;

    @ApiModelProperty(value = "签名来源 " +
        "0：企事业单位的全称或简称。\n" +
        "1：工信部备案网站的全称或简称。\n" +
        "2：APP应用的全称或简称。\n" +
        "3：公众号或小程序的全称或简称。\n" +
        "4：电商平台店铺名的全称或简称。\n" +
        "5：商标名的全称或简称")
    private Integer signSource;

    @ApiModelProperty(value = " 0审核通过  1审核通过  2审核不通过")
    private Integer status;

    /**
     * 关联任务数
     */
    @ApiModelProperty(value = "关联任务数")
    private Integer correlationAssignmentNum;

    @ApiModelProperty(value = "审核不通过原因")
    private String reason;

    @ApiModelProperty(value = "签名内容")
    private String signContent;
}
