package com.deepexi.dxp.middle.marketing.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Class: SmsVO
 * @Description: 短信模板 VO
 * @Author: mou
 * @Date: 2020/3/25
 */
@Data
@ApiModel
public class SmsVO {

    /**
     * 主键 ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * app id
     */
    @ApiModelProperty(value = "应用ID")
    private String appId;

    /**
     * 租户 ID
     */
    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    /**
     * 模板名称
     */
    @ApiModelProperty(value = "短信模板名称")
    private String title;

    /**
     * 模板类型 1-短信模板、2-短信签名
     */
    @ApiModelProperty(value = "模板类型 1-短信模板、2-短信签名")
    private Integer type;

    /**
     * 服务商  1-阿里云
     */
    @ApiModelProperty(value = "服务商  1-阿里云")
    private Integer serviceProvider;

    /**
     * 短信类型 1-验证码、2-通知短信、3-营销短信
     */
    @ApiModelProperty(value = "短信类型 1-验证码、2-通知短信、3-营销短信")
    private Integer smsType;

    /**
     * 关联任务数
     */
    @ApiModelProperty(value = "关联任务数")
    private Integer correlationAssignmentNum;

    /**
     * SMSTemplateStatusEnum
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 模板code
     */
    @ApiModelProperty(value = "模板编码")
    private String code;

    /**
     * 短信模板事件列表
     */
    @ApiModelProperty(value = "短信事件标识ID")
    private Long eventId;
}
