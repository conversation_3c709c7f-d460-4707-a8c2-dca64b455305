package com.deepexi.dxp.middle.marketing.dao.specify.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.deepexi.dxp.middle.marketing.dao.specify.MarketingKpiFormulaDAO;
import com.deepexi.dxp.middle.marketing.domain.dto.MarketingKpiFormulaDTO;
import com.deepexi.dxp.middle.marketing.domain.entity.MarketingKpiFormulaDO;
import com.deepexi.dxp.middle.marketing.mapper.specify.MarketingKpiFormulaMapper;
import com.deepexi.util.pojo.ObjectCloneUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-03-09 17:43
 */
@Repository
public class MarketingKpiFormulaDAOImpl extends ServiceImpl<MarketingKpiFormulaMapper, MarketingKpiFormulaDO> implements MarketingKpiFormulaDAO {

    /**
     * 根据派生指标ID查询列表
     */
    @Override
    public List<MarketingKpiFormulaDTO> queryList(Long kpiItemsId){
        MarketingKpiFormulaDO formulaDO = new MarketingKpiFormulaDO();
        formulaDO.setKpiId(kpiItemsId);
        return ObjectCloneUtils.convertList(baseMapper.queryListByKpiItems(formulaDO), MarketingKpiFormulaDTO.class);
    }

    @Override
    public List<MarketingKpiFormulaDTO> queryListByKpiItemsIds(List<Long> kpiItemsIds) {
        return ObjectCloneUtils.convertList(baseMapper.queryListByKpiItemsIds(kpiItemsIds), MarketingKpiFormulaDTO.class);
    }

    @Override
    public Boolean deleteByKpiItems(Long kpiItemsId) {
        MarketingKpiFormulaDO kpiItemsFormulaDO = new MarketingKpiFormulaDO();
        kpiItemsFormulaDO.setKpiId(kpiItemsId);
        return SqlHelper.retBool(this.baseMapper.deleteByKpiItems(kpiItemsFormulaDO));
    }

}
