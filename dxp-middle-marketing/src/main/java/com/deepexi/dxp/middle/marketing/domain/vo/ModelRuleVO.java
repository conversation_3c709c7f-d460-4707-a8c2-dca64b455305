package com.deepexi.dxp.middle.marketing.domain.vo;

import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Class: ModelRuleVO
 * @Description: 模型规则VO
 * @Author: zht
 * @Date: 2020/3/24
 */
@Data
@Api("ModelRuleVO")
public class ModelRuleVO extends SuperVO {


    @ApiModelProperty("事件名称")
    private String eventName;

    @ApiModelProperty("事件值类型")
    private String valueType;

    @ApiModelProperty("事件编码")
    private String eventCode;

    /**
     * 维度id
     */
    @ApiModelProperty("维度id")
    private Long dimensionId;

    /**
     * 事件
     */
    @ApiModelProperty("事件")
    private Long event;

    /**
     * 操作类型 1:> 2:>= 3:< 4:<= 5:区间
     */
    @ApiModelProperty("操作类型 1:> 2:>= 3:< 4:<= 5:区间")
    private Integer operationType;

    /**
     * 左区间值
     */
    @ApiModelProperty("区间左值")
    private String firstValue;

    /**
     * 右区间值
     */
    @ApiModelProperty("区间右值")
    private String lastValue;

    @ApiModelProperty("单位")
    private String unit;

    /**
     * 评分值
     */
    @ApiModelProperty("评分值")
    private Integer score;

    @ApiModelProperty("商品名称")
    private String skuName;
}
