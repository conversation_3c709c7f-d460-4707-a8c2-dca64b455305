package com.deepexi.dxp.middle.marketing.domain.dto.sdk;

import com.deepexi.util.pojo.AbstractObject;
import lombok.Data;

import java.util.List;

/**
 * PromotionActivityLimitQuery
 */
@Data
public class SdkPromotionActivityLimitQuery extends AbstractObject {
  private List<Integer> activityStatusList = null;

  private Long appId = null;

  private String code = null;

  private String couponLimit = null;

  private String name = null;

  private List<Integer> paTemplateList = null;

  private Integer page = null;

  private Integer size = null;

  private String tenantId = null;

  private String userLimit = null;
}

