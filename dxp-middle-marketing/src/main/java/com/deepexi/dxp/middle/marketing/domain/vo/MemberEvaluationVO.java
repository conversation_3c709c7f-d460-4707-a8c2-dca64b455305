package com.deepexi.dxp.middle.marketing.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 会员综合模型评估Vo
 *
 * <AUTHOR>
 * @Date 2020/3/10
 */
@Data
@ApiModel
public class MemberEvaluationVO implements Serializable {

    /**
     * 最小值
     */
    @ApiModelProperty("最小值")
    private Integer minValue;

    /**
     * 模型项
     */
    @ApiModelProperty("模型项")
    private List<MemberEvaluationItemVO> item;

}
