package com.deepexi.dxp.middle.marketing.domain.dto.sdk;

import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.ToString;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2020年07月28日 10:50
 */
@Data
@ToString
public class SdkSmsSendAddDTO extends AbstractObject {


    private String eventCode = null;

    private String requestNo = null;

    private Map<String, String> paramMap = null;

    private String remark = null;

    private Integer pushType = null;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date pushTime = null;

    private List<SdkPhoneDTO> receiverPhones = new ArrayList();

    private String backUrl = null;

    private String outId = null;

    private String appId = null;

    private String caseId = null;

    private String tenantId = null;
}
