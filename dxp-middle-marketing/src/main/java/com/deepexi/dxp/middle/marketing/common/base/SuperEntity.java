package com.deepexi.dxp.middle.marketing.common.base;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.deepexi.util.domain.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * DO类统一父类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SuperEntity extends BaseEntity {

    /**
     * 版本号
     */
    @TableField("version")
    private Long version;
    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
    /**
     * 逻辑删除
     */
    @TableLogic
    @TableField("is_deleted")
    private Integer deleted = 0;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 更新人
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 逻辑删除，数据正常状态
     */
    public static final Integer DR_NORMAL = 0;
    /**
     * 逻辑删除，数据已标记为逻辑删除
     */
    public static final Integer DR_DELETE = 1;

    /**
     * 数据状态(1:启用，2:禁用)
     */
    public static final Integer STATUS_ENABLE = 1;
    /**
     * 数据状态(1:启用，2:禁用)
     */
    public static final Integer STATUS_DISABLE = 2;

}