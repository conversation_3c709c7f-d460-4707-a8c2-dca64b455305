package com.deepexi.dxp.middle.marketing.domain.vo;

import com.deepexi.dxp.marketing.common.base.vo.SuperExtVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Class: SceneVO
 * @Description: 场景VO
 * @Author: zht
 * @Date: 2020/3/24
 */
@Data
@ApiModel
public class SceneVO extends SuperExtVO {
    /**
     * 场景名称
     */
    @ApiModelProperty("场景名称")
    private String sceneName;

    /**
     * 场景描述
     */
    @ApiModelProperty("场景描述")
    private String sceneDesc;

    /**
     * 一级分类（1：行业场景 2：我的场景）
     */
    @ApiModelProperty("一级分类（1：行业场景 2：我的场景）")
    private Integer type;

    /**
     * 二级分类（1：美妆行业）
     */
    @ApiModelProperty("1：美妆行业")
    private Integer sceneCategory;

    /**
     * 场景分类：1：交易场景，2：权益场景，3、服务场景（修改后：1：认知 2：交易 3：售后 4：权益）
     */
    @ApiModelProperty("场景分类：1：交易场景，2：权益场景，3、服务场景（修改后：1：认知 2：交易 3：售后 4：权益）")
    private Integer sceneType;

    @ApiModelProperty("场景分类名称：1：交易场景，2：权益场景，3、服务场景")
    private String sceneTypeName;

    /**
     * 0-启用 1-禁用
     */
    @ApiModelProperty("0-启用 1-禁用")
    private Integer status;

    /**
     * 渠道范围 1:H5商城
     */
    @ApiModelProperty("渠道范围 1:H5商城")
    private Integer channelRange;

    /**
     * 人群范围 1:全体会员,2:指定会员,3:指定客群包
     */
    @ApiModelProperty("人群范围 1:全体会员,2:指定会员,3:指定客群包")
    private Integer populationRange;

    /**
     * 目标人群值 (, 分隔)
     */
    @ApiModelProperty("目标人群值 (, 分隔)")
    private String populationValue;

    /**
     * 事件结构 1:满足事件关系 2：满足事件序列
     */
    @ApiModelProperty("事件结构 1:满足事件关系 2：满足事件序列")
    private Integer eventStructure;

    /**
     * 监测周期
     */
    @ApiModelProperty("监测周期")
    private Integer detectionCycle;

    /**
     * 周期单位
     */
    @ApiModelProperty("周期单位")
    private Integer cycleUnit;

    /**
     * 规则数量统计
     */
    @ApiModelProperty("规则数量")
    private Integer ruleCount;
//    /**
//     * 场景规则
//     */
//    @ApiModelProperty("场景规则json  \"{\\\"left\\\": {\\\"left\\\": {\\\"event\\\": \\\"71\\\",\\\"doType\\\": \\\"0\\\",\\\"countType\\\": 1,\\\"operationType\\\": 0,\\\"firstValue\\\": 6},\\\"right\\\": {\\\"event\\\": \\\"72\\\",\\\"doType\\\": \\\"1\\\",\\\"countType\\\": 2,\\\"operationType\\\": 5,\\\"firstValue\\\": 5,\\\"lastValue\\\": 9},\\\"ruleType\\\": 0},\\\"right\\\": {\\\"event\\\": \\\"70\\\",\\\"doType\\\": \\\"1\\\",\\\"countType\\\": 1,\\\"operationType\\\": 0,\\\"firstValue\\\": 5 },\\\"ruleType\\\": 0 }\"")
//    private String ruleJson;

    @ApiModelProperty("场景规则")
    private List<SceneRuleVO> sceneRules;

}
