package com.deepexi.dxp.middle.marketing.domain.vo;



import com.deepexi.dxp.marketing.common.base.vo.SuperVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 行动模板
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-11 11:55
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MarketingActionTemplateInfoVO extends SuperVO {

    /**
     * 行动模板名称
     */
    @ApiModelProperty("发送规则类型")
    private String name;

    /**
     * 行动模板编码
     */
    @ApiModelProperty("发送规则类型")
    private String code;

    /**
     * 状态：0：启用 1：禁用
     */
    @ApiModelProperty("发送规则类型")
    private Integer status;

    /**
     * 使用次数
     */
    @ApiModelProperty("发送规则类型")
    private Integer usedQty;
}
