package com.deepexi.dxp.middle.marketing.domain.vo;

import com.deepexi.dxp.marketing.common.base.vo.SuperExtVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Class: WxDmTemplateVO
 * @Description: DM 模板 VO
 * @Author: mou
 * @Date: 2020/3/25
 */
@Data
@ApiModel
public class MarketingWxTemplateVO extends SuperExtVO {

    /**
     * 模板名称
     */
    @ApiModelProperty(value = "模板名称")
    private String title;

    /**
     * 微信模板ID
     */
    @ApiModelProperty(value = "微信模板ID")
    private String templateId;

    @ApiModelProperty(value = "微信模板内容")
    private String content;


    /**
     * 公众号名字
     */
    @ApiModelProperty(value = "公众号名字")
    private String authorizerNickName;

    /**
     * 公众号appid
     */
    @ApiModelProperty(value = "公众号名字")
    private String authorizerAppId;

    @ApiModelProperty(value = "模板实例")
    private String example;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * dm 自定义内容
     */
    @ApiModelProperty(value = "自定义内容")
    private String contentParam;


    /**
     * 资源分类
     */
    @ApiModelProperty(value = "资源分类")
    private String resourceActionType;

    /**
     * 资源类别
     */
    @ApiModelProperty(value = "资源类别")
    private String resourceKind;

}
