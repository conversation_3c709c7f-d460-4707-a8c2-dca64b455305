package com.deepexi.dxp.middle.marketing.dao.specify;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepexi.dxp.middle.marketing.domain.dto.MarketingKpiRouteMapNodeDTO;
import com.deepexi.dxp.middle.marketing.domain.entity.MarketingKpiRouteMapNodeDO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-03-09 17:48
 */
public interface MarketingKpiRouteMapNodeDAO extends IService<MarketingKpiRouteMapNodeDO> {

    /**
     * 根据条件查询列表
     */
    List<MarketingKpiRouteMapNodeDTO> queryByRouteMap(Long routeMapId, String tenantId);
    /**
     * 根据条件查询列表
     */
    List<MarketingKpiRouteMapNodeDTO> queryByRouteMap(List<Long> routeMapIds);

    /**
     * 根据路径图删除
     * @param routeMapId
     * @return
     */
    Boolean deleteByRouteMap(Long routeMapId, String tenantId);

}
