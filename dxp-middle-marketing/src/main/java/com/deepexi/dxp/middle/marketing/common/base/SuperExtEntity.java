package com.deepexi.dxp.middle.marketing.common.base;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.deepexi.util.domain.entity.BaseExtEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * DO类统一父类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SuperExtEntity extends BaseExtEntity {

    /**
     * 版本号
     */
    @TableField("version")
    private Long version;
    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
    /**
     * 逻辑删除
     */
    @TableLogic
    @TableField("is_deleted")
    private Integer deleted = 0;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 更新人
     */
    @TableField("updated_by")
    private String updatedBy;

}