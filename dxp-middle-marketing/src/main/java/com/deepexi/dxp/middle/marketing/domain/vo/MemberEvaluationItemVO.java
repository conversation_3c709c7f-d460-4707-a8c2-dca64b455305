package com.deepexi.dxp.middle.marketing.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 会员综合模型评估项Vo
 *
 * <AUTHOR>
 * @Date 2020/3/10
 */
@Data
@ApiModel
public class MemberEvaluationItemVO implements Serializable {
    /**
     * 模型id
     */
    @ApiModelProperty("模型id")
    private Long modelId;

    /**
     * 模型名称
     */
    @ApiModelProperty("模型名称")
    private String label;

    /**
     * 模型值
     */
    @ApiModelProperty("模型值")
    private Integer value;

    /**
     * 最大值
     */
    @ApiModelProperty("最大值")
    private Integer maxValue;

}
