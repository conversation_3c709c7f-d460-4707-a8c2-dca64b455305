package com.deepexi.dxp.middle.marketing.dao.specify;

import com.baomidou.mybatisplus.extension.service.IService;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.MarketingKpiQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.KpiOperationDTO;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.MarketingKpiExtDTO;
import com.deepexi.dxp.middle.marketing.domain.dto.MarketingKpiDTO;
import com.deepexi.dxp.middle.marketing.domain.entity.MarketingKpiDO;
import com.deepexi.util.pageHelper.PageBean;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-03-09 17:41
 */
public interface MarketingKpiDAO extends IService<MarketingKpiDO> {

    /**
     * 根据ID查询
     */
    MarketingKpiDTO queryById(Long id);

    /**
     * 保存
     */
    Boolean save(MarketingKpiDTO dtoEntity);


    /**
     * 保存原生指标
     */
    Boolean saveOriginalKpiItems(MarketingKpiDTO kpiItemsDTO);

    /**
     * 根据条件查询列表
     */
    List<MarketingKpiDTO> queryList(MarketingKpiQuery query);

    /**
     * 获取分页集合列表
     */
    PageBean<MarketingKpiDTO> pageList(MarketingKpiQuery query);

    /**
     * 查询指定列的所有数据
     */
    List<Object> queryByColumn(MarketingKpiQuery query);

    /**
     * 启用/禁用
     */
    Boolean processStatus(Long id, Integer status);

    /**
     * 根据活动目标查询可被作为核心指标的原生/派生指标
     */
    PageBean<MarketingKpiDTO> queryAsCoreKpiItems(MarketingKpiQuery query);

    /**
     * 根据活动目标查询核心指标
     */
    List<MarketingKpiDTO> queryCoreKpiItems(Integer type, Integer status, String tenantId);

    /**
     * 根据指标组ID获取原生/派生指标集合
     */
    List<MarketingKpiExtDTO> queryByKpiItemGroup(Long groupId, Integer status, String tenantId);

    /**
     * 根据ID查询运算列表
     *
     * @param id KPI指标ID
     * @return List
     */
    List<KpiOperationDTO> queryOperationList(Long id, String tenantId);

    List<MarketingKpiDTO> queryByName(String tenantId, String name);

    Boolean deleteByKpi(Long id);
}
