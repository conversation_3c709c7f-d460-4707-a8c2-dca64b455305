package com.deepexi.dxp.middle.marketing.domain.dto.sdk;

import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2020年07月28日 10:09
 */
@Data
@ToString
public class SdkSmsEventQuery extends AbstractObject {


    private Long id = null;

    private String tenantId = null;

    private String appId = null;

    private String caseId = null;

    private String createdBy = null;

    private String updatedBy = null;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updatedTimeFrom = null;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updatedTimeTo = null;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createdTimeFrom = null;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createdTimeTo = null;

    private Integer page = null;

    private Integer size = null;

    private String code = null;

    private String name = null;

    private Boolean enable = null;

    private Integer category = null;

    private Integer serviceProvider = null;

    private String smsKey = null;

    private Integer international = null;

    private Integer smsType = null;

    private String sign = null;

    private String templateCode = null;
}
