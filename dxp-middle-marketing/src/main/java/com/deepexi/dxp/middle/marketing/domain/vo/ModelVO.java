package com.deepexi.dxp.middle.marketing.domain.vo;

import com.deepexi.dxp.marketing.common.base.vo.SuperExtVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Class: ModelVO
 * @Description: 模型VO
 * @Author: zht
 * @Date: 2020/3/24
 */
@Data
@ApiModel
public class ModelVO extends SuperExtVO {

    /**
     * 模型名称
     */
    @ApiModelProperty("模型名称")
    private String modelName;

    /**
     * 描述
     */
    @ApiModelProperty("描述")
    private String modelDesc;

    /**
     * 是否显示于雷达图 0 是 1否
     */
    @ApiModelProperty("是否显示于雷达图 0 是 1否")
    private Integer display;

    /**
     * 模型类别：1：人群特征模型，2：个体评估模型，3、数据指标模型
     */
    @ApiModelProperty("模型类别：1：人群特征模型，2：个体评估模型，3、数据指标模型")
    private Integer modelType;


    @ApiModelProperty("模型分类名称：1：人群特征模型，2：个体评估模型，3、数据指标模型")
    private String modeTypeName;

    /**
     * 0-启用 1-禁用
     */
    @ApiModelProperty("0-启用 1-禁用")
    private Integer status;

    /**
     * 模型的维度数量
     */
    @ApiModelProperty("维度数量")
    private Integer dimensionCount;

    /**
     * 模型维度列表
     */
    @ApiModelProperty("模型维度列表")
    List<ModelDimensionVO> modelDimensions;

}
