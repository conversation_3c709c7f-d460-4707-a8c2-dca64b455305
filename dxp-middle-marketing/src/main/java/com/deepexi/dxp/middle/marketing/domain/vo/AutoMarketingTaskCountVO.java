package com.deepexi.dxp.middle.marketing.domain.vo;


import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 * @desc AutoMarketingTaskVO  自动营销任务
 * @Date: Mon Mar 09 15:26:44 CST 2020
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AutoMarketingTaskCountVO extends AbstractObject {
    /**
     * 条件触发后时
     */
    @ApiModelProperty("自动任务总数")
    private Long allCount;

    @ApiModelProperty("草稿")
    private Long draftCount;

    @ApiModelProperty("终止")
    private Long endCount;

    @ApiModelProperty("运行中")
    private Long runningCount;

    @ApiModelProperty("暂停")
    private Long stoppingCount;

    @ApiModelProperty("更多")
    private Long elseCount;
}

