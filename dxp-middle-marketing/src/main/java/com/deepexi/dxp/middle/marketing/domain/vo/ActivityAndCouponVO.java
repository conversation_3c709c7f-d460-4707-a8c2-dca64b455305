package com.deepexi.dxp.middle.marketing.domain.vo;

import com.deepexi.dxp.middle.marketing.domain.dto.sdk.SdkPromotionActivityListPostResponseVOActivityRuleDTO;
import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020-03-06 16:02
 */
@Data
public class ActivityAndCouponVO extends AbstractObject {

    /**
     * 活动id
     */
    @ApiModelProperty("活动id")
    private Long activityId;

    /**
     * 优惠券id
     */
    @ApiModelProperty("优惠券id")
    private Long couponId;

    /**
     * 活动名称
     */
    @ApiModelProperty("活动名称")
    private String activityName;

    /**
     * 活动编码
     */
    @ApiModelProperty("活动编码")
    private String activityCode;

    /**
     * 活动状态
     */
    @ApiModelProperty("活动状态")
    private Integer status;

    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date startTime;

    /**
     *结束时间
     */
    @ApiModelProperty("结束时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date endTime;

    /**
     *活动类型
     */
    @ApiModelProperty("活动类型")
    private Integer paTemplateId;

    @ApiModelProperty("活动策略的规则")
    private List<SdkPromotionActivityListPostResponseVOActivityRuleDTO> activityRuleDTOList;

    /**
     * 优惠券名称
     */
    @ApiModelProperty("优惠券名称")
    private String couponName;

    /**
     * 优惠券数量
     */
    @ApiModelProperty("优惠券数量")
    private Integer couponQty;

    /**
     * 优惠券类型
     */
    @ApiModelProperty("优惠券类型")
    private String couponType;

    /**
     * 优惠券面值
     */
    @ApiModelProperty("优惠券面值")
    private BigDecimal couponValue ;

    /**
     * 优惠券条件
     */
    @ApiModelProperty("优惠券条件")
    private BigDecimal couponCondition;

    /**
     * 是否可用 true可用 false不可用
     */
    @ApiModelProperty("是否可用")
    private Boolean available;
}
