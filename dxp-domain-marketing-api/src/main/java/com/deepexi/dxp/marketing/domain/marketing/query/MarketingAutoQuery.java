package com.deepexi.dxp.marketing.domain.marketing.query;


import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @desc AutoMarketingTask
 * @Date: Mon Mar 09 15:26:46 CST 2020
 * //@ApiModel(description = "auto_marketing_task")
 */
@Data
@ToString
public class MarketingAutoQuery extends SuperQuery {


    /**
     * 条件触发后时
     */
    private Long executeAfter;


    /**
     * 执行时间单位 0 秒 1 分 2时 3日 4月 5年
     */
    private Integer executeUnit;
    /**  */
    @ApiModelProperty(value = "")
    private Long id;
    /**
     * 自动任务名称
     */
    @ApiModelProperty(value = "自动任务名称")
    private String name;

    /**
     * 自动任务名称-模糊
     */
    @ApiModelProperty(value = "自动任务名称-模糊")
    private String approximateName;

    /**
     * 任务编码(暂未定生成规则)
     */
    @ApiModelProperty(value = "任务编码(暂未定生成规则)")
    private String code;
    /**
     * 任务状态(1:草稿，2:未开始，3:运行中，4:暂停，5:结束)
     */
    @ApiModelProperty(value = "任务状态(1:草稿，2:未开始，3:运行中，4:暂停，5:结束)")
    private Integer status;
    /**
     * 执行时机
     */
    @ApiModelProperty(value = "执行时机")
    private Date executeTime;


    /**
     * 版本号，乐观锁
     */
    @ApiModelProperty(value = "版本号，乐观锁")
    private Long version;
    /**
     * 行动模版ID
     */
    @ApiModelProperty(value = "行动模版ID")
    private Long actionTemplateId;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;
    /**
     * 0 禁用 1 启用
     */
    @ApiModelProperty(value = "0 禁用 1 启用")
    private Integer useStatus;
    /**
     * 0 立即执行 1 延后执行
     */
    @ApiModelProperty(value = "0 立即执行 1 延后执行")
    private Integer executeNow;

    @ApiModelProperty(value = "执行开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startExecuteTime;

    @ApiModelProperty(value = "执行结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endExecuteTime;


    private List<Long> tempIds ;

    private List<Long> ids ;

    /** 触发类型 （0 模型，1 场景 ，2 关联） */
    @ApiModelProperty(value="触发类型 （0 模型，1 场景 ，2 关联）")
    private Integer type;

    /**
     * 模型ID
     */
    @ApiModelProperty(value = "模型ID")
    private Long modelId;
    /**
     * 模型名称
     */
    @ApiModelProperty(value = "模型名称")
    private String modelName;
    /**
     * 模型分类id
     */
    @ApiModelProperty(value = "模型分类id")
    private Long modelTypeId;

    /**
     * 场景id
     */
    @ApiModelProperty(value = "场景id")
    private Long sceneId;
    /**
     * 场景类型id
     */
    @ApiModelProperty(value = "场景类型id")
    private Long sceneTypeId;
    /**
     * 场景名称
     */
    @ApiModelProperty(value = "场景名称")
    private String sceneName;

    /**
     * 发送规则渠道
     */
    @ApiModelProperty(value = "发送规则渠道 1.发送微信  2.发送短信")
    private Integer sendRuleChannel;

    /**
     * 发送规则类型
     */
    @ApiModelProperty(value = "发送规则类型 1.验证码短信,微信模板消息2.通知短信 3.营销短信")
    private Integer sendRuleType;

    /**
     * 发送规则类型
     */
    @ApiModelProperty(value = "资源类别编码 1：优惠券")
    private Integer resourceCategoryCode;

    /**
     * 资源名称
     */
    @ApiModelProperty(value = "资源名称")
    private String resourceName;



}

