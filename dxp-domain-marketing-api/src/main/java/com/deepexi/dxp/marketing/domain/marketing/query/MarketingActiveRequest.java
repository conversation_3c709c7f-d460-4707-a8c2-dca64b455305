package com.deepexi.dxp.marketing.domain.marketing.query;

import com.deepexi.dxp.marketing.common.base.request.SuperRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @description：主动营销任务请求对象
 * @author: cheng<PERSON><PERSON>
 * @version: 1.0.0
 * @date 2021-03-17 12:00
 */
@Data
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
public class MarketingActiveRequest extends SuperRequest implements Serializable {

    @ApiModelProperty(value = "主动营销任务id", required = true)
    private Long id;

}
