package com.deepexi.dxp.marketing.api.specify;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.ActivityTargetQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.ActivityTargetRequestDTO;
import com.deepexi.dxp.marketing.domain.marketing.response.ActivityTargetResponseDTO;

import com.deepexi.util.pageHelper.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.validation.Valid;

@Validated
@RequestMapping("/marketing-api/v1/activity-target")
public interface ActivityTargetApi {

	@PostMapping("/save")
	Data<Boolean> save(@RequestBody @Valid ActivityTargetRequestDTO form);

	@GetMapping("/getPage")
	Data<PageBean<ActivityTargetResponseDTO>> getPage(ActivityTargetQuery query);
}
