package com.deepexi.dxp.marketing.common.base.request;

import com.deepexi.util.domain.request.BaseExtRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description：
 * @author：<PERSON><PERSON><PERSON><PERSON>
 * @version：1.0.0
 * @date：2021-03-31 2:15 下午
 */
@Data
public class SuperRequestExt extends BaseExtRequest {
    /**
     * APP_ID
     */
    @ApiModelProperty(value = "APP_ID", hidden = true)
    private Long appId;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID", hidden = true)
    private String tenantId;

}
