package com.deepexi.dxp.marketing.domain.marketing.query.specify;


import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@ApiModel(value = "ActivityOrder查询对象", description = "ActivityOrder查询对象")
public class ActivityOrderQuery  extends SuperQuery {


    @ApiModelProperty(value="投放渠道")
    private String deliveryChannel;

    @ApiModelProperty(value = "昵称/姓名/手机号码查询字段")
    private String code;
    /**
    *支付时间
    **/
    @ApiModelProperty(value = "支付时间")
    private String payTime;

    @ApiModelProperty(value = "用户id")
    private String userId;

    @ApiModelProperty(value = "活动id")
    private Long activityId;

    @ApiModelProperty(value = "资源id")
    private Long hisResourceId;

    @ApiModelProperty(value = "订单状态")
    private Integer status;

    @ApiModelProperty(value = "手机号")
    private String phone;

    /**
     * 支付平台订单号(返回的)
     */
    @ApiModelProperty(value = "支付平台订单号")
    private String wxOrderNo;
}