package com.deepexi.dxp.marketing.api.promotion;

import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.CouponFlagRequest;
import com.deepexi.dxp.marketing.domain.promotion.dto.coupon.PromotionCouponDetailPostResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.coupon.PromotionCouponListPostResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.query.coupon.PromotionCouponQuery;
import com.deepexi.dxp.marketing.domain.promotion.request.coupon.PromotionCouponCreatePostRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.coupon.PromotionCouponUpdatePostRequest;
import com.deepexi.util.config.Payload;
import com.deepexi.util.pageHelper.PageBean;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR> ming.zhong
 * @date created in 17:18 2019/12/4
 */
@RequestMapping("/middle-api/v1/promotion-coupon")
public interface PromotionCouponMiddleApi {

    @GetMapping("/list")
    Payload<List<PromotionCouponListPostResponseDTO>> findAll(PromotionCouponQuery query);

    @GetMapping("/page")
    Payload<PageBean<PromotionCouponListPostResponseDTO>> page(PromotionCouponQuery query);

    @GetMapping("/detail")
    Payload<PromotionCouponDetailPostResponseDTO> detail(@RequestParam Long id);

    @PutMapping("/update")
    Payload<Boolean> update(@RequestBody PromotionCouponUpdatePostRequest dto);

    @PostMapping("/create")
    Payload<Boolean> create(@Valid @RequestBody PromotionCouponCreatePostRequest dto);

    @PostMapping("/create-flag")
    Boolean createFlag(@Valid @RequestBody CouponFlagRequest dto);

    @DeleteMapping("/deleted")
    Payload<Boolean> deleted(@RequestBody List<Long> dtoList);

    @PostMapping("/save-or-create-batch")
    Payload<Boolean> saveOrCreateBatch(@Valid @RequestBody List<PromotionCouponCreatePostRequest> dtoList);
}
