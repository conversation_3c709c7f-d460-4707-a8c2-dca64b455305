package com.deepexi.dxp.marketing.api.promotion;


import com.deepexi.dxp.marketing.domain.promotion.request.activity.OrderLockRequest;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.OrderEditResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.OrderLockVO;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.OrderUsingPostRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.ReturnSalesRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.calculate.ActivityOrderParamsRequest;
import com.deepexi.util.config.Payload;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.validation.Valid;
import java.util.List;

/**
 * 促销中心对接订单下单流程统一接口管理
 *
 * <AUTHOR> x<PERSON><PERSON><PERSON>.yao
 * @date 2019/12/13 17:04
 */
@RequestMapping("/middle-api/v1/promotion-activity/calculate/order")
public interface PromotionActivityOrderMiddleApi {

    /**
     * 下单校验 优惠计算
     *
     * @param activityRequestParams 入参
     * @return 出参
     */
    @PostMapping("/down-order-calculate")
    Payload<OrderEditResponseDTO> downOrderCalculate(@RequestBody @Valid ActivityOrderParamsRequest activityRequestParams);

    /**
     * 下单锁定
     *
     * @param dto 入参
     * @return
     */
    @PostMapping("/lock")
    Payload<OrderLockVO> lock(@RequestBody @Valid OrderLockRequest dto);

    /**
     * 使用接口
     *
     * @param dto
     * @return
     */
    @PostMapping("/using")
    Payload<Boolean> using(@RequestBody OrderUsingPostRequest dto);

    /**
     * 超时取消
     *
     * @param activityIds
     * @return
     */
    @PostMapping("/cancel")
    Payload<Boolean> cancel(@RequestBody List<String> activityIds);


    /**
     * 退货
     *
     * @param returnSalesRequest
     * @return
     */
    @PostMapping("/return-sales")
    Payload<Boolean> returnSales(@RequestBody List<ReturnSalesRequest> returnSalesRequest);


}
