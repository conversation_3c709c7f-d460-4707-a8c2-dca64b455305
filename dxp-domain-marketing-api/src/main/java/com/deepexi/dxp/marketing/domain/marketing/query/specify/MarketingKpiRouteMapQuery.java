package com.deepexi.dxp.marketing.domain.marketing.query.specify;


import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 指标路径图Query
 * @Author: HuangBo.
 * @Date: 2020/6/18 11:56
 */

@Data
public class MarketingKpiRouteMapQuery extends SuperQuery {

    /**
     * 指标组名称
     */
    @ApiModelProperty(value = "指标路径图名称")
    private String name;

    /**
     * 状态(1:启用; 2:禁用)
     */
    @ApiModelProperty(value = "状态(1:启用; 2:禁用)")
    private Integer status;

    /**
     * 指标组定义描述
     */
    @ApiModelProperty(value = "指标路径图描述")
    private String remark;

}
