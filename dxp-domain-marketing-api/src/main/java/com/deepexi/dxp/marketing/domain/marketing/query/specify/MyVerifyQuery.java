package com.deepexi.dxp.marketing.domain.marketing.query.specify;

import com.deepexi.util.pojo.AbstractObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@EqualsAndHashCode(callSuper = false)
@ApiModel
@Data
public class MyVerifyQuery extends AbstractObject implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID")
    private Long activityId;


    /**
     * 查询的页码(-1 表示查询所有数据)
     */
    @ApiModelProperty(value = "查询的页码(-1 表示查询所有数据)")
    @Min(value = -1, message = "page不能小于{value}")
    private Integer page = 1;

    /**
     * 每页条数
     */
    @ApiModelProperty(value = "每页条数")
    @Min(value = 1, message = "size必须大于0")
    private Integer size = 10;


    /**
     * 退款状态 0、未退款 1、已退款
     */
    @ApiModelProperty(value = "退款状态 0、未退款 1、已退款")
    private Integer refundStatus;

    /**
     * 是否付费 0否 1是
     */
    @ApiModelProperty(value = "是否付费 0否 1是")
    private Integer isPay;

    /**
     * 核销状态 0、待核销 1、已核销 2、已过期 3、已失效
     */
    @ApiModelProperty(value = "核销状态 0、待核销 1、已核销 2、已过期 3、已失效",required = true)
    @NotNull(message = "核销状态不能为空")
    private Integer verifyStatus;



    @ApiModelProperty(value = "userId")
    //@NotNull(message = "用户id不能为空")
    private String userId;

    @ApiModelProperty(value = "手机号")
    @NotNull(message = "手机号不能为空")
    private String phone;
}
