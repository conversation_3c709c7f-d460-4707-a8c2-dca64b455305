package com.deepexi.dxp.marketing.domain.marketing.query.specify;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 用户项目查询请求
 * <AUTHOR>
 */
@ApiModel(value = "用户项目查询入参", description = "用户项目查询入参")
@Data
public class UserProjectQuery {

    @ApiModelProperty("页码")
    private Integer pageNumber = 1;

    @ApiModelProperty("页数大小")
    private Integer pageSize = 10;

    @ApiModelProperty("用户ID")
    @NotNull(message = "用户ID不能为空")
    private String userId;

    @ApiModelProperty("项目ID")
    private String projectId;

    @ApiModelProperty("项目名称（支持模糊查询）")
    private String projectName;

    @ApiModelProperty("搜索类型，0：项目名称搜索；1：公司名称搜索")
    private Integer searchType;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "区域名称（支持模糊查询）")
    private String areaName;

    @ApiModelProperty(value = "真实区域名称（支持模糊查询）")
    private String realAreaName;

    @ApiModelProperty(value = "区域Id")
    private String areaId;

    @ApiModelProperty(value = "真实区域Id")
    private String realAreaId;

    @ApiModelProperty(value = "真实城市名称（支持模糊查询）")
    private String realCityName;

    @ApiModelProperty(value = "活动Id")
    private Long activityId;
}
