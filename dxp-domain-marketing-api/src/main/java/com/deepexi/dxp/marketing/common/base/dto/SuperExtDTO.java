package com.deepexi.dxp.marketing.common.base.dto;

import com.deepexi.util.domain.dto.BaseExtDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * @description：
 * @author：zhoujian
 * @version：1.0.0
 * @date：2021-03-31 2:15 下午
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SuperExtDTO extends BaseExtDTO implements Serializable {

    /**
     * 自增主键
     */
    @ApiModelProperty("自增主键")
    private Long id;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 乐观锁
     */
    @ApiModelProperty("乐观锁")
    private Long version;

    /**
     * 逻辑删除
     * */
    @ApiModelProperty(value = "逻辑删除")
    private Integer deleted = 0;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createdTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updatedTime;


    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updatedBy;
}
