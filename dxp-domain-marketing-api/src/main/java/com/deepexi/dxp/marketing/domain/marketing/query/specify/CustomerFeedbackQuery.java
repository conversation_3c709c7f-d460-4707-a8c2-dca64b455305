package com.deepexi.dxp.marketing.domain.marketing.query.specify;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * CustomerFeedbackDTO入参对象
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@ApiModel(value = "CustomerFeedback查询对象", description = "CustomerFeedback查询对象")
public class CustomerFeedbackQuery extends SuperQuery {

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;
    /**
     *项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "用户ID集合")
    private List<String> userIds;

    @ApiModelProperty(value = "用户手机集合")
    private List<String> phoneList;

    /**
     * 微信昵称
     */
    @ApiModelProperty(value = "微信昵称")
    private String nickName;
    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;
    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String phone;
    /**
     *资源奖品ID
     */
    @ApiModelProperty(value = "资源奖品ID")
    private Long resourceId;
    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID")
    private Long activityId;

    @ApiModelProperty(value = "多字段搜索条件")
    private String searchText;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String endTime;

    @ApiModelProperty(value = "活动类型:0、表单活动,1、其它活动")
    private Integer activityType;
}
