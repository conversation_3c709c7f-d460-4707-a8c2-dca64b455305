package com.deepexi.dxp.marketing.common.base.utils;


import com.deepexi.util.exception.ApplicationException;
import com.deepexi.util.exception.CommonExceptionCode;
import com.deepexi.util.pojo.AbstractObject;
import com.deepexi.util.pojo.BeanCopierUtils;
import com.deepexi.util.pojo.CloneDirection;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * Created by donh on 2018/6/12.
 */
public class Data<T> implements Serializable {
    private static final long serialVersionUID = -1549643581827130116L;
    private T data;
    private String code = ResultConstant.SUCCESS.getCode();
    private String msg = ResultConstant.SUCCESS.getMsg();

    /**
     * 成功，不用返回数据
     * @return
     */
    public Data() {
    }

    /**
     * 成功，返回数据
     * @param data
     * @return
     */
    public Data(T data) {
        this.data = data;
    }

    /**
     * 失败，自定义错误码
     * @param code
     * @param msg
     */
    public Data(String code, String msg) {
        this.code =code;
        this.msg = msg;
    }

    /**
     * 自定义返回结果
     * @param data
     * @param code
     * @param msg
     */
    public Data(T data, String code, String msg) {
        this.data = data;
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public T getdata() {
        return data;
    }

    /**
     * 判断是否成功返回
     * @return
     */
    public boolean success(){
        if (getCode().equals(CommonExceptionCode.SUCCESS)) {
            return true;
        }else{
            return false;
        }
    }

    /**
     * 返回解析报文
     * @return
     */
    public  T resolverdata(){
        if (success()) {
            return data;
        }else{
            throw new ApplicationException(getCode(),getMsg());
        }
    }

    /**
     * 对象转换
     * @param targetClazz 目标
     * @return
     */
    public <T> T copydata(Class<T> targetClazz){
        if (!success()) {
            throw new ApplicationException(getCode(),getMsg());
        }
        if (data==null) {
            return null;
        }
        if (data instanceof AbstractObject) {
            AbstractObject abstractObject  = (AbstractObject) data;
           return abstractObject.clone(targetClazz, CloneDirection.FORWARD) ;
        }else{
            T target = null;
            try {
                target = targetClazz.newInstance();
            } catch (Exception e) {
                throw new ApplicationException("创建实例错误！",e);
            }
            BeanCopierUtils.copyProperties(data,target);
            return target;
        }
    }


    /**
     * 构建通用的异常的返回（失败的结果返回）
     *
     * @param e
     */
    private Data(Throwable e) {
        ResultConstant resultConstant = ResultConstant.ERROR;
        code = resultConstant.getCode();
        msg = resultConstant.getMsg();
        if (e != null && !StringUtils.isEmpty(e.toString())) {
            msg = "系统异常：" + e.toString();
        }
    }

//    public static void main(String[] args){
//        data<Object> objectdata = new data<>(ResultConstant.ERROR);
//        System.out.println("");
//    }

}