package com.deepexi.dxp.marketing.domain.marketing.query.specify;

import lombok.Data;

/**
 * 事件分析报告请求参数
 * <AUTHOR>
 */
@Data
public class EventsReportQuery {

    private String sqlIn;

    private String activityId;

    private String eventName;

    /**
     * 日期类型（1按日，2按月）
     **/
    private Integer dateType;

    /**
     *时间
     **/
    private String date;

    /**
     * 活动开始时间
     */
    private String startTime;

    /**
     * 活动结束时间
     */
    private String endTime;
}
