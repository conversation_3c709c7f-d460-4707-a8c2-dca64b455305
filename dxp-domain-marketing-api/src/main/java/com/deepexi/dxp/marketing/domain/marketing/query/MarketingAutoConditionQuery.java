package com.deepexi.dxp.marketing.domain.marketing.query;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @desc MarketingTaskAutoConditionQuery
 * @Date: Mon Mar 09 15:26:46 CST 2020
 * //@ApiModel(description = "MarketingTaskAutoConditionQuery")
 */
@Data
@ToString
@ApiModel
public class MarketingAutoConditionQuery extends SuperQuery {


    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "主键id列表")
    private List<Long> ids;

    /**
     * 触发类型 1-场景
     */
    @ApiModelProperty(value = "触发类型 1-场景")
    private Integer type;

    /**
     * 自动任务id
     */
    @ApiModelProperty(value = "自动任务id")
    private Long autoId;

    /**
     * 关联场景id等
     */
    @ApiModelProperty(value = "关联场景id等")
    private Long itemId;
}

