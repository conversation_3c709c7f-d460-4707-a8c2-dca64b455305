package com.deepexi.dxp.marketing.domain.marketing.query;

import com.deepexi.dxp.marketing.common.base.request.SuperRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description：主动营销任务检查名称请求对象
 * @author: ch<PERSON><PERSON><PERSON>
 * @version: 1.0.0
 * @date 2021-03-17 12:00
 */
@Data
@ApiModel
public class MarketActiveCheckNameRequest extends SuperRequest {

    @ApiModelProperty(value = "营销任务名称", required = true)
    private String name;

    @ApiModelProperty(value = "营销任务旧名称")
    private String oldName;
}
