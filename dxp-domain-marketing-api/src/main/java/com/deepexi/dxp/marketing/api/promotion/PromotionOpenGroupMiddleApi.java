package com.deepexi.dxp.marketing.api.promotion;

import com.deepexi.dxp.marketing.domain.promotion.dto.opengroup.*;
import com.deepexi.dxp.marketing.domain.promotion.query.opengroup.OpenGroupCommodityListGetQuery;
import com.deepexi.dxp.marketing.domain.promotion.query.opengroup.OpenGroupMineListGetQuery;
import com.deepexi.dxp.marketing.domain.promotion.query.opengroup.ValidateOpenGroupLimitQuery;
import com.deepexi.dxp.marketing.domain.promotion.query.opengroup.WaitCompleteGroupsPageQuery;
import com.deepexi.dxp.marketing.domain.promotion.request.opengroup.*;
import com.deepexi.util.config.Payload;
import com.deepexi.util.pageHelper.PageBean;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> ming.zhong
 * @date created in 17:33 2019/12/4
 */
@RequestMapping("/middle-api/v1/promotion-open-group")
public interface PromotionOpenGroupMiddleApi {

    @PostMapping("/order-before-validate")
    Payload<OpenGroupOrderVO> orderBeforeValidate(@RequestBody OpenGroupOrderRequest openGroupOrderRequest);

    @PostMapping("/order-create-group-lock-inventory")
    Payload<OpenGroupOrderVO> orderCreateGroupLockInventory(@RequestBody OpenGroupOrderRequest openGroupOrderRequest);

    @PostMapping("/order-close-cancel-inventory")
    Payload<OpenGroupOrderVO> orderCloseCancelInventory(@RequestBody OpenGroupOrderRequest openGroupOrderRequest);

    @PostMapping("/order-pay-success")
    Payload<OpenGroupOrderVO> orderPaySuccess(@RequestBody OpenGroupOrderRequest openGroupOrderRequest);


    @GetMapping("/open-group-commodity-list")
    Payload<PageBean<OpenGroupCommodityListResponseDTO>> openGroupCommodityList(OpenGroupCommodityListGetQuery dto);

    @PostMapping("/validate/open-group-limit")
    Payload<Boolean> validateOpenGroupLimit(@RequestBody ValidateOpenGroupLimitQuery validateOpenGroupLimitQuery);

    @GetMapping("/wait-complete-groups")
    Payload<PageBean<PromotionOpenGroupDetailResponseDTO>> waitCompleteGroups(WaitCompleteGroupsPageQuery waitCompleteGroupsPageQuery);

    @GetMapping("/get-open-group-commodity-info-by-activity-id/{activityId}")
    Payload<OpenGroupCommodityListResponseDTO> getOpenGroupCommodityInfoByActivityId(@PathVariable(value = "activityId") Long activityId);

    @GetMapping("/mine/list")
    Payload<PageBean<OpenGroupMineListResponseDTO>> mimeList(OpenGroupMineListGetQuery dto);

    @GetMapping("find-open-group-by-order-code/{orderCode}")
    Payload<PromotionOpenGroupListPostResponseDTO> findOpenGroupByOrderCode(@PathVariable(value = "orderCode") String orderCode);

    @GetMapping("/{activity-groupid}")
    Payload<PromotionOpenGroupDetailResponseDTO> findOpenGroupById(@PathVariable(value = "activityGroupId") Long activityGroupId);


    @PostMapping("/timing-close-overtime")
    Payload<List<PromotionOpenGroupBaseResponseDTO>> timingCloseOvertime();

    @PostMapping("/close-open-group-by-activity-id/{activityId}")
    Payload<List<PromotionOpenGroupBaseResponseDTO>> closeOpenGroupByActivityId(@PathVariable(value = "activityId") Long activityId);

}
