package com.deepexi.dxp.marketing.domain.marketing.query.specify;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import java.util.List;

@EqualsAndHashCode(callSuper = false)
@ApiModel
@Data
public class VerifyPageQuery {

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID")
    private Long activityId;

    /**
     * 奖品名称
     */
    @ApiModelProperty(value = "奖品名称")
    private String resourceName;


    /**
     * 查询的页码(-1 表示查询所有数据)
     */
    @ApiModelProperty(value = "查询的页码(-1 表示查询所有数据)")
    @Min(value = -1, message = "page不能小于{value}")
    private Integer page = 1;

    /**
     * 每页条数
     */
    @ApiModelProperty(value = "每页条数")
    @Min(value = 1, message = "size必须大于0")
    private Integer size = 10;

    @ApiModelProperty(value = "昵称/手机号码/资源/核销码/核销人/退款人模糊查询")
    private String likeParams;


    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 项目名称集合
     */
    @ApiModelProperty(value = "项目名称")
    private List<String> projectNameList;

    /**
     * 退款状态 0、未退款 1、已退款
     */
    @ApiModelProperty(value = "退款状态 0、未退款 1、已退款")
    private Integer refundStatus;

    /**
     * 是否付费 0否 1是
     */
    @ApiModelProperty(value = "是否付费 0否 1是")
    private Integer isPay;

    /**
     * 核销状态 0、待核销 1、已核销 2、已过期 3、已失效
     */
    @ApiModelProperty(value = "核销状态 0、待核销 1、已核销 2、已过期 3、已失效")
    private Integer verifyStatus;


    /**
     * 核销时间 start
     */
    @ApiModelProperty(value = "核销时间 start")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String verifyStartTime;

    /**
     * 核销时间 end
     */
    @ApiModelProperty(value = "核销时间 end")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String verifyEndTime;


    /**
     * 退款时间 start
     */
    @ApiModelProperty(value = "退款时间 start")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String refundStartTime;


    /**
     * 退款时间 end
     */
    @ApiModelProperty(value = "退款时间 end")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String refundEndTime;


    @ApiModelProperty(value = "核销类型：0、优惠券 1、资源 2、激励类型")
    //@NotNull(message = "核销类型不能为空")
    private Integer verifyType;

    @ApiModelProperty(value = "核销类型：0、优惠券 1、资源 2、激励类型")
    private String verifyTypeList;//如1,2

    @ApiModelProperty(value = "userId")
    private String userId;

    @ApiModelProperty(value = "手机号")
    private String phone;

    /**
     * 2、已过期 3、已失效  1、已退款 列入已失效
     */
    @ApiModelProperty(value = "是否已失效")
    private Integer invalidType;

    @ApiModelProperty(value = "核销人")
    private String verifyBy;

    private List<Integer> verifyTypeLists;

    @ApiModelProperty(value = "活动ID")
    private List<Long> activityIdList;
}
