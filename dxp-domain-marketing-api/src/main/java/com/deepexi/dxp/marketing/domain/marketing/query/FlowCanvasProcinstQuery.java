package com.deepexi.dxp.marketing.domain.marketing.query;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 流程实例查询封装对象
 * <AUTHOR>
 * @Date: 2020/7/25 10:37
 */
@Data
public class FlowCanvasProcinstQuery extends SuperQuery {
    /**
     * 实例id列表
     */
    private List<Long> ids;
    /**
     * 流程画布定义id
     */
    private Long flowId;

    /**
     * 流程画布定义CODE,用于根据版本区分的同一个画布
     */
    private String flowCode;

    /**
     * 执行开始时间 (单次和触发的存定时执行时间，周期重复的存定义的开始时间)
     */
    private Date execStartTime;

    private Date execEndTime;

    /**
     * 任务状态(1:运行中，2:失败，3:已暂停，4:已结束)
     */
    private Integer status;

    /**
     * 任务状态集合
     */
    private List<Integer> statusList;
}
