package com.deepexi.dxp.marketing.api.promotion;

import com.deepexi.dxp.marketing.domain.promotion.dto.coupon.PromotionCouponLoggerDetailPostResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.coupon.PromotionCouponLoggerListPostResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.query.coupon.CouponStatisticsQuery;
import com.deepexi.dxp.marketing.domain.promotion.query.coupon.PromotionCouponLoggerQuery;
import com.deepexi.dxp.marketing.domain.promotion.request.coupon.PromotionCouponLoggerCreatePostRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.coupon.PromotionCouponLoggerUpdatePostRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.coupon.UpdateCouponLoggerStatusBatchRequest;
import com.deepexi.util.config.Payload;
import com.deepexi.util.pageHelper.PageBean;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> ming.zhong
 * @date created in 17:07 2019/12/4
 */
@RequestMapping("/middle-api/v1/promotion-coupon-logger")
public interface PromotionCouponLoggerMiddleApi {

    @PostMapping("/list")
    Payload<List<PromotionCouponLoggerListPostResponseDTO>> findAll(@RequestBody PromotionCouponLoggerQuery query);

    @PostMapping("/page-list")
    Payload<PageBean<PromotionCouponLoggerListPostResponseDTO>> pageList(@RequestBody PromotionCouponLoggerQuery query);

    @PostMapping("/detail")
    Payload<PromotionCouponLoggerDetailPostResponseDTO> detail(Long id);

    @PostMapping("/update")
    Payload<Boolean> update(@RequestBody PromotionCouponLoggerUpdatePostRequest dto);

    @PostMapping("/create")
    Payload<PromotionCouponLoggerDetailPostResponseDTO> create(@RequestBody PromotionCouponLoggerCreatePostRequest dto);

    @PostMapping("/create-batchs")
    Payload<Boolean> createBatchs(@RequestBody List<PromotionCouponLoggerCreatePostRequest> dtoList);

    @PostMapping("/deleted")
    Payload<Boolean> deleted(@RequestBody List<Long> ids);

    @PostMapping("/update-status-batch")
    Payload<Boolean> updateStatusBatch(@RequestBody UpdateCouponLoggerStatusBatchRequest requestDTO);

    @GetMapping("/statistics")
    Payload<Long> couponStatistics(CouponStatisticsQuery query);

}
