//package com.deepexi.dxp.marketing.api.promotion;
//
//import com.deepexi.dxp.marketing.domain.promotion.dto.activity.PromotionActivityCouponVO;
//import com.deepexi.dxp.marketing.domain.promotion.dto.activity.PromotionActivityListPostVO;
//import com.deepexi.dxp.marketing.domain.promotion.dto.activity.PromotionActivityStatusUpdateRequest;
//import com.deepexi.dxp.marketing.domain.promotion.dto.activity.PromotionSeckillActivityVO;
//import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityCouponQuery;
//import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityQuery;
//import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionSeckillActivityQuery;
//import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityCreatePostRequest;
//import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityUpdatePostRequest;
//import com.deepexi.util.config.Payload;
//import com.deepexi.util.pageHelper.PageBean;
//import org.springframework.web.bind.annotation.*;
//
//import javax.validation.Valid;
//import java.util.List;
//
///**
// * <AUTHOR> xinjian.yao
// * @date 2019/12/4 16:05
// */
//@RequestMapping("/middle-api/v1/promotion-activity")
//public interface PromotionActivityMiddleApi {
//
//    @GetMapping("/page")
//    Payload<PageBean<PromotionActivityListPostVO>> findPage(PromotionActivityQuery dto);
//
//    @GetMapping("/list")
//    Payload<List<PromotionActivityListPostVO>> findAll(PromotionActivityQuery dto);
//
//    @GetMapping("/find-activity-by-id")
//    Payload<PromotionActivityListPostVO> findActivityById(@RequestParam Long id);
//
//    @PostMapping("/create")
//    Payload<Long> create(@Valid @RequestBody PromotionActivityCreatePostRequest dto);
//
//    @PutMapping("/update")
//    Payload<Boolean> update(@RequestParam Long id, @RequestBody PromotionActivityUpdatePostRequest dto);
//
//    @DeleteMapping("/delete")
//    Payload<Boolean> delete(@RequestBody List<Long> ids);
//
//    @PutMapping("/release")
//    Payload<Boolean> release(@RequestParam Long id);
//
//    @PutMapping("/stop")
//    Payload<Boolean> stop(@RequestParam Long id);
//
//    @PutMapping("/update-Activity-Status")
//    Payload<Long> updateActivityStatus(@RequestBody @Valid PromotionActivityStatusUpdateRequest dto);
//
//    @PostMapping("/seckill/page")
//    Payload<PageBean<PromotionSeckillActivityVO>> findSeckillPage(@RequestBody @Valid PromotionSeckillActivityQuery dto);
//
//    @PostMapping("/activity-coupon/page")
//    Payload<PageBean<PromotionActivityCouponVO>> findActivityCouponPage(@RequestBody @Valid PromotionActivityCouponQuery query);
//}
