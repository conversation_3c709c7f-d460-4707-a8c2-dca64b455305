package com.deepexi.dxp.marketing.domain.specify.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.deepexi.dxp.marketing.common.base.dto.SuperDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/6/3 19:36
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "promotion_activity_form_feedback")
public class ActivityFormFeedbackDTO extends SuperDTO {
    /**
     * 租户id
     */
    @TableField(value = "`tenant_id`")
    private String tenantId;

    /**
     * 接入方id
     */
    @TableField(value = "`app_id`")
    /**
     *
     */
    private Long appId;
    /**
     * 项目ID
     */
    @TableField(value = "`project_id`")
    private String projectId;
    /**
     *项目名称
     */
    @TableField(value = "`project_name`")
    private String projectName;
    /**
     * 用户表单数据
     */
    @TableField(
            typeHandler = FastjsonTypeHandler.class
    )
    private Map<String,Object> limits;
    /**
     * 用户ID
     */
    @TableField(value = "`user_id`")
    private String userId;
    /**
     * 微信昵称
     */
    @TableField(value = "`nick_name`")
    private String nickName;
    /**
     * 用户名称
     */
    @TableField(value = "`user_name`")
    private String userName;
    /**
     * 手机号
     */
    @TableField(value = "`phone`")
    private String phone;
    /**
     * 活动ID
     */
    @TableField(value = "`activity_id`")
    private Long activityId;
    /**
     *资源奖品ID
     */
    @TableField(value = "`resource_id`")
    private Long resourceId;
    /**
     * 0、表单活动,1、其它活动
     */
    @TableField(value = "`activity_type`")
    private Integer activityType;
    /**
     * unionId
     */
    @TableField(value = "`union_id`")
    private String unionId;
    /**
     * 0、微信小程序 1、H5  2、抖音
     */
    @TableField(value = "`type`")
    private Integer type;
}
