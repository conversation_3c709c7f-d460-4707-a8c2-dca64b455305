package com.deepexi.dxp.marketing.domain.marketing.query;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 流程画布定义列表查询参数对象
 * @Author: HuangBo.
 * @Date: 2020/7/20 10:58
 */

@Data
@Api(value = "流程画布定义Query")
public class FlowCanvasProcDefQuery extends SuperQuery {

    @ApiModelProperty(value = "主键id列表")
    private List<Long> ids;

    /**
     *  画布流程定义
     */
    public final static Integer FLOW_CANVAS_TYPE_PROC_DEF = 1;
    /**
     *  画布模板
     */
    public final static Integer FLOW_CANVAS_TYPE_TEMPLATE = 2;

    @ApiModelProperty(value = "流程画布编码，不同版本号的相同画布具有同样的Code")
    private String code;

    @ApiModelProperty(value = "流程画布名称")
    private String name;
    @ApiModelProperty(value = "流程画布类型，1:流程画布 2:画布模板")
    private Integer type;

    @ApiModelProperty(value = "执行方式， once:单次 fixed：周期重复 event：事件触发")
    private String executeType;

    @ApiModelProperty(value = "运行方式， 立即执行:right_now  、定时执行:scheduled  、指定时间重复执行:fixed_at_spec_time  、" +
            "每隔一段时间重复:fixed_at_spec_period  、事件触发:event")
    private String runType;

    @ApiModelProperty(value = "画布状态(1:未发布，2:运行中，3:暂停中，4:已终止)")
    private Integer status;

    @ApiModelProperty(value = "画布状态集合")
    private List<Integer> statusList;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "查询指定属性集合")
    private String queryFieldName;
}
