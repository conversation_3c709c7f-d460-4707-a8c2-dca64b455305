package com.deepexi.dxp.marketing.domain.marketing.query.specify;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.MarketingKpiDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 原生指标和派生指标VO
 * @Author: HuangBo.
 * @Date: 2020/5/15 15:51
 */

@Data
@Api(value = "指标查询Query")
public class MarketingKpiQuery extends SuperQuery {

    /**
     * 原生指标
     */
    public final static Integer ORIGINAL_TYPE = MarketingKpiDTO.ORIGINAL_TYPE;
    /**
     * 派生指标
     */
    public final static Integer DERIVATIVE_TYPE = MarketingKpiDTO.DERIVATIVE_TYPE;


    @ApiModelProperty(value = "编码")
    private String code;

    @ApiModelProperty(value = "指标类型(1:原生指标; 2:派生指标)")
    private Integer type;

    @ApiModelProperty(value = "指标名称")
    private String name;


    @ApiModelProperty(value = "指标单位名称")
    private String unitName;

    @ApiModelProperty(value = "1:启用; 2:禁用")
    private Integer status;

    @ApiModelProperty(value = "更新频率")
    private String frequency;

    @ApiModelProperty(value = "查询指定属性集合")
    private String queryFieldName;

    @ApiModelProperty(value = "活动目标类型(1:营收,2:商品,3:用户)")
    private Integer purposeType;

}
