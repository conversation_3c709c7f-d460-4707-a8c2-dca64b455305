package com.deepexi.dxp.marketing.domain.marketing.query;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * 资源备份Query
 *
 * <AUTHOR>
 * @Date 2020/4/10
 */
@Data
@ApiModel
public class ThirdResourcesQuery extends SuperQuery {

    /**
     * 任务ID
     */
    @ApiModelProperty(value = "任务ID")
    private Long taskId;

    /**
     * 渠道
     */
    @ApiModelProperty(value = "渠道")
    private String resourceChannel;

    /**
     * 资源ID
     */
    @ApiModelProperty(value = "资源ID")
    private String resourceId;

    /**
     * 资源id列表
     */
    private List<String> resourceIds;

    /**
     * 活动名称
     */
    @ApiModelProperty(value = "`活动名称`")
    private String resourceName;
    /**
     * 活动类型ID
     */
    @ApiModelProperty(value = "`活动类型ID`")
    private String resourceTypeId;
    /**
     * 活动类型名称
     */
    @ApiModelProperty(value = "`活动类型名称`")
    private String resourceTypeName;

    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "`活动开始时间`")
    private Date activityStartTime;
    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "`活动结束时间`")
    private Date activityEndTime;
    /**
     * 活动状态：0=草稿，1=未开始，2=进行中，3=已完成，4=已终止
     */
    @ApiModelProperty(value = "`活动状态：0=草稿，1=未开始，2=进行中，3=已完成，4=已终止`")
    private Integer activityStatus;

    /**
     * 资源url
     */
    @ApiModelProperty(value = "`资源url`")
    private String resourceDetailUrl;

}
