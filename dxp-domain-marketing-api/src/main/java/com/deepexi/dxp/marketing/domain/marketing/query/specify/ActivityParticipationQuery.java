package com.deepexi.dxp.marketing.domain.marketing.query.specify;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;


@EqualsAndHashCode(callSuper = false)
@ApiModel
@Data
public class ActivityParticipationQuery {

    /**
     * 活动id
     */
    @ApiModelProperty(value = "活动id")
    private Long activityId;


    @ApiModelProperty(value = "活动idList")
    private List<Long> activityIdList;

    @ApiModelProperty(value = "推广类型，0：活动推广；1：活动专题推广")
    @NotNull(message = "推广类型不能为空")
    private Integer promotionType;

    @ApiModelProperty(value = "区域idList")
    private List<String> areaIdList;


    @ApiModelProperty(value = "城市idList")
    private List<String> cityIdList;


    @ApiModelProperty(value = "查询下拉类型:0区域,1城市,2项目")
    @NotNull(message = "下拉列表类型不能为空")
    private Integer selectType;
}
