package com.deepexi.dxp.marketing.domain.marketing.query.specify;

import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 使用优惠券参与活动明细
 */
@EqualsAndHashCode(callSuper = false)
@ApiModel
@Data
public class PartakeLogQuery extends AbstractObject {
    /**
     * 搜索关键字
     */
    @ApiModelProperty(value = "搜索关键字：订单号/昵称/手机号")
    @Length(max = 20, message = "最多输入20个字符")
    private String searchText;

    /**
     * 中奖结果
     */
    @ApiModelProperty(value = "中奖结果")
    private String prizeResult;

    @ApiModelProperty(value = "用户id")
    private String userId;

    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID",required = true)
    @NotNull(message = "活动ID不能为空")
    private Long activityId;

    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID")
    @NotNull(message = "活动类型不能为空")
    private Long paTemplateId;

    /**
     * 资源ID
     */
    @ApiModelProperty(value = "资源ID")
    private Long resourceId;

    /**
     * 查询的页码(-1 表示查询所有数据)
     */
    @ApiModelProperty(value = "查询的页码(-1 表示查询所有数据)")
    @Min(value = -1, message = "page不能小于{value}")
    private Integer page = 1;

    /**
     * 每页条数
     */
    @ApiModelProperty(value = "每页条数")
    @Min(value = 1, message = "size必须大于0")
    private Integer size = 10;

    @ApiModelProperty(value="支付开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String startTime;


    @ApiModelProperty(value="支付结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String endTime;

    @ApiModelProperty(value="not中奖结果")
    private String prizeResultNot;

    @ApiModelProperty(value = "助力砍价状态 0、助力中（砍价中），1、助力成功（砍价成功）,2、助力失败（砍价失败）")
    private Integer fissonStatus;


    @ApiModelProperty(value = "所在地区")
    private String area;

    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "用户昵称")
    private String nickName;
}
