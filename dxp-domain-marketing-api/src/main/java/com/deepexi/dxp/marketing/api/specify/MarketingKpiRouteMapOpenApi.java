package com.deepexi.dxp.marketing.api.specify;
import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.MarketingKpiRouteMapQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.MarketingKpiCheckNameRequest;
import com.deepexi.dxp.marketing.domain.marketing.response.MarketingKpiRouteMapVO;
import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.List;

/**
 * 指标路径图Controller
 * @Author: HuangBo.
 * @Date: 2020/6/18 15:48
 */

@RestController
@RequestMapping("/open-api/v1/kpi/route/map")

public interface MarketingKpiRouteMapOpenApi {




    @PostMapping("/save")
    Data<MarketingKpiRouteMapVO> save(@RequestBody @Valid MarketingKpiRouteMapVO routeMapVO);


    @PostMapping("/query-list")
    Data<List<MarketingKpiRouteMapVO>> findList(@RequestBody @Valid MarketingKpiRouteMapQuery query);

    @GetMapping("/query-page-list")
    Data<PageBean<MarketingKpiRouteMapVO>> pageList(@Valid MarketingKpiRouteMapQuery query);

    @GetMapping("/detail/{id:[0-9,]+}")
    @ApiOperation(value = "指标管理-路径图—详情", notes = "路径图—详情", nickname = "marketingKpiRouteMapDetail")
    Data<MarketingKpiRouteMapVO> detail(@PathVariable(value = "id") Long id);


    @PutMapping("/process/status/{status}/{id:[0-9,]+}")
    @ApiOperation(value = "指标管理-路径图—启用禁用", notes = "指标管理-路径图—启用禁用", nickname = "marketingKpiRouteMapProcessStatus")
    public Data<Boolean> processStatus(@PathVariable(value = "status") Integer status, @PathVariable(value = "id") Long id);

    @GetMapping("/check/name")
    @ApiOperation(value = "指标管理-路径图—同名检查", notes = "指标管理-路径图—同名检查", nickname = "marketingKpiRouteMapCheckName")
    public Data<Boolean> checkName(MarketingKpiCheckNameRequest request);


    @DeleteMapping("/delete/{id:[0-9,]+}")
    @ApiOperation(value = "指标管理-路径图—删除", notes = "指标管理-路径图—删除", nickname = "deleteByRoute")
    public Data<Boolean> deleteByRoute(@PathVariable(value = "id") Long id);

}
