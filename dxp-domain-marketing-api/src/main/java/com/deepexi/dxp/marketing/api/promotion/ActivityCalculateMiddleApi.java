package com.deepexi.dxp.marketing.api.promotion;

import com.deepexi.dxp.marketing.domain.promotion.dto.activity.BaseActivityDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.CommodityActivityVO;
import com.deepexi.dxp.marketing.domain.promotion.dto.calculate.*;
import com.deepexi.dxp.marketing.domain.promotion.dto.coupon.PromotionCouponLoggerListPostResponseDTO;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.CommodityActivityRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.calculate.*;
import com.deepexi.util.config.Payload;
import com.deepexi.util.pageHelper.PageBean;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> ming.zhong
 * @date created in 17:36 2019/12/4
 */
@RequestMapping("/middle-api/v1/promotion-activity/calculate")
public interface ActivityCalculateMiddleApi {

    /**
     * 根据活动id获取活动商品
     * @param activityIds
     * @return
     */
    @PostMapping("/activity-commodity")
    Payload<List<ActivityConfigDTO>> getCommodityByActivityId(@RequestBody List<Long> activityIds);

    /**
     * 商品可参与的活动
     * @param commodityActivityRequest
     * @return
     */
    @PostMapping("/commodity-activity")
    Payload<CommodityActivityVO> commodityActivity(@RequestBody CommodityActivityRequest commodityActivityRequest);

    /**
     * 多个商品可参与活动
     * @param commodityListActivityRequest
     * @return
     */
    @PostMapping("/commodity-list-activity")
    Payload<List<CommodityActivityVO>> commodityListActivity(@RequestBody CommodityListActivityRequest commodityListActivityRequest);

    /**
     * 购物车金额计算
     * @param activityParamsRequest
     * @return 购物车计算结果
     */
    @PostMapping("/shopping-cart")
    Payload<ShoppingCartVO> shoppingCart(@RequestBody ActivityParamsRequest activityParamsRequest);

    /**
     * 购物车计算（门店维度）
     * @param activityParamsRequest
     * @return 购物车计算结果
     */
    @PostMapping("/shopping-cart-shop-aspect")
    Payload<ShoppingCartShopAspectVO> shoppingCartShopAspect(@RequestBody ActivityParamsRequest activityParamsRequest);

    /**
     * 订单计算
     * @param activityRequestParams
     * @return 订单计算返回结果
     */
    @PostMapping("/order-calculate")
    Payload<OrderEditResponseDTO> orderCalculate(@RequestBody ActivityOrderParamsRequest activityRequestParams);

    /**
     * 积分兑换活动
     * @param activityParamsRequest
     * @param page
     * @param size
     * @return
     */
    @PostMapping("/show-all")
    Payload<PageBean<ActivityResponseParamsDTO>> showJfdh(@RequestBody ActivityParamsRequest activityParamsRequest,
                                                          @RequestParam(value = "page") Integer page,
                                                          @RequestParam(value = "size") Integer size);

    @PostMapping("/exchange")
    Payload<ActivityResponseParamsDTO> exchange(@RequestBody ActivityParamsRequest activityRequestParams);

    @PostMapping("/coupon-list")
    Payload<PageBean<CouponResponseDTO>> couponList(@RequestBody ActivityParamsRequest activityParamsRequest,
                                                    @RequestParam(value = "page") Integer page,
                                                    @RequestParam(value = "size") Integer size);

    @PostMapping("/usable-coupon-list")
    Payload<PageBean<PromotionCouponLoggerListPostResponseDTO>> usableCouponList(@RequestBody ActivityParamsRequest activityParamsRequest);

    @PostMapping("/get-coupon")
    Payload<BaseActivityDTO> getCoupon(@RequestBody CouponActivityParamsRequest activityRequestParams);

    @GetMapping("/get-jfdh-times")
    Payload<Integer> getJFDHTimes(@RequestParam Long appId,
                         @RequestParam String tenantId,
                         @RequestParam Long userId,
                         @RequestParam String userType);


}
