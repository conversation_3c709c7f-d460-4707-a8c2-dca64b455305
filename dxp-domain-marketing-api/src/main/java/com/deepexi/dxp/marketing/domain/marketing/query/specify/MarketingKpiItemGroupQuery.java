package com.deepexi.dxp.marketing.domain.marketing.query.specify;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description：
 * @author: cheng<PERSON><PERSON>
 * @version: 1.0.0
 * @date: 2021-03-30 19:52
 */
@Data
public class MarketingKpiItemGroupQuery extends SuperQuery {

    @ApiModelProperty("groupId")
    private Long groupId;

    @ApiModelProperty("状态")
    private Integer status;
}
