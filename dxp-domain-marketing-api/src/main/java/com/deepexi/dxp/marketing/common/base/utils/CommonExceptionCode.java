package com.deepexi.dxp.marketing.common.base.utils;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020-06-28 16:11
 * 编码
 * {1位标志客户端/2服务端错误/3未知异常}-
 * {具体模块（中心层：1xxx）,(域层2xxx）-
 * {具体业务：（xxxx）1000--1999 属于 公共定义异常范围 业务系统：应该从 2001--9999 开始 }
 * 例如：1-2006-10001  客户端-商品域-输入数据错误
 */
public interface CommonExceptionCode {

        /**  成功返回异常处理   **/
        String SUCCESS = "200";
        /** 全局 未知异常处理 **/
        String ERROR_CODE = "500";


        /** ======== 误码表 由继承者实现 前缀  如 "1-2006-"+INVALIDATION   开始*/
        /** 输入的数据错误, 如非空校验不通过 */
        String INVALIDATION = "1001";

        /** 数据类型不对 **/
        String DATE_TYPE_ERROR = "1002";

        /** 没有请求体 **/
        String NOT_BODY = "1003";

        /** 微服务未连接上 **/
        String SERVER_NOT_CONNECTED = "1004";

        /** 操作不支持 **/
        String OPERATION = "1005";

        /** 未填写活动表单信息 **/
        String NOT_ACTIVITY_FORM = "1006";

        /** 已经参加过活动 **/
        String PARTICIPATED = "1007";



}
