package com.deepexi.dxp.marketing.api.specify;

import com.deepexi.dxp.marketing.common.base.utils.Data;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.MarketingKpiItemGroupQuery;
import com.deepexi.dxp.marketing.domain.marketing.query.specify.MarketingKpiQuery;
import com.deepexi.dxp.marketing.domain.marketing.request.specify.*;

import com.deepexi.util.pageHelper.PageBean;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Set;
@Validated
@RequestMapping("/marketing-api/v1/marketing-kpi")
public interface MarketingKpiOpenApi {

	@PostMapping("/save")
	@ApiOperation(value = "指标管理—新增修改", nickname = "marketingKipSave")
	Data<MarketingKpiVO> save(@RequestBody @Valid MarketingKpiVO vo);

	@GetMapping("/original/query-page-list")
	@ApiOperation(value = "指标管理—原生指标分页查询", nickname = "marketingKipOriginalQueryPageList")
	Data<PageBean<MarketingKpiVO>> originalPageList(@Valid MarketingKpiQuery query);

	@GetMapping("/derivative/query-page-list")
	@ApiOperation(value = "指标管理—派生指标分页查询", notes = "派生指标—派生指标分页查询", nickname = "marketingKipDerivativeQueryPageList")
	Data<PageBean<MarketingKpiVO>> derivativePageList(@Valid MarketingKpiQuery query);

	@GetMapping("/query-page-list")
	@ApiOperation(value = "指标管理—分页查询", notes = "指标管理—分页查询", nickname = "marketingKipQueryPageList")
	Data<PageBean<MarketingKpiVO>> pageList(@Valid MarketingKpiQuery query);

	@PostMapping("/query-list")
	@ApiOperation(value = "指标管理—列表查询", notes = "指标管理—列表查询", nickname = "marketingKipQueryList")
	Data<List<MarketingKpiVO>> queryList(@RequestBody @Valid MarketingKpiQuery query);

	@GetMapping("/select/data/{type}")
	@ApiOperation(value = "指标管理—获取下拉框数据", notes = "指标管理—获取下拉框数据", nickname = "marketingKipSelectData")
	Data<Map<String, Set<Object>>> selectData(MarketingKpiSelectDataRequestDTO request);

	@GetMapping("/detail/{id:[0-9,]+}")
	@ApiOperation(value = "指标管理——详情", notes = "指标管理——详情", nickname = "marketingKipDetail")
	Data<MarketingKpiVO> detail(@PathVariable(value = "id", required = true) Long id);

	@PutMapping("/process/status/{status}/{id:[0-9,]+}")
	@ApiOperation(value = "指标管理——启用禁用", notes = "指标管理——启用禁用", nickname = "marketingKipProcessStatus")
	Data<Boolean> processStatus(@PathVariable(value = "status") Integer status, @PathVariable(value = "id") Long id);

	@GetMapping("/check/name")
	@ApiOperation(value = "指标管理—同名检查", notes = "指标管理—同名检查", nickname = "marketingKipCheckName")
	Data<Boolean> checkName(MarketingKpiCheckNameRequestDTO request);

	@GetMapping("/query-operation-list/{id:[0-9,]+}")
	@ApiOperation(value = "指标管理—查询运算列表" , notes = "指标管理—查询运算列表", nickname = "marketingKpiQueryOperationList")
	Data<List<KpiOperationDTO>> queryOperationList(@PathVariable(value = "id", required = true)Long id);

	@GetMapping("/query-by-item-group")
	@ApiOperation(value = "指标管理-指标组—根据指标组ID获取原生/派生指标集合", notes = "指标管理-指标组—根据类型查询核心指标", nickname = "marketingKipQueryByKpiItemGroup")
	Data<List<MarketingKpiExtDTO>> queryByKpiItemGroup(MarketingKpiItemGroupQuery query);

	@DeleteMapping("/delete/{id:[0-9,]+}")
	@ApiOperation(value = "删除", notes = "指标管理—删除", nickname = "删除")
	Data<Boolean> deleteByKpi(@PathVariable(value = "id", required = true) Long id);

}
