package com.deepexi.dxp.marketing.domain.marketing.query.specify;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 活动专题分析-活动详情请求参数
 * <AUTHOR>
 */
@Data
public class ActivityAnalysisQuery extends SuperQuery {

    /**
     *自定义指标
     **/
    @ApiModelProperty(value = "自定义指标code")
    private List<String> customIndicator;

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty(value = "活动模板")
    private Long templateId;

    @ApiModelProperty(value="上架状态 0-下架1-上架")
    private Integer upperStatus;

    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间", hidden = true)
    private String startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间", hidden = true)
    private String endTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "昵称和手机号")
    private String nickNameOrPhone;

    @ApiModelProperty(value = "活动ID")
    private List<Long> activityIdList;

    public EventsReportQuery toSensorQuery() {
        EventsReportQuery eventsReportQuery = new EventsReportQuery();
        eventsReportQuery.setActivityId(getId().toString());
        eventsReportQuery.setEndTime(endTime);
        eventsReportQuery.setStartTime(startTime);
        return eventsReportQuery;
    }
}
