package com.deepexi.dxp.marketing.common.base.query;

import com.deepexi.util.domain.query.BaseQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * @description：
 * @author：<PERSON><PERSON><PERSON><PERSON>
 * @version：1.0.0
 * @date：2021-03-31 2:15 下午
 */
@Data
public class SuperQuery extends BaseQuery implements Serializable {

    /**
     * APP_ID
     */
    @ApiModelProperty(value = "APP_ID", hidden = true)
    private Long appId;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID", hidden = true)
    private String tenantId;

    @ApiModelProperty("ID")
    private Long id;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 乐观锁
     */
    @ApiModelProperty(value = "乐观锁", hidden = true)
    private Long version;

    /**
     * 逻辑删除
     * */
    @ApiModelProperty(value = "逻辑删除", hidden = true)
    private Integer deleted = 0;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createdTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updatedTime;


    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updatedBy;
}
