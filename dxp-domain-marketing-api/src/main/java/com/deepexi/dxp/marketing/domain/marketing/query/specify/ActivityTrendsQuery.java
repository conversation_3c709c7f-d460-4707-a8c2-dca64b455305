package com.deepexi.dxp.marketing.domain.marketing.query.specify;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class ActivityTrendsQuery {

    /**
     *活动id
     **/
    @ApiModelProperty(value = "活动id")
    @NotNull(message = "活动id不能为空")
    private Long id;

    /**
     *指标类型
     **/
    @ApiModelProperty(value = "指标类型（1访问人数，2参与人数，3分享人数，4留资人数）")
    @NotNull(message = "指标类型不能为空")
    private Integer indexType;

    /**
     *时间类型
     **/
    @ApiModelProperty(value = "日期类型（1按日，2按月）")
    @NotNull(message = "日期类型不能为空")
    private Integer dateType;

    /**
     *时间
     **/
    @ApiModelProperty(value = "时间")
    private String date;
}
