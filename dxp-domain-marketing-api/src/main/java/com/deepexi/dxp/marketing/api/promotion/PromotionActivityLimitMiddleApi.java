package com.deepexi.dxp.marketing.api.promotion;


import com.deepexi.dxp.marketing.domain.promotion.dto.activity.PromotionActivityLimitFindVO;
import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityLimitQuery;
import com.deepexi.util.config.Payload;
import com.deepexi.util.pageHelper.PageBean;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR> ming.zhong
 * @date created in 17:25 2019/12/4
 */
@RequestMapping("/middle-api/v1/promotion-activity-limit")
public interface PromotionActivityLimitMiddleApi {


    @PostMapping("/page")
    Payload<PageBean<PromotionActivityLimitFindVO>> findPage(@RequestBody PromotionActivityLimitQuery query);
}
