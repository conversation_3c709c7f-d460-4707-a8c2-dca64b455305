package com.deepexi.dxp.marketing.api.promotion;

import com.deepexi.dxp.marketing.domain.promotion.dto.activity.PromotionActivityTemplateDetailPostDTO;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityTemplateCreatePostRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityTemplateDeletedPostRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityTemplateUpdatePostRequest;
import com.deepexi.util.config.Payload;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> ming.zhong
 * @date created in 17:31 2019/12/4
 */
@RequestMapping("/middle-api/v1/promotion-activity-template")
public interface PromotionActivityTemplateMiddleApi {

    @PostMapping("/detail")
    Payload<PromotionActivityTemplateDetailPostDTO> detail(@RequestParam Long id);

    @PostMapping("/update")
    Payload<Boolean> update(@RequestParam Long id, @RequestBody PromotionActivityTemplateUpdatePostRequest dto);

    @PostMapping("/create")
    Payload<Boolean> create(@RequestBody PromotionActivityTemplateCreatePostRequest dto);

    @PostMapping("/deleted")
    Payload<Boolean> deleted(@RequestBody List<PromotionActivityTemplateDeletedPostRequest> dtoList);
}
