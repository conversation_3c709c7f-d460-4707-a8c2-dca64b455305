package com.deepexi.dxp.marketing.domain.marketing.query.specify;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 活动分组查询请求
 * <AUTHOR>
 */
@ApiModel(value = "活动分组查询入参", description = "活动分组查询入参")
@Data
public class ActivityGroupRelatedQuery extends SuperQuery {
    /**
     * 活动组ID
     */
    private Long groupId;
}
