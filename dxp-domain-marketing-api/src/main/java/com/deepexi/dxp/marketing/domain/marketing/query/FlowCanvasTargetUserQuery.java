package com.deepexi.dxp.marketing.domain.marketing.query;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/11
 */
@Data
@NoArgsConstructor
public class FlowCanvasTargetUserQuery extends SuperQuery {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 主键id列表
     */
    private List<Long> ids;

    /**
     * 画布ID
     */
    private Long flowId;

    /**
     * 画布节点ID
     */
    private String nodeId;

    /**
     * 类型
     */
    private String type;

    public FlowCanvasTargetUserQuery (Long flowId, String tenantId, Long appId){
        this.flowId = flowId;
        this.setTenantId(tenantId);
        this.setAppId(appId);
    }
}
