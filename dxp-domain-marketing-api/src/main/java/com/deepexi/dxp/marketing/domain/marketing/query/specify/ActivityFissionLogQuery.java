package com.deepexi.dxp.marketing.domain.marketing.query.specify;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "裂变活动助力-砍价记录查询入参", description = "裂变活动助力-砍价记录查询入参")
@Data
public class ActivityFissionLogQuery extends SuperQuery {

    @ApiModelProperty(value = "活动id")
    private Long activityId;

    @ApiModelProperty(value = "活动发起者参与记录id",required = true)
    private Long partakeLogId;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "手机号")
    private String phone;
}
