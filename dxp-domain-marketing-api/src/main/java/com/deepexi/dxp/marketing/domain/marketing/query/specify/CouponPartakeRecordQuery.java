package com.deepexi.dxp.marketing.domain.marketing.query.specify;

import com.deepexi.util.pojo.AbstractObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 使用优惠券参与活动明细
 */
@EqualsAndHashCode(callSuper = false)
@ApiModel
@Data
public class CouponPartakeRecordQuery extends AbstractObject {
    /**
     * 呢称/手机号
     */
    @ApiModelProperty(value = "呢称/手机号/活动订单编号")
    @Length(max = 20, message = "最多输入20个字符")
    private String nameOrPhoneOrOrderNo;



    @ApiModelProperty(value = "用户id")
    private Long userId;

    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID")
    @NotNull(message = "活动ID不能为空")
    private Long activityId;


    /**
     * 查询的页码(-1 表示查询所有数据)
     */
    @ApiModelProperty(value = "查询的页码(-1 表示查询所有数据)")
    @Min(value = -1, message = "page不能小于{value}")
    private Integer page = 1;

    /**
     * 每页条数
     */
    @ApiModelProperty(value = "每页条数")
    @Min(value = 1, message = "size必须大于0")
    private Integer size = 10;


    @ApiModelProperty(value="支付开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String startTime;


    @ApiModelProperty(value="支付结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String endTime;
}
