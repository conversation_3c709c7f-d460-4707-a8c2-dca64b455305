package com.deepexi.dxp.marketing.common.base.query;

import com.deepexi.dxp.marketing.domain.marketing.query.MarketingActiveRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description：查询主动任务详情对象
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-03-16 15:49
 */
@Data
public class MarketActiveDetailQuery extends MarketingActiveRequest {

    @ApiModelProperty(value = "是否查询实验组内其他任务资源")
    private Boolean showExperimentResources;
}
