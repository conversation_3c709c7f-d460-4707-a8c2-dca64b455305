package com.deepexi.dxp.marketing.domain.marketing.query.specify;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = false)
@ApiModel
@Data
public class VerifyInfoQuery {

    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID",required = true)
    @NotNull(message = "活动ID不能为空")
    private Long activityId;

    /**
     * 奖品名称
     */
    @ApiModelProperty(value = "奖品名称")
    private String resourceName;


    /**
     * 查询的页码(-1 表示查询所有数据)
     */
    @ApiModelProperty(value = "查询的页码(-1 表示查询所有数据)")
    @Min(value = -1, message = "page不能小于{value}")
    private Integer page = 1;

    /**
     * 每页条数
     */
    @ApiModelProperty(value = "每页条数")
    @Min(value = 1, message = "size必须大于0")
    private Integer size = 10;
}
