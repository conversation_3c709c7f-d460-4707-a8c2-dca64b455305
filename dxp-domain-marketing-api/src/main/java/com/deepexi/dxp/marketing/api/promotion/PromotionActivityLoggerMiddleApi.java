package com.deepexi.dxp.marketing.api.promotion;

import com.deepexi.dxp.marketing.domain.promotion.dto.activity.PreSaleTailMoneyVO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.PromotionActivityLoggerListDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.activity.StatisticsUserActivityCountVO;
import com.deepexi.dxp.marketing.domain.promotion.query.activity.PreSaleTailMoneyQuery;
import com.deepexi.dxp.marketing.domain.promotion.query.activity.PromotionActivityLoggerQuery;
import com.deepexi.dxp.marketing.domain.promotion.query.activity.StatisticsQuery;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityLoggerCreatePostRequest;
import com.deepexi.dxp.marketing.domain.promotion.request.activity.PromotionActivityLoggerUpdatePostRequest;
import com.deepexi.util.config.Payload;
import com.deepexi.util.pageHelper.PageBean;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> ming.zhong
 * @date created in 17:25 2019/12/4
 */
@RequestMapping("/middle-api/v1/promotion-activity-logger")
public interface PromotionActivityLoggerMiddleApi {

    @PostMapping("/update")
    Payload<Boolean> update(@RequestParam Long id, @RequestBody PromotionActivityLoggerUpdatePostRequest request);

    @PostMapping("/update-batch-by-order-id")
    Payload<Boolean> updateBatchByOrderId(@RequestBody List<PromotionActivityLoggerUpdatePostRequest> dtoList);

    @GetMapping("/select")
    Payload<List<PromotionActivityLoggerListDTO>> findAll(PromotionActivityLoggerQuery query);


    @GetMapping("/select-page")
    Payload<PageBean<PromotionActivityLoggerListDTO>> findPage(PromotionActivityLoggerQuery query);

    @PostMapping("/create")
    Payload<Boolean> create(@RequestBody PromotionActivityLoggerCreatePostRequest request);

    @PostMapping("/deleted-by-order-ids")
    Payload<Boolean> deletedByOrderIds(@RequestBody List<String> orders);

    @PostMapping("/deleted-by-ids")
    Payload<Boolean> deletedByIds(@RequestBody List<Long> ids);

    @PostMapping("/statistics-user-activity-count")
    Payload<StatisticsUserActivityCountVO> statisticsUserActivityCount(@RequestBody StatisticsQuery query);

    @PostMapping("/retorn-presales-tail-money")
    Payload<PreSaleTailMoneyVO> retornPreSalesTailMoney(@RequestBody PreSaleTailMoneyQuery query);

}
