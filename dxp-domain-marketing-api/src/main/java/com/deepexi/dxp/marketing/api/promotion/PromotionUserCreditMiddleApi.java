package com.deepexi.dxp.marketing.api.promotion;

import com.deepexi.dxp.marketing.domain.promotion.dto.usercredit.UserCreditCheckDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.usercredit.UserCreditDetailDTO;
import com.deepexi.dxp.marketing.domain.promotion.dto.usercredit.UserCreditQueryDTO;
import com.deepexi.dxp.marketing.domain.promotion.query.usercredit.PromotionUserCreditCheckQuery;
import com.deepexi.dxp.marketing.domain.promotion.query.usercredit.PromotionUserCreditDetailQuery;
import com.deepexi.dxp.marketing.domain.promotion.query.usercredit.PromotionUserCreditQuery;
import com.deepexi.util.config.Payload;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 中心充值赠送活动特殊信息查询接口.
 *
 * <AUTHOR> fengjun
 * @date 2020-03-16
 */
@RequestMapping("/middle-api/v1/promotion-user-credit")
public interface PromotionUserCreditMiddleApi {

    @GetMapping("/page")
    Payload<UserCreditQueryDTO> userCreditPage(@Valid PromotionUserCreditQuery query);


    @PostMapping("/check")
    Payload<UserCreditCheckDTO> orderCreateGroupLockInventory(
            @Valid @RequestBody PromotionUserCreditCheckQuery promotionUserCreditCheckQuery);

    @GetMapping
    Payload<UserCreditDetailDTO> orderCreateGroupLockInventory(@Valid PromotionUserCreditDetailQuery query);

}
