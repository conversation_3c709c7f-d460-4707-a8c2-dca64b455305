package com.deepexi.dxp.marketing.common.base.utils;

import com.deepexi.util.constant.BaseEnumType;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020-06-28 16:15
 */
public enum ResultConstant implements BaseEnumType {

    ERROR(CommonExceptionCode.ERROR_CODE, "系统异常"),
    SUCCESS(CommonExceptionCode.SUCCESS, "ok");

    private String code;
    private String msg;
    ResultConstant(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
