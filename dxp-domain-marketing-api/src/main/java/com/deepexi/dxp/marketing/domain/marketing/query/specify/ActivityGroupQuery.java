package com.deepexi.dxp.marketing.domain.marketing.query.specify;

import com.deepexi.dxp.marketing.common.base.query.SuperQuery;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 活动分组查询请求
 * <AUTHOR>
 */
@ApiModel(value = "活动分组查询入参", description = "活动分组查询入参")
@Data
public class ActivityGroupQuery extends SuperQuery {
    /**
     * 活动专题名称
     */
    @ApiModelProperty("活动专题名称")
    private String name;

    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String startTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String endTime;

    /**
     * 0、活动组,1、活动专题
     */
    @ApiModelProperty(value = "0、活动组,1、活动专题",hidden = true)
    private Integer type;

    @ApiModelProperty(value = "userId")
    private String userId;

    @ApiModelProperty(value = "活动ids")
    private List<Long> activityIdList;
}
