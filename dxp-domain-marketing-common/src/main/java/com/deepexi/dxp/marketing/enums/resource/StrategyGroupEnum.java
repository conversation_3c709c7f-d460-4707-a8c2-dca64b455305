//package com.deepexi.dxp.marketing.enums.resource;
//
//import lombok.Getter;
//import lombok.ToString;
//
//import java.util.Arrays;
//
///**
// * 活动类型
// */
//@ToString
//@Getter
//public enum StrategyGroupEnum {
//    /**
//     * 策略组
//     */
//    MJS_G("1", "满减"),
//    ZJRX_G("3", "N元N件"),
//    DZCX_G("4", "打折促销"),
//    MZ_G("5", "买赠"),
//    JFDH_G("6", "积分兑换"),
//    PTHD_G("7", "拼团活动"),
//    LQHD_G("8", "领券活动"),
//    YS_G("10", "预售活动"),
//    CZ_G("11", "充值活动");
//
//    private final String id;
//    private final String value;
//
//    StrategyGroupEnum(String id, String value) {
//        this.id = id;
//        this.value = value;
//    }
//
//    public static String getValueById(String id) {
//
//        return Arrays.stream(StrategyGroupEnum.values())
//                .filter(val -> val.getId().equals(id))
//                .findFirst()
//                .map(StrategyGroupEnum::getValue)
//                .orElse("该活动类型无对应的活动");
//
//    }
//
//}