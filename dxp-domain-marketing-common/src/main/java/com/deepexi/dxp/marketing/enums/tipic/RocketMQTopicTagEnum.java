package com.deepexi.dxp.marketing.enums.tipic;

import com.deepexi.dxp.marketing.enums.BaseEnum;

public enum RocketMQTopicTagEnum implements BaseEnum<String> {

    MEMBER_TO_ES("MEMBER_TO_ES", "会员修改同步到es的topic"),
    MTA_MEMBER_FOR_ES("ACTIVE_MARKETING_MEMBER_FOR_ES","从标签域中取出的会员数据放入该Topic中"),
    MTA_MEMBER_FOR_SEND("ACTIVE_MARKETING_MEMBER_FOR_SEND", "组装好的待发送的会员信息放入该Topic中"),
    MTA_RESULT("ACTIVE_MARKETING_RESULT","主动营销结果处理器使用");

    RocketMQTopicTagEnum(String name, String value) {
        this.name = name;
        this.value = value;
    }

    private final String name;

    private final String value;


    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String getName() {
        return name;
    }

}
