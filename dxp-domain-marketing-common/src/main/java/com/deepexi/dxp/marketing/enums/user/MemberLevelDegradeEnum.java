package com.deepexi.dxp.marketing.enums.user;

import lombok.Getter;

/**
 * 会员等级降级类型
 *
 * <AUTHOR>
 * @since 2020年06月05日 17:01
 */
@Getter
public enum MemberLevelDegradeEnum {

    /**
     * 无
     */
    NOTHING(0, "无"),
    ;

    private final Integer state;

    private final String msg;

    MemberLevelDegradeEnum(Integer state, String msg) {
        this.state = state;
        this.msg = msg;
    }

    public static MemberLevelDegradeEnum getEnumByState(Integer state) {
        MemberLevelDegradeEnum[] items = values();
        for (MemberLevelDegradeEnum item : items) {
            if (item.state.equals(state)) {
                return item;
            }
        }
        return null;
    }
}
