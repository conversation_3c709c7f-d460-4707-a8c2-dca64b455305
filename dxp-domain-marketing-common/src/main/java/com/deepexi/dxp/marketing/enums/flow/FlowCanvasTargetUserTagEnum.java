package com.deepexi.dxp.marketing.enums.flow;

import lombok.Getter;

/**
 * @Enum: FlowCanvasTargetUserTagEnum
 * @Description:
 * @Author: zht
 * @Date: 2020/7/17
 */
@Getter
public enum FlowCanvasTargetUserTagEnum {
    TAG(1, "标签"),
    GROUP(2, "客群");

    private final Integer index;

    private final String describe;

    FlowCanvasTargetUserTagEnum(Integer index, String describe){
        this.index = index;
        this.describe = describe;
    }
}
