package com.deepexi.dxp.marketing.enums.common;


import lombok.Getter;

/**
 * 状态 枚举
 *
 * <AUTHOR>
 * @Date 2020/3/5
 */
@Getter
public enum ParamEnum {

    /**
     * 租户
     */
    TENANT_ID("tenantId"),
    /**
     * 禁用
     */
    APP_ID("appId"),
    /**
     * iam app
     */
    IAM_APP_ID("iam_app_id"),
    /**
     * 授权
     */
    AUTHORIZE("Authorization")
    ;

    private final String value;

    ParamEnum(String value) {
        this.value = value;
    }

}
