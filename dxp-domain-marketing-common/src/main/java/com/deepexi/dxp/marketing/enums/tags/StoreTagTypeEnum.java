package com.deepexi.dxp.marketing.enums.tags;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/11/28
 */
@Getter
public enum StoreTagTypeEnum implements TagSubTypeEnum {
    MANUAL(1, "门店标签（手工）", "manual");

    private Integer index;
    private String name;
    private String code;

    StoreTagTypeEnum(Integer index, String name, String code){
        this.index = index;
        this.name = name;
        this.code = code;
    }
}
