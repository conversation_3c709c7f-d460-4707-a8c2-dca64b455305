package com.deepexi.dxp.marketing.enums.flow;

import com.deepexi.dxp.marketing.enums.BaseEnum;
import lombok.Getter;

import java.util.Objects;

/**
 * @Enum: FlowCanvasExecuteTypeEnum
 * @Description:
 * @Author: zht
 * @Date: 2020/7/16
 */
@Getter
public enum FlowCanvasExecuteTypeEnum implements BaseEnum<String> {
    ONCE("once", "单次型"),
    FIXED("fixed", "周期重复型"),
    EVENT("event", "触发型");

    private final String value;

    private final String name;

    FlowCanvasExecuteTypeEnum(String value, String name){
        this.value = value;
        this.name = name;
    }

    public static Boolean isExist(String executeType) {
        if (Objects.isNull(executeType)) {
            return Boolean.FALSE;
        }
        for (FlowCanvasExecuteTypeEnum value : values()) {
            if (executeType.equals(value.getValue())) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }
}
