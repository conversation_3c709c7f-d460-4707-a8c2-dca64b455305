package com.deepexi.dxp.marketing.enums.tags;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/12/2
 */
@Getter
public enum CombinedTypeEnum {

    AND(0, "与", "and"),
    OR(1, "或", "or");


    private Integer index;
    private String name;
    private String operation;

    CombinedTypeEnum(Integer index, String name, String operation){
        this.index = index;
        this.name = name;
        this.operation = operation;
    }

    public static String getOperationByIndex(Integer index) {
        CombinedTypeEnum[] values = values();
        for (CombinedTypeEnum value : values) {
            if (value.getIndex().equals(index)) {
                return value.getOperation();
            }
        }
        return "";
    }
}
