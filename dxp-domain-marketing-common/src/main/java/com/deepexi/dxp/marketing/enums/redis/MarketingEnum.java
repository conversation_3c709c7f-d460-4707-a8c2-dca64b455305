package com.deepexi.dxp.marketing.enums.redis;

import com.deepexi.util.ArrayUtils;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * @Enum: MarketingEnum
 * @Description:
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2020/7/28
 */
@Getter
public enum MarketingEnum {
    WHITE_LIST("whitelist", "SECURITY_FILTER_MEMBERS");

    private final String name;

    private final String key;


    MarketingEnum(String name, String key) {
        this.name = name;
        this.key = key;
    }


    public static MarketingEnum getEnumByValue(String name) {
        MarketingEnum[] marketingEnums = values();
        for (MarketingEnum marketingEnum : marketingEnums) {
            if (marketingEnum.name.equals(name)) {
                return marketingEnum;
            }
        }
        return null;
    }

    /**
     * 获取redis完整的key
     *
     * @param marketingEnum
     * @param combineKeyNames
     * @return
     */
    public static String getRedisFullKey(MarketingEnum marketingEnum, String... combineKeyNames) {
        if (ArrayUtils.isEmpty(combineKeyNames)) {
            return marketingEnum.getKey();
        } else {
            String keyPrefix = StringUtils.join(combineKeyNames, "_");
            return keyPrefix + "_" + marketingEnum.getKey();
        }
    }
}
