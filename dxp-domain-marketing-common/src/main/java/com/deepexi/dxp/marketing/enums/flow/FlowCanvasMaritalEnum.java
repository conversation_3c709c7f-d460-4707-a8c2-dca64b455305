package com.deepexi.dxp.marketing.enums.flow;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020-08-03 19:07
 */
@Getter
public enum FlowCanvasMaritalEnum {
    SPINSTERHOOD("未婚","1"),
    MARRIED("已婚","2"),
    WIDOWED("丧偶","3"),
    DIVORCED("离异","4");

    private final String code;

    private final String describe;

    FlowCanvasMaritalEnum(String code, String describe){
        this.code = code;
        this.describe = describe;
    }

    public static FlowCanvasMaritalEnum getEnumByValue(String value) {
        FlowCanvasMaritalEnum[] items = values();
        for (FlowCanvasMaritalEnum item : items) {
            if (item.code.equals(value)) {
                return item;
            }
        }
        return null;
    }
}
