package com.deepexi.dxp.marketing.enums.tags;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/12/17
 */
@Getter
public enum TagUsageRecordTypeEnum {
    MARKETING(1, "主动任务"),
    AUTO_MARKETING(2, "自动任务"),
    FLOW(3, "场景"),
    SCENE(4, "流程画布");

    private Integer type;

    private String name;

    TagUsageRecordTypeEnum(Integer type, String name){
        this.type = type;
        this.name = name;
    }
}
