package com.deepexi.dxp.marketing.enums.user;


/**
 * 年收入枚举
 *
 * <AUTHOR>
 * @Date 2020/3/24
 */
public enum AnnualIncomeEnum {
    ANNUAL_INCOME_MIN_10 ("10万以下", 0L, 99999L),
    ANNUAL_INCOME_10_20 ("10万-20万", 100000L, 200000L),
    ANNUAL_INCOME_20_50 ("20万-50万", 200000L, 500000L),
    ANNUAL_INCOME_50_100 ("50万-100万", 500000L, 1000000L),
    ANNUAL_INCOME_100_MAX ("100万以上", 1000001L, (long) Integer.MAX_VALUE),
    ;

    private final String label;

    private final Long min;

    private final Long max;

    AnnualIncomeEnum(String label, Long min, Long max) {
        this.label = label;
        this.min = min;
        this.max = max;
    }

    public static AnnualIncomeEnum getEnumByValue(Long min, Long max) {
        AnnualIncomeEnum[] items = values();
        for (AnnualIncomeEnum item : items) {
            if (item.min.equals(min) && item.max.equals(max)) {
                return item;
            }
        }
        return null;
    }

    public static AnnualIncomeEnum getEnumByLabel(String label) {
        AnnualIncomeEnum[] items = values();
        for (AnnualIncomeEnum item : items) {
            if (item.label.equals(label)) {
                return item;
            }
        }
        return null;
    }

    public String getLabel() {
        return label;
    }

    public Long getMin() {
        return min;
    }

    public Long getMax() {
        return max;
    }
}
