package com.deepexi.dxp.marketing.enums.tags;

import lombok.Getter;

/**
 * 商品标签类型枚举
 * <AUTHOR>
 * @version 1.0
 * @date 2020/11/28
 */
@Getter
public enum CommodityTagTypeEnum implements TagSubTypeEnum {
    MANUAL(1, "资源标签(手工)", "manual");

    private Integer index;
    private String name;
    private String code;

    CommodityTagTypeEnum(Integer index, String name, String code){
        this.index = index;
        this.name = name;
        this.code = code;
    }
}
