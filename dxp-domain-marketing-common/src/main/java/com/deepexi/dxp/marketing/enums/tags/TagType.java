package com.deepexi.dxp.marketing.enums.tags;

import lombok.Getter;

/**
 * @author: zhang.yongwei
 */
@Getter
public enum TagType {
    NATIVE_TAG(0,"原生标签"),
    DERIVE_TAG(1,"预制组合标签"),
    COMPOSE_TAG(2,"组合标签"),
    CROWDPACK_TAG(3,"客群包"),
    MANUAL_TAG(4,"主观标签"),
    ACTIVITY_TAG(5,"活动标签"),
    LIFE_CYCLE_TAG(6,"生命周期标签");

    private final Integer code;
    private final String name;

    TagType(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

}
