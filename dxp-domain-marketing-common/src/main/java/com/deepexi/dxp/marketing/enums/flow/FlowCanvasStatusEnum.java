package com.deepexi.dxp.marketing.enums.flow;

import com.deepexi.dxp.marketing.enums.BaseEnum;
import lombok.Getter;

/**
 * @Enum: FlowCanvasStatusEnum
 * @Description:
 * @Author: zht
 * @Date: 2020/7/20
 */
@Getter
public enum FlowCanvasStatusEnum implements BaseEnum<Integer> {
    /**
     * 未发布
     */
    UN_PUBLISHED(1, "未发布"),

    /**
     * 发布中
     */
    PUBLISHING(2, "发布中"),

    /**
     * 运行中
     */
    RUNNING(3, "运行中"),


    /**
     * 暂停中
     */
    SUSPENDING(4, "暂停中"),

    /**
     * 暂停
     */
    SUSPEND(5, "已暂停"),


    /**
     * 暂停执行后，重新开始执行过程
     */
    RECOVERING(6, "恢复中"),

    /**
     * 已结束
     */
    FINISHED(7,"已结束"),

    /**
     * 正在终止中
     */
    BREAKING_OFF(8,"终止中"),

    /**
     * 已终止
     */
    BREAK_OFF(9,"已终止");

    private final Integer value;

    private final String name;

    FlowCanvasStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static FlowCanvasStatusEnum getEnumByValue(Integer value) {
        FlowCanvasStatusEnum[] items = values();
        for (FlowCanvasStatusEnum item : items) {
            if (item.value.equals(value)) {
                return item;
            }
        }
        return null;
    }
}
