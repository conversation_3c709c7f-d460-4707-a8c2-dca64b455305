package com.deepexi.dxp.marketing.enums.tags;

import lombok.Getter;
import org.springframework.util.Assert;

import java.util.Arrays;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 资源标签类型枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020-12-01 11:34
 */
@Getter
public enum ResourceTagTypeEnum  implements TagSubTypeEnum{
    /**
     * 促销
     */
    COUPON(1, "促销", "coupon"),
    /**
     * 24好玩
     */
    HAOWAN(2, "24好玩", "24haowan"),
    /**
     * 活动
     */
    ACTIVITY(3, "活动", "activity"),

    ;

    private Integer index;
    private String name;
    private String code;


    ResourceTagTypeEnum(Integer index, String name, String code){
        this.index = index;
        this.name = name;
        this.code = code;
    }

    private static final Map<Integer, ResourceTagTypeEnum> map;
    private static final Set<String> codes;

    static {
        map = Arrays.stream(ResourceTagTypeEnum.values()).collect(Collectors.toMap(ResourceTagTypeEnum::getIndex, Function.identity()));
        codes = Arrays.stream(ResourceTagTypeEnum.values()).map(ResourceTagTypeEnum::getCode).collect(Collectors.toSet());
    }

    /**
     * 根据int类型子分类获取枚举
     *
     * @param type 类型code
     * @return 对应枚举类型
     * @throws IllegalArgumentException 如果未找到会抛异常
     */
    public static ResourceTagTypeEnum of(Integer type) {
        ResourceTagTypeEnum resourceTagTypeEnum = map.get(type);
        Assert.notNull(resourceTagTypeEnum, "标签所属类型错误");
        return resourceTagTypeEnum;
    }

    /**
     * 获取资源类型code列表
     *
     * @return 资源类型code列表
     */
    public static Set<String> getCodes() {
        return codes;
    }
}
