package com.deepexi.dxp.marketing.enums.user;

import lombok.Getter;

/**
 * 会员等级升级类型
 *
 * <AUTHOR>
 * @since 2020年06月05日 17:01
 */
@Getter
public enum MemberLevelUpgradeEnum {

    /**
     * 成长值
     */
    GROWTH_VALUE(1, "成长值"),
    ;

    private final Integer state;

    private final String msg;

    MemberLevelUpgradeEnum(Integer state, String msg) {
        this.state = state;
        this.msg = msg;
    }

    public static MemberLevelUpgradeEnum getEnumByState(Integer state) {
        MemberLevelUpgradeEnum[] items = values();
        for (MemberLevelUpgradeEnum item : items) {
            if (item.state.equals(state)) {
                return item;
            }
        }
        return null;
    }
}
