package com.deepexi.dxp.marketing.enums.redis;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020-09-17 11:49
 */
@Getter
public enum RedisPrefixEnum {
    SEND_MQ_MSG_ID("结果处理器幂等处理", "send_mq_msg_id"),
    TASK_RECORD("结果处理器记录已进行过任务的人", "task_record"),
    TASK_PRODUCE_NUMS("任务要发送的人数", "task_produce_nums"),
    TASK_CONSUME_NUMS("任务已发送的人数", "task_consume_nums"),
    TASK_EXPERIMENT("实验任务","task_experiment"),
    WHITE_LIST("白名单", "white_list"),
    BLACK_LIST("黑名单", "black_list"),
    REPEAT_SUBMIT("重复提交AOP", "repeatSubmit"),
    // 第一个占位符是租户 ，第二个占位符是资源渠道（由于24好玩渠道存的是中文所以需要做转换为24_hao_wan），第三个占位符是资源id
    TASK_MAPPING("备份任务资源mapping关系到redis", "task_mapping:%s:%s:%s")
    ;


    private final String name;

    private final String key;


    RedisPrefixEnum(String name, String key) {
        this.name = name;
        this.key = key;
    }


    public static RedisPrefixEnum getEnumByValue(String name) {
        RedisPrefixEnum[] marketingEnums = values();
        for (RedisPrefixEnum marketingEnum : marketingEnums) {
            if (marketingEnum.name.equals(name)) {
                return marketingEnum;
            }
        }
        return null;
    }
}
