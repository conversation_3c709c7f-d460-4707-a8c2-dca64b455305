package com.deepexi.dxp.marketing.enums.flow;

import lombok.Getter;

/**
 * @Enum: FlowCanvasUserTypeEnum
 * @Description:
 * @Author: zht
 * @Date: 2020/7/21
 */
@Getter
public enum FlowCanvasUserTypeEnum {
    MEMBER("member", "会员"),
    VISITOR("visitor", "游客"),
    ALL("all", "全部用户"),
    COMPOSE_TAG("compose_tag", "综合选择"),
    SCENE("scene", "场景");

    private final String code;

    private final String describe;

    FlowCanvasUserTypeEnum(String code, String describe){
        this.code = code;
        this.describe = describe;
    }
}
