package com.deepexi.dxp.marketing.enums.tags;

import lombok.Getter;

/**
 * 用户包含的所有标签
 * <AUTHOR>
 * @version 1.0
 * @date 2020-12-07 17:11
 */
@Getter
public enum UserAllTagTypeEnum {
    MEMBER_MANUAL(1, "印象标签"),
    MEMBER_RULE(2, "规则标签"),
    MEMBER_PREFER(3, "偏好标签"),
    MEMBER_CROWD(4, "客群"),
    MEMBER_CYCLE(5, "生命周期"),
    COMMODITY(20,"商品"),
    COMMODITY_TOP_10(200,"商品top10"),
    RESOURCE(30,"资源"),
    RESOURCE_TOP_10(300,"资源top10"),
    STORE(40,"门店");

    private Integer type;
    private String name;

    UserAllTagTypeEnum(Integer type, String name){
        this.type = type;
        this.name = name;
    }

}
