package com.deepexi.dxp.marketing.enums.flow;

import lombok.Getter;

/**
 * @Enum: XxlJobRecordStatusEnum
 * @Description:
 * @Author: zht
 * @Date: 2020/8/5
 */
@Getter
public enum FlowCanvasXxlJobRecordStatusEnum {
    RUN(0, "启动"),
    STOP(1, "停止");

    private final Integer value;

    private final String name;

    FlowCanvasXxlJobRecordStatusEnum(Integer value, String name){
        this.value = value;
        this.name = name;
    }
}
