package com.deepexi.dxp.marketing.enums.flow;

import lombok.Getter;

/**
 * 流程画布-变更属性枚举
 * <AUTHOR>
 * @version 1.0
 * @date 2020-07-17 15:25
 */
@Getter
public enum FlowCanvasExecutePropertyEnum {
    ANNUAL_INCOME("annualIncome", "年收入"),
    AVATAR_URL("avatarUrl", "头像URL"),
    BIRTHDAY("birthday", "生日"),
    PROVINCE("province", "省"),
    CITY("city", "市"),
    DISTRICT("district", "区"),
    DETAILED_ADDRESS("detailedAddress", "详细地址"),
    EDUCATION("education", "学历"),
    EMAIL("email", "邮箱"),
    MARITAL("marital", "婚姻状况"),
    NAME("name", "型名"),
    NICKNAME("nickName", "昵称"),
    PHONE("phone", "手机号"),
    QQ("qq", "QQ号"),
    SEX("sex", "性别"),
    HOBBIES("hobbies", "爱好"),
    JOB("job", "工作"),
    STATUS("status", "状态"),
    WE_CHAT("wechat", "微信号"),
    MEMBER_LEVEL("memberLevel", "会员等级"),
    ORIGIN("origin","会员来源"),
    INTEGRAL("integral","积分"),
    LIFE_CYCLE("lifeCycle","生命周期")
    ;
    private final String code;

    private final String describe;

    FlowCanvasExecutePropertyEnum(String code, String describe){
        this.code = code;
        this.describe = describe;
    }
}
