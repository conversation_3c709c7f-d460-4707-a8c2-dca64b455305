package com.deepexi.dxp.marketing.enums.flow;

import lombok.Getter;

/**
 * @Enum: FlowCanvasModelFilterTypeEnum
 * @Description:
 * @Author: zht
 * @Date: 2020/7/28
 */
@Getter
public enum FlowCanvasModelFilterTypeEnum {
    RANK(1,"按排名"),
    SCORE(2,"按分值");

    private final Integer value;

    private final String name;

    FlowCanvasModelFilterTypeEnum(Integer value, String name){
        this.value = value;
        this.name = name;
    }
}
