package com.deepexi.dxp.marketing.enums.flow;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;

/**
 * 状态操作命令与状态操作过程状态映射关系
 * @Author: HuangBo
 * @Date: 2020/7/24 18:59
 */
@Getter
public enum FlowCanvasStatusMapEnum {

    /**
     * 发布操作：未发布 —> 发布中
     */
    RELEASE_COMMAND(Lists.newArrayList(FlowCanvasStatusEnum.UN_PUBLISHED), FlowCanvasStatusEnum.PUBLISHING),

    /**
     * 暂停操作：运行中 —> 暂停中
     */
    SUSPEND_COMMAND(Lists.newArrayList(FlowCanvasStatusEnum.RUNNING), FlowCanvasStatusEnum.SUSPENDING),

    /**
     * 恢复执行操作：已暂停 —> 恢复中
     */
    RECOVER_COMMAND(Lists.newArrayList(FlowCanvasStatusEnum.SUSPEND), FlowCanvasStatusEnum.RECOVERING),


    /**
     * 终止操作：运行中 —> 终止中
     */
    BREAK_OFF_COMMAND(Lists.newArrayList(FlowCanvasStatusEnum.RUNNING, FlowCanvasStatusEnum.BREAKING_OFF), FlowCanvasStatusEnum.BREAKING_OFF);


    FlowCanvasStatusMapEnum(List<FlowCanvasStatusEnum> command, FlowCanvasStatusEnum transitionStatus) {
        this.command = command;
        this.transitionStatus = transitionStatus;
    }

    private final List<FlowCanvasStatusEnum> command;
    private final FlowCanvasStatusEnum transitionStatus;

    /**
     * 检查状态值是否包含在mapEnum中
     * @param statusValue
     * @return
     */
    public Boolean contain(Integer statusValue){
        if (statusValue == null){
            return false;
        }
        for(FlowCanvasStatusEnum statusEnum : this.getCommand()){
            if(statusEnum.getValue().equals(statusValue)){
                return true;
            }
        }
        return false;
    }

    /*public String getCommandDescribe(){
        return Collections3.extractToString(this.getCommand(),"name", "或");
    }*/

}
