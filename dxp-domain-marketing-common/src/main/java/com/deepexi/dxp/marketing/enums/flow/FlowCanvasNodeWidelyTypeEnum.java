package com.deepexi.dxp.marketing.enums.flow;

import lombok.Getter;

/**
 * @Class: FlowCanvasNodeWidelyTypeEnum
 * @Description:
 * @Author: zht
 * @Date: 2020/7/16
 */
@Getter
public enum  FlowCanvasNodeWidelyTypeEnum {
    USER("user", "用户"),
    TASK("task", "动作"),
    CONDITION("condition", "条件");

    private final String code;

    private final String describe;

    FlowCanvasNodeWidelyTypeEnum(String code, String describe){
        this.code = code;
        this.describe = describe;
    }
}
