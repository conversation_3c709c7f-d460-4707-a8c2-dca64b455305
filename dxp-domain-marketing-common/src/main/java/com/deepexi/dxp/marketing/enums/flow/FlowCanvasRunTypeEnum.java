package com.deepexi.dxp.marketing.enums.flow;

import com.deepexi.dxp.marketing.enums.BaseEnum;
import lombok.Getter;

import java.util.Objects;

/**
 * @Enum: FlowCanvasRunTypeEnum
 * @Description:
 * @Author: zht
 * @Date: 2020/7/16
 */
@Getter
public enum FlowCanvasRunTypeEnum implements BaseEnum<String> {
    RIGHT_NOW("right_now", "立即执行"),
    SCHEDULED("scheduled", "定时执行"),
    FIXED_AT_SPEC_TIME("fixed_at_spec_time", "指定时间重复执行"),
    FIXED_AT_SPEC_PERIOD("fixed_at_spec_period", "每隔一段时间重复"),
    EVENT("event", "事件触发");

    /**
     * 枚举值
     */
    private final String value;
    /**
     * 枚举名称
     */
    private final String name;

    FlowCanvasRunTypeEnum(String value, String name){
        this.value = value;
        this.name = name;
    }

    public static Boolean isExist(String runType) {
        if (Objects.isNull(runType)) {
            return Boolean.FALSE;
        }
        for (FlowCanvasRunTypeEnum value : values()) {
            if (runType.equals(value.getValue())) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

}
