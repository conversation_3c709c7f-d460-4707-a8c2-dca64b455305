package com.deepexi.dxp.marketing.enums.tags;

import lombok.Getter;
import org.springframework.util.Assert;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Enum: MemberTagTypeEnum
 * @Description:
 * @Author: zht
 * @Date: 2020/11/27
 */
@Getter
public enum UserTagTypeEnum implements TagSubTypeEnum {
    MANUAL(1, "印象标签", "manual"),
    RULE(2, "规则标签", "rule"),
    PREFER(3, "偏好标签", "prefer"),
    CROWD(4, "客群", "crowd"),
    CYCLE(5, "生命周期", "cycle");

    private Integer index;
    private String name;
    private String code;

    UserTagTypeEnum(Integer index, String name, String code){
        this.index = index;
        this.name = name;
        this.code = code;
    }

    private static final Map<Integer, UserTagTypeEnum> map;

    static {
        map = Arrays.stream(UserTagTypeEnum.values()).collect(Collectors.toMap(UserTagTypeEnum::getIndex, Function.identity()));
    }

    /**
     * 根据index获取枚举
     *
     * @param index 类型code
     * @return 对应枚举类型
     * @throws IllegalArgumentException 如果未找到会抛异常
     */
    public static UserTagTypeEnum of(Integer index) {
        UserTagTypeEnum userTagTypeEnum = map.get(index);
        Assert.notNull(userTagTypeEnum, "标签所属类型错误");
        return userTagTypeEnum;
    }

}
