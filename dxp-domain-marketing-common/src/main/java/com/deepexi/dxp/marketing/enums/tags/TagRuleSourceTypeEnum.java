package com.deepexi.dxp.marketing.enums.tags;

import lombok.Getter;

/**
 * @Enum: TagRuleSourceTypeEnum
 * @Description:
 * @Author: zht
 * @Date: 2020/11/4
 */
@Getter
public enum TagRuleSourceTypeEnum {
    META(1, "元数据"),
    TAG(2,"标签");

    private Integer code;
    private String name;

    TagRuleSourceTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
