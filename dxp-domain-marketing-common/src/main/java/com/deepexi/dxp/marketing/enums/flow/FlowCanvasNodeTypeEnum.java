package com.deepexi.dxp.marketing.enums.flow;

import lombok.Getter;

/**
 * @Enum: FlowCanvasNodeTypeEnum
 * @Description:
 * @Author: zht
 * @Date: 2020/7/16
 */
@Getter
public enum FlowCanvasNodeTypeEnum {
    USER_QX("user_qx","圈选用户"),
    TASK_SMS_MSG("task_sms_msg","短信通知"),
    TASK_WX_MSG("task_wx_msg","微信通知"),
    TASK_NOTIFI("task_notifi","PUSH通知"),
    TASK_APP_NOTIFI("task_app_notifi","站内信"),
    TASK_EDM("task_edm","EDM邮件"),
    TASK_SET_RESOURCE("task_set_resource","配置资源"),
    TASK_SLEEP("task_sleep","延时等待"),
    TASK_ADD_TAG("task_add_tag","打标签"),
    TASK_REMOVE_TAG("task_remove_tag","移除标签"),
    TASK_ADD_SCORE("task_add_score","发放积分"),
    TASK_DES_SCORE("task_des_score","扣减积分"),
    TASK_MODIFY_ATTR("task_modify_attr","更变属性"),
    COND_ATTR("cond_attr","属性判断"),
    COND_TAG("cond_tag","标签判断"),
    COND_KQ("cond_kq","客群判断"),
    COND_MODEL("cond_model","模型判断"),
    COND_EVENT("cond_event","行为判断"),
    COND_ZB("cond_zb","指标判断");

    private final String code;

    private final String describe;

    FlowCanvasNodeTypeEnum(String code, String describe){
        this.code = code;
        this.describe = describe;
    }
}
