package com.deepexi.dxp.marketing.aop;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * 缓存切面
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/9/23 16:18
 */
@Component
@Aspect
@Slf4j
public class CacheAspect {

//    @Autowired(required = false)
//    private RedisService redisService;
//
//    @Around(value = "@annotation(com.deepexi.dxp.marketing.annotation.GetCacheable)")
//    public Object getCache(ProceedingJoinPoint point) throws Throwable {
//        StopWatch stopWatch = new StopWatch();
//        stopWatch.start();
//
//        Object result;
//        GetCacheable getCacheable = ((MethodSignature) point.getSignature()).getMethod().getAnnotation(GetCacheable.class);
//        Class<?> returnType = ((MethodSignature) point.getSignature()).getMethod().getReturnType();
//        String key = getCacheable.cacheKey() + ":" + generateKey(point);
//        Object value = null;
//        try {
//            value = redisService.get(key);
//        } catch (Exception e) {
//            log.error("查询redis缓存错误：{}", e.getMessage());
//        }
//        if (Objects.isNull(value)) {
//            result = point.proceed(point.getArgs());
//            try {
//                redisService.setex(key, result, getCacheable.expireTime());
//                long ttlExpireTime = redisService.getExpire(getCacheable.cacheKey());
//                redisService.saddc(getCacheable.cacheKey(), key, String.class);
//                if (ttlExpireTime > 0 && (ttlExpireTime + 1) > getCacheable.expireTime()) {
//                    redisService.expire(getCacheable.cacheKey(), (int) (ttlExpireTime + 1));
//                } else {
//                    redisService.expire(getCacheable.cacheKey(), getCacheable.expireTime());
//                }
//            } catch (Exception e) {
//                log.error("数据写入redis错误：{}", e.getMessage());
//            }
//        } else {
//            String newValue = value.toString();
//            if (returnType == List.class) {
//                result = parseArray(newValue, getCacheable.returnType());
//            } else {
//                result = parseObject(newValue, returnType);
//            }
//        }
//
//        stopWatch.stop();
//        log.info("缓存切面获取缓存 ↑ ↑ ↑ ↑ ↑ ↑ end ↑ ↑ ↑ ↑ ↑ ↑,响应时间:{}毫秒\n",
//                stopWatch.getTotalTimeMillis());
//        log.info("get cache! key={},value={}", key, result);
//        return result;
//    }
//
//    @Around(value = "@annotation(com.deepexi.dxp.marketing.annotation.ClearCache)")
//    public Object clearCache(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
//        StopWatch stopWatch = new StopWatch();
//        stopWatch.start();
//
//        Object result = null;
//        Signature signature = proceedingJoinPoint.getSignature();
//        Method method = ((MethodSignature) signature).getMethod();
//        Method realMethod = proceedingJoinPoint.getTarget().getClass().getDeclaredMethod(signature.getName(), method.getParameterTypes());
//        ClearCache clearCache = realMethod.getAnnotation(ClearCache.class);
//        String[] clearKeys = clearCache.cacheKey();
//        result = proceedingJoinPoint.proceed(proceedingJoinPoint.getArgs());
//        try {
//            for (String cacheKey : clearKeys) {
//                Set<String> cacheKeys = redisService.smembers(cacheKey);
//                if (CollectionUtil.isNotEmpty(cacheKeys)) {
//                    cacheKeys.forEach(item -> redisService.del(item));
//                }
//                redisService.del(cacheKey);
//                log.info("clear cache! key={}, value={}", clearCache.cacheKey(), cacheKeys);
//            }
//        } catch (Exception e) {
//            log.error("清除缓存失败：{}", e.getMessage());
//        }
//        stopWatch.stop();
//        log.info("缓存切面清除缓存 ↑ ↑ ↑ ↑ ↑ ↑ end ↑ ↑ ↑ ↑ ↑ ↑,响应时间:{}毫秒\n",
//                stopWatch.getTotalTimeMillis());
//        return result;
//    }
//
//    private String generateKey(ProceedingJoinPoint pJoinPoint) {
//        return ":" + StringUtils.join(pJoinPoint.getArgs()) + "::";
//    }
}
