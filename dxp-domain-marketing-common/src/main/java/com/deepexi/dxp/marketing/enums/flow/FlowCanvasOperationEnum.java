package com.deepexi.dxp.marketing.enums.flow;

import lombok.Getter;

/**
 * 流程画布运算符枚举
 * <AUTHOR>
 * @version 1.0
 * @date 2020-07-28 10:53
 */
@Getter
public enum FlowCanvasOperationEnum {
    EQUAL("=","等于"),
    LESS_THAN("<","小于"),
    LE("<=","小于或等于"),
    GREATER_THAN(">","大于"),
    GE(">=","大于或等于"),
    IN("in","闭区间"),
    NE("ne","不等于"),
    NOT_IN("not_in","不等于"),
    LESS("less","正相差"),
    MORE("more","反相差"),
    CONTAINS("contains","包含"),
    NOT_CONTAINS("not_contains","不包含"),
    BETWEEN("between","之间"),
    NOT_BETWEEN("not_between","区间外"),
    NULL("null","为空值"),
    NOT_NULL("not_null","不为空值"),
    TOP_OF_THE_LIST("1","排名前"),
    BOTTOM_OF_THE_LIST("2","排名后");

    private final String code;

    private final String describe;

    FlowCanvasOperationEnum(String code, String describe){
        this.code = code;
        this.describe = describe;
    }
}
