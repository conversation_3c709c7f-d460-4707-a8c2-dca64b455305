package com.deepexi.dxp.marketing.enums.home;

import lombok.Getter;

/**
 * 时间单位类型
 *
 * <AUTHOR>
 * @since 2020年04月09日 17:50
 */
@Getter
public enum TimeUnitTypeEnum {

    /**
     * 小时
     */
    HOUR(1, "小时"),
    /**
     * 天
     */
    DAY(2, "天"),
    /**
     * 周
     */
    WEEK(3, "周"),
    /**
     * 月
     */
    MONTH(4, "月");

    private final Integer state;

    private final String msg;

    TimeUnitTypeEnum(Integer state, String msg) {
        this.state = state;
        this.msg = msg;
    }

    public Integer getState() {
        return state;
    }

    public String getMsg() {
        return msg;
    }
}
