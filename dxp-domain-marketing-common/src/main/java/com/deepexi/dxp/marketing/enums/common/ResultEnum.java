package com.deepexi.dxp.marketing.enums.common;

import com.deepexi.util.constant.BaseEnumType;

public enum ResultEnum implements BaseEnumType {
    UNKNOWN_ERROR("500", "系统出异常啦!请联系管理员!!!"),
    SMS_STATUS       ("200", "发送成功"),
    USER_EXIST("100002", "用户已存在！"),
    NETWORK_LIMIT("100001", "网络限流！"),
    MEMBER_PHONE_EXISTS("100003", "该用户已存在，将更新部分新数据"),
    PARAMETER_ERROR("100004", "入参错误"),
    DATA_SELECT_ERROR("100005","系统异常，查询数据失败"),

    TOKEN_NOT_FOUND("200001", "token不能为空！"),
    TENANT_NOT_FOUND("200002", "tenantId不能为空！"),
    QUERY_PARAM_NOT_FOUND("200003", "查询参数不能为空"),
    REQUEST_PARAM_NOT_FOUND("200003", "请求参数不能为空"),
    MEMBER_NOT_EXIST("200005", "会员不存在"),
    MEMBER_ID_CANNOT_NULL("200006", "会员id不能为空");

    private final String code;
    private final String msg;

    ResultEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }


    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }


}
