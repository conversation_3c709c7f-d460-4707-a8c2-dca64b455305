package com.deepexi.dxp.marketing.aop;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.deepexi.common.extension.AppRuntimeEnv;
import com.deepexi.dxp.marketing.annotation.LogPrint;
import com.deepexi.dxp.marketing.extension.HuafaRuntimeEnv;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 日志统一打印切面
 *
 * <AUTHOR>
 */
@Component
@Aspect
class LogAspect {

    private static Logger log = LoggerFactory.getLogger(LogAspect.class);

    @Pointcut("within(@org.springframework.web.bind.annotation.RestController *)")
    public void apiLogAop() {
        // apo harder
    }

    @Around("apiLogAop()")
    public Object aroundApi(ProceedingJoinPoint point) throws Throwable {
        String args = argsToString(point.getArgs());
        log.info("日志统一打印 ↓ ↓ ↓ ↓ ↓ ↓ {}.{}() start ↓ ↓ ↓ ↓ ↓ ↓,参数:\n{}",
                point.getSignature().getDeclaringTypeName(),
                point.getSignature().getName(),
                args);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        //默认一个tenantId appId 开始
        AppRuntimeEnv.setTenantId("hfb981fd0e654f7c90470bc865d83690");
        AppRuntimeEnv.setAppId(1001L);
        //默认一个tenantId appId 开始

        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = (HttpServletRequest)requestAttributes.resolveReference(RequestAttributes.REFERENCE_REQUEST);
        //用户ID
        HuafaRuntimeEnv.setUserId(StrUtil.blankToDefault(request.getHeader("userId"),request.getParameter("userId")));
        //岗位ID
        HuafaRuntimeEnv.setPositionId(StrUtil.blankToDefault(request.getHeader("positionId"),request.getParameter("positionId")));
        HuafaRuntimeEnv.setCreatedBy(StrUtil.blankToDefault(request.getHeader("createdBy"),request.getParameter("createdBy")));
        HuafaRuntimeEnv.setPhone(StrUtil.blankToDefault(request.getHeader("phone"),request.getParameter("phone")));
        log.info("请求用户，岗位---------{}，{}", HuafaRuntimeEnv.getUserId(), HuafaRuntimeEnv.getPositionId());
        MethodSignature signature = (MethodSignature) point.getSignature();
        String declaringTypeName = signature.getDeclaringTypeName();
        String methodName = signature.getName();


        Object response = null;
        LogPrint logPrint = ((MethodSignature) point.getSignature()).getMethod().getAnnotation(LogPrint.class);
        try {
            //执行该方法
            response = point.proceed();
        } finally {
            stopWatch.stop();

            if (logPrint == null || logPrint.isPrint()) {
                log.info("统一日志打印(end): {}.{}() ↑ ↑ ↑ ↑ ↑,响应时间:{}毫秒,响应内容:\n{}",
                        declaringTypeName, methodName, stopWatch.getTotalTimeMillis(), argsToString(response));
            }

        }
        return response;
    }

    private String argsToString(Object object) {
        try {
            return JSON.toJSONString(object);
        } catch (Exception e) {
            log.error("APO拦截时，参数装换异常", e);
        }
        return String.valueOf(object);
    }
}
