package com.deepexi.dxp.marketing.enums.tags;

import lombok.Getter;

/**
 * @Enum: TagValidityTypeEnum
 * @Description:
 * @Author: zht
 * @Date: 2020/11/4
 */
@Getter
public enum TagValidityTypeEnum {
    FOREVER(0,"永久"),
    MONTH(1,"月份")
    ;

    private Integer code;
    private String name;

    TagValidityTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
