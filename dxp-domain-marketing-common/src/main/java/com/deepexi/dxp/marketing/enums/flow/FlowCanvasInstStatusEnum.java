package com.deepexi.dxp.marketing.enums.flow;

import com.deepexi.dxp.marketing.enums.BaseEnum;
import lombok.Getter;

/**
 * 流程画布实例状态枚举
 * @Date: 2020/7/25 10:44
 */
@Getter
public enum FlowCanvasInstStatusEnum implements BaseEnum<Integer> {

    /**
     * 运行中
     */
    RUNNING(1, "运行中"),

    /**
     * 启动/恢复执行流程实例失败
     */
    FAILED(2, "失败"),

    /**
     * 已暂停
     */
    SUSPEND(3, "已暂停"),

    /**
     * 已结束
     */
    FINISHED(4,"已结束");


    private final Integer value;

    private final String name;

    FlowCanvasInstStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static FlowCanvasInstStatusEnum getEnumByValue(Integer value) {
        FlowCanvasInstStatusEnum[] items = values();
        for (FlowCanvasInstStatusEnum item : items) {
            if (item.value.equals(value)) {
                return item;
            }
        }
        return null;
    }
}
