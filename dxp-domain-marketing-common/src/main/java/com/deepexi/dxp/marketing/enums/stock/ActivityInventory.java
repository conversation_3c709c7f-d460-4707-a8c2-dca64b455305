package com.deepexi.dxp.marketing.enums.stock;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> xin<PERSON><PERSON>.yao
 * @date 2019/12/16 14:10
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ActivityInventory {

    /**
     * 活动扣减库存
     */
    ACTIVITY_INVENTORY("activityInventory", "活动库存"),
    SALES_INVENTORY("salesInventory", "销售库存");
    private String id;
    private String value;
}
