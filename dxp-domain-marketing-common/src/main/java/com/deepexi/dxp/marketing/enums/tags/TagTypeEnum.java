package com.deepexi.dxp.marketing.enums.tags;

import lombok.Getter;

import java.util.Objects;

/**
 * @author: zhang.yongwei
 */
@Getter
public enum TagTypeEnum {
    MEMBER(1,"用户", "tag_user", UserTagTypeEnum.values()),
    COMMODITY(2,"商品", "tag_commodity", CommodityTagTypeEnum.values()),
    RESOURCE(3,"资源", "tag_resource", ResourceTagTypeEnum.values()),
    STORE(4,"门店", "tag_store", StoreTagTypeEnum.values());

    private Integer index;
    private String name;
    private String code;
    private TagSubTypeEnum[] tagSubTypeEnum;

    TagTypeEnum(Integer index, String name, String code, TagSubTypeEnum[] tagSubTypeEnum){
        this.index = index;
        this.name = name;
        this.code = code;
        this.tagSubTypeEnum = tagSubTypeEnum;
    }

    public static TagTypeEnum getByIndex(Integer index){
        TagTypeEnum[] values = values();
        for (TagTypeEnum item : values){
            if (item.getIndex().equals(index)){
                return item;
            }
        }
        return null;
    }

    public static String getCode(Integer index){
        TagTypeEnum[] values = values();
        for (TagTypeEnum item : values){
            if (item.getIndex().equals(index)){
                return item.getCode();
            }
        }
        return null;
    }

    public static String getCode(Integer index, Integer subIndex){
        TagTypeEnum[] values = values();
        TagTypeEnum value = null;
        TagSubTypeEnum[] subTypeEnums = null;
        for (TagTypeEnum item : values){
            if (item.getIndex().equals(index)){
                value = item;
                subTypeEnums = item.getTagSubTypeEnum();
            }
        }
        if (Objects.isNull(value)){
            return null;
        }
        for (TagSubTypeEnum item : subTypeEnums){
            if (item.getIndex().equals(subIndex)){
                return TagTypeEnum.MEMBER.equals(value) ? value.getCode() + "_" + item.getCode() : value.getCode();
            }
        }
        return null;
    }

}
