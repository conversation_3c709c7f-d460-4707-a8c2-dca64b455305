package com.deepexi.dxp.marketing.enums.user;


import lombok.Getter;

/**
 * 状态 枚举
 *
 * <AUTHOR>
 * @Date 2020/3/5
 */
@Getter
public enum MemberStatusEnum {

    /**
     * 启用
     */
    ENABLE(1, "启用"),
    /**
     * 禁用
     */
    DISABLE(0, "禁用");

    private final Integer value;

    private final String label;

    MemberStatusEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    public static MemberStatusEnum getEnumByStrValue(Integer value) {
        MemberStatusEnum[] items = values();
        for (MemberStatusEnum item : items) {
            if (item.value.equals(value)) {
                return item;
            }
        }
        return null;
    }

}
